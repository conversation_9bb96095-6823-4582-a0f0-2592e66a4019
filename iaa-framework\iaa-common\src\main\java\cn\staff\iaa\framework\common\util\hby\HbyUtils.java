package cn.staff.iaa.framework.common.util.hby;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class HbyUtils {

    /**
     * 比较并更新对象的字段
     * @param oldObj 旧对象
     * @param newObj 新对象
     * @param excludedFields 排除的字段名：可变参数 "id", "createTime" 或者 new String[]{"id", "createTime"}
     * @return 是否进行了更新
     */
    public static boolean compareAndUpdate(Object oldObj, Object newObj, String... excludedFields) {
        if (!oldObj.getClass().equals(newObj.getClass())) {
            throw new IllegalArgumentException("对象类型不一致");
        }

        List<String> excluded = Arrays.asList(excludedFields);
        boolean isDifferent = false;
        Class<?> clazz = oldObj.getClass();

        // 遍历所有字段进行比较
        for (Field field : clazz.getDeclaredFields()) {
            if (excluded.contains(field.getName())) continue;

            field.setAccessible(true);
            Object oldValue = ReflectionUtils.getField(field, oldObj);
            Object newValue = ReflectionUtils.getField(field, newObj);

            if (!Objects.equals(oldValue, newValue)) {
                log.info("比较不同字段值：{},{}",oldValue,newValue);
                isDifferent = true;
                break; // 发现不同即中断循环
            }
        }
        return isDifferent;
    }

}
