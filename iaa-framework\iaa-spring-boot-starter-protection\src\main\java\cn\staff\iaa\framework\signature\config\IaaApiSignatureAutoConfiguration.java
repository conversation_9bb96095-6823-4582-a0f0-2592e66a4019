package cn.staff.iaa.framework.signature.config;

import cn.staff.iaa.framework.redis.config.IaaRedisAutoConfiguration;
import cn.staff.iaa.framework.signature.core.aop.ApiSignatureAspect;
import cn.staff.iaa.framework.signature.core.redis.ApiSignatureRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * HTTP API 签名的自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(after = IaaRedisAutoConfiguration.class)
public class IaaApiSignatureAutoConfiguration {

    @Bean
    public ApiSignatureAspect signatureAspect(ApiSignatureRedisDAO signatureRedisDAO) {
        return new ApiSignatureAspect(signatureRedisDAO);
    }

    @Bean
    public ApiSignatureRedisDAO signatureRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new ApiSignatureRedisDAO(stringRedisTemplate);
    }

}
