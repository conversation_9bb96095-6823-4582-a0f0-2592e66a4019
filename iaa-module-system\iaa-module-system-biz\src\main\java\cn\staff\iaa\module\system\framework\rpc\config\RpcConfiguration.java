package cn.staff.iaa.module.system.framework.rpc.config;

import cn.staff.iaa.module.infra.api.config.ConfigApi;
import cn.staff.iaa.module.infra.api.file.FileApi;
import cn.staff.iaa.module.infra.api.websocket.WebSocketSenderApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, WebSocketSenderApi.class, ConfigApi.class})
public class RpcConfiguration {
}
