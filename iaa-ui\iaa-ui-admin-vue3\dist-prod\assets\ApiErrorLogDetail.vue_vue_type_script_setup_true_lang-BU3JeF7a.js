import{_ as U}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as A,r as _,A as p,o as m,w as l,g as t,a3 as n,H as u,t as r,a as e,D as y,P as E,m as R}from"./index-CRsFgzy0.js";import{E as g,a as k}from"./el-descriptions-item-lelixL8M.js";import{_ as P}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{f as T}from"./formatTime-DhdtkSIS.js";const V=A({name:"ApiErrorLogDetail",__name:"ApiErrorLogDetail",setup(h,{expose:v}){const o=_(!1),c=_(!1),a=_({});return v({open:async f=>{o.value=!0,c.value=!0;try{a.value=f}finally{c.value=!1}}}),(f,d)=>{const s=k,b=P,x=E,S=g,I=U;return m(),p(I,{modelValue:e(o),"onUpdate:modelValue":d[1]||(d[1]=i=>R(o)?o.value=i:null),"max-height":500,scroll:!0,title:"\u8BE6\u60C5",width:"800"},{default:l(()=>[t(S,{column:1,border:""},{default:l(()=>[t(s,{label:"\u65E5\u5FD7\u4E3B\u952E","min-width":"120"},{default:l(()=>[u(r(e(a).id),1)]),_:1}),t(s,{label:"\u94FE\u8DEF\u8FFD\u8E2A"},{default:l(()=>[u(r(e(a).traceId),1)]),_:1}),t(s,{label:"\u5E94\u7528\u540D"},{default:l(()=>[u(r(e(a).applicationName),1)]),_:1}),t(s,{label:"\u7528\u6237\u7F16\u53F7"},{default:l(()=>[u(r(e(a).userId)+" ",1),t(b,{type:e(y).USER_TYPE,value:e(a).userType},null,8,["type","value"])]),_:1}),t(s,{label:"\u7528\u6237 IP"},{default:l(()=>[u(r(e(a).userIp),1)]),_:1}),t(s,{label:"\u7528\u6237 UA"},{default:l(()=>[u(r(e(a).userAgent),1)]),_:1}),t(s,{label:"\u8BF7\u6C42\u4FE1\u606F"},{default:l(()=>[u(r(e(a).requestMethod)+" "+r(e(a).requestUrl),1)]),_:1}),t(s,{label:"\u8BF7\u6C42\u53C2\u6570"},{default:l(()=>[u(r(e(a).requestParams),1)]),_:1}),t(s,{label:"\u5F02\u5E38\u65F6\u95F4"},{default:l(()=>[u(r(e(T)(e(a).exceptionTime)),1)]),_:1}),t(s,{label:"\u5F02\u5E38\u540D"},{default:l(()=>[u(r(e(a).exceptionName),1)]),_:1}),e(a).exceptionStackTrace?(m(),p(s,{key:0,label:"\u5F02\u5E38\u5806\u6808"},{default:l(()=>[t(x,{modelValue:e(a).exceptionStackTrace,"onUpdate:modelValue":d[0]||(d[0]=i=>e(a).exceptionStackTrace=i),autosize:{maxRows:20},readonly:!0,type:"textarea"},null,8,["modelValue"])]),_:1})):n("",!0),t(s,{label:"\u5904\u7406\u72B6\u6001"},{default:l(()=>[t(b,{type:e(y).INFRA_API_ERROR_LOG_PROCESS_STATUS,value:e(a).processStatus},null,8,["type","value"])]),_:1}),e(a).processUserId?(m(),p(s,{key:1,label:"\u5904\u7406\u4EBA"},{default:l(()=>[u(r(e(a).processUserId),1)]),_:1})):n("",!0),e(a).processTime?(m(),p(s,{key:2,label:"\u5904\u7406\u65F6\u95F4"},{default:l(()=>[u(r(e(T)(e(a).processTime)),1)]),_:1})):n("",!0)]),_:1})]),_:1},8,["modelValue"])}}});export{V as _};
