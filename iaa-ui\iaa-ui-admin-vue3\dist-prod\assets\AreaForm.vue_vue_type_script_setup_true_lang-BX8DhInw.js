import{d as h,p as x,r as i,f as A,A as v,o as c,w as s,J as F,s as U,a as l,g as u,v as C,P as j,M as q,G,H as _,m as H}from"./index-CRsFgzy0.js";import{_ as J}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as M}from"./index-DLC3Afbg.js";const R=h({name:"SystemAreaForm",__name:"AreaForm",setup(S,{expose:V}){const y=x(),o=i(!1),t=i(!1),a=i({ip:"",result:void 0}),b=A({ip:[{required:!0,message:"IP \u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),d=i();V({open:async()=>{o.value=!0,g()}});const P=async()=>{if(d&&await d.value.validate()){t.value=!0;try{a.value.result=await M(a.value.ip.trim()),y.success("\u67E5\u8BE2\u6210\u529F")}finally{t.value=!1}}},g=()=>{var m;a.value={ip:"",result:void 0},(m=d.value)==null||m.resetFields()};return(m,e)=>{const p=j,n=C,I=U,f=G,k=J,w=q;return c(),v(k,{modelValue:l(o),"onUpdate:modelValue":e[3]||(e[3]=r=>H(o)?o.value=r:null),title:"IP \u67E5\u8BE2"},{footer:s(()=>[u(f,{disabled:l(t),type:"primary",onClick:P},{default:s(()=>e[4]||(e[4]=[_("\u786E \u5B9A")])),_:1},8,["disabled"]),u(f,{onClick:e[2]||(e[2]=r=>o.value=!1)},{default:s(()=>e[5]||(e[5]=[_("\u53D6 \u6D88")])),_:1})]),default:s(()=>[F((c(),v(I,{ref_key:"formRef",ref:d,model:l(a),rules:l(b),"label-width":"80px"},{default:s(()=>[u(n,{label:"IP",prop:"ip"},{default:s(()=>[u(p,{modelValue:l(a).ip,"onUpdate:modelValue":e[0]||(e[0]=r=>l(a).ip=r),placeholder:"\u8BF7\u8F93\u5165 IP \u5730\u5740"},null,8,["modelValue"])]),_:1}),u(n,{label:"\u5730\u5740",prop:"result"},{default:s(()=>[u(p,{modelValue:l(a).result,"onUpdate:modelValue":e[1]||(e[1]=r=>l(a).result=r),placeholder:"\u5C55\u793A\u67E5\u8BE2 IP \u7ED3\u679C",readonly:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[w,l(t)]])]),_:1},8,["modelValue"])}}});export{R as _};
