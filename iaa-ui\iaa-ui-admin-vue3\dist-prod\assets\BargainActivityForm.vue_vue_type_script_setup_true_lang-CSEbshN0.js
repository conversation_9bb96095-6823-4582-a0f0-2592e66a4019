import{as as v,f as T,d as O,b as Q,p as j,r as u,c as q,o as U,F as z,g as s,a as n,m as K,w as l,J as W,A as X,G as Z,H as I,L as $,an as aa,M as ea,aO as k,aG as ia,aP as ra,aQ as w}from"./index-CRsFgzy0.js";import{_ as ta}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as oa}from"./Form-BF4H89jq.js";import{b as R}from"./formatTime-DhdtkSIS.js";import{r as P}from"./formRules-V2Qetfkc.js";import{u as sa}from"./useCrudSchemas-CNYomGr4.js";import{_ as na}from"./SpuSelect.vue_vue_type_script_setup_true_lang-tSVtv9n7.js";import{_ as la}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-DGosLJYC.js";import{g as ca}from"./index-CQgbu5O7.js";import{b as ua}from"./spu-BHhhuUrI.js";const ma=async M=>await v.get({url:"/promotion/bargain-activity/page",params:M}),pa=async M=>await v.put({url:"/promotion/bargain-activity/close?id="+M}),da=T({name:[P],startTime:[P],endTime:[P],helpMaxCount:[P],bargainCount:[P],singleLimitCount:[P]}),fa=T([{label:"\u780D\u4EF7\u6D3B\u52A8\u540D\u79F0",field:"name",isSearch:!0,isTable:!1,form:{colProps:{span:24}}},{label:"\u6D3B\u52A8\u5F00\u59CB\u65F6\u95F4",field:"startTime",formatter:R,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u6D3B\u52A8\u7ED3\u675F\u65F6\u95F4",field:"endTime",formatter:R,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u780D\u4EF7\u4EBA\u6570",field:"helpMaxCount",isSearch:!1,form:{component:"InputNumber",labelMessage:"\u53C2\u4E0E\u4EBA\u6570\u4E0D\u80FD\u5C11\u4E8E\u4E24\u4EBA",value:2}},{label:"\u6700\u5927\u5E2E\u780D\u6B21\u6570",field:"bargainCount",isSearch:!1,form:{component:"InputNumber",labelMessage:"\u53C2\u4E0E\u4EBA\u6570\u4E0D\u80FD\u5C11\u4E8E\u4E24\u4EBA",value:2}},{label:"\u603B\u9650\u8D2D\u6570\u91CF",field:"totalLimitCount",isSearch:!1,form:{component:"InputNumber",labelMessage:"\u7528\u6237\u6700\u5927\u80FD\u53D1\u8D77\u780D\u4EF7\u7684\u6B21\u6570",value:0}},{label:"\u780D\u4EF7\u7684\u6700\u5C0F\u91D1\u989D",field:"randomMinPrice",isSearch:!1,isTable:!1,form:{component:"InputNumber",componentProps:{min:0,precision:2,step:.1},labelMessage:"\u7528\u6237\u6BCF\u6B21\u780D\u4EF7\u7684\u6700\u5C0F\u91D1\u989D",value:0}},{label:"\u780D\u4EF7\u7684\u6700\u5927\u91D1\u989D",field:"randomMaxPrice",isSearch:!1,isTable:!1,form:{component:"InputNumber",componentProps:{min:0,precision:2,step:.1},labelMessage:"\u7528\u6237\u6BCF\u6B21\u780D\u4EF7\u7684\u6700\u5927\u91D1\u989D",value:0}},{label:"\u780D\u4EF7\u5546\u54C1",field:"spuId",isSearch:!1,form:{colProps:{span:24}}}]),{allSchemas:ga}=sa(fa),ba=O({name:"PromotionBargainActivityForm",__name:"BargainActivityForm",emits:["success"],setup(M,{expose:A,emit:L}){const{t:C}=Q(),_=j(),p=u(!1),S=u(""),d=u(!1),V=u(""),m=u(),x=u(),D=u(),y=u([]),F=u([]),N=[{name:"productConfig.bargainFirstPrice",rule:i=>i>0,message:"\u5546\u54C1\u780D\u4EF7\u8D77\u59CB\u4EF7\u683C\u4E0D\u80FD\u5C0F\u4E8E 0 \uFF01\uFF01\uFF01"},{name:"productConfig.bargainMinPrice",rule:i=>i>=0,message:"\u5546\u54C1\u780D\u4EF7\u5E95\u4EF7\u4E0D\u80FD\u5C0F\u4E8E 0 \uFF01\uFF01\uFF01"},{name:"productConfig.stock",rule:i=>i>=1,message:"\u5546\u54C1\u6D3B\u52A8\u5E93\u5B58\u4E0D\u80FD\u5C0F\u4E8E 1 \uFF01\uFF01\uFF01"}],E=(i,a)=>{m.value.setValues({spuId:i}),Y(i,a)},Y=async(i,a,e)=>{var h;const t=[],f=await ua([i]);if(f.length==0)return;y.value=[];const o=f[0],g=a===void 0?o==null?void 0:o.skus:(h=o==null?void 0:o.skus)==null?void 0:h.filter(r=>a.includes(r.id));g==null||g.forEach(r=>{let c={spuId:o.id,skuId:r.id,bargainFirstPrice:1,bargainMinPrice:1,stock:1};if(e!==void 0){const b=e.find(J=>J.skuId===r.id);b&&(b.bargainFirstPrice=k(b.bargainFirstPrice),b.bargainMinPrice=k(b.bargainMinPrice)),c=b||c}r.productConfig=c}),o.skus=g,t.push({spuId:o.id,spuDetail:o,propertyList:ca(o)}),y.value.push(o),F.value=t};A({open:async(i,a)=>{if(p.value=!0,S.value=C("action."+i),V.value=i,await B(),a){d.value=!0;try{const e=await(async t=>await v.get({url:"/promotion/bargain-activity/get?id="+t}))(a);e.randomMinPrice=k(e.randomMinPrice),e.randomMaxPrice=k(e.randomMaxPrice),await Y(e.spuId,[e.skuId],[{spuId:e.spuId,skuId:e.skuId,bargainFirstPrice:e.bargainFirstPrice,bargainMinPrice:e.bargainMinPrice,stock:e.stock}]),m.value.setValues(e)}finally{d.value=!1}}}});const B=async()=>{y.value=[],F.value=[],await ia(),m.value.getElFormRef().resetFields()},G=L,H=async()=>{if(m&&await m.value.getElFormRef().validate()){d.value=!0;try{const i=ra(m.value.formModel),a=D.value.getSkuConfigs("productConfig");a.forEach(t=>{t.bargainFirstPrice=w(t.bargainFirstPrice),t.bargainMinPrice=w(t.bargainMinPrice)}),i.randomMinPrice=w(i.randomMinPrice),i.randomMaxPrice=w(i.randomMaxPrice);const e={...i,...a[0]};V.value==="create"?(await(async t=>await v.post({url:"/promotion/bargain-activity/create",data:t}))(e),_.success(C("common.createSuccess"))):(await(async t=>await v.put({url:"/promotion/bargain-activity/update",data:t}))(e),_.success(C("common.updateSuccess"))),p.value=!1,G("success")}finally{d.value=!1}}};return(i,a)=>{const e=Z,t=aa,f=$,o=oa,g=ta,h=ea;return U(),q(z,null,[s(g,{modelValue:n(p),"onUpdate:modelValue":a[2]||(a[2]=r=>K(p)?p.value=r:null),title:n(S),width:"65%"},{footer:l(()=>[s(e,{disabled:n(d),type:"primary",onClick:H},{default:l(()=>a[4]||(a[4]=[I("\u786E \u5B9A")])),_:1},8,["disabled"]),s(e,{onClick:a[1]||(a[1]=r=>p.value=!1)},{default:l(()=>a[5]||(a[5]=[I("\u53D6 \u6D88")])),_:1})]),default:l(()=>[W((U(),X(o,{ref_key:"formRef",ref:m,"is-col":!0,rules:n(da),schema:n(ga).formSchema,class:"mt-10px"},{spuId:l(()=>[s(e,{onClick:a[0]||(a[0]=r=>n(x).open())},{default:l(()=>a[3]||(a[3]=[I("\u9009\u62E9\u5546\u54C1")])),_:1}),s(n(la),{ref_key:"spuAndSkuListRef",ref:D,"rule-config":N,"spu-list":n(y),"spu-property-list-p":n(F)},{default:l(()=>[s(f,{align:"center",label:"\u780D\u4EF7\u8D77\u59CB\u4EF7\u683C(\u5143)","min-width":"168"},{default:l(({row:r})=>[s(t,{modelValue:r.productConfig.bargainFirstPrice,"onUpdate:modelValue":c=>r.productConfig.bargainFirstPrice=c,min:0,precision:2,step:.1,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),s(f,{align:"center",label:"\u780D\u4EF7\u5E95\u4EF7(\u5143)","min-width":"168"},{default:l(({row:r})=>[s(t,{modelValue:r.productConfig.bargainMinPrice,"onUpdate:modelValue":c=>r.productConfig.bargainMinPrice=c,min:0,precision:2,step:.1,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),s(f,{align:"center",label:"\u6D3B\u52A8\u5E93\u5B58","min-width":"168"},{default:l(({row:r})=>[s(t,{modelValue:r.productConfig.stock,"onUpdate:modelValue":c=>r.productConfig.stock=c,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["spu-list","spu-property-list-p"])]),_:1},8,["rules","schema"])),[[h,n(d)]])]),_:1},8,["modelValue","title"]),s(n(na),{ref_key:"spuSelectRef",ref:x,isSelectSku:!0,radio:!0,onConfirm:E},null,512)],64)}}});export{ba as _,pa as c,ma as g};
