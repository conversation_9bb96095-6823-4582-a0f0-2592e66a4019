import{as as U,d as z,p as I,r as p,f as S,A as c,o as f,w as n,g as o,J as D,K as V,a,L as B,M as P,m as R}from"./index-CRsFgzy0.js";import{_ as j}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as k}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as A}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{E}from"./el-avatar-Nl9DW69B.js";import{d as F}from"./formatTime-DhdtkSIS.js";import{f as J}from"./formatter-D3GpDdeL.js";const K=z({name:"BargainRecordListDialog",__name:"BargainRecordListDialog",setup(M,{expose:v}){I();const m=p(!0),d=p(0),u=p([]),r=S({pageNo:1,pageSize:10,recordId:void 0}),w=p(),s=p(!1);v({open:async e=>{s.value=!0,r.recordId=e,_()}});const g=async()=>{m.value=!0;try{const e=await(async t=>await U.get({url:"/promotion/bargain-help/page",params:t}))(r);u.value=e.list,d.value=e.total}finally{m.value=!1}},_=()=>{var e;(e=w.value)==null||e.resetFields(),r.pageNo=1,g()};return(e,t)=>{const i=B,h=E,x=V,b=A,y=k,L=j,N=P;return f(),c(L,{modelValue:a(s),"onUpdate:modelValue":t[2]||(t[2]=l=>R(s)?s.value=l:null),title:"\u52A9\u529B\u5217\u8868"},{default:n(()=>[o(y,null,{default:n(()=>[D((f(),c(x,{data:a(u),stripe:!0,"show-overflow-tooltip":!0},{default:n(()=>[o(i,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId","min-width":"80px"}),o(i,{label:"\u7528\u6237\u5934\u50CF",prop:"avatar","min-width":"80px"},{default:n(l=>[o(h,{src:l.row.avatar},null,8,["src"])]),_:1}),o(i,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname","min-width":"100px"}),o(i,{label:"\u780D\u4EF7\u91D1\u989D",prop:"reducePrice","min-width":"100px",formatter:a(J)},null,8,["formatter"]),o(i,{label:"\u52A9\u529B\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(F),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[N,a(m)]]),o(b,{total:a(d),page:a(r).pageNo,"onUpdate:page":t[0]||(t[0]=l=>a(r).pageNo=l),limit:a(r).pageSize,"onUpdate:limit":t[1]||(t[1]=l=>a(r).pageSize=l),onPagination:g},null,8,["total","page","limit"])]),_:1})]),_:1},8,["modelValue"])}}});export{K as _};
