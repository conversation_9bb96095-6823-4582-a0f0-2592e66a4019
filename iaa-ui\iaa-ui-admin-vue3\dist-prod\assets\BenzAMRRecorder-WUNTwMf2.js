import{dr as Ii,ds as Fi}from"./index-CRsFgzy0.js";var m4={exports:{}};m4.exports=function(){function Q5(H1,r1){if(!(H1 instanceof r1))throw new TypeError("Cannot call a class as a function")}function $5(H1,r1){for(var f1=0;f1<r1.length;f1++){var L=r1[f1];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(H1,L.key,L)}}function J5(H1,r1,f1){return r1&&$5(H1.prototype,r1),f1&&$5(H1,f1),H1}typeof globalThis<"u"||typeof window<"u"||Fi!==void 0||typeof self<"u";var e6={exports:{}};(function(H1){(function(r1,f1){H1.exports=f1()})(0,function(){var r1=(function(){var L,b1=0,R1=[],M1=[];function V1(C1){L=C1.sampleRate}function T0(C1){R1.push(C1[0]),M1.push(C1[1]),b1+=C1[0].length}function A0(C1){var W1=y2(q0(h1(R1,b1),h1(M1,b1))),k1=new Blob([W1],{type:C1});self.postMessage({type:"blob",data:k1})}function K0(){var C1=[];C1.push(h1(R1,b1)),C1.push(h1(M1,b1)),self.postMessage({type:"buffer",data:C1})}function $1(){b1=0,R1=[],M1=[]}function h1(C1,W1){for(var k1=new Float32Array(W1),J1=0,c0=0;c0<C1.length;c0++)k1.set(C1[c0],J1),J1+=C1[c0].length;return k1}function q0(C1,W1){for(var k1=C1.length+W1.length,J1=new Float32Array(k1),c0=0,y0=0;c0<k1;)J1[c0++]=C1[y0],J1[c0++]=W1[y0],y0++;return J1}function f2(C1,W1,k1){for(var J1=0;J1<k1.length;J1++,W1+=2){var c0=Math.max(-1,Math.min(1,k1[J1]));C1.setInt16(W1,c0<0?32768*c0:32767*c0,!0)}}function E0(C1,W1,k1){for(var J1=0;J1<k1.length;J1++)C1.setUint8(W1+J1,k1.charCodeAt(J1))}function y2(C1){var W1=new ArrayBuffer(44+2*C1.length),k1=new DataView(W1);return E0(k1,0,"RIFF"),k1.setUint32(4,36+2*C1.length,!0),E0(k1,8,"WAVE"),E0(k1,12,"fmt "),k1.setUint32(16,16,!0),k1.setUint16(20,1,!0),k1.setUint16(22,2,!0),k1.setUint32(24,L,!0),k1.setUint32(28,4*L,!0),k1.setUint16(32,4,!0),k1.setUint16(34,16,!0),E0(k1,36,"data"),k1.setUint32(40,2*C1.length,!0),f2(k1,44,C1),k1}self.onmessage=function(C1){switch(C1.data.command){case"init":V1(C1.data.config);break;case"record":T0(C1.data.buffer);break;case"exportWAV":A0(C1.data.type);break;case"getBuffer":K0();break;case"clear":$1()}}}).toString().replace(/^\s*function.*?\(\)\s*{/,"").replace(/}\s*$/,""),f1=function(L,b1){var R1=b1||{},M1=R1.bufferLen||4096;this.context=L.context,this.node=(this.context.createScriptProcessor||this.context.createJavaScriptNode).call(this.context,M1,2,2);var V1=new Worker((window.URL||window.webkitURL).createObjectURL(new Blob([r1],{type:"text/javascript"})));V1.onmessage=function($1){$1.data.type==="blob"?A0($1.data.data):T0($1.data.data)},V1.postMessage({command:"init",config:{sampleRate:this.context.sampleRate}});var T0,A0,K0=!1;this.node.onaudioprocess=function($1){K0&&V1.postMessage({command:"record",buffer:[$1.inputBuffer.getChannelData(0),$1.inputBuffer.getChannelData(1)]})},this.configure=function($1){for(var h1 in $1)$1.hasOwnProperty(h1)&&(R1[h1]=$1[h1])},this.record=function(){K0=!0},this.stop=function(){K0=!1},this.clear=function(){V1.postMessage({command:"clear"})},this.getBuffer=function($1){T0=$1||R1.callback,V1.postMessage({command:"getBuffer"})},this.exportWAV=function($1,h1){if(A0=$1||R1.callback,h1=h1||R1.type||"audio/wav",!A0)throw new Error("Callback not set");V1.postMessage({command:"exportWAV",type:h1})},this.release=function(){this.stop(),this.clear(),this.configure=this.record=this.stop=this.clear=this.getBuffer=this.exportWAV=function(){},L.disconnect(this.node),this.node.onaudioprocess=null,this.node.disconnect(),V1.terminate()},L.connect(this.node),this.node.connect(this.context.destination)};return f1.forceDownload=function(L,b1){var R1=(window.URL||window.webkitURL).createObjectURL(L),M1=window.document.createElement("a");M1.href=R1,M1.download=b1||"output.wav";var V1=document.createEvent("Event");V1.initEvent("click",!0,!0),M1.dispatchEvent(V1)},f1})})(e6);var p4=e6.exports,i6=window.AudioContext||window.webkitAudioContext||window.mozAudioContext,u0=null,t6=!0;i6||(t6=!1);var v0=function(){function H1(){Q5(this,H1),this._recorderStream=null,this._recorderStreamSourceNode=null,this._recorder=null,this._isRecording=!1,this._curSourceNode=null}return J5(H1,[{key:"playPcm",value:function(r1,f1,L,b1){u0&&u0.state!=="closed"||(u0=new i6),u0.state!=="interrupted"&&u0.state!=="suspended"||u0.resume(),f1=f1||8e3,this.stopPcm();var R1,M1=b1&&b1>.001?r1.slice(f1*b1):r1;if(!M1.length)return L();this._curSourceNode=u0.createBufferSource();try{R1=u0.createBuffer(1,M1.length,f1)}catch{f1<11025?(R1=u0.createBuffer(1,M1.length,4*f1),this._curSourceNode.playbackRate.value=.25):(R1=u0.createBuffer(1,M1.length,2*f1),this._curSourceNode.playbackRate.value=.5)}R1.copyToChannel?R1.copyToChannel(M1,0,0):R1.getChannelData(0).set(M1),this._curSourceNode.buffer=R1,this._curSourceNode.loop=!1,this._curSourceNode.connect(u0.destination),this._curSourceNode.onended=L,this._curSourceNode.start()}},{key:"stopPcm",value:function(){this._curSourceNode&&(this._curSourceNode.stop(),this._curSourceNode=null)}},{key:"stopPcmSilently",value:function(){this._curSourceNode.onended=null,this.stopPcm()}},{key:"initRecorder",value:function(){var r1=this;return new Promise(function(f1,L){var b1=function(M1){r1._recorderStream=M1,r1._recorderStreamSourceNode=u0.createMediaStreamSource(M1),r1._recorder=new p4(r1._recorderStreamSourceNode),r1._isRecording=!1,f1()},R1=function(M1){L(M1)};r1._recorder?f1():window.navigator.mediaDevices&&window.navigator.mediaDevices.getUserMedia?window.navigator.mediaDevices.getUserMedia({audio:!0}).then(b1).catch(R1):window.navigator.getUserMedia?window.navigator.getUserMedia({audio:!0},b1,R1):R1()})}},{key:"isRecording",value:function(){return this._recorder&&this._isRecording}},{key:"startRecord",value:function(){this._recorder&&(this._recorder.clear(),this._recorder.record(),this._isRecording=!0)}},{key:"stopRecord",value:function(){this._recorder&&(this._recorder.stop(),this._isRecording=!1)}},{key:"generateRecordSamples",value:function(){var r1=this;return new Promise(function(f1){r1._recorder&&r1._recorder.getBuffer(function(L){f1(L[0])})})}},{key:"releaseRecord",value:function(){this._recorderStream&&this._recorderStream.getTracks&&(this._recorderStream.getTracks().forEach(function(r1){r1.stop()}),this._recorderStream=null),this._recorder&&(this._recorder.release(),this._recorder=null)}}],[{key:"isPlaySupported",value:function(){return t6}},{key:"isRecordSupported",value:function(){return!!(window.navigator.mediaDevices&&window.navigator.mediaDevices.getUserMedia||window.navigator.getUserMedia)}},{key:"getCtxSampleRate",value:function(){return u0&&u0.sampleRate||0}},{key:"getCtxTime",value:function(){return u0&&u0.currentTime||0}},{key:"decodeAudioArrayBufferByContext",value:function(r1){return new Promise(function(f1,L){u0.decodeAudioData(r1,function(b1){var R1=b1.numberOfChannels,M1=new Float32Array(b1.length);switch(R1){default:case 1:M1=b1.getChannelData(0);break;case 2:for(var V1=b1.getChannelData(0),T0=b1.getChannelData(1),A0=0,K0=M1.length;A0<K0;A0++)M1[A0]=.5*(V1[A0]+T0[A0]);break;case 4:for(var $1=b1.getChannelData(0),h1=b1.getChannelData(1),q0=b1.getChannelData(2),f2=b1.getChannelData(3),E0=0,y2=M1.length;E0<y2;E0++)M1[E0]=.25*($1[E0]+h1[E0]+q0[E0]+f2[E0]);break;case 6:for(var C1=b1.getChannelData(0),W1=b1.getChannelData(1),k1=b1.getChannelData(2),J1=b1.getChannelData(4),c0=b1.getChannelData(5),y0=0,g2=M1.length;y0<g2;y0++)M1[y0]=.7071*(C1[y0]+W1[y0])+k1[y0]+.5*(J1[y0]+c0[y0])}f1(M1)},L)})}}]),H1}(),k4=function(){var H1=function(){var L,b1={toWAV:function(p){var v=this._decode(p);if(!v)return null;var y=new Uint8Array(v.buffer,v.byteOffset,v.byteLength),_=new Uint8Array(y.length+this.WAV_HEADER_SIZE),e=0,a=function(e1){var s1=new Uint8Array(2);new Int16Array(s1.buffer)[0]=e1,_.set(s1,e),e+=2},x=function(e1){var s1=new Uint8Array(4);new Int32Array(s1.buffer)[0]=e1,_.set(s1,e),e+=4},D=function(e1){var s1=new TextEncoder("utf-8").encode(e1);_.set(s1,e),e+=s1.length};D("RIFF"),x(36+y.length),D("WAVEfmt "),x(16);var G=16,J=8e3,Q=G/8*1,C=Q*J;return a(1),a(1),x(J),x(C),a(Q),a(G),D("data"),x(y.length),_.set(y,e),_},decode:function(p){var v=this._decode(p);if(!v)return null;for(var y=new Float32Array(v.length),_=0;_<y.length;_++)y[_]=v[_]/32768;return y},_decode:function(p){if(String.fromCharCode.apply(null,p.subarray(0,this.AMR_HEADER.length))!==this.AMR_HEADER)return null;var v=this.Decoder_Interface_init();if(!v)return null;var y=new Int16Array(Math.floor(p.length/6*this.PCM_BUFFER_COUNT)),_=L._malloc(this.AMR_BUFFER_COUNT),e=new Uint8Array(L.HEAPU8.buffer,_,this.AMR_BUFFER_COUNT);_=L._malloc(2*this.PCM_BUFFER_COUNT);for(var a=new Int16Array(L.HEAPU8.buffer,_,this.PCM_BUFFER_COUNT),x=6,D=0;x+1<p.length&&D+1<y.length;){var G=this.SIZES[p[x]>>3&15];if(x+G+1>p.length)break;if(e.set(p.subarray(x,x+G+1)),this.Decoder_Interface_Decode(v,e.byteOffset,a.byteOffset,0),D+this.PCM_BUFFER_COUNT>y.length){var J=new Int16Array(2*y.length);J.set(y.subarray(0,D)),y=J}y.set(a,D),D+=this.PCM_BUFFER_COUNT,x+=G+1}return L._free(e.byteOffset),L._free(a.byteOffset),this.Decoder_Interface_exit(v),y.subarray(0,D)},encode:function(p,v,y){if(v<8e3)return null;y===void 0&&(y=this.Mode.MR795);var _=this.Encoder_Interface_init();if(!_)return null;var e=L._malloc(2*this.PCM_BUFFER_COUNT),a=new Int16Array(L.HEAPU8.buffer,e,this.PCM_BUFFER_COUNT);e=L._malloc(this.AMR_BUFFER_COUNT);for(var x=new Uint8Array(L.HEAPU8.buffer,e,this.AMR_BUFFER_COUNT),D=v/8e3,G=Math.floor(p.length/D),J=new Int16Array(G),Q=0;Q<G;Q++)J[Q]=32767*p[Math.floor(Q*D)];var C=this.SIZES[y]+1,e1=new Uint8Array(Math.ceil(G/this.PCM_BUFFER_COUNT*C)+this.AMR_HEADER.length);e1.set(new TextEncoder("utf-8").encode(this.AMR_HEADER));for(var s1=0,_1=this.AMR_HEADER.length;s1+this.PCM_BUFFER_COUNT<J.length&&_1+C<e1.length;){a.set(J.subarray(s1,s1+this.PCM_BUFFER_COUNT));var N1=this.Encoder_Interface_Encode(_,y,a.byteOffset,x.byteOffset,0);if(N1!=C)break;e1.set(x.subarray(0,N1),_1),s1+=this.PCM_BUFFER_COUNT,_1+=N1}return L._free(a.byteOffset),L._free(x.byteOffset),this.Encoder_Interface_exit(_),e1.subarray(0,_1)},Decoder_Interface_init:function(){return 0},Decoder_Interface_exit:function(p){},Decoder_Interface_Decode:function(p,v,y,_){},Encoder_Interface_init:function(p){return 0},Encoder_Interface_exit:function(p){},Encoder_Interface_Encode:function(p,v,y,_,e){},Mode:{MR475:0,MR515:1,MR59:2,MR67:3,MR74:4,MR795:5,MR102:6,MR122:7,MRDTX:8},SIZES:[12,13,15,17,19,20,26,31,5,6,5,5,0,0,0,0],AMR_BUFFER_COUNT:32,PCM_BUFFER_COUNT:160,AMR_HEADER:`#!AMR
`,WAV_HEADER_SIZE:44};(L={canvas:{},print:function(p){},_main:function(){return b1.Decoder_Interface_init=L._Decoder_Interface_init,b1.Decoder_Interface_exit=L._Decoder_Interface_exit,b1.Decoder_Interface_Decode=L._Decoder_Interface_Decode,b1.Encoder_Interface_init=L._Encoder_Interface_init,b1.Encoder_Interface_exit=L._Encoder_Interface_exit,b1.Encoder_Interface_Encode=L._Encoder_Interface_Encode,0}})||(L=(L!==void 0?L:null)||{});var R1={};for(var M1 in L)L.hasOwnProperty(M1)&&(R1[M1]=L[M1]);var V1=typeof window=="object",T0=typeof importScripts=="function",A0=!V1&&!T0;if(A0)L.print||(L.print=print),typeof printErr<"u"&&(L.printErr=printErr),typeof read<"u"?L.read=read:L.read=function(){throw"no read() available (jsc?)"},L.readBinary=function(p){if(typeof readbuffer=="function")return new Uint8Array(readbuffer(p));var v=read(p,"binary");return k1(typeof v=="object"),v},typeof scriptArgs<"u"?L.arguments=scriptArgs:arguments!==void 0&&(L.arguments=arguments);else{if(!V1&&!T0)throw"Unknown runtime environment. Where are we?";if(L.read=function(p){var v=new XMLHttpRequest;return v.open("GET",p,!1),v.send(null),v.responseText},arguments!==void 0&&(L.arguments=arguments),typeof console<"u")L.print||(L.print=function(p){}),L.printErr||(L.printErr=function(p){});else{var K0=!1;L.print||(L.print=K0&&typeof dump<"u"?function(p){dump(p)}:function(p){})}T0&&(L.load=importScripts),L.setWindowTitle===void 0&&(L.setWindowTitle=function(p){document.title=p})}function $1(p){eval.call(null,p)}for(var M1 in!L.load&&L.read&&(L.load=function(v){$1(L.read(v))}),L.print||(L.print=function(){}),L.printErr||(L.printErr=L.print),L.arguments||(L.arguments=[]),L.thisProgram||(L.thisProgram="./this.program"),L.print=L.print,L.printErr=L.printErr,L.preRun=[],L.postRun=[],R1)R1.hasOwnProperty(M1)&&(L[M1]=R1[M1]);var h1={setTempRet0:function(p){E0=p},getTempRet0:function(){return E0},stackSave:function(){return Z0},stackRestore:function(p){Z0=p},getNativeTypeSize:function(p){switch(p){case"i1":case"i8":return 1;case"i16":return 2;case"i32":case"float":return 4;case"i64":case"double":return 8;default:if(p[p.length-1]==="*")return h1.QUANTUM_SIZE;if(p[0]==="i"){var v=parseInt(p.substr(1));return k1(v%8==0),v/8}return 0}},getNativeFieldSize:function(p){return Math.max(h1.getNativeTypeSize(p),h1.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(p,v){return v==="double"||v==="i64"?7&p&&(k1((7&p)==4),p+=4):k1(!(3&p)),p},getAlignSize:function(p,v,y){return y||p!="i64"&&p!="double"?p?Math.min(v||(p?h1.getNativeFieldSize(p):0),h1.QUANTUM_SIZE):Math.min(v,8):8},dynCall:function(p,v,y){return y&&y.length?(y.splice||(y=Array.prototype.slice.call(y)),y.splice(0,0,v),L["dynCall_"+p].apply(null,y)):L["dynCall_"+p].call(null,v)},functionPointers:[],addFunction:function(p){for(var v=0;v<h1.functionPointers.length;v++)if(!h1.functionPointers[v])return h1.functionPointers[v]=p,2*(1+v);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(p){h1.functionPointers[(p-2)/2]=null},warnOnce:function(p){h1.warnOnce.shown||(h1.warnOnce.shown={}),h1.warnOnce.shown[p]||(h1.warnOnce.shown[p]=1,L.printErr(p))},funcWrappers:{},getFuncWrapper:function(p,v){k1(v),h1.funcWrappers[v]||(h1.funcWrappers[v]={});var y=h1.funcWrappers[v];return y[p]||(y[p]=function(){return h1.dynCall(v,p,arguments)}),y[p]},getCompilerSetting:function(p){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(p){var v=Z0;return Z0=15+(Z0=Z0+p|0)&-16,v},staticAlloc:function(p){var v=S2;return S2=15+(S2=S2+p|0)&-16,v},dynamicAlloc:function(p){var v=Q0;return(Q0=15+(Q0=Q0+p|0)&-16)>=c2&&!U4()?(Q0=v,0):v},alignMemory:function(p,v){return p=Math.ceil(p/(v||16))*(v||16)},makeBigInt:function(p,v,y){return y?+(p>>>0)+4294967296*+(v>>>0):+(p>>>0)+4294967296*+(0|v)},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};L.Runtime=h1;var q0,f2,E0,y2,C1,W1=!1;function k1(p,v){p||w2("Assertion failed: "+v)}function J1(p){var v=L["_"+p];if(!v)try{v=[eval][0]("_"+p)}catch{}return k1(v,"Cannot call unknown function "+p+" (perhaps LLVM optimizations or closure removed it?)"),v}function c0(p,v,y,_){switch((y=y||"i8").charAt(y.length-1)==="*"&&(y="i32"),y){case"i1":case"i8":H0[0|p]=v;break;case"i16":u2[p>>1]=v;break;case"i32":g0[p>>2]=v;break;case"i64":f2=[v>>>0,(q0=v,+G4(q0)>=1?q0>0?(0|Q4(+Z4(q0/4294967296),4294967295))>>>0:~~+K4((q0-+(~~q0>>>0))/4294967296)>>>0:0)],g0[p>>2]=f2[0],g0[p+4>>2]=f2[1];break;case"float":K2[p>>2]=v;break;case"double":Z2[p>>3]=v;break;default:w2("invalid type for setValue: "+y)}}function y0(p,v,y){switch((v=v||"i8").charAt(v.length-1)==="*"&&(v="i32"),v){case"i1":case"i8":return H0[0|p];case"i16":return u2[p>>1];case"i32":case"i64":return g0[p>>2];case"float":return K2[p>>2];case"double":return Z2[p>>3];default:w2("invalid type for setValue: "+v)}return null}(function(){var p={stackSave:function(){h1.stackSave()},stackRestore:function(){h1.stackRestore()},arrayToC:function(x){var D=h1.stackAlloc(x.length);return c6(x,D),D},stringToC:function(x){var D=0;return x!=null&&x!==0&&b5(x,D=h1.stackAlloc(1+(x.length<<2))),D}},v={string:p.stringToC,array:p.arrayToC};C1=function(x,D,G,J,Q){var C=J1(x),e1=[],s1=0;if(J)for(var _1=0;_1<J.length;_1++){var N1=v[G[_1]];N1?(s1===0&&(s1=h1.stackSave()),e1[_1]=N1(J[_1])):e1[_1]=J[_1]}var I1=C.apply(null,e1);if(D==="string"&&(I1=T2(I1)),s1!==0){if(Q&&Q.async)return void EmterpreterAsync.asyncFinalizers.push(function(){h1.stackRestore(s1)});h1.stackRestore(s1)}return I1};var y=/^function\s\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function _(x){var D=x.toString().match(y);return D?{arguments:(D=D.slice(1))[0],body:D[1],returnValue:D[2]}:{}}var e={};for(var a in p)p.hasOwnProperty(a)&&(e[a]=_(p[a]));y2=function(x,D,G){G=G||[];var J=J1(x),Q=G.every(function(F1){return F1==="number"}),C=D!=="string";if(C&&Q)return J;var e1=G.map(function(F1,V0){return"$"+V0}),s1="(function("+e1.join(",")+") {",_1=G.length;if(!Q){s1+="var stack = "+e.stackSave.body+";";for(var N1=0;N1<_1;N1++){var I1=e1[N1],D1=G[N1];if(D1!=="number"){var d0=e[D1+"ToC"];s1+="var "+d0.arguments+" = "+I1+";",s1+=d0.body+";",s1+=I1+"="+d0.returnValue+";"}}}return s1+="var ret = "+_(function(){return J}).returnValue+"("+e1.join(",")+");",C||(s1+="ret = "+_(function(){return T2}).returnValue+"(ret);"),Q||(s1+=e.stackRestore.body.replace("()","(stack)")+";"),s1+="return ret})",[eval][0](s1)}})(),L.ccall=C1,L.cwrap=y2,L.setValue=c0,L.getValue=y0;var g2=0,y4=1,_2=2,g4=3,R2=4;function I0(p,v,y,_){var e,a;typeof p=="number"?(e=!0,a=p):(e=!1,a=p.length);var x,D=typeof v=="string"?v:null;if(x=y==R2?_:[U2,h1.stackAlloc,h1.staticAlloc,h1.dynamicAlloc][y===void 0?_2:y](Math.max(a,D?1:v.length)),e){var G;for(_=x,k1(!(3&x)),G=x+(-4&a);_<G;_+=4)g0[_>>2]=0;for(G=x+a;_<G;)H0[0|_++]=0;return x}if(D==="i8")return p.subarray||p.slice?F0.set(p,x):F0.set(new Uint8Array(p),x),x;for(var J,Q,C,e1=0;e1<a;){var s1=p[e1];typeof s1=="function"&&(s1=h1.getFunctionIndex(s1)),(J=D||v[e1])!==0?(J=="i64"&&(J="i32"),c0(x+e1,s1,J),C!==J&&(Q=h1.getNativeTypeSize(J),C=J),e1+=Q):e1++}return x}function _4(p){return n6?J2!==void 0&&!J2.called||!k5?h1.dynamicAlloc(p):U2(p):h1.staticAlloc(p)}function T2(p,v){if(v===0||!p)return"";for(var y,_=0,e=0;_|=y=F0[p+e|0],(y!=0||v)&&(e++,!v||e!=v););v||(v=e);var a="";if(_<128){for(var x,D=1024;v>0;)x=String.fromCharCode.apply(String,F0.subarray(p,p+Math.min(v,D))),a=a?a+x:x,p+=D,v-=D;return a}return L.UTF8ToString(p)}function R4(p){for(var v="";;){var y=H0[0|p++];if(!y)return v;v+=String.fromCharCode(y)}}function S4(p,v){return d6(p,v,!1)}function l2(p,v){for(var y,_,e,a,x,D="";;){if(!(y=p[v++]))return D;if(128&y)if(_=63&p[v++],(224&y)!=192)if(e=63&p[v++],(240&y)==224?y=(15&y)<<12|_<<6|e:(a=63&p[v++],(248&y)==240?y=(7&y)<<18|_<<12|e<<6|a:(x=63&p[v++],y=(252&y)==248?(3&y)<<24|_<<18|e<<12|a<<6|x:(1&y)<<30|_<<24|e<<18|a<<12|x<<6|63&p[v++])),y<65536)D+=String.fromCharCode(y);else{var G=y-65536;D+=String.fromCharCode(55296|G>>10,56320|1023&G)}else D+=String.fromCharCode((31&y)<<6|_);else D+=String.fromCharCode(y)}}function D4(p){return l2(F0,p)}function Y2(p,v,y,_){if(!(_>0))return 0;for(var e=y,a=y+_-1,x=0;x<p.length;++x){var D=p.charCodeAt(x);if(D>=55296&&D<=57343&&(D=65536+((1023&D)<<10)|1023&p.charCodeAt(++x)),D<=127){if(y>=a)break;v[y++]=D}else if(D<=2047){if(y+1>=a)break;v[y++]=192|D>>6,v[y++]=128|63&D}else if(D<=65535){if(y+2>=a)break;v[y++]=224|D>>12,v[y++]=128|D>>6&63,v[y++]=128|63&D}else if(D<=2097151){if(y+3>=a)break;v[y++]=240|D>>18,v[y++]=128|D>>12&63,v[y++]=128|D>>6&63,v[y++]=128|63&D}else if(D<=67108863){if(y+4>=a)break;v[y++]=248|D>>24,v[y++]=128|D>>18&63,v[y++]=128|D>>12&63,v[y++]=128|D>>6&63,v[y++]=128|63&D}else{if(y+5>=a)break;v[y++]=252|D>>30,v[y++]=128|D>>24&63,v[y++]=128|D>>18&63,v[y++]=128|D>>12&63,v[y++]=128|D>>6&63,v[y++]=128|63&D}}return v[y]=0,y-e}function A4(p,v,y){return Y2(p,F0,v,y)}function c5(p){for(var v=0,y=0;y<p.length;++y){var _=p.charCodeAt(y);_>=55296&&_<=57343&&(_=65536+((1023&_)<<10)|1023&p.charCodeAt(++y)),_<=127?++v:v+=_<=2047?2:_<=65535?3:_<=2097151?4:_<=67108863?5:6}return v}function M4(p){for(var v=0,y="";;){var _=u2[p+2*v>>1];if(_==0)return y;++v,y+=String.fromCharCode(_)}}function P4(p,v,y){if(y===void 0&&(y=2147483647),y<2)return 0;for(var _=v,e=(y-=2)<2*p.length?y/2:p.length,a=0;a<e;++a){var x=p.charCodeAt(a);u2[v>>1]=x,v+=2}return u2[v>>1]=0,v-_}function N4(p){return 2*p.length}function O4(p){for(var v=0,y="";;){var _=g0[p+4*v>>2];if(_==0)return y;if(++v,_>=65536){var e=_-65536;y+=String.fromCharCode(55296|e>>10,56320|1023&e)}else y+=String.fromCharCode(_)}}function C4(p,v,y){if(y===void 0&&(y=2147483647),y<4)return 0;for(var _=v,e=_+y-4,a=0;a<p.length;++a){var x=p.charCodeAt(a);if(x>=55296&&x<=57343&&(x=65536+((1023&x)<<10)|1023&p.charCodeAt(++a)),g0[v>>2]=x,(v+=4)+4>e)break}return g0[v>>2]=0,v-_}function L4(p){for(var v=0,y=0;y<p.length;++y){var _=p.charCodeAt(y);_>=55296&&_<=57343&&++y,v+=4}return v}function T4(p){var v=!!L.___cxa_demangle;if(v)try{var y=U2(p.length);b5(p.substr(1),y);var _=U2(4),e=L.___cxa_demangle(y,0,0,_);if(y0(_,"i32")===0&&e)return T2(e)}catch{}finally{y&&E5(y),_&&E5(_),e&&E5(e)}var a=3,x={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},D=[],G=!0;function J(){a++,p[a]==="K"&&a++;for(var e1=[];p[a]!=="E";)if(p[a]!=="S")if(p[a]!=="C"){var s1=parseInt(p.substr(a)),_1=s1.toString().length;if(!s1||!_1){a--;break}var N1=p.substr(a+_1,s1);e1.push(N1),D.push(N1),a+=_1+s1}else e1.push(e1[e1.length-1]),a+=2;else{a++;var I1=p.indexOf("_",a),D1=p.substring(a,I1)||0;e1.push(D[D1]||"?"),a=I1+1}return a++,e1}function Q(e1,s1,_1){s1=s1||1/0;var N1,I1="",D1=[];function d0(){return"("+D1.join(", ")+")"}if(p[a]==="N"){if(N1=J().join("::"),--s1==0)return e1?[N1]:N1}else if((p[a]==="K"||G&&p[a]==="L")&&a++,M0=parseInt(p.substr(a))){var F1=M0.toString().length;N1=p.substr(a+F1,M0),a+=F1+M0}if(G=!1,p[a]==="I"){a++;var V0=Q(!0);I1+=Q(!0,1,!0)[0]+" "+N1+"<"+V0.join(", ")+">"}else I1=N1;e:for(;a<p.length&&s1-- >0;){var B0=p[a++];if(B0 in x)D1.push(x[B0]);else switch(B0){case"P":D1.push(Q(!0,1,!0)[0]+"*");break;case"R":D1.push(Q(!0,1,!0)[0]+"&");break;case"L":a++;var M0=p.indexOf("E",a)-a;D1.push(p.substr(a,M0)),a+=M0+2;break;case"A":if(M0=parseInt(p.substr(a)),a+=M0.toString().length,p[a]!=="_")throw"?";a++,D1.push(Q(!0,1,!0)[0]+" ["+M0+"]");break;case"E":break e;default:I1+="?"+B0;break e}}return _1||D1.length!==1||D1[0]!=="void"||(D1=[]),e1?(I1&&D1.push(I1+"?"),D1):I1+d0()}var C=p;try{if(p=="Object._main"||p=="_main")return"main()";if(typeof p=="number"&&(p=T2(p)),p[0]!=="_"||p[1]!=="_"||p[2]!=="Z")return p;switch(p[3]){case"n":return"operator new()";case"d":return"operator delete()"}C=Q()}catch{C+="?"}return C.indexOf("?")>=0&&!v&&h1.warnOnce("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),C}function I4(p){return p.replace(/__Z[\w\d_]+/g,function(v){var y=T4(v);return v===y?v:v+" ["+y+"]"})}function F4(){var p=new Error;if(!p.stack){try{throw new Error(0)}catch(v){p=v}if(!p.stack)return"(no stack trace available)"}return p.stack.toString()}function d5(){return I4(F4())}L.ALLOC_NORMAL=g2,L.ALLOC_STACK=y4,L.ALLOC_STATIC=_2,L.ALLOC_DYNAMIC=g4,L.ALLOC_NONE=R2,L.allocate=I0,L.getMemory=_4,L.Pointer_stringify=T2,L.AsciiToString=R4,L.stringToAscii=S4,L.UTF8ArrayToString=l2,L.UTF8ToString=D4,L.stringToUTF8Array=Y2,L.stringToUTF8=A4,L.lengthBytesUTF8=c5,L.UTF16ToString=M4,L.stringToUTF16=P4,L.lengthBytesUTF16=N4,L.UTF32ToString=O4,L.stringToUTF32=C4,L.lengthBytesUTF32=L4,L.stackTrace=d5;var B4,H0,F0,u2,r6,g0,G2,K2,Z2,o6=4096;function x4(p){return p%4096>0&&(p+=4096-p%4096),p}var S2=0,n6=!1,s6=0,Z0=0,h5=0,Q0=0;function U4(){w2("Cannot enlarge memory arrays. Either (1) compile with -s TOTAL_MEMORY=X with X higher than the current value "+c2+", (2) compile with ALLOW_MEMORY_GROWTH which adjusts the size at runtime but prevents some optimizations, or (3) set Module.TOTAL_MEMORY before the program runs.")}for(var z0,w5=L.TOTAL_STACK||65536,c2=L.TOTAL_MEMORY||524288,$0=65536;$0<c2||$0<2*w5;)$0<16777216?$0*=2:$0+=16777216;function I2(p){for(;p.length>0;){var v=p.shift();if(typeof v!="function"){var y=v.func;typeof y=="number"?v.arg===void 0?h1.dynCall("v",y):h1.dynCall("vi",y,[v.arg]):y(v.arg===void 0?null:v.arg)}else v()}}$0!==c2&&(L.printErr("increasing TOTAL_MEMORY to "+$0+" to be compliant with the asm.js spec (and given that TOTAL_STACK="+w5+")"),c2=$0),k1(typeof Int32Array<"u"&&typeof Float64Array<"u"&&!!new Int32Array(1).subarray&&!!new Int32Array(1).set,"JS engine does not provide full typed array support"),z0=new ArrayBuffer(c2),H0=new Int8Array(z0),u2=new Int16Array(z0),g0=new Int32Array(z0),F0=new Uint8Array(z0),r6=new Uint16Array(z0),G2=new Uint32Array(z0),K2=new Float32Array(z0),Z2=new Float64Array(z0),g0[0]=255,k1(F0[0]===255&&F0[3]===0,"Typed arrays 2 must be run on a little-endian system"),L.HEAP=B4,L.buffer=z0,L.HEAP8=H0,L.HEAP16=u2,L.HEAP32=g0,L.HEAPU8=F0,L.HEAPU16=r6,L.HEAPU32=G2,L.HEAPF32=K2,L.HEAPF64=Z2;var m5=[],F2=[],p5=[],Q2=[],a6=[],k5=!1;function z4(){if(L.preRun)for(typeof L.preRun=="function"&&(L.preRun=[L.preRun]);L.preRun.length;)l6(L.preRun.shift());I2(m5)}function f6(){k5||(k5=!0,I2(F2))}function j4(){I2(p5)}function q4(){I2(Q2)}function H4(){if(L.postRun)for(typeof L.postRun=="function"&&(L.postRun=[L.postRun]);L.postRun.length;)u6(L.postRun.shift());I2(a6)}function l6(p){m5.unshift(p)}function V4(p){F2.unshift(p)}function W4(p){p5.unshift(p)}function X4(p){Q2.unshift(p)}function u6(p){a6.unshift(p)}function d2(p,v,y){var _=y>0?y:c5(p)+1,e=new Array(_),a=Y2(p,e,0,e.length);return v&&(e.length=a),e}function Y4(p){for(var v=[],y=0;y<p.length;y++){var _=p[y];_>255&&(_&=255),v.push(String.fromCharCode(_))}return v.join("")}function b5(p,v,y){for(var _=d2(p,y),e=0;e<_.length;){var a=_[e];H0[v+e|0]=a,e+=1}}function c6(p,v){for(var y=0;y<p.length;y++)H0[0|v++]=p[y]}function d6(p,v,y){for(var _=0;_<p.length;++_)H0[0|v++]=p.charCodeAt(_);y||(H0[0|v]=0)}L.addOnPreRun=l6,L.addOnInit=V4,L.addOnPreMain=W4,L.addOnExit=X4,L.addOnPostRun=u6,L.intArrayFromString=d2,L.intArrayToString=Y4,L.writeStringToMemory=b5,L.writeArrayToMemory=c6,L.writeAsciiToMemory=d6,Math.imul&&Math.imul(4294967295,5)===-5||(Math.imul=function(p,v){var y=65535&p,_=65535&v;return y*_+((p>>>16)*_+y*(v>>>16)<<16)|0}),Math.imul=Math.imul,Math.clz32||(Math.clz32=function(p){p>>>=0;for(var v=0;v<32;v++)if(p&1<<31-v)return v;return 32}),Math.clz32=Math.clz32;var G4=Math.abs,K4=Math.ceil,Z4=Math.floor,Q4=Math.min,r2=0,B2=null;function v5(p){r2++,L.monitorRunDependencies&&L.monitorRunDependencies(r2)}function $2(p){if(r2--,L.monitorRunDependencies&&L.monitorRunDependencies(r2),r2==0&&B2){var v=B2;B2=null,v()}}L.addRunDependency=v5,L.removeRunDependency=$2,L.preloadedImages={},L.preloadedAudios={},S2=31784,F2.push(),I0([154,14,0,0,188,14,0,0,226,14,0,0,8,15,0,0,46,15,0,0,84,15,0,0,130,15,0,0,208,15,0,0,66,16,0,0,108,16,0,0,42,17,0,0,248,17,0,0,228,18,0,0,240,19,0,0,24,21,0,0,86,22,0,0,238,23,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,13,0,15,0,17,0,19,0,20,0,26,0,31,0,5,0,6,0,5,0,5,0,0,0,0,0,0,0,0,0,1,252,146,252,36,253,182,253,72,254,218,254,108,255,0,0,0,0,32,78,32,78,32,78,32,78,32,78,80,70,0,64,0,32,0,0,0,0,255,127,112,125,112,125,112,125,112,125,112,125,153,89,255,127,112,125,112,125,102,102,102,38,153,25,153,25,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,0,96,0,72,0,54,128,40,96,30,200,22,22,17,209,12,157,9,54,7,102,70,184,38,75,21,182,11,113,6,139,3,243,1,18,1,151,0,83,0,154,89,185,62,232,43,188,30,132,21,16,15,139,10,97,7,42,5,157,3,44,3,128,0,30,2,140,0,57,11,111,4,218,8,74,13,19,8,51,2,133,49,135,2,36,16,6,7,225,21,165,20,9,30,118,1,151,14,185,1,160,42,78,10,31,46,190,9,10,80,29,3,98,20,163,2,68,26,162,32,162,20,160,6,208,5,172,1,250,22,196,1,212,20,232,15,255,13,244,4,165,9,133,3,22,62,237,3,134,58,199,12,91,40,250,18,51,14,229,7,36,10,67,3,72,48,28,19,174,47,168,6,120,52,68,6,158,35,37,9,128,15,2,6,103,21,208,38,211,14,161,1,79,5,158,1,56,14,33,6,59,31,213,13,141,44,133,2,104,33,123,2,216,15,97,5,224,64,236,23,156,44,188,2,215,7,95,2,127,48,42,6,111,43,46,18,112,53,172,6,214,46,205,4,60,31,129,28,175,51,83,22,124,9,135,4,25,8,149,7,74,24,233,23,218,13,12,7,221,34,10,7,231,33,44,6,111,54,248,13,1,52,93,24,254,23,106,4,106,23,198,6,61,55,54,18,7,44,249,12,194,47,15,6,107,54,199,11,217,19,224,40,228,36,50,26,153,6,171,2,156,5,26,5,44,28,93,15,242,15,153,10,113,30,192,2,222,58,34,3,155,24,92,20,241,16,237,20,20,26,29,2,174,23,114,2,83,53,116,14,234,44,104,9,28,63,204,2,145,47,239,2,129,31,225,44,170,24,208,8,114,17,240,1,125,28,11,2,229,39,249,14,202,32,221,11,211,32,198,3,148,55,88,7,255,33,33,21,11,64,255,18,252,28,187,7,201,23,206,4,155,36,46,17,222,56,35,13,247,52,57,11,107,51,185,5,158,21,142,6,82,51,179,57,170,28,88,2,38,5,36,2,156,16,211,13,60,39,60,9,91,41,110,2,32,51,157,2,46,55,198,13,175,19,56,38,234,59,107,2,43,12,78,2,58,64,197,11,182,60,72,16,177,60,75,6,45,60,204,4,151,62,83,36,110,29,112,19,198,7,189,4,183,44,133,4,224,48,143,21,3,37,84,10,36,30,242,7,224,51,191,8,139,62,229,19,130,31,105,26,99,39,133,5,138,19,43,9,235,48,87,23,22,59,83,11,88,71,241,8,211,61,223,9,137,63,14,40,59,57,55,44,5,7,81,1,43,12,141,1,182,13,112,11,240,17,110,10,95,29,116,2,151,44,144,2,58,23,131,9,144,25,199,28,46,32,61,3,160,15,95,3,48,39,188,9,185,62,223,13,28,71,30,4,215,23,174,5,252,22,220,30,64,73,140,13,72,7,32,2,238,35,171,2,103,45,64,16,242,17,108,6,86,12,133,4,81,62,0,10,61,48,149,14,12,68,140,20,218,23,212,7,101,11,206,6,83,64,137,20,147,65,144,6,53,67,223,6,165,18,159,12,218,28,147,23,6,56,28,39,195,15,186,1,98,16,202,1,254,35,194,8,3,29,121,16,60,50,33,3,178,43,57,3,104,49,36,8,156,50,154,25,33,37,228,3,229,25,217,3,41,41,198,9,185,59,142,19,58,49,7,8,124,60,117,6,66,63,9,27,151,55,158,22,66,10,60,3,239,21,150,6,95,53,146,22,84,14,18,6,49,44,73,10,42,38,179,5,179,54,125,18,25,62,147,24,134,24,78,7,230,30,237,8,82,66,219,17,192,64,9,15,144,59,7,9,151,62,172,12,123,56,144,69,71,46,203,10,189,7,127,5,120,5,108,3,239,16,219,13,39,17,114,16,29,21,168,2,53,68,13,3,101,25,254,19,155,31,253,29,187,28,26,3,141,32,158,4,193,58,88,12,80,58,223,11,197,79,112,3,209,56,84,3,49,48,116,57,248,26,128,7,129,16,165,3,26,32,63,4,163,41,244,15,98,39,181,17,175,10,72,3,177,80,57,4,71,65,78,23,1,62,226,17,119,42,14,10,189,14,142,4,183,56,204,15,219,80,67,10,115,59,174,10,170,59,138,8,113,24,154,12,69,51,24,76,28,28,162,3,158,9,82,6,163,17,20,12,28,54,181,16,220,40,65,3,187,67,42,3,251,65,241,8,186,60,25,32,35,53,148,6,125,12,42,7,76,62,4,11,196,61,207,20,110,66,134,9,148,65,46,5,55,61,220,31,206,45,108,33,178,14,5,8,91,37,37,5,249,52,134,26,195,47,144,7,244,31,222,13,231,51,242,6,171,63,199,25,163,63,78,30,73,33,247,9,57,28,85,10,93,71,65,29,245,65,200,8,218,69,68,11,113,67,0,13,201,36,194,78,34,43,128,32,6,5,108,2,151,5,71,2,105,23,241,8,138,15,42,14,24,20,240,2,97,52,62,3,177,21,44,11,244,45,20,23,241,41,48,2,70,21,52,2,9,52,192,11,170,46,99,14,175,77,30,3,97,38,216,2,95,53,44,34,223,28,237,11,211,9,10,3,162,23,65,3,69,25,210,19,113,32,159,9,253,23,73,7,204,59,238,4,72,56,195,17,95,53,163,17,65,12,167,11,175,9,235,4,240,58,39,18,22,60,47,10,156,56,88,9,174,48,233,9,115,29,133,11,109,50,28,47,92,21,172,2,69,12,210,2,217,19,250,4,188,49,104,16,198,59,169,2,139,30,80,2,134,25,229,7,94,64,33,34,52,52,114,3,21,21,131,3,64,57,130,8,149,57,131,16,190,55,18,5,105,54,237,7,117,60,58,29,199,61,220,17,217,9,221,7,198,19,12,7,39,20,182,25,218,27,13,14,168,42,75,6,209,45,172,6,7,66,127,13,140,63,240,25,90,36,239,3,153,36,58,8,238,74,173,19,153,48,173,16,47,62,52,5,253,59,184,13,122,46,61,55,229,62,198,26,218,7,225,2,195,14,93,3,190,44,64,11,236,13,212,13,97,35,217,4,103,48,128,3,98,33,21,18,41,45,144,22,193,31,77,2,26,32,76,2,40,73,171,14,173,50,77,12,113,61,246,2,250,64,242,2,118,59,130,43,255,61,160,8,65,18,98,2,234,39,166,2,153,59,50,16,97,22,255,12,185,32,134,6,150,77,17,9,90,60,135,21,230,54,105,21,96,22,72,11,156,29,66,5,48,56,205,20,108,63,110,15,14,59,160,14,202,59,155,5,5,57,230,15,13,48,80,61,193,29,163,6,122,8,116,3,107,17,215,17,174,70,234,12,198,49,47,3,78,58,139,3,168,58,185,16,158,60,176,32,74,70,63,4,54,9,97,3,153,63,203,14,63,61,244,17,228,63,254,5,200,64,162,8,193,65,225,37,57,62,161,17,205,12,61,4,171,37,139,8,197,46,180,23,239,35,110,17,251,34,93,6,49,40,246,11,97,64,35,20,106,60,154,27,110,53,239,9,153,20,229,8,106,65,69,24,15,65,80,13,80,79,35,13,0,73,193,7,92,55,67,50,50,59,87,61,121,17,252,3,145,6,118,3,215,16,205,16,248,34,73,14,5,23,123,4,127,45,172,5,14,62,179,8,230,17,244,25,17,27,181,4,76,24,31,3,127,48,81,13,96,62,37,15,147,77,61,8,217,37,93,8,150,57,126,34,144,56,39,10,25,7,214,4,91,30,45,3,135,74,58,17,178,21,16,8,103,14,28,11,27,68,208,8,57,65,134,17,71,63,12,21,92,31,203,10,77,13,71,8,18,68,101,21,130,53,226,10,167,77,160,10,138,35,40,15,252,70,225,18,184,67,175,47,252,19,228,3,71,19,220,3,160,38,9,12,126,23,251,20,9,62,131,6,213,32,159,4,239,58,62,9,65,77,90,27,187,46,26,6,111,28,104,4,219,65,252,5,146,61,5,21,116,57,17,8,137,78,107,8,6,67,53,32,247,69,174,24,91,21,224,5,4,16,14,10,13,68,154,26,41,22,72,11,252,64,54,13,15,35,39,7,191,78,129,18,94,76,126,28,2,26,221,10,208,44,249,12,197,75,190,19,190,73,114,18,55,64,69,9,206,79,34,17,89,44,158,103,73,45,252,11,50,11,30,6,244,19,46,4,142,37,51,19,75,19,208,13,117,29,110,3,237,80,83,3,26,27,43,17,159,65,53,30,153,39,251,3,117,38,196,3,134,60,115,15,99,60,102,13,175,73,214,3,152,78,195,3,236,65,87,50,254,55,104,16,199,25,196,4,6,36,46,3,46,66,14,20,29,22,34,19,112,21,6,7,34,79,122,15,109,66,34,24,9,70,41,23,149,36,92,13,50,29,179,7,81,76,57,20,59,74,190,11,70,64,204,14,198,62,63,9,216,33,183,10,229,36,246,102,104,42,7,5,227,13,241,3,230,21,38,14,253,75,136,21,165,48,29,3,154,80,143,3,67,60,250,11,141,66,35,40,195,73,73,10,73,15,244,4,63,76,43,13,132,70,110,20,91,75,142,6,52,76,100,12,152,70,2,42,241,64,189,26,62,12,250,8,117,42,133,9,220,60,1,27,53,49,53,13,108,43,225,12,122,65,120,9,165,73,59,26,19,67,159,38,199,49,45,10,233,34,68,12,89,74,84,30,171,71,40,15,251,79,98,14,146,76,52,13,244,50,173,75,30,41,84,90,1,0,3,0,0,0,1,0,2,0,4,0,82,120,26,113,81,106,240,99,241,93,78,88,2,83,7,78,89,73,242,68,51,115,174,103,80,93,251,83,149,75,6,68,56,61,25,55,150,49,161,44,205,76,21,46,166,27,151,16,244,9,249,5,149,3,38,2,74,1,198,0,249,79,26,80,59,80,92,80,125,80,164,80,197,80,236,80,13,81,52,81,85,81,124,81,157,81,196,81,236,81,19,82,58,82,97,82,137,82,176,82,215,82,255,82,38,83,84,83,123,83,169,83,208,83,254,83,38,84,84,84,129,84,175,84,221,84,11,85,57,85,103,85,149,85,201,85,247,85,43,86,89,86,142,86,194,86,247,86,43,87,95,87,148,87,200,87,3,88,56,88,115,88,174,88,233,88,36,89,95,89,154,89,219,89,22,90,88,90,153,90,212,90,28,91,94,91,159,91,231,91,48,92,113,92,192,92,8,93,80,93,159,93,237,93,60,94,138,94,224,94,46,95,131,95,217,95,52,96,138,96,229,96,72,97,163,97,6,98,104,98,209,98,51,99,156,99,11,100,123,100,234,100,96,101,214,101,76,102,201,102,76,103,207,103,82,104,220,104,108,105,252,105,147,106,48,107,205,107,113,108,27,109,204,109,125,110,59,111,249,111,197,112,150,113,111,114,84,115,64,116,50,117,50,118,63,119,88,120,225,122,255,127,255,127,255,127,255,127,255,127,255,127,255,127,225,122,88,120,63,119,50,118,50,117,64,116,84,115,111,114,150,113,197,112,249,111,59,111,125,110,204,109,27,109,113,108,205,107,48,107,147,106,252,105,108,105,220,104,82,104,207,103,76,103,201,102,76,102,214,101,96,101,234,100,123,100,11,100,156,99,51,99,209,98,104,98,6,98,163,97,72,97,229,96,138,96,52,96,217,95,131,95,46,95,224,94,138,94,60,94,237,93,159,93,80,93,8,93,192,92,113,92,48,92,231,91,159,91,94,91,28,91,212,90,153,90,88,90,22,90,219,89,154,89,95,89,36,89,233,88,174,88,115,88,56,88,3,88,200,87,148,87,95,87,43,87,247,86,194,86,142,86,89,86,43,86,247,85,201,85,149,85,103,85,57,85,11,85,221,84,175,84,129,84,84,84,38,84,254,83,208,83,169,83,123,83,84,83,38,83,255,82,215,82,176,82,137,82,97,82,58,82,19,82,236,81,196,81,157,81,124,81,85,81,52,81,13,81,236,80,197,80,164,80,125,80,92,80,59,80,26,80,249,79,210,79,177,79,145,79,112,79,13,0,14,0,16,0,18,0,20,0,21,0,27,0,32,0,6,0,7,0,6,0,6,0,0,0,0,0,0,0,1,0,13,0,14,0,16,0,18,0,19,0,21,0,26,0,31,0,6,0,6,0,6,0,6,0,0,0,0,0,0,0,1,0,79,115,156,110,74,97,126,77,72,54,9,31,195,10,153,251,125,242,48,239,127,240,173,244,231,249,176,254,22,2,202,3,255,3,55,3,4,2,220,0,0,0,125,255,62,255,41,255,0,0,216,127,107,127,182,126,187,125,123,124,248,122,53,121,53,119,250,116,137,114,128,46,128,67,0,120,0,101,128,94,64,113,64,95,192,28,64,76,192,57,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,5,0,10,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,10,0,19,0,20,0,84,0,1,0,254,255,2,0,3,0,6,0,5,0,9,0,20,0,94,0,0,0,253,255,3,0,3,0,6,0,5,0,9,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,17,0,19,0,19,0,19,0,19,0,23,0,39,0,57,0,5,0,8,0,8,0,7,0,8,0,7,0,2,0,8,0,4,0,7,0,2,0,4,0,7,0,2,0,8,0,4,0,7,0,2,0,8,0,8,0,7,0,8,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,4,0,7,0,2,0,6,0,8,0,9,0,9,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,2,0,6,0,4,0,9,0,2,0,6,0,8,0,9,0,9,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,11,0,3,0,7,0,4,0,11,0,3,0,7,0,8,0,9,0,9,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,8,0,13,0,4,0,7,0,5,0,13,0,4,0,7,0,9,0,9,0,9,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,13,0,4,0,4,0,5,0,6,0,13,0,4,0,4,0,5,0,8,0,9,0,9,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,8,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,5,0,1,0,1,0,1,0,1,0,10,0,10,0,7,0,7,0,7,0,8,0,9,0,8,0,6,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,9,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,6,0,4,0,4,0,4,0,4,0,4,0,4,0,3,0,3,0,3,0,3,0,3,0,5,0,3,0,8,0,9,0,9,0,6,0,95,0,103,0,118,0,134,0,148,0,159,0,204,0,244,0,39,0,43,0,38,0,37,0,0,0,0,0,0,0,0,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,23,0,24,0,25,0,26,0,27,0,28,0,48,0,49,0,61,0,62,0,82,0,83,0,47,0,46,0,45,0,44,0,81,0,80,0,79,0,78,0,17,0,18,0,20,0,22,0,77,0,76,0,75,0,74,0,29,0,30,0,43,0,42,0,41,0,40,0,38,0,39,0,16,0,19,0,21,0,50,0,51,0,59,0,60,0,63,0,64,0,72,0,73,0,84,0,85,0,93,0,94,0,32,0,33,0,35,0,36,0,53,0,54,0,56,0,57,0,66,0,67,0,69,0,70,0,87,0,88,0,90,0,91,0,34,0,55,0,68,0,89,0,37,0,58,0,71,0,92,0,31,0,52,0,65,0,86,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,23,0,24,0,25,0,26,0,27,0,46,0,65,0,84,0,45,0,44,0,43,0,64,0,63,0,62,0,83,0,82,0,81,0,102,0,101,0,100,0,42,0,61,0,80,0,99,0,28,0,47,0,66,0,85,0,18,0,41,0,60,0,79,0,98,0,29,0,48,0,67,0,17,0,20,0,22,0,40,0,59,0,78,0,97,0,21,0,30,0,49,0,68,0,86,0,19,0,16,0,87,0,39,0,38,0,58,0,57,0,77,0,35,0,54,0,73,0,92,0,76,0,96,0,95,0,36,0,55,0,74,0,93,0,32,0,51,0,33,0,52,0,70,0,71,0,89,0,90,0,31,0,50,0,69,0,88,0,37,0,56,0,75,0,94,0,34,0,53,0,72,0,91,0,0,0,1,0,4,0,5,0,3,0,6,0,7,0,2,0,13,0,15,0,8,0,9,0,11,0,12,0,14,0,10,0,16,0,28,0,74,0,29,0,75,0,27,0,73,0,26,0,72,0,30,0,76,0,51,0,97,0,50,0,71,0,96,0,117,0,31,0,77,0,52,0,98,0,49,0,70,0,95,0,116,0,53,0,99,0,32,0,78,0,33,0,79,0,48,0,69,0,94,0,115,0,47,0,68,0,93,0,114,0,46,0,67,0,92,0,113,0,19,0,21,0,23,0,22,0,18,0,17,0,20,0,24,0,111,0,43,0,89,0,110,0,64,0,65,0,44,0,90,0,25,0,45,0,66,0,91,0,112,0,54,0,100,0,40,0,61,0,86,0,107,0,39,0,60,0,85,0,106,0,36,0,57,0,82,0,103,0,35,0,56,0,81,0,102,0,34,0,55,0,80,0,101,0,42,0,63,0,88,0,109,0,41,0,62,0,87,0,108,0,38,0,59,0,84,0,105,0,37,0,58,0,83,0,104,0,0,0,1,0,4,0,3,0,5,0,6,0,13,0,7,0,2,0,8,0,9,0,11,0,15,0,12,0,14,0,10,0,28,0,82,0,29,0,83,0,27,0,81,0,26,0,80,0,30,0,84,0,16,0,55,0,109,0,56,0,110,0,31,0,85,0,57,0,111,0,48,0,73,0,102,0,127,0,32,0,86,0,51,0,76,0,105,0,130,0,52,0,77,0,106,0,131,0,58,0,112,0,33,0,87,0,19,0,23,0,53,0,78,0,107,0,132,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,50,0,75,0,104,0,129,0,47,0,72,0,101,0,126,0,54,0,79,0,108,0,133,0,46,0,71,0,100,0,125,0,128,0,103,0,74,0,49,0,45,0,70,0,99,0,124,0,42,0,67,0,96,0,121,0,39,0,64,0,93,0,118,0,38,0,63,0,92,0,117,0,35,0,60,0,89,0,114,0,34,0,59,0,88,0,113,0,44,0,69,0,98,0,123,0,43,0,68,0,97,0,122,0,41,0,66,0,95,0,120,0,40,0,65,0,94,0,119,0,37,0,62,0,91,0,116,0,36,0,61,0,90,0,115,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,15,0,16,0,26,0,87,0,27,0,88,0,28,0,89,0,29,0,90,0,30,0,91,0,51,0,80,0,112,0,141,0,52,0,81,0,113,0,142,0,54,0,83,0,115,0,144,0,55,0,84,0,116,0,145,0,58,0,119,0,59,0,120,0,21,0,22,0,23,0,17,0,18,0,19,0,31,0,60,0,92,0,121,0,56,0,85,0,117,0,146,0,20,0,24,0,25,0,50,0,79,0,111,0,140,0,57,0,86,0,118,0,147,0,49,0,78,0,110,0,139,0,48,0,77,0,53,0,82,0,114,0,143,0,109,0,138,0,47,0,76,0,108,0,137,0,32,0,33,0,61,0,62,0,93,0,94,0,122,0,123,0,41,0,42,0,43,0,44,0,45,0,46,0,70,0,71,0,72,0,73,0,74,0,75,0,102,0,103,0,104,0,105,0,106,0,107,0,131,0,132,0,133,0,134,0,135,0,136,0,34,0,63,0,95,0,124,0,35,0,64,0,96,0,125,0,36,0,65,0,97,0,126,0,37,0,66,0,98,0,127,0,38,0,67,0,99,0,128,0,39,0,68,0,100,0,129,0,40,0,69,0,101,0,130,0,8,0,7,0,6,0,5,0,4,0,3,0,2,0,14,0,16,0,9,0,10,0,12,0,13,0,15,0,11,0,17,0,20,0,22,0,24,0,23,0,19,0,18,0,21,0,56,0,88,0,122,0,154,0,57,0,89,0,123,0,155,0,58,0,90,0,124,0,156,0,52,0,84,0,118,0,150,0,53,0,85,0,119,0,151,0,27,0,93,0,28,0,94,0,29,0,95,0,30,0,96,0,31,0,97,0,61,0,127,0,62,0,128,0,63,0,129,0,59,0,91,0,125,0,157,0,32,0,98,0,64,0,130,0,1,0,0,0,25,0,26,0,33,0,99,0,34,0,100,0,65,0,131,0,66,0,132,0,54,0,86,0,120,0,152,0,60,0,92,0,126,0,158,0,55,0,87,0,121,0,153,0,117,0,116,0,115,0,46,0,78,0,112,0,144,0,43,0,75,0,109,0,141,0,40,0,72,0,106,0,138,0,36,0,68,0,102,0,134,0,114,0,149,0,148,0,147,0,146,0,83,0,82,0,81,0,80,0,51,0,50,0,49,0,48,0,47,0,45,0,44,0,42,0,39,0,35,0,79,0,77,0,76,0,74,0,71,0,67,0,113,0,111,0,110,0,108,0,105,0,101,0,145,0,143,0,142,0,140,0,137,0,133,0,41,0,73,0,107,0,139,0,37,0,69,0,103,0,135,0,38,0,70,0,104,0,136,0,7,0,6,0,5,0,4,0,3,0,2,0,1,0,0,0,16,0,15,0,14,0,13,0,12,0,11,0,10,0,9,0,8,0,26,0,27,0,28,0,29,0,30,0,31,0,115,0,116,0,117,0,118,0,119,0,120,0,72,0,73,0,161,0,162,0,65,0,68,0,69,0,108,0,111,0,112,0,154,0,157,0,158,0,197,0,200,0,201,0,32,0,33,0,121,0,122,0,74,0,75,0,163,0,164,0,66,0,109,0,155,0,198,0,19,0,23,0,21,0,22,0,18,0,17,0,20,0,24,0,25,0,37,0,36,0,35,0,34,0,80,0,79,0,78,0,77,0,126,0,125,0,124,0,123,0,169,0,168,0,167,0,166,0,70,0,67,0,71,0,113,0,110,0,114,0,159,0,156,0,160,0,202,0,199,0,203,0,76,0,165,0,81,0,82,0,92,0,91,0,93,0,83,0,95,0,85,0,84,0,94,0,101,0,102,0,96,0,104,0,86,0,103,0,87,0,97,0,127,0,128,0,138,0,137,0,139,0,129,0,141,0,131,0,130,0,140,0,147,0,148,0,142,0,150,0,132,0,149,0,133,0,143,0,170,0,171,0,181,0,180,0,182,0,172,0,184,0,174,0,173,0,183,0,190,0,191,0,185,0,193,0,175,0,192,0,176,0,186,0,38,0,39,0,49,0,48,0,50,0,40,0,52,0,42,0,41,0,51,0,58,0,59,0,53,0,61,0,43,0,60,0,44,0,54,0,194,0,179,0,189,0,196,0,177,0,195,0,178,0,187,0,188,0,151,0,136,0,146,0,153,0,134,0,152,0,135,0,144,0,145,0,105,0,90,0,100,0,107,0,88,0,106,0,89,0,98,0,99,0,62,0,47,0,57,0,64,0,45,0,63,0,46,0,55,0,56,0,0,0,1,0,2,0,3,0,4,0,5,0,6,0,7,0,8,0,9,0,10,0,11,0,12,0,13,0,14,0,23,0,15,0,16,0,17,0,18,0,19,0,20,0,21,0,22,0,24,0,25,0,26,0,27,0,28,0,38,0,141,0,39,0,142,0,40,0,143,0,41,0,144,0,42,0,145,0,43,0,146,0,44,0,147,0,45,0,148,0,46,0,149,0,47,0,97,0,150,0,200,0,48,0,98,0,151,0,201,0,49,0,99,0,152,0,202,0,86,0,136,0,189,0,239,0,87,0,137,0,190,0,240,0,88,0,138,0,191,0,241,0,91,0,194,0,92,0,195,0,93,0,196,0,94,0,197,0,95,0,198,0,29,0,30,0,31,0,32,0,33,0,34,0,35,0,50,0,100,0,153,0,203,0,89,0,139,0,192,0,242,0,51,0,101,0,154,0,204,0,55,0,105,0,158,0,208,0,90,0,140,0,193,0,243,0,59,0,109,0,162,0,212,0,63,0,113,0,166,0,216,0,67,0,117,0,170,0,220,0,36,0,37,0,54,0,53,0,52,0,58,0,57,0,56,0,62,0,61,0,60,0,66,0,65,0,64,0,70,0,69,0,68,0,104,0,103,0,102,0,108,0,107,0,106,0,112,0,111,0,110,0,116,0,115,0,114,0,120,0,119,0,118,0,157,0,156,0,155,0,161,0,160,0,159,0,165,0,164,0,163,0,169,0,168,0,167,0,173,0,172,0,171,0,207,0,206,0,205,0,211,0,210,0,209,0,215,0,214,0,213,0,219,0,218,0,217,0,223,0,222,0,221,0,73,0,72,0,71,0,76,0,75,0,74,0,79,0,78,0,77,0,82,0,81,0,80,0,85,0,84,0,83,0,123,0,122,0,121,0,126,0,125,0,124,0,129,0,128,0,127,0,132,0,131,0,130,0,135,0,134,0,133,0,176,0,175,0,174,0,179,0,178,0,177,0,182,0,181,0,180,0,185,0,184,0,183,0,188,0,187,0,186,0,226,0,225,0,224,0,229,0,228,0,227,0,232,0,231,0,230,0,235,0,234,0,233,0,238,0,237,0,236,0,96,0,199,0,0,0,2,0,0,0,3,0,0,0,2,0,0,0,3,0,1,0,3,0,2,0,4,0,1,0,4,0,1,0,4,0,0,0,205,12,156,25,0,32,102,38,205,44,0,48,51,51,102,54,154,57,205,60,0,64,51,67,102,70,154,73,205,76,159,0,64,241,53,167,206,0,190,242,52,176,12,1,67,244,88,185,93,1,201,245,133,194,163,1,215,246,223,200,226,1,166,247,189,205,42,2,116,248,147,210,125,2,66,249,109,215,221,2,18,250,77,220,74,3,222,250,30,225,201,3,174,251,0,230,90,4,124,252,216,234,1,5,74,253,179,239,193,5,25,254,141,244,158,6,231,254,104,249,156,7,181,255,67,254,193,8,133,0,33,3,17,10,83,1,252,7,147,11,33,2,213,12,80,13,240,2,178,17,79,15,190,3,140,22,155,17,141,4,104,27,63,20,91,5,67,32,72,23,41,6,29,37,199,26,248,6,249,41,203,30,199,7,212,46,105,35,149,8,175,51,185,40,100,9,138,56,222,48,113,10,224,62,135,63,244,11,253,71,150,82,120,13,27,81,93,107,252,14,57,90,93,107,252,14,57,90,0,0,1,0,3,0,2,0,6,0,4,0,5,0,7,0,0,0,1,0,3,0,2,0,5,0,6,0,4,0,7,0,248,127,211,127,76,127,108,126,51,125,163,123,188,121,127,119,239,116,12,114,217,110,89,107,141,103,121,99,31,95,130,90,166,85,141,80,60,75,182,69,0,64,28,58,15,52,223,45,141,39,32,33,156,26,6,20,97,13,178,6,0,0,78,249,159,242,250,235,100,229,224,222,115,216,33,210,241,203,228,197,0,192,74,186,196,180,115,175,90,170,126,165,225,160,135,156,115,152,167,148,39,145,244,141,17,139,129,136,68,134,93,132,205,130,148,129,180,128,45,128,8,128,255,127,46,124,174,120,118,117,125,114,186,111,41,109,194,106,131,104,102,102,105,100,137,98,194,96,19,95,122,93,245,91,130,90,33,89,207,87,139,86,85,85,44,84,15,83,252,81,244,80,246,79,1,79,20,78,48,77,83,76,126,75,175,74,231,73,37,73,104,72,178,71,0,71,84,70,173,69,10,69,107,68,209,67,59,67,168,66,25,66,142,65,6,65,130,64,0,64,0,0,175,5,50,11,140,16,192,21,207,26,188,31,136,36,53,41,196,45,55,50,143,54,206,58,245,62,4,67,252,70,223,74,174,78,105,82,17,86,167,89,44,93,159,96,3,100,87,103,155,106,209,109,250,112,20,116,33,119,34,122,23,125,255,127,255,127,217,127,98,127,157,126,138,125,42,124,125,122,133,120,66,118,182,115,227,112,202,109,110,106,208,102,242,98,215,94,130,90,246,85,52,81,64,76,29,71,206,65,87,60,186,54,252,48,31,43,40,37,26,31,249,24,200,18,140,12,72,6,0,0,184,249,116,243,56,237,7,231,230,224,216,218,225,212,4,207,70,201,169,195,50,190,227,184,192,179,204,174,10,170,126,165,41,161,14,157,48,153,146,149,54,146,29,143,74,140,190,137,123,135,131,133,214,131,118,130,99,129,158,128,39,128,0,128,249,150,148,221,53,235,27,241,93,244,116,246,223,247,237,248,184,249,86,250,214,250,61,251,148,251,221,251,26,252,78,252,123,252,163,252,197,252,227,252,252,252,18,253,38,253,55,253,69,253,81,253,91,253,100,253,106,253,111,253,114,253,116,253,116,253,114,253,111,253,106,253,100,253,91,253,81,253,69,253,55,253,38,253,18,253,252,252,227,252,197,252,163,252,123,252,78,252,26,252,221,251,148,251,61,251,214,250,86,250,184,249,237,248,223,247,116,246,93,244,27,241,53,235,148,221,249,150,48,117,144,101,8,82,152,58,64,31,0,0,192,224,104,197,248,173,112,154,153,104,33,3,201,9,85,253,154,250,70,2,92,2,6,251,183,13,250,232,182,17,13,254,108,248,195,11,62,236,238,21,58,248,219,251,77,250,90,17,68,253,41,235,1,18,196,1,179,253,232,242,137,11,243,4,68,251,226,245,195,6,86,14,133,238,49,252,39,17,23,246,181,3,173,250,45,252,102,22,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,98,57,254,44,244,4,55,245,217,233,90,29,221,255,9,245,32,244,215,18,136,11,24,223,201,14,175,5,131,8,67,222,115,31,201,247,82,250,9,3,84,4,175,246,206,8,149,254,94,253,201,247,158,23,207,233,48,4,51,12,62,236,192,20,231,246,112,241,12,27,207,240,163,2,17,249,29,0,161,39,66,118,247,14,60,240,156,11,232,251,22,252,173,9,29,244,255,10,73,247,217,6,181,249,178,6,17,249,7,6,16,252,173,1,87,255,216,1,16,251,128,8,110,245,219,9,171,249,88,1,58,3,7,250,188,6,135,249,165,6,241,247,84,10,12,244,81,11,70,248,45,2,12,3,167,250,74,3,143,2,0,64,103,65,213,66,76,68,203,69,82,71,226,72,122,74,28,76,199,77,123,79,56,81,255,82,209,84,172,86,146,88,130,90,126,92,132,94,150,96,180,98,221,100,18,103,84,105,162,107,254,109,102,112,221,114,96,117,242,119,147,122,66,125,255,127,3,115,186,110,119,98,225,79,109,57,245,33,71,12,184,250,206,238,23,233,38,233,191,237,33,245,96,253,187,4,232,9,58,12,175,11,211,8,146,4,0,0,23,252,140,249,180,248,126,249,133,251,48,254,218,0,244,2,36,4,75,4,136,3,38,2,135,0,11,255,254,253,134,253,166,253,61,254,25,255,0,0,191,0,52,1,84,1,40,1,198,0,78,0,220,255,136,255,93,255,91,255,124,255,177,255,237,255,34,0,73,0,91,0,89,0,70,0,38,0,0,0,254,254,194,254,73,254,134,253,112,253,251,252,57,253,10,254,244,254,63,255,254,255,125,0,122,0,217,255,247,255,105,0,129,0,27,1,116,1,63,2,235,254,188,254,59,255,25,254,67,254,150,254,220,254,229,255,177,0,31,2,86,1,5,2,4,2,130,0,27,0,152,255,136,255,116,255,182,255,200,255,204,253,81,252,16,250,59,252,210,252,242,253,190,254,254,255,159,0,145,2,200,254,228,254,126,254,171,253,19,254,242,253,94,254,27,255,105,0,193,1,211,253,154,252,205,251,105,252,74,252,16,253,59,253,196,254,62,0,230,1,198,254,65,255,53,255,182,254,96,255,153,255,205,255,131,0,82,1,3,2,10,6,224,8,194,14,112,21,60,27,190,32,63,39,221,43,222,49,146,53,84,37,17,42,27,49,236,51,45,56,131,45,92,41,39,38,145,33,84,25,6,0,82,0,125,255,154,0,200,255,33,253,183,0,191,255,247,254,9,0,46,255,151,254,113,0,206,2,25,7,242,3,190,4,37,6,89,3,53,5,228,8,59,3,32,6,141,7,205,2,197,7,158,8,70,3,148,4,31,7,209,2,232,3,106,8,30,1,220,1,229,5,9,255,237,253,230,0,147,0,174,255,57,2,26,0,79,255,80,252,229,255,239,254,180,2,92,255,248,254,73,255,224,0,22,3,15,4,131,3,178,3,89,2,229,1,3,3,126,4,12,2,165,2,135,3,116,255,119,1,10,3,154,1,164,2,173,1,45,1,18,2,241,3,207,2,134,2,38,0,226,0,111,1,40,0,145,0,211,255,7,254,34,1,121,0,135,255,46,1,127,0,166,0,132,255,129,254,68,252,154,254,57,254,47,252,203,2,110,3,126,3,210,3,155,3,211,0,221,1,16,1,64,0,188,0,178,255,17,0,113,255,191,255,38,0,131,2,74,2,109,2,122,255,86,254,117,253,91,1,33,2,4,11,164,4,166,10,138,9,142,0,176,255,199,6,27,1,130,0,205,1,250,254,113,254,135,251,101,254,155,0,174,1,73,1,119,1,11,3,53,0,30,255,117,255,127,255,20,255,146,6,29,1,232,2,47,5,226,2,185,2,128,6,56,1,153,1,10,1,69,1,208,2,135,0,1,0,221,0,197,1,8,0,203,0,145,0,43,1,128,2,248,2,29,0,212,1,126,2,103,0,173,1,123,1,164,1,186,3,164,3,46,5,186,4,234,4,192,2,244,3,128,4,90,255,68,254,246,254,196,254,126,255,136,254,191,0,127,4,112,7,16,255,225,253,20,251,144,255,12,1,183,4,70,0,38,4,47,6,22,1,80,5,38,6,254,254,240,254,0,253,19,0,51,2,192,8,253,255,247,254,135,0,217,254,177,253,124,254,140,0,98,1,50,255,252,254,8,254,229,252,79,254,50,253,217,250,109,0,75,1,194,3,83,254,169,255,140,2,216,254,170,1,251,3,17,255,7,3,83,3,233,1,54,5,49,4,178,254,180,254,25,0,31,2,182,4,15,7,70,1,61,0,215,2,66,2,81,3,125,5,48,255,235,254,73,1,104,255,64,0,157,2,78,254,90,253,41,253,58,254,185,255,251,0,93,2,224,1,254,0,30,254,11,0,228,3,223,254,139,1,230,1,210,2,25,4,160,5,226,255,196,254,238,252,150,255,141,255,149,253,93,3,194,5,132,5,31,4,86,5,160,4,44,3,213,4,157,3,42,0,5,255,192,253,86,1,141,0,58,254,88,255,176,255,79,5,170,254,112,253,29,249,100,0,53,3,213,2,222,3,235,2,32,3,76,1,184,1,56,2,151,2,123,1,84,3,112,0,165,0,143,254,85,2,142,3,26,1,248,255,66,3,1,5,160,254,60,2,183,2,206,1,198,8,14,7,89,1,190,0,94,5,160,1,147,3,118,8,168,0,174,255,24,1,252,253,66,254,72,3,47,0,21,2,44,0,150,254,57,253,137,251,22,0,193,0,192,5,171,255,233,0,21,7,194,255,67,2,224,5,38,2,176,3,213,6,211,2,138,2,124,4,204,3,116,3,115,5,87,254,131,2,0,0,232,3,184,3,74,4,249,0,166,5,160,2,178,254,169,255,124,8,214,253,90,7,112,10,140,0,34,7,61,7,152,3,213,6,30,10,52,4,141,7,246,7,119,255,69,254,237,249,245,4,150,4,212,1,19,254,134,255,241,5,61,254,9,4,190,4,226,1,159,6,94,4,47,3,137,2,128,1,66,254,76,253,107,0,193,254,163,253,138,255,49,255,7,254,13,2,44,254,244,255,176,10,75,0,142,7,25,5,112,3,54,9,219,8,5,5,39,6,212,7,208,255,208,254,94,251,77,254,51,254,5,255,146,254,108,254,221,253,223,254,163,253,171,253,230,253,214,252,91,255,136,255,3,0,100,1,127,2,217,4,222,5,96,0,177,0,238,2,77,254,183,253,106,251,156,254,109,0,177,255,27,254,32,1,213,7,9,0,92,4,219,2,112,3,86,8,178,3,247,254,49,6,41,4,133,4,186,4,75,3,14,254,100,253,175,1,118,1,65,1,27,255,160,5,53,8,101,5,193,1,205,1,131,4,151,255,39,0,128,254,249,254,111,1,182,0,141,254,108,253,5,3,68,255,127,4,203,3,53,5,96,6,155,5,6,3,243,4,197,4,30,254,192,252,47,250,19,255,46,255,92,3,122,3,79,6,40,4,216,1,38,4,168,4,185,0,53,4,221,3,200,253,32,252,88,249,63,254,122,252,5,248,114,255,135,254,54,254,46,255,214,253,251,251,245,255,109,4,217,8,183,254,93,253,131,252,6,255,145,2,163,4,7,2,230,5,243,6,8,2,27,2,123,5,15,2,141,5,22,5,205,253,153,252,32,251,109,255,49,254,111,3,180,255,30,9,24,11,51,2,13,10,81,9,120,2,134,7,104,11,207,2,231,7,48,7,223,253,45,253,84,4,129,0,131,255,116,3,137,5,96,6,157,3,162,255,30,6,215,6,171,254,253,5,15,6,79,2,139,1,238,254,180,255,213,3,15,11,153,0,169,11,52,7,8,4,5,10,189,10,228,5,16,11,87,7,23,3,175,4,26,2,66,255,59,254,209,5,234,254,220,253,134,4,11,255,149,7,252,7,0,4,24,6,114,6,0,2,253,0,210,1,194,255,189,254,127,4,39,254,136,254,251,1,79,254,100,5,114,8,131,3,151,7,165,5,134,0,192,2,184,1,204,1,13,2,228,255,62,254,23,1,58,5,0,0,203,3,252,0,67,254,141,253,33,252,164,254,166,253,112,250,142,1,200,2,120,6,149,255,58,1,78,255,93,0,178,8,190,8,6,2,81,3,144,2,50,254,57,253,65,254,174,0,222,255,167,4,137,255,42,0,237,3,140,254,18,1,246,2,12,4,48,9,46,7,163,2,188,6,218,5,174,1,6,5,85,8,127,255,73,254,0,0,139,254,32,3,96,8,6,0,51,6,174,9,222,1,84,2,80,8,84,254,32,253,225,5,129,1,178,0,212,3,139,0,193,1,201,4,242,253,182,252,42,252,145,0,18,6,218,4,111,2,168,5,144,2,93,1,248,3,202,5,31,0,232,254,159,1,196,254,212,2,105,6,104,1,34,4,44,2,76,254,154,254,177,4,157,254,99,4,147,7,145,1,48,6,200,8,241,253,12,252,99,1,233,0,238,0,185,8,218,253,127,252,129,253,147,254,11,254,165,7,133,1,68,7,85,6,162,0,108,4,240,4,19,255,150,4,110,5,128,253,101,254,116,0,28,255,158,6,250,8,103,6,138,8,219,8,50,2,249,4,98,10,67,1,82,1,238,6,66,2,83,4,84,3,22,0,82,2,166,3,113,255,206,2,190,1,50,0,71,0,247,255,174,254,70,253,129,250,102,0,118,255,204,252,202,254,43,254,133,251,158,1,67,0,245,254,36,4,46,3,161,5,12,6,80,5,248,4,218,6,103,7,125,6,227,7,85,8,28,7,16,7,14,9,53,7,132,2,163,255,198,1,90,3,73,1,120,255,233,1,254,254,128,255,58,255,23,253,215,255,204,255,247,254,39,252,90,1,137,0,223,1,51,249,20,253,84,253,117,251,67,249,145,254,129,252,135,251,240,252,24,254,78,252,56,252,171,255,122,254,43,253,215,0,172,254,85,255,252,3,148,3,177,7,52,2,179,0,234,2,150,2,209,3,198,6,119,3,110,2,146,3,171,3,88,3,141,4,53,1,176,2,35,3,149,3,161,0,58,2,118,0,236,255,229,254,208,252,214,255,204,0,52,251,187,254,50,254,61,252,54,255,113,255,36,252,28,254,151,254,66,253,46,252,35,254,210,254,234,252,92,251,156,255,238,252,192,251,226,251,77,252,108,249,54,255,181,252,242,252,241,251,158,250,123,252,144,253,146,255,171,255,100,1,213,0,246,255,19,254,108,1,6,3,169,1,54,3,223,1,173,255,45,2,8,2,32,252,232,249,196,253,165,253,27,253,230,255,10,254,130,253,121,252,209,0,50,1,147,0,196,254,175,253,172,253,171,255,45,255,31,255,106,252,239,253,117,0,233,0,73,254,30,253,77,4,239,2,121,2,177,5,180,6,231,5,229,6,177,5,142,3,98,4,132,4,81,3,74,5,100,3,214,1,153,252,130,251,252,248,153,252,163,252,32,252,138,255,155,0,212,0,229,251,175,252,162,253,163,251,199,248,66,245,5,252,109,250,179,248,114,1,72,255,98,254,191,3,237,1,104,0,190,3,15,4,31,2,154,0,141,2,201,0,225,4,251,1,150,0,151,2,247,1,230,0,111,2,9,3,163,2,147,2,88,0,146,255,75,3,244,0,224,0,126,1,29,2,46,1,212,2,177,1,154,2,142,4,222,2,85,1,118,255,20,0,115,254,97,251,88,254,210,255,191,254,160,254,132,255,53,5,253,3,56,4,6,1,110,1,211,2,154,3,27,1,217,253,31,0,132,253,157,253,79,253,71,253,97,254,72,252,245,252,55,255,207,250,170,253,153,254,71,252,251,250,166,0,237,1,49,1,221,0,78,3,191,2],"i8",R2,h1.GLOBAL_BASE),I0([98,2,72,3,168,3,6,3,45,253,212,250,19,251,155,254,255,251,148,250,184,251,160,250,147,254,120,250,167,248,160,253,250,248,65,249,94,253,223,253,107,251,65,253,166,2,18,3,148,0,133,255,184,2,8,5,132,2,94,1,246,255,158,1,102,2,15,0,137,0,88,1,45,255,210,252,24,250,205,252,121,254,94,252,180,253,47,0,177,253,126,252,115,252,183,251,93,255,8,251,113,251,99,255,72,250,11,250,123,254,6,251,92,251,144,253,159,2,213,0,198,1,124,0,238,254,243,253,39,253,16,254,104,255,192,250,122,0,135,0,167,244,179,253,118,254,64,249,185,1,206,255,196,5,136,3,19,3,60,1,236,0,72,254,165,254,217,0,157,1,113,252,107,252,121,0,57,254,92,252,202,0,164,255,47,254,137,254,232,1,134,1,218,1,108,3,217,2,60,1,233,248,224,250,99,253,87,0,194,3,176,1,51,2,7,255,222,251,250,0,29,1,81,4,117,4,171,1,184,2,242,251,128,249,210,249,76,252,90,1,160,0,203,254,240,254,166,252,158,2,112,2,226,4,80,252,104,254,102,253,162,253,192,254,128,254,20,254,230,0,65,0,78,1,206,255,240,255,240,255,78,253,139,250,255,6,180,6,119,5,174,9,15,8,124,5,221,4,191,5,146,5,130,254,243,251,254,255,173,0,114,254,121,4,211,5,232,7,9,7,4,3,250,4,226,5,149,5,199,6,209,7,55,4,194,4,249,4,126,251,197,248,207,250,216,252,147,251,184,251,61,254,247,251,70,249,65,0,66,2,172,255,60,250,126,246,14,249,3,253,170,250,18,254,38,255,174,253,93,252,81,1,20,255,50,2,53,9,102,10,146,7,209,5,252,4,106,3,189,0,102,1,118,1,17,250,23,247,214,246,57,252,9,251,209,247,140,253,92,251,250,249,125,6,19,4,34,2,53,2,37,4,220,2,192,255,188,252,78,254,76,254,160,255,203,0,54,4,192,4,100,6,139,3,254,5,218,3,70,1,197,3,77,3,142,0,172,255,197,0,214,1,75,9,34,6,109,4,214,1,190,4,139,1,96,5,176,4,101,4,18,4,92,1,225,253,46,251,136,254,41,255,75,255,225,1,101,248,171,249,46,255,18,253,95,251,134,1,29,0,113,254,27,0,52,3,212,4,243,2,183,2,211,3,153,1,82,255,173,4,11,4,144,3,76,5,54,7,32,252,99,250,228,1,51,250,92,249,208,0,100,254,180,4,152,5,241,254,128,3,120,4,96,254,241,6,154,5,96,249,172,245,52,255,3,249,241,249,9,4,136,249,233,249,23,5,27,251,203,249,57,4,99,253,185,251,190,255,86,253,64,1,167,254,147,2,49,1,45,4,244,250,220,252,237,255,157,249,245,250,29,0,109,249,15,254,71,0,225,254,249,255,156,255,18,254,62,252,19,255,84,3,89,7,204,6,63,251,149,250,227,0,108,253,46,1,117,1,96,0,63,4,233,4,206,251,123,249,160,0,229,1,28,8,6,7,90,252,36,255,40,2,172,253,156,253,237,0,80,1,184,6,111,3,131,2,117,2,178,1,243,4,10,2,97,6,15,0,244,0,71,254,195,5,205,2,184,0,27,7,54,6,173,6,220,3,5,1,169,3,45,8,41,9,240,5,91,8,66,7,70,6,191,253,189,253,77,251,68,252,135,0,24,254,48,254,51,0,174,254,139,253,164,254,45,253,122,4,25,8,162,5,144,8,186,5,143,3,92,250,220,249,26,247,120,5,198,2,17,5,55,5,121,2,160,3,154,5,146,8,34,10,118,9,156,8,89,7,214,3,194,8,62,7,124,1,24,3,121,4,193,255,229,253,158,1,4,255,60,252,198,254,19,251,85,253,244,252,193,252,242,253,19,252,126,249,145,251,88,254,181,249,60,254,213,254,244,4,24,4,130,2,123,4,85,3,88,3,93,253,176,254,139,0,220,8,63,5,138,5,29,0,0,3,29,3,56,251,167,1,52,2,218,250,198,251,245,0,234,250,212,252,61,2,238,250,175,249,134,2,56,252,66,3,211,2,225,3,116,6,235,7,65,255,207,252,176,1,150,2,60,0,198,0,114,2,229,3,50,5,112,6,171,7,9,5,195,249,163,255,211,255,192,251,37,0,172,255,117,6,47,10,33,9,41,4,248,7,73,9,115,4,22,9,70,8,91,3,101,1,230,5,152,2,203,4,75,4,223,1,80,5,144,3,105,7,218,6,227,7,144,4,117,7,248,6,143,1,34,0,0,1,175,253,208,254,227,251,35,2,158,6,127,5,135,2,157,255,171,254,212,5,111,6,166,4,38,0,124,253,44,255,139,1,78,3,222,0,64,253,3,253,52,253,44,253,84,248,12,245,106,255,35,1,174,255,209,4,179,5,239,3,116,255,101,255,153,0,183,1,41,1,32,6,7,250,102,254,132,253,0,6,199,1,19,255,208,250,117,255,252,254,19,2,42,2,100,3,13,1,240,4,94,2,23,255,115,3,207,1,230,2,88,2,136,255,183,255,165,1,212,0,73,254,198,255,36,3,250,250,39,251,216,2,38,1,22,254,50,0,177,253,119,252,26,251,42,0,81,253,147,0,231,255,17,1,84,2,201,254,189,4,89,2,14,253,81,3,72,2,173,1,95,2,75,2,166,253,90,255,205,1,228,252,201,252,9,3,100,5,142,3,219,6,119,0,137,5,204,3,37,255,144,252,196,249,231,251,14,252,182,1,55,253,157,250,78,0,0,0,65,254,101,251,144,251,217,250,219,249,200,8,231,6,29,5,178,3,47,6,152,5,126,4,226,1,180,1,43,254,172,251,106,2,65,254,58,252,64,4,28,251,21,250,142,255,176,251,40,248,189,253,210,0,101,2,241,1,73,248,99,250,130,2,11,251,168,252,243,3,146,249,95,251,39,4,237,249,96,253,180,4,100,249,166,251,111,2,45,252,210,250,3,251,27,2,109,255,126,3,182,250,127,252,78,254,120,3,219,1,172,1,153,0,128,254,82,1,44,250,1,254,103,1,50,252,165,251,42,254,105,0,218,253,165,2,87,252,135,251,109,3,124,1,252,254,210,0,149,6,156,3,232,4,239,6,166,4,71,4,139,5,119,2,21,2,115,2,43,1,165,254,101,254,234,253,135,2,118,253,29,0,173,253,134,254,169,250,27,6,122,5,97,4,185,5,65,4,130,5,136,2,208,247,190,251,250,255,55,1,62,255,155,252,129,253,193,252,160,1,118,251,56,251,69,5,33,251,83,252,21,7,111,247,61,248,197,1,149,253,169,250,68,252,186,249,76,248,29,250,105,251,223,251,176,251,135,254,89,2,201,0,84,7,57,3,118,1,82,254,213,250,29,0,139,250,31,251,205,250,17,252,32,250,192,3,135,250,39,248,197,0,157,250,99,248,20,255,203,251,123,0,166,1,103,2,245,4,34,2,206,254,246,5,136,3,170,4,252,6,153,4,142,253,140,252,10,250,199,0,254,2,224,5,215,251,94,3,197,0,246,251,19,249,137,252,224,252,145,0,87,2,146,251,249,253,114,2,75,251,122,248,244,1,114,252,239,251,141,250,60,250,225,249,55,252,245,253,74,3,34,0,2,7,134,2,94,3,73,251,160,248,22,252,178,255,247,255,96,253,20,4,247,2,80,0,168,253,115,4,251,3,57,0,208,7,142,5,191,252,134,5,97,4,78,251,94,6,236,4,51,254,140,5,220,4,1,6,207,3,253,0,229,254,68,1,153,254,87,2,61,255,106,0,76,2,62,0,181,253,11,253,133,2,205,0,51,0,177,4,246,2,71,251,161,2,122,254,144,253,45,6,173,3,105,255,255,3,223,2,4,11,21,5,178,2,210,254,12,2,157,255,124,252,204,249,91,251,60,4,251,0,238,0,222,7,0,7,242,3,221,4,97,6,205,6,53,251,252,249,72,251,147,253,200,1,147,255,40,0,191,255,20,3,219,252,69,253,186,250,185,253,136,3,64,3,223,252,20,2,82,2,180,7,128,5,71,5,103,251,168,248,190,247,251,252,56,2,180,3,9,252,55,4,236,4,169,251,226,1,126,255,242,6,20,4,12,3,45,250,245,0,144,3,196,254,139,251,107,252,232,253,94,250,214,246,239,252,246,249,60,248,45,248,1,1,141,3,199,248,135,253,71,251,254,249,130,248,226,251,70,6,191,8,40,6,201,253,36,250,248,249,1,251,195,0,89,5,207,252,37,1,195,4,243,253,118,2,173,4,94,249,135,246,208,248,209,254,219,2,235,2,111,251,5,255,13,1,74,252,181,255,148,6,98,251,59,254,237,3,193,249,73,2,122,1,229,247,197,253,85,254,239,253,121,251,109,251,229,254,51,255,204,253,228,252,222,4,205,2,229,8,159,3,27,2,58,254,47,2,184,1,51,253,180,5,79,6,250,251,28,4,74,6,111,251,118,255,79,3,226,0,39,0,156,253,29,251,150,255,39,253,117,253,200,3,22,5,54,253,132,253,191,6,97,1,45,4,154,1,226,252,100,255,75,4,194,253,150,3,190,1,226,250,244,3,210,1,128,5,55,6,253,2,149,5,100,5,221,6,157,7,164,7,74,9,42,6,255,7,100,8,148,3,98,0,249,255,101,7,138,5,93,8,92,1,125,5,43,6,152,0,110,4,9,7,245,254,154,0,115,5,114,251,213,1,30,4,138,251,107,254,207,251,195,250,40,247,211,249,148,254,101,3,170,6,118,251,37,2,14,6,55,251,116,248,126,249,51,250,71,248,249,247,65,249,118,252,158,255,151,248,233,0,212,5,124,3,108,0,181,254,64,249,110,251,92,249,220,251,188,7,254,6,210,251,51,249,139,248,245,255,3,6,37,5,192,249,94,0,241,1,165,1,187,1,59,255,214,249,163,254,30,252,169,253,229,253,116,4,59,252,117,250,127,255,195,250,175,0,65,254,137,254,31,5,7,8,141,254,118,253,205,254,207,251,93,2,109,1,247,247,143,255,174,1,140,2,146,3,199,3,12,252,206,249,237,246,225,5,224,4,47,2,6,1,26,254,111,254,65,249,62,5,10,6,50,0,56,0,176,1,182,254,119,0,164,253,19,250,200,251,214,252,178,3,103,4,31,4,136,250,89,249,80,249,10,251,64,253,219,250,39,3,29,7,119,4,200,10,70,6,123,8,96,4,153,1,106,255,109,255,148,1,191,3,135,9,119,7,141,8,118,252,115,255,158,252,120,252,114,255,54,254,211,253,60,253,113,249,194,252,105,250,209,249,206,248,190,250,194,251,188,249,240,254,147,3,84,251,4,3,32,4,130,253,46,251,151,248,12,254,175,255,202,252,247,250,179,249,33,253,139,255,17,3,168,0,190,251,109,4,154,3,184,251,22,253,104,5,31,1,221,253,217,251,160,250,103,247,76,251,128,247,222,249,35,249,25,250,63,247,253,252,55,249,75,4,62,3,204,249,212,2,219,4,250,249,181,2,37,3,102,249,16,255,129,6,92,249,252,255,100,253,101,8,48,3,18,4,206,252,207,248,22,0,4,253,5,254,193,1,129,251,151,253,33,1,181,252,196,249,16,255,242,1,22,255,111,253,16,253,224,1,142,6,193,254,31,254,193,0,213,252,171,0,137,255,176,247,54,255,176,252,181,6,116,4,164,6,67,0,239,255,66,0,244,255,102,249,187,253,152,255,240,254,204,251,94,251,203,248,136,254,140,251,98,252,92,254,198,255,253,254,112,253,146,251,215,253,252,6,203,4,199,1,129,0,206,1,185,1,16,255,240,253,72,3,2,2,130,0,181,255,90,4,111,2,153,0,216,0,44,4,52,2,250,255,236,254,95,4,215,2,190,0,188,255,192,2,50,1,119,0,248,254,73,1,61,0,156,255,156,0,108,1,123,0,183,0,48,255,85,255,133,255,220,0,191,255,206,254,194,255,146,1,17,0,108,253,86,252,246,254,0,0,129,1,235,0,20,1,29,1,64,1,12,1,176,254,56,255,44,253,17,0,172,255,125,1,224,253,173,1,238,1,7,2,139,255,32,1,48,1,73,1,131,2,157,0,189,2,252,1,176,4,113,2,28,3,96,2,230,3,165,1,236,1,120,2,180,4,12,3,190,1,132,0,233,4,76,3,35,2,193,1,61,3,146,2,29,2,214,1,108,4,234,4,150,3,127,2,35,2,51,0,167,1,23,1,9,0,136,1,83,0,94,0,30,2,31,2,229,0,109,255,58,255,129,0,194,0,71,255,161,252,215,250,210,254,30,0,171,253,139,253,237,255,114,0,124,252,199,251,210,1,97,1,53,250,219,249,15,0,113,255,84,249,245,247,17,253,196,0,172,248,237,247,126,253,254,254,225,246,66,250,62,254,204,253,184,253,70,255,152,252,98,254,243,248,36,252,155,251,226,250,42,253,151,251,28,0,169,0,241,251,160,252,50,253,10,255,228,1,36,0,23,255,207,255,9,1,67,0,33,1,211,1,178,0,31,2,42,3,28,2,84,0,26,1,160,2,191,2,49,252,247,252,129,0,31,1,86,252,29,255,187,3,83,2,175,249,223,254,68,3,137,2,201,248,41,255,82,4,206,2,14,248,195,251,138,2,184,1,203,247,239,253,139,3,63,2,37,248,176,254,158,2,204,0,171,246,76,253,104,1,137,0,148,247,100,247,247,255,24,1,246,254,119,0,39,0,193,0,78,0,197,255,136,255,226,0,49,252,166,252,243,252,185,251,149,253,99,254,61,254,182,252,64,251,215,250,211,252,141,252,160,250,177,249,118,254,84,254,31,253,167,251,219,253,234,252,144,252,49,252,57,252,126,253,39,252,138,252,7,251,175,250,39,254,220,252,135,250,129,250,160,0,247,254,105,252,237,254,8,255,6,255,50,253,132,254,97,0,153,255,137,254,27,255,97,254,63,255,121,255,213,253,116,2,105,1,119,0,216,0,67,2,108,1,135,1,209,0,122,2,10,2,102,255,108,255,14,2,133,1,170,0,33,0,105,0,11,1,64,0,124,1,33,250,24,252,226,255,143,254,210,251,58,0,135,2,223,0,16,250,221,254,109,2,51,1,5,250,156,0,250,2,148,1,19,248,141,0,222,2,243,1,199,248,118,253,50,1,0,2,69,255,152,255,197,255,182,1,134,0,26,255,156,0,70,255,195,255,252,254,240,255,10,0,199,253,253,255,91,254,215,254,67,249,247,253,166,254,178,0,174,250,197,255,212,255,157,0,158,247,51,254,42,254,163,254,134,247,255,255,143,254,135,255,213,249,139,254,124,252,9,252,163,251,177,253,155,253,240,252,207,253,122,0,181,255,63,254,252,255,85,255,133,255,140,254,192,0,168,0,180,255,124,255,252,0,149,255,84,1,210,0,136,1,253,1,16,1,181,0,147,255,145,0,218,0,119,0,96,254,249,254,229,1,9,1,75,255,248,255,226,254,226,0,12,255,38,255,69,0,222,254,98,255,191,0,255,255,192,255,176,253,166,255,213,0,160,255,255,0,179,1,178,0,176,255,143,254,238,255,223,255,176,255,214,255,159,1,140,0,34,255,119,4,139,2,137,2,73,1,255,2,44,2,249,0,235,0,180,3,157,1,186,1,23,1,141,0,83,1,100,1,45,2,42,254,86,255,99,0,237,0,199,253,224,252,96,1,53,2,26,1,217,1,214,1,76,1,57,255,78,253,252,250,107,252,63,255,86,254,224,252,158,251,230,255,141,254,22,254,63,255,125,2,83,2,7,2,74,1,152,1,141,255,79,0,12,0,221,1,87,0,153,255,136,254,102,253,165,254,235,254,221,254,2,254,31,254,169,0,41,1,195,252,30,253,51,255,85,255,192,254,228,253,72,1,27,1,165,252,66,252,186,1,254,255,44,2,174,2,130,0,56,0,103,5,244,3,243,2,171,1,100,2,229,2,116,2,41,2,173,254,228,252,134,0,21,1,135,253,195,251,254,255,10,255,144,252,245,251,185,249,216,251,30,252,38,254,142,251,24,254,98,254,229,252,73,0,50,255,248,255,117,255,183,1,204,0,80,255,190,253,23,0,131,0,243,254,11,253,65,255,245,0,147,255,174,254,112,0,60,1,120,0,106,254,138,255,99,2,76,255,70,255,123,253,115,0,83,255,34,0,250,253,23,254,105,255,61,0,185,253,180,252,220,0,118,255,87,253,4,252,135,1,239,255,170,253,191,254,157,0,217,254,129,0,155,0,98,252,149,252,37,252,29,1,241,0,173,255,131,255,131,255,108,2,85,2,176,1,92,0,137,1,78,0,153,1,61,0,119,254,29,253,99,254,20,253,83,0,54,0,105,1,27,0,196,251,130,0,175,254,74,253,227,249,41,1,62,1,237,255,175,248,36,0,51,0,195,254,237,246,10,255,231,0,172,255,254,246,241,252,40,0,77,255,71,247,94,252,38,254,50,254,14,253,170,255,224,254,142,253,149,246,57,254,193,255,171,0,181,251,186,251,230,255,113,255,87,251,57,254,106,254,131,254,163,253,46,255,160,255,205,255,188,253,36,254,236,254,241,255,85,251,134,253,77,251,143,252,134,254,35,255,99,253,72,252,82,2,178,0,109,254,92,253,251,2,71,1,89,2,34,1,172,0,44,1,203,0,157,0,200,255,176,254,100,1,24,0,28,255,216,254,253,254,227,255,70,255,7,1,160,1,14,0,159,254,117,1,244,255,40,255,1,1,96,0,174,0,57,0,10,250,152,253,70,252,13,254,15,254,104,255,179,254,125,0,105,0,200,0,179,0,159,255,181,254,32,255,253,2,185,2,248,2,0,1,45,1,59,0,199,1,171,255,204,0,32,1,254,253,240,0,251,0,147,255,0,1,161,1,222,255,99,254,101,0,174,1,128,1,156,0,225,255,246,255,206,0,170,1,77,2,145,0,143,0,71,0,40,3,138,3,77,1,93,1,218,3,170,3,77,2,75,1,20,5,56,3,187,0,253,1,38,4,141,2,123,1,210,1,182,5,169,3,145,1,18,1,19,3,93,3,9,1,2,0,97,2,41,2,28,0,49,1,158,3,84,1,106,0,130,1,241,0,245,254,109,255,225,0,78,255,234,253,91,1,246,1,125,253,131,254,141,1,30,0,117,253,35,253,77,254,142,1,105,254,42,253,28,254,8,255,235,252,110,252,74,254,36,254,14,254,122,254,75,0,217,254,60,252,178,253,162,253,150,0,135,255,207,255,101,255,178,255,167,3,38,2,133,1,38,0,191,254,127,0,168,1,59,1,227,254,143,255,27,1,3,1,146,2,203,0,66,1,230,1,135,3,249,1,236,2,161,1,99,2,167,1,43,2,0,2,239,0,173,255,190,253,237,255,173,254,37,253,93,1,13,0,90,252,137,250,142,255,152,254,107,0,180,2,182,0,90,0,37,251,254,249,241,249,43,253,200,253,121,252,173,250,243,253,251,253,171,252,163,252,20,252,88,255,78,253,189,252,63,0,119,255,212,253,221,253,144,0,226,254,207,252,229,1,63,1,109,255,104,254,14,2,246,0,165,254,78,254,41,1,228,255,222,254,41,254,170,251,251,250,52,254,153,254,36,252,230,252,67,5,19,5,178,2,11,2,192,4,44,4,70,4,245,2,57,3,116,4,240,2,238,1,228,4,85,5,171,4,130,3,9,2,29,4,20,2,176,1,178,254,40,255,199,254,249,254,96,255,52,0,40,254,101,255,127,0,136,0,132,254,44,0,83,3,154,1,94,255,23,254,123,0,1,255,228,252,101,253,66,4,149,3,21,3,237,1,117,5,173,4,46,2,202,0,205,255,138,255,170,254,67,253,83,0,108,0,214,255,71,254,61,0,95,0,31,1,0,1,229,255,89,0,12,2,19,2,95,1,227,0,80,2,33,2,185,2,155,0,92,255,51,1,126,2,18,1,23,254,206,255,242,2,240,0,90,255,132,255,140,255,189,253,68,251,193,255,190,0,217,254,240,251,240,250,147,0,136,254,79,255,143,255,73,3,217,4,27,4,156,2,2,0,37,1,39,2,48,1,184,251,71,252,8,255,120,1,18,253,59,252,87,0,4,2,237,254,252,253,177,2,135,1,133,254,125,253,108,3,82,2,122,254,11,252,123,253,61,2,149,255,200,253,79,253,198,252,255,251,229,255,184,254,53,255,93,3,237,2,36,2,233,0,132,249,237,251,195,1,108,0,108,253,148,253,174,1,236,0,21,0,116,254,122,251,137,253,92,5,18,5,199,3,65,2,101,4,101,4,77,2,198,1,189,254,159,252,45,254,153,0,44,254,69,253,220,252,3,254,120,254,50,253,52,255,221,255,165,253,187,251,201,253,94,255,7,254,20,252,154,255,94,1,219,0,224,0,167,1,252,0,139,1,79,2,96,2,107,1,22,253,160,255,117,1,172,0,171,0,39,1,202,2,83,1,233,0,77,0,107,0,21,1,157,0,153,0,13,254,156,254,11,6,49,4,64,2,238,1,220,254,173,254,8,254,176,253,121,252,184,255,149,253,31,254,198,249,163,251,201,253,2,255,231,252,5,254,204,253,221,254,20,254,236,253,246,1,48,2,130,254,171,1,88,2,230,0,29,255,221,1,251,0,75,0,29,1,74,3,45,3,220,1,226,250,203,250,186,0,121,1,181,253,107,252,131,2,125,1,94,251,215,253,155,1,82,0,153,251,204,252,82,255,228,253,164,253,119,0,31,2,205,0,132,254,145,2,141,3,55,2,112,0,214,254,138,254,114,0,167,252,5,255,56,0,159,0,145,1,89,1,222,255,116,255,145,255,161,253,41,0,102,2,99,1,142,255,179,255,218,1,66,2,56,0,170,5,156,3,74,4,140,5,229,2,144,1,246,0,22,0,76,2,57,1,135,255,71,1,63,3,216,1,142,251,160,253,88,3,40,2,39,251,208,251,126,2,88,2,154,254,254,0,179,254,209,254,122,253,227,2,102,1,74,0,202,4,135,6,197,4,81,3,193,8,88,6,215,3,124,2,49,7,197,5,237,2,128,1,94,1,7,1,87,0,128,0,146,248,83,252,112,255,192,255,58,249,1,255,32,1,225,255,172,245,42,251,110,1,235,0,149,249,188,251,192,250,208,254,227,253,205,251,164,251,123,0,102,251,4,255,208,252,76,255,8,252,21,2,53,2,233,0,25,254,82,254,68,255,78,1,99,3,212,4,22,2,171,0,202,249,185,249,123,2,118,2,108,247,54,1,156,3,156,1,202,246,184,254,188,3,17,2,177,245,135,254,118,2,22,1,214,245,61,1,31,3,43,1,154,246,133,0,84,1,31,0,148,247,68,250,131,0,125,0,96,251,22,254,117,255,46,0,24,253,191,1,123,3,52,2,67,0,61,254,134,2,92,2,215,253,83,254,148,252,140,1,162,0,190,255,25,5,147,3,223,1,67,2,64,4,26,3,194,1,22,1,54,2,68,1,223,251,102,255,148,0,79,255,15,246,168,0,46,4,80,2,209,246,214,255,51,3,89,1,216,246,61,253,209,2,250,0,129,247,39,250,203,254,122,0,178,255,183,255,120,0,173,0,252,255,6,1,249,254,251,254,81,254,192,255,107,254,36,253,207,245,116,0,173,255,63,255,11,250,80,252,35,254,43,253,4,254,51,1,170,0,172,0,64,3,161,1,64,3,174,2,31,255,177,0,126,3,50,3,30,254,123,254,255,4,15,4,129,254,201,0,162,254,40,0,218,2,123,2,226,0,14,2,247,1,206,1,82,1,142,1,23,2,202,2,40,0,230,254,202,5,191,5,61,4,219,2,25,6,48,4,141,3,181,2,139,5,2,5,121,3,111,3,129,4,216,2,162,4,72,3,30,255,106,4,181,3,177,2,18,254,38,252,236,249,128,255,200,253,47,253,55,253,230,255,61,1,12,2,70,0,135,0,107,254,159,252,26,249,116,253,82,255,223,252,117,3,5,3,103,255,165,255,75,4,239,2,6,254,131,251,85,3,134,2,241,0,14,3,7,2,27,2,61,7,164,6,77,4,172,2,31,251,50,250,48,254,188,0,131,252,127,250,224,250,171,254,121,255,182,1,81,255,18,0,87,4,208,3,63,1,208,0,106,250,24,249,83,0,202,1,238,253,24,252,51,1,129,0,184,252,241,255,227,255,156,254,113,252,100,252,133,251,14,255,137,255,240,253,127,0,123,255,7,253,3,253,190,0,173,255,197,254,127,3,10,2,231,0,34,255,102,0,193,255,84,254,60,1,187,2,123,1,70,0,25,0,204,2,58,1,148,255,251,1,106,3,54,2,238,0,108,0,173,3,7,2,195,0,169,1,196,255,85,254,1,1,139,0,153,255,138,253,190,1,78,1,114,1,156,1,48,0,84,255,78,253,229,254,45,2,187,0,226,254,158,0,227,1,140,0,14,1,168,254,137,253,156,3,67,2,140,255,132,0,142,0,210,1,188,255,192,255,230,0,111,255,210,254,226,253,221,252,112,252,250,3,225,2,251,252,247,3,118,2,41,1,220,245,95,0,189,1,80,1,182,247,235,1,254,1,191,0,27,251,161,0,254,255,188,254,86,250,135,253,56,253,151,255,182,252,2,255,101,254,100,0,128,253,222,254,242,3,251,2,118,253,57,1,145,4,218,2,140,0,249,1,6,4,254,2,4,3,31,1,43,4,55,3,239,1,237,2,49,1,67,1,92,255,206,1,78,0,143,1,170,254,150,252,69,0,85,2,240,255,108,2,109,2,81,1,118,255,68,254,247,254,218,0,84,0,62,254,185,3,154,2,34,255,221,252,29,2,92,2,103,252,160,250,244,0,116,0,183,252,45,253,118,2,76,2,140,0,151,2,38,1,112,1,167,3,22,4,113,3,247,2,210,6,184,5,148,3,116,2,180,1,195,3,25,1,1,0,137,255,74,0,30,2,213,0,1,0,201,253,45,1,241,0,4,1,179,1,222,0,140,1,168,3,189,3,84,4,191,2,254,1,250,1,40,3,222,1,89,2,182,2,192,3,108,2,204,3,229,2,212,3,88,2,66,3,205,2,255,2,172,2,131,2,204,3,167,3,126,2,245,1,149,2,208,2,83,3,151,255,136,253,209,254,139,255,83,254,130,0,21,3,186,1,246,253,68,255,192,2,117,1,9,253,42,0,46,3,11,2,237,253,143,251,117,1,66,2,86,253,77,251,57,254,29,1,117,251,215,249,182,251,44,0,81,0,174,255,200,2,107,1,221,1,246,0,186,3,110,2,68,6,86,6,253,4,123,3,129,5,91,3,156,3,124,3,6,3,17,4,179,3,118,4,40,0,222,253,181,255,32,1,152,253,150,255,71,253,230,255,87,255,96,255,133,252,29,253,233,254,128,254,251,251,162,254,245,6,28,5,22,4,48,3,44,6,253,5,192,5,154,4,225,5,52,4,192,4,131,3,122,3,136,3,52,2,142,2,152,3,180,2,253,3,88,3,19,254,132,0,177,0,249,1,71,0,195,0,228,255,97,0,200,1,95,1,92,255,88,0,183,1,22,1,216,255,94,1,115,5,181,3,234,0,161,255,219,252,40,254,38,0,93,255,111,1,158,255,233,1,11,2,1,4,154,4,188,4,138,3,63,1,34,5,46,3,205,1,133,255,225,253,220,252,191,1,20,253,188,254,127,252,153,251,31,253,11,254,235,252,55,253,203,2,9,3,215,4,154,3,157,7,147,7,88,5,97,3,218,2,112,3,246,2,132,1,153,252,198,1,17,0,5,255,131,254,214,252,209,249,239,0,247,253,58,252,232,252,3,1,134,252,178,250,254,252,183,255,166,0,93,1,44,255,67,1,184,252,211,254,217,1,179,1,89,253,48,254,216,2,95,1,100,255,57,255,155,2,176,1,29,0,4,255,159,1,224,1,37,253,133,254,145,0,47,2,240,253,137,253,122,251,97,255,189,1,17,1,123,0,127,2,117,1,130,255,32,3,56,2,84,0,94,255,208,2,200,2,194,252,232,253,71,255,222,0,152,1,196,1,245,1,3,3,127,252,181,250,189,255,186,1,232,252,130,250,54,2,90,2,167,0,186,254,253,1,74,1,161,255,142,253,38,253,168,254,132,6,193,4,11,3,199,1,36,5,60,3,72,2,207,2,148,1,225,255,245,3,21,3,89,0,107,0,123,3,37,2,103,3,45,6,149,3,159,2,98,3,199,5,9,5,86,3,135,1,44,4,98,4,44,3,78,0,206,253,89,1,51,2,173,1,153,255,161,1,19,3,134,255,75,254,155,1,20,3,111,252,95,254,90,2,242,2,30,255,240,255,151,0,248,2,68,253,118,0,152,255,242,255,152,251,48,0,28,1,137,1,122,254,93,254,129,253,140,255,114,252,50,1,60,1,243,255,183,4,216,3,53,3,157,2,85,251,75,253,140,0,43,255,140,252,96,254,57,255,210,253,152,253,245,0,108,254,104,253,6,1,56,0,151,253,44,253,171,255,21,254,192,254,112,253,198,253,193,252,127,255,240,253,30,250,193,255,145,254,127,254,154,254,191,254,4,0,51,0,146,254,42,255,63,1,255,1,146,0,159,2,239,255,221,254,146,255,208,1,117,255,16,254,54,255,220,0,200,254,137,253,108,253,183,255,113,253,204,252,106,253,115,253,248,250,167,252,82,254,71,252,65,252,248,254,207,255,44,254,184,255,131,254,162,254,205,253,63,255,105,254,55,0,104,254,221,252,11,0,203,254,137,2,188,0,58,255,0,254,205,1,177,255,54,254,218,250,249,254,122,255,245,253,135,249,77,254,17,254,3,253,57,0,165,254,98,254,178,1,139,251,14,255,104,253,167,252,34,0,188,255,61,253,174,254,163,1,163,0,226,255,250,254,57,254,235,252,106,250,47,253,238,3,152,2,13,1,25,0,107,2,4,1,183,0,96,0,56,252,178,250,124,254,135,0,75,253,67,3,200,1,154,0,81,4,191,2,57,2,107,1,89,6,46,5,217,3,236,2,36,255,219,0,76,0,48,255,81,250,130,249,49,0,149,0,60,252,84,255,16,253,176,254,113,2,209,0,6,255,190,255,7,252,186,252,254,255,61,1,136,247,51,250,118,255,123,0,172,248,205,247,247,253,85,0,57,252,146,254,73,253,143,252,103,252,13,252,5,253,75,252,132,255,0,255,160,254,108,253,178,0,207,1,98,1,48,1,48,249,177,253,230,254,79,0,55,247,175,0,99,3,243,1,118,255,76,255,75,255,235,255,13,247,39,251,52,254,248,253,253,252,195,1,246,255,204,254,15,1,191,255,4,0,214,0,233,254,77,254,213,255,164,254,98,253,35,0,191,255,45,255,38,3,23,2,85,0,41,1,57,0,239,0,210,2,237,1,225,0,149,2,72,3,35,2,228,253,136,254,14,0,93,1,213,1,209,2,75,1,162,0,224,253,16,253,194,255,246,255,142,1,168,255,212,2,189,2,237,255,235,253,162,255,89,2,136,0,185,255,87,253,21,253,90,255,168,254,5,1,206,255,161,0,204,255,229,1,81,1,117,249,50,0,190,0,163,255,22,247,25,255,62,255,174,255,161,255,173,253,102,255,128,0,126,3,245,1,76,2,201,1,167,254,206,0,122,0,110,0,137,253,29,255,199,253,3,0,152,1,239,0,141,1,226,0,59,255,254,255,128,0,235,1,1,5,136,3,36,1,215,0,26,2,50,1,3,1,253,1,91,253,233,251,13,0,65,1,89,253,180,253,154,254,44,255,210,253,243,0,134,2,223,1,230,1,86,1,122,2,20,2,107,0,34,3,75,1,136,0,144,255,114,254,249,251,226,254,186,254,63,253,32,1,16,1,19,5,120,4,154,4,92,3,89,254,121,0,127,254,108,255,217,254,210,254,190,252,205,252,16,0,232,255,55,255,36,254,43,2,91,0,11,255,38,1,218,255,133,254,62,252,59,251,89,251,18,250,239,254,117,254,122,254,11,252,123,253,61,2,205,248,250,251,249,1,212,1,232,2,179,3,97,2,237,1,79,253,108,251,140,253,121,255,254,251,195,0,155,1,196,0,46,6,123,4,63,2,81,1,41,251,247,252,120,253,114,255,83,2,57,3,199,3,223,2,74,251,54,252,175,255,170,254,23,253,13,0,184,255,119,1,198,1,19,0,127,5,153,3,145,249,84,255,93,3,50,2,160,3,1,6,39,4,228,2,88,246,72,252,8,1,82,0,10,254,59,252,202,250,123,0,99,3,212,4,22,2,171,0,240,246,52,254,12,3,107,1,90,251,151,253,252,0,195,255,82,255,34,0,243,3,20,3,227,246,247,0,167,1,153,0,240,255,157,254,6,1,193,1,216,249,207,251,224,253,141,254,153,253,207,254,27,4,37,3,175,2,16,2,6,0,74,255,167,3,107,3,234,3,41,3,199,0,1,1,126,0,76,0,184,253,142,251,87,2,44,2,175,251,145,250,201,249,249,253,47,252,211,250,108,0,91,1,46,253,49,252,109,1,101,0,111,255,169,2,249,0,103,255,0,0,178,254,198,253,159,0,156,1,29,1,176,254,151,253,71,252,58,252,119,3,177,2,29,251,84,0,71,255,114,254,176,253,177,1,20,4,141,2,85,0,73,1,216,255,105,1,79,254,63,253,210,1,62,2,102,255,142,2,80,2,34,1,89,255,72,0,93,1,175,0,162,2,41,1,209,3,208,2,211,4,180,4,245,2,232,1,112,254,243,254,26,2,116,1,186,250,149,250,86,251,165,255,238,4,108,3,7,3,188,2,169,253,218,255,82,254,46,253,184,7,94,6,223,3,96,2,111,0,20,1,30,255,160,255,77,252,124,254,245,255,249,255,209,254,237,253,185,252,82,1,198,6,174,6,125,5,245,3,252,253,169,252,123,253,210,0,80,253,96,254,1,2,230,0,202,252,131,253,134,251,192,254,72,252,110,253,74,253,183,0,142,255,145,253,50,3,162,2,65,255,52,255,219,2,123,2,51,0,197,4,115,3,64,2,70,252,81,254,58,3,86,2,170,254,13,253,124,252,105,254,154,251,158,254,50,255,0,254,221,253,214,252,155,254,148,253,66,0,3,2,183,255,102,254,152,252,79,252,92,250,53,251,191,0,239,255,224,253,25,255,252,249,224,253,123,252,138,252,134,252,242,249,19,246,205,252,54,252,175,0,198,252,46,251,6,253,169,253,234,255,122,2,213,252,37,252,122,252,189,254,203,0,26,0,129,254,21,255,243,252,113,254,238,4,138,3,92,252,137,250,156,250,144,253,93,0,87,0,98,254,229,253,77,253,37,0,121,2,254,1,125,254,36,254,206,250,143,1,66,0,7,1,105,254,207,255,177,254,95,254,17,4,73,7,245,252,191,251,96,250,22,253,166,252,64,3,187,253,9,253,141,254,95,253,6,254,40,8,208,253,134,253,101,251,15,1,241,0,14,0,74,254,12,255,115,254,207,1,178,4,23,4,162,253,227,252,98,250,205,255,189,254,225,1,32,255,184,253,241,253,238,1,113,3,170,2,79,254,206,254,22,252,42,2,147,2,222,0,171,0,96,255,159,254,169,2,6,7,29,6,172,252,99,251,97,249,176,254,102,253,114,0,187,253,12,253,24,253,61,255,119,1,241,1,47,254,220,252,182,251,154,0,26,1,125,255,206,255,65,255,49,253,67,1,220,2,6,6,46,253,205,252,132,250,105,0,6,255,185,0,78,255,10,254,26,253,65,1,254,1,87,4,189,254,201,253,58,252,127,0,228,1,82,1,96,255,52,0,174,254,220,2,87,5,18,6,142,253,222,252,96,249,226,254,182,253,164,2,73,253,169,254,142,254,22,254,39,1,101,7,138,253,194,253,10,252,176,255,133,2,187,255,250,255,194,254,148,254,14,3,170,5,14,4,199,254,35,253,141,250,120,0,60,0,221,1,248,254,183,253,133,255,199,2,221,4,121,2,165,255,157,254,8,252,3,3,246,2,5,1,253,0,81,0,38,254,162,3,167,8,184,6,216,252,181,251,123,248,208,253,242,252,169,0,220,252,206,251,68,255,142,253,201,255,125,5,74,253,52,253,86,251,108,253,98,1,73,1,254,253,201,255,225,253,110,1,9,4,158,4,110,253,65,252,179,250,201,255,72,255,93,0,163,253,226,254,106,253,148,1,193,1,59,3,226,254,162,254,17,251,116,2,50,1,227,0,240,255,147,0,145,253,186,0,155,3,98,8,94,253,134,252,186,249,69,254,28,255,83,1,143,254,234,252,103,254,231,0,86,0,189,5,64,254,187,253,219,251,82,2,194,1,79,255,132,255,86,255,65,254,159,2,135,4,124,5,36,254,101,253,25,250,179,255,118,255,204,2,79,255,140,254,131,254,195,1,166,3,147,3,6,255,80,254,202,252,16,1,60,3,190,1,26,0,19,0,225,255,186,2,156,6,120,8,122,253,47,252,124,248,77,255,39,254,12,1,133,254,23,253,77,253,11,0,127,0,9,4,24,254,107,252,199,252,61,0,67,1,135,0,147,0,111,255,82,253,173,2,18,3,146,6,6,254,176,252,239,250,35,0,90,0,222,0,233,255,166,254,98,253,199,1,79,2,7,5,53,255,175,253,194,251,140,2,96,1,181,1,39,0,63,0,55,254,73,3,241,4,57,8,248,253,142,252,208,249,184,254,57,253,141,5,172,253,170,254,186,255,209,0,173,0,136,7,89,254,170,253,103,252,165,1,93,2,218,255,254,255,11,255,129,255,128,3,177,7,111,4,133,254,250,253,213,249,173,0,118,0,241,2,201,255,131,254,204,255,217,3,253,3,241,2,254,255,221,254,133,252,241,2,224,3,167,1,8,1,131,0,60,255,127,3,226,8,239,9,133,253,192,251,61,246,239,253,42,252,14,2,4,253,194,252,220,253,76,254,60,1,87,2,93,253,84,252,22,253,199,255,236,0,245,255,55,255,175,255,226,252,16,0,77,3,22,6,31,253,39,252,68,251,44,254,17,0,34,1,233,254,184,253,68,253,183,0,54,3,193,2,247,254,20,254,93,251,165,1,152,0,212,1,122,254,166,0,244,254,39,0,14,6,76,7,133,253,58,252,221,249,59,254,20,254,142,3,228,254,253,251,181,255,75,255,123,255,60,7,67,254,144,253,106,251,164,1,111,1,207,255,123,254,44,255,87,255,195,2,49,4,184,4,229,253,58,253,87,250,83,0,93,255,228,1,20,255,225,253,157,254,82,1,151,4,46,3,10,255,203,254,66,252,94,2,248,2,60,0,166,0,248,255,93,255,206,254,57,7,3,10,21,253,255,251,9,249,93,254,66,254,209,0,50,253,202,253,234,253,6,254,181,2,89,3,49,254,71,253,198,251,69,1,175,1,50,255,241,255,248,255,5,253,33,2,151,3,238,5,157,253,241,252,223,250,0,1,201,255,208,0,91,255,164,254,106,253,65,1,168,2,162,3,186,254,83,254,73,252,228,1,190,1,58,2,59,255,72,0,183,255,141,3,175,5,205,6,205,253,31,253,74,248,132,255,96,254,206,2,34,254,108,254,198,254,240,255,190,1,100,6,217,253,231,253,18,253,198,255,126,2,214,0,55,0,71,255,241,254,124,4,21,5,188,4,29,254,97,253,16,251,117,0,29,1,31,2,52,255,121,254,145,255,1,2,2,6,86,3,142,255,66,255,46,252,109,3,83,2,208,1,4,1,4,1,201,254,236,2,235,8,168,8,251,253,79,252,133,247,186,254,60,253,122,1,212,252,77,253,24,255,208,253,175,2,129,5,36,253,78,253,188,252,153,254,133,2,130,1,247,254,62,0,90,253,145,0,108,6,184,4,213,253,36,252,47,251,178,255,14,0,114,0,185,254,154,254,23,254,136,1,165,2,185,2,55,255,20,255,140,251,181,2,193,1,178,0,13,255,0,1,79,254,99,2,105,5,152,9,156,253,123,252,72,250,205,254,239,255,243,1,197,254,101,253,2,255,0,1,172,1,183,5,26,254,90,254,224,251,143,2,114,1,18,0,154,255,71,255,236,254,243,2,42,6,55,5,24,254,165,253,118,250,182,0,163,255,102,3,183,255,54,254,164,254,67,3,94,3,189,3,230,254,179,254,22,253,35,2,71,3,172,1,17,1,167,255,13,0,172,3,172,6,16,10,94,254,196,251,34,249,212,255,154,254,3,1,15,254,125,253,208,253,99,0,45,2,193,3,91,254,2,253,107,252,39,1,70,1,184,0,175,0,15,0,142,253,20,2,110,3,189,7,69,254,0,253,5,251,221,0,156,0,12,1,39,0,149,254,7,254,183,2,4,3,116,4,94,255,53,254,112,252,197,2,188,1,146,2,25,0,47,1,200,254,244,4,130,5,179,6,215,254,2,253,212,248,249,254,148,255,46,4,106,254,243,255,127,255,57,0,182,1,174,10,138,254,25,254,189,252,48,1,184,2,164,0,104,0,21,255,5,0,75,6,108,7,119,5,27,255,186,253,211,250,149,1,192,0,49,3,169,255,74,254,111,0,4,4,175,4,225,3,68,0,81,255,90,252,9,4,93,4,195,1,222,1,200,0,8,255,79,8,136,10,250,7,189,252,213,250,173,247,225,252,76,253,210,1,212,252,248,251,43,254,146,253,32,1,152,3,67,253,183,252,210,251,101,254,0,2,8,0,122,254,165,255,24,253,226,255,19,4,137,4,202,252,132,251,124,251,218,254,210,255,110,0,101,254,138,254,90,253,214,0,19,2,156,2,106,254,92,254,86,251,231,1,232,0,47,1,194,254,91,0,40,254,123,0,208,4,141,9,46,253,72,252,41,250,30,253,93,253,52,5,225,253,162,253,45,255,161,255,158,255,228,5,219,253,254,253,87,251,217,1,211,0,73,0,224,254,144,255,123,254,25,2,52,5,234,4,201,253,13,253,247,249,71,0,229,254,120,2,86,255,31,254,19,254,169,2,234,3,49,3,156,254,181,254,147,252,163,1,194,2,90,1,241,0,222,255,186,254,121,1,158,7,91,7,41,253,205,251,167,249,23,255,225,253,116,0,244,253,218,252,183,253,183,255,222,1,217,2,224,254,99,252,137,251,173,0,191,1,204,255,68,0,27,255,162,253,193,1,17,2,5,7,177,253,149,252,173,250,183,0,112,255,68,1,153,255,60,254,102,253,111,2,232,1,152,4,18,255,1,254,20,252,70,1,40,2,202,1,136,0,108,0,193,254,114,2,63,5,91,7,22,254,122,253,62,249,70,255,63,254,216,3,30,253,180,255,86,255,218,253,243,2,0,10,16,254,2,254,77,252,210,0,182,2,204,255,84,0,190,254,57,255,66,4,89,6,200,4,136,254,165,253,140,250,87,1,74,0,120,2,81,255,10,254,224,255,204,3,52,5,222,2,52,0,217,254,167,251,41,4,150,3,160,0,137,1,107,0,115,254,190,4,89,10,205,6,136,253,79,251,157,248,49,253,235,254,97,1,117,253,144,252,134,255,45,255,209,0,58,5,206,253,54,253,221,251,48,255,132,1,159,0,192,254,195,255,217,253,37,1,68,4,163,5,120,253,159,252,27,251,207,255,113,255,49,1,111,254,29,255,183,253,49,2,20,2,159,3,139,255,69,254,92,251,251,1,180,1,36,1,177,255,233,0,54,254,159,2,1,4,92,9,135,253,182,252,11,250,204,254,226,254,128,2,139,254,147,253,105,254,162,1,253,0,25,5,197,254,187,253,143,251,60,2,173,2,231,254,61,0,188,255,141,254,223,3,77,4,218,5,19,254,85,253,174,250,209,255,164,0,192,2,0,255,198,254,244,254,119,2,181,3,28,4,138,255,164,254,191,252,68,0,156,4,56,2,152,0,117,0,34,0,89,4,110,7,191,8,167,253,65,252,86,249,113,255,23,254,224,1,180,254,113,253,194,253,54,0,97,1,168,4,50,254,116,253,228,252,150,0,37,2,112,0,195,0,145,255,253,253,167,2,84,4,111,6,210,253,19,253,63,251,247,255,16,1,85,1,203,255,247,254,233,253,233,1,75,3,18,5,136,255,30,254,248,251,120,2,31,2,152,1,179,0,50,1,242,253,100,4,184,5,196,8,95,254,238,252,230,249,32,255,128,254,84,5,135,254,53,254,231,255,129,1,233,1,126,8,180,254,117,253,195,252,32,2,41,2,61,0,22,0,143,255,167,255,104,4,189,6,244,5,40,255,139,254,139,249,161,0,60,1,140,3,91,255,34,255,189,255,82,5,151,4,21,3,73,0,4,255,1,253,226,2,164,3,104,2,106,1,246,0,130,255,19,3,94,10,211,11,77,253,174,251,114,247,203,253,180,253,12,2,178,253,45,252,22,254,249,254,141,1,214,3,191,253,187,252,79,252,234,255,179,1,207,255,66,255,138,255,139,253,168,255,216,4,233,5,132,253,229,251,5,252,221,254,189,0,3,1,255,254,42,254,139,253,145,0,177,3,126,3,186,254,148,254,186,251,31,2,4,1,118,2,54,255,189,0,47,255,101,1,99,5,43,8,199,253,205,251,87,250,54,253,17,255,151,3,92,254,63,253,172,255,147,255,142,255,103,9,99,254,239,253,103,251,226,1,112,1,131,0,70,255,184,255,125,255,93,3,231,4,196,4,157,253,110,253,195,250,227,0,135,255,119,2,80,255,23,254,38,255,233,2,151,4,189,3,191,254,108,255,88,252,159,2,198,3,216,0,84,1,253,255,113,255,213,1,56,7,133,9,39,253,63,252,109,249,43,255,2,255,65,1,1,254,74,254,247,253,130,255,213,2,135,3,172,254,83,253,248,251,60,1,224,1,20,0,23,0,167,255,217,253,97,1,27,4,253,6,224,253,11,253,172,250,42,1,231,255,180,1,156,255,120,254,249,253,211,1,242,2,54,4,46,255,114,254,202,251,108,2,146,2,118,2],"i8",R2,h1.GLOBAL_BASE+10240),I0([33,0,147,0,78,255,153,3,151,6,129,7,187,254,240,253,70,248,2,0,227,254,142,3,141,254,22,254,26,255,0,0,85,2,218,7,16,254,117,254,190,252,37,0,177,3,245,0,181,0,96,255,112,255,201,5,93,5,77,5,157,254,167,253,10,251,42,1,66,1,160,2,63,255,176,254,77,0,65,4,253,5,154,3,177,0,217,255,155,251,228,3,13,3,24,2,200,1,110,1,80,254,135,5,136,9,231,8,46,254,10,253,235,246,209,254,3,254,131,1,41,253,211,253,66,0,111,255,131,2,224,4,224,253,92,253,108,252,31,255,94,3,76,2,104,255,40,0,235,253,167,1,143,5,22,6,196,253,181,252,135,251,128,255,85,0,205,1,18,255,255,254,184,253,93,2,236,2,93,3,24,0,54,255,127,250,29,3,231,1,47,1,75,255,108,1,74,255,104,2,98,5,126,11,18,254,172,252,95,250,220,254,61,0,44,3,172,255,45,253,74,255,43,2,20,2,226,5,147,254,19,254,223,251,54,3,76,2,11,0,242,255,238,255,26,255,233,3,121,5,171,5,38,254,199,253,244,250,46,1,62,0,38,4,186,255,136,254,34,255,214,3,206,3,125,4,60,255,22,255,229,252,223,1,74,4,243,1,106,1,58,0,70,0,123,4,21,8,41,11,25,254,146,252,224,248,73,0,224,254,92,1,154,254,12,254,4,254,199,0,209,2,218,4,178,255,71,253,229,252,105,1,24,2,196,0,118,1,110,0,33,253,79,3,27,4,104,7,146,254,55,253,98,251,59,1,64,1,173,1,72,0,41,255,62,254,247,2,118,3,83,5,226,255,84,254,190,252,93,3,115,2,28,3,118,0,212,1,233,254,75,5,91,7,101,7,68,255,126,253,180,249,63,0,81,255,174,4,94,254,45,255,51,0,158,1,75,2,41,10,22,255,211,253,166,252,168,1,121,3,222,0,136,0,155,255,83,0,133,5,230,8,103,5,172,255,67,254,147,250,158,1,57,1,21,4,29,0,169,254,65,0,16,6,111,6,212,3,183,0,165,255,195,252,249,4,133,5,104,1,41,2,16,1,149,255,51,6,77,12,43,10,104,5,29,8,92,13,244,19,86,26,186,31,135,38,84,43,170,49,133,53,61,254,215,251,239,253,231,250,62,254,12,253,15,254,161,252,128,254,149,253,99,254,99,253,195,254,230,253,181,254,212,253,98,254,4,254,88,254,134,254,238,254,188,254,78,254,154,253,30,255,12,254,24,255,254,253,249,254,135,254,214,254,102,254,105,255,58,253,82,255,206,252,107,255,100,254,100,255,83,254,224,254,50,254,70,255,53,255,86,255,210,254,65,255,191,254,125,255,109,255,215,254,117,254,28,255,42,255,11,255,64,255,189,255,196,254,185,255,185,254,152,255,51,255,162,255,73,255,113,255,218,255,63,255,161,255,16,0,180,255,132,255,8,255,23,0,19,255,24,0,12,255,18,0,120,255,44,0,145,255,223,255,232,255,231,255,0,0,149,0,19,0,23,0,113,255,158,0,87,255,174,0,75,255,133,0,201,255,165,0,230,255,111,0,84,0,98,0,75,0,87,0,183,0,141,255,245,255,248,255,130,0,11,0,170,0,254,0,77,0,205,0,17,0,183,0,112,0,6,1,194,0,202,0,31,1,95,0,189,0,214,255,151,255,234,0,179,0,39,0,186,0,163,0,89,1,76,1,199,0,43,1,161,0,202,255,29,1,178,255,25,1,123,255,141,0,74,255,111,0,249,0,85,1,15,1,108,1,93,0,147,1,75,0,135,1,92,0,254,1,118,255,220,0,71,255,227,255,222,255,105,1,141,255,64,1,3,0,42,2,99,0,30,1,218,0,79,2,11,255,150,1,244,254,197,1,0,0,68,2,25,0,94,2,19,1,20,2,148,0,194,1,183,255,227,2,227,254,6,2,224,254,94,0,53,255,162,2,116,255,182,255,205,0,202,2,142,255,43,1,176,0,155,3,182,0,45,2,240,0,193,2,240,255,1,2,229,1,81,2,37,1,128,1,195,1,105,2,218,255,50,0,51,2,17,2,47,1,209,0,203,1,107,1,177,1,196,1,194,1,198,1,111,1,94,2,221,1,229,2,176,1,97,1,112,1,11,1,105,1,204,2,17,1,71,2,197,1,166,0,254,1,172,0,201,0,117,2,18,1,191,0,56,2,127,2,46,1,42,1,122,2,131,1,131,2,94,1,75,2,48,2,100,2,53,2,88,2,20,3,231,1,160,2,0,2,247,3,65,1,77,1,101,1,86,3,131,255,157,1,218,1,200,2,17,0,105,255,52,2,29,1,14,1,15,255,203,3,121,3,233,1,220,0,254,1,128,3,37,2,156,3,71,1,57,3,34,1,143,3,28,2,84,4,158,0,37,3,199,0,189,3,255,1,218,2,100,0,106,3,13,0,23,3,179,1,120,2,164,2,204,3,249,0,132,3,211,1,194,4,13,3,50,4,73,2,17,3,233,255,157,2,11,1,19,4,107,2,60,4,103,2,121,4,110,2,137,3,148,3,25,4,80,0,75,1,72,2,51,4,89,0,127,2,220,3,193,3,2,3,208,2,30,3,187,2,236,1,191,1,131,3,115,2,15,1,164,4,213,2,53,5,87,0,91,2,64,3,67,6,104,2,103,4,122,3,225,5,232,3,132,4,98,3,241,3,227,3,59,3,125,4,90,3,49,3,170,5,5,3,40,5,244,1,109,5,56,1,129,4,236,255,60,4,64,0,3,5,2,0,148,4,143,1,77,7,2,2,170,6,246,1,100,6,118,3,242,5,160,1,88,2,107,4,70,5,251,4,110,5,121,3,3,7,146,3,230,6,227,0,159,4,226,4,34,7,249,1,62,7,151,3,49,9,57,255,175,1,152,0,199,6,43,255,228,255,136,1,54,5,103,255,204,255,210,3,127,4,189,254,112,254,45,3,167,6,120,255,84,0,169,5,223,7,181,254,113,255,119,255,168,4,0,255,22,2,99,255,7,4,205,254,73,254,30,2,219,2,183,254,92,254,159,255,104,2,150,254,88,255,190,254,110,1,9,255,146,255,45,255,89,0,60,255,203,254,20,0,59,0,148,254,49,254,226,254,89,0,176,254,175,0,80,254,141,0,133,254,66,255,78,254,60,255,177,255,150,0,234,254,29,255,232,254,166,0,213,253,90,254,101,255,29,2,146,254,54,0,227,255,173,255,211,254,250,252,186,0,116,2,115,254,248,254,242,0,37,1,59,255,183,253,124,0,154,1,53,0,123,255,10,0,84,1,198,253,215,251,65,0,66,254,68,0,19,254,127,1,169,3,155,254,57,253,153,254,6,255,91,253,212,251,36,1,230,255,107,1,6,0,95,2,33,5,129,255,246,255,233,5,94,7,201,2,204,3,189,5,133,8,163,5,224,7,161,249,192,249,252,248,14,247,253,251,22,249,180,251,23,248,3,251,148,250,169,250,2,250,77,252,75,250,52,252,12,250,25,252,58,251,4,252,108,251,209,252,37,252,32,252,165,250,64,251,18,252,247,250,186,251,24,253,12,251,13,253,243,250,162,252,101,252,119,252,40,252,90,253,229,251,83,253,230,251,193,251,39,252,218,251,89,253,35,252,127,253,153,251,48,252,6,253,114,253,134,252,218,252,191,252,189,251,62,253,139,253,147,253,218,252,128,253,212,252,249,252,134,253,245,252,225,253,28,252,203,253,205,251,188,253,222,253,157,253,196,253,149,253,8,253,222,254,145,252,242,253,201,252,50,254,229,252,3,255,215,253,97,254,179,253,73,254,235,253,172,254,76,253,89,252,7,254,252,252,66,253,149,251,249,254,206,254,53,252,29,254,67,254,182,255,213,253,220,253,154,253,127,255,75,253,22,255,116,254,10,255,37,254,6,255,247,254,108,254,136,254,254,253,95,254,2,254,212,254,199,254,178,254,104,253,49,254,210,252,126,254,64,253,175,254,153,253,22,255,55,255,23,255,17,255,89,255,201,253,53,255,149,253,109,255,97,254,141,255,160,254,90,255,18,253,85,255,7,253,242,254,145,252,248,254,121,252,145,254,24,253,43,0,37,254,14,0,115,253,43,0,98,253,11,0,64,254,197,255,247,253,130,255,137,255,101,255,155,253,214,255,161,252,229,255,93,252,136,0,29,254,183,0,44,254,55,0,214,254,55,0,208,254,57,1,159,253,57,1,48,253,66,1,89,255,100,0,227,253,253,255,137,255,145,255,69,255,233,0,20,255,4,1,22,255,26,0,91,255,134,0,211,255,216,255,219,253,104,1,53,255,122,1,124,254,194,1,129,254,19,1,20,0,182,0,153,255,246,0,145,255,175,1,37,0,206,1,110,255,231,1,99,255,228,254,197,255,247,1,72,255,24,0,53,0,253,255,54,0,122,0,3,1,77,1,66,0,228,1,104,0,180,1,68,0,195,0,116,0,190,0,206,0,13,1,247,255,226,1,96,1,126,1,29,1,143,1,21,1,196,1,0,1,69,0,186,0,13,0,41,1,243,255,3,1,161,255,30,0,56,0,138,1,196,0,169,1,205,0,200,1,25,1,65,2,15,0,191,0,119,1,34,1,151,1,64,2,200,255,227,0,32,2,149,1,0,0,37,2,164,255,16,2,27,255,95,1,11,255,82,1,150,254,179,1,167,0,15,2,181,255,46,1,91,0,56,3,129,0,87,2,240,1,167,2,186,0,237,2,153,0,225,2,231,254,88,2,164,254,103,2,20,255,1,3,41,0,113,3,38,0,122,3,36,255,73,3,155,254,115,3,119,254,135,3,134,253,218,1,68,254,82,3,81,255,166,2,19,254,242,0,249,253,17,3,54,253,70,2,227,253,110,1,225,253,178,1,171,253,244,1,3,253,222,0,66,253,149,3,25,253,194,3,155,252,245,1,125,252,36,2,133,254,200,0,77,254,157,0,205,252,214,0,163,252,157,0,154,253,40,0,136,253,94,0,141,252,202,255,27,253,4,2,11,254,42,1,154,253,85,255,154,252,95,255,159,252,233,255,206,252,93,0,9,252,245,254,106,253,153,254,219,253,2,0,70,254,135,255,135,254,0,0,29,255,33,0,98,254,130,255,127,255,212,0,90,252,34,0,198,251,230,254,161,251,244,254,58,253,199,252,92,254,65,255,204,251,96,252,107,252,163,255,140,253,154,254,97,0,7,0,50,255,119,254,155,255,24,0,53,255,38,0,88,255,83,0,169,253,89,254,233,254,170,1,68,253,118,0,181,255,206,0,43,252,95,253,88,253,161,1,145,254,37,0,233,254,218,1,127,255,194,254,63,1,40,1,142,253,217,255,87,1,90,2,72,253,217,255,209,254,172,3,104,0,233,0,132,254,137,0,220,255,13,1,181,255,42,255,120,0,43,0,239,253,35,254,203,1,164,0,54,255,27,255,207,255,89,255,97,2,24,3,98,0,36,255,147,3,148,0,37,1,27,1,101,3,91,0,63,2,138,1,70,1,178,255,205,2,67,0,109,1,189,254,104,2,220,255,219,2,27,0,107,2,238,0,120,2,17,1,192,1,99,0,33,3,220,1,101,3,17,1,173,2,64,0,21,3,72,0,253,3,217,0,25,3,203,1,222,2,104,1,134,2,224,1,104,1,66,1,173,1,208,1,126,2,174,1,244,2,107,1,232,3,148,1,171,2,16,2,90,2,103,2,143,2,157,1,178,3,175,2,169,3,90,2,136,3,92,2,43,2,225,2,18,3,150,2,211,1,142,2,106,1,77,2,161,3,198,2,242,1,222,1,159,1,164,1,181,2,115,3,45,3,171,2,13,3,157,3,145,3,171,3,214,2,220,2,235,1,85,3,19,2,180,3,222,2,195,3,59,1,40,3,249,2,243,2,120,4,248,2,143,2,52,4,58,3,33,4,67,4,70,3,235,3,40,3,23,4,109,4,147,2,77,4,224,3,26,4,50,4,51,4,203,3,182,2,202,4,30,4,59,2,73,3,116,3,124,5,99,5,72,4,56,4,93,3,207,4,223,2,4,5,248,2,248,4,223,3,87,5,29,4,233,4,188,2,26,4,22,2,220,3,197,1,240,4,87,2,116,4,167,2,85,6,47,3,104,5,9,2,37,5,137,1,28,6,37,3,168,5,174,2,44,4,136,2,107,3,51,1,59,4,105,1,23,4,61,1,137,5,196,3,163,2,59,2,128,4,79,0,90,4,209,255,250,5,55,1,185,6,58,1,142,4,177,2,2,2,162,255,93,1,26,1,132,5,72,1,1,4,231,1,191,255,57,0,37,3,202,3,36,0,62,0,1,3,249,254,23,3,166,254,125,2,187,2,119,255,108,2,22,2,29,2,33,253,194,0,199,2,44,1,244,254,161,252,158,3,1,3,60,253,84,254,250,1,174,0,132,252,138,253,179,1,35,2,101,250,254,254,109,2,215,1,6,252,168,250,119,254,9,2,104,252,82,253,231,255,20,0,42,252,124,251,84,1,9,0,234,249,145,251,160,254,48,0,213,249,110,254,137,252,6,0,124,251,136,252,220,253,160,254,149,249,112,251,97,255,98,2,24,248,61,252,31,255,193,0,136,249,88,248,11,255,19,254,60,252,112,249,88,252,133,253,237,250,48,249,148,250,164,253,252,249,189,252,139,250,121,255,204,249,222,254,122,249,56,253,37,248,160,249,129,249,229,255,46,247,213,252,123,251,184,0,15,251,189,0,169,250,74,2,37,248,201,0,234,252,200,2,70,251,3,0,247,251,40,3,29,251,62,3,145,255,123,2,156,249,191,1,49,254,75,252,67,254,96,252,8,254,118,251,11,254,69,251,144,0,161,254,140,254,228,251,229,254,221,251,233,254,157,251,193,253,98,250,181,253,178,249,89,252,40,252,229,0,178,2,103,252,49,253,109,254,82,5,83,253,47,254,106,3,141,1,3,254,210,255,61,1,54,5,27,254,200,1,45,3,183,1,101,254,83,1,130,3,43,4,87,254,46,0,161,5,241,1,115,252,224,252,185,5,22,4,2,255,191,254,150,5,141,4,68,0,94,1,10,4,154,2,114,1,11,0,31,5,22,3,143,0,232,0,17,4,26,6,142,255,151,2,80,6,54,4,198,1,67,2,251,4,16,4,180,255,141,3,240,2,43,4,153,0,0,2,92,1,190,4,102,2,129,1,51,7,40,3,13,1,10,4,203,0,62,4,140,2,249,3,247,6,106,4,173,1,47,5,131,1,104,5,207,255,159,4,184,255,191,4,96,254,233,3,32,2,213,6,160,254,199,4,10,254,175,4,179,253,57,2,29,255,94,6,114,255,42,6,26,255,179,6,54,253,8,5,186,252,118,5,107,4,77,5,48,255,208,4,181,1,197,3,95,252,50,3,43,3,130,5,91,3,227,5,164,0,188,4,107,5,1,7,228,1,82,7,200,1,15,8,228,3,146,4,46,5,122,5,36,5,80,5,111,4,238,4,210,4,82,6,81,5,232,6,141,5,203,4,48,6,67,5,86,3,160,2,149,6,30,6,115,4,246,4,224,7,33,7,237,6,45,6,252,5,180,5,207,5,178,3,123,6,253,3,208,6,188,4,112,5,209,3,236,6,137,4,34,7,140,4,182,6,149,5,181,7,55,6,161,4,96,3,84,8,37,4,7,7,46,3,46,7,245,2,56,8,35,5,6,8,234,4,65,8,147,3,27,9,162,3,187,5,123,4,30,10,159,5,197,8,208,6,42,8,84,6,54,9,174,5,106,10,226,5,84,7,45,7,22,8,183,7,203,6,41,6,170,2,9,5,48,6,253,7,174,5,50,8,194,9,212,7,151,10,18,8,214,2,52,6,196,10,32,9,228,0,79,3,152,9,123,6,36,0,45,1,150,7,165,7,66,254,160,255,106,8,116,5,253,5,77,4,14,0,96,2,101,252,36,253,103,5,190,7,65,5,184,3,88,253,65,1,1,5,244,4,198,249,109,1,173,3,178,3,55,249,202,252,70,9,227,10,29,7,228,10,236,248,29,247,169,248,23,246,152,249,200,248,97,249,44,248,60,251,136,248,59,251,198,247,233,249,204,249,219,249,236,249,85,251,177,249,56,251,65,249,177,250,129,251,176,249,100,248,6,251,145,250,231,250,133,250,185,249,101,251,116,249,225,250,93,250,58,250,169,250,126,252,24,251,221,251,205,250,146,251,42,252,147,251,131,251,32,250,200,251,228,250,4,252,97,251,44,252,50,250,57,252,41,250,36,252,102,252,233,251,203,251,186,252,101,251,166,252,58,251,149,251,239,251,216,251,1,253,152,252,123,251,67,253,144,252,62,253,118,252,250,252,8,252,190,253,200,251,223,252,58,250,177,253,169,251,176,253,134,251,55,253,148,250,128,253,160,250,171,253,221,251,96,254,121,252,82,253,192,252,107,253,60,253,68,254,156,252,22,254,103,252,138,254,248,252,149,253,110,251,183,253,219,253,255,252,229,252,77,254,109,253,238,253,27,253,14,254,187,252,155,254,171,253,233,254,153,252,13,255,137,252,230,254,103,253,232,254,101,253,91,255,208,253,118,254,121,252,150,254,102,254,64,254,185,253,103,254,194,253,199,254,155,254,131,253,220,253,198,253,76,254,128,252,8,254,130,254,11,253,198,255,31,254,91,255,150,253,65,255,138,254,22,255,130,254,34,255,85,253,231,255,32,254,94,254,153,254,38,253,159,254,188,254,99,255,80,254,190,254,118,254,209,254,228,254,152,255,167,253,223,254,212,253,60,255,180,253,106,255,109,253,160,253,39,254,232,255,188,255,64,254,38,254,248,255,6,254,211,255,20,253,72,255,180,252,4,255,123,252,165,255,184,253,159,255,116,253,138,0,4,253,125,255,90,253,244,255,98,253,165,0,253,254,253,255,184,252,149,255,115,252,37,0,32,252,44,0,170,252,97,254,185,252,13,0,23,252,241,254,254,251,203,254,226,252,34,254,192,252,24,254,81,252,168,0,168,251,125,254,95,251,155,255,97,251,216,255,83,252,196,254,250,251,254,252,236,251,143,253,199,251,230,253,56,251,213,254,224,250,76,254,83,251,105,253,113,251,95,255,64,251,78,253,43,251,193,252,104,250,48,253,133,250,19,254,126,252,28,253,102,252,223,252,178,251,110,254,213,249,60,252,219,251,130,253,11,251,98,250,37,250,90,252,34,250,129,252,194,249,204,253,69,249,51,253,162,253,171,253,114,251,195,251,167,250,44,254,102,248,43,250,210,248,71,252,116,248,93,252,37,250,68,255,157,249,91,254,79,250,174,254,88,250,234,255,106,248,90,254,42,248,7,255,16,254,142,255,138,248,13,253,247,250,174,0,85,250,147,255,30,254,255,254,59,251,4,254,175,249,151,0,98,249,208,0,114,253,107,0,141,249,29,0,139,251,23,1,65,251,50,1,52,251,6,254,38,253,81,255,44,251,155,255,55,252,39,2,154,252,22,1,201,252,59,1,205,253,120,1,229,251,228,0,5,254,24,1,169,253,25,1,10,253,253,0,207,254,123,1,13,253,122,255,157,253,148,2,200,252,24,2,207,252,134,2,99,254,49,0,171,254,177,0,59,254,14,2,30,254,77,2,185,255,83,1,111,253,8,1,12,255,39,1,19,255,59,1,125,254,57,2,6,254,247,255,135,254,14,0,96,255,149,2,40,255,40,0,204,254,210,255,95,0,214,0,14,255,167,0,170,255,192,0,200,255,27,0,180,255,31,0,36,0,53,1,150,255,74,255,143,255,74,0,71,254,234,255,23,0,139,0,81,0,245,255,44,0,15,0,169,255,119,255,138,255,49,255,98,255,198,255,16,1,164,255,100,255,71,254,8,0,120,255,128,0,35,255,101,0,38,255,40,0,59,255,180,255,56,254,9,0,67,254,33,0,89,254,226,0,60,0,73,0,34,255,156,0,113,254,24,1,194,254,245,0,171,254,166,0,13,254,83,1,66,255,71,1,37,255,69,1,119,255,167,255,172,253,100,0,141,253,144,0,91,253,231,1,28,0,252,0,121,254,214,0,215,255,26,1,228,255,99,0,226,254,75,1,49,0,203,1,124,254,53,2,143,254,180,1,28,0,80,1,247,255,141,1,89,255,106,2,34,0,84,2,239,255,49,2,116,255,43,1,79,0,10,2,125,0,203,0,2,0,244,0,32,1,255,0,211,0,175,0,82,0,84,2,187,0,5,2,108,0,125,1,255,0,109,1,41,1,241,1,96,1,71,1,174,255,25,0,210,0,115,1,245,0,5,1,3,0,33,2,193,1,140,0,38,1,44,0,39,1,212,0,91,1,244,0,238,1,75,1,16,2,201,0,51,1,93,1,155,1,101,2,28,1,102,2,157,1,208,1,66,1,112,2,141,1,97,0,200,0,96,255,128,1,149,0,106,1,239,1,13,2,13,1,73,2,33,0,235,1,135,255,177,1,171,1,99,2,242,1,4,2,171,0,187,1,241,1,154,2,184,1,19,1,54,2,63,2,146,0,127,2,155,0,158,2,223,255,173,0,212,0,184,2,90,255,89,2,65,255,183,2,23,254,247,1,175,0,230,2,214,0,220,1,116,1,59,4,66,2,18,2,74,2,9,3,169,1,106,3,59,1,73,3,118,1,80,3,91,255,53,2,35,0,223,3,217,255,38,4,73,1,200,2,18,3,72,3,133,2,27,3,149,2,164,2,59,2,150,3,120,2,55,4,161,2,49,3,62,1,132,1,106,3,244,3,52,2,80,3,112,3,108,2,45,2,223,1,159,2,197,1,180,2,212,1,72,3,130,2,76,3,133,2,250,1,172,1,129,3,55,2,69,3,131,1,194,3,243,1,179,2,49,2,171,3,158,3,15,3,40,1,22,3,12,1,4,4,18,2,106,3,73,1,36,2,143,0,163,2,35,1,247,1,66,0,17,4,103,1,18,3,97,0,37,3,33,0,69,3,214,1,255,1,49,0,68,4,71,1,150,4,67,1,3,0,242,0,104,3,218,1,177,2,173,1,49,5,166,2,18,4,108,2,85,4,152,2,65,1,193,0,121,3,182,3,129,4,106,3,125,3,123,2,109,3,94,3,180,3,145,3,13,5,153,2,40,5,127,2,229,3,25,3,122,5,6,4,152,4,244,3,86,4,191,3,130,5,157,3,123,5,147,3,31,2,94,3,92,4,198,4,67,3,166,4,67,3,166,4,191,3,124,4,123,4,96,5,20,5,169,4,135,5,207,4,55,5,61,5,234,2,68,4,175,6,3,5,109,5,49,4,54,5,30,6,129,4,195,5,109,6,113,4,33,7,196,4,32,4,102,5,241,5,194,6,96,6,9,6,84,6,6,6,87,3,60,6,97,3,131,6,181,2,117,3,180,6,239,5,143,4,16,5,161,8,224,6,160,7,213,5,228,7,202,5,254,5,74,7,158,6,216,7,30,6,236,2,225,6,57,3,38,1,112,5,60,4,10,8,109,2,35,5,109,1,7,5,198,0,4,4,232,1,128,5,249,0,147,1,246,3,25,6,68,1,107,1,109,6,20,4,193,0,111,1,242,7,67,7,5,255,67,2,238,2,226,3,13,255,30,0,45,5,111,3,228,255,87,255,112,2,149,3,59,254,159,0,186,0,90,5,154,253,6,0,25,2,136,1,162,255,221,254,13,3,229,0,128,255,214,254,245,0,235,1,67,253,120,253,204,3,21,3,11,254,128,253,178,0,255,0,147,254,122,254,1,255,61,1,66,252,218,254,65,255,228,0,249,252,65,254,157,0,19,255,111,253,48,253,105,254,92,0,139,255,157,253,78,1,26,255,89,253,196,251,112,255,195,254,123,252,163,252,30,253,152,254,171,255,41,253,166,255,237,252,100,0,234,255,121,254,249,254,200,255,183,255,175,254,14,253,5,0,67,255,62,253,144,253,89,0,168,254,121,255,167,251,159,254,19,255,84,253,145,251,237,254,178,251,243,254,77,251,152,0,145,0,46,253,48,251,49,0,80,0,32,251,248,252,8,255,135,1,36,253,221,253,213,1,218,0,1,255,160,252,69,0,110,1,90,255,27,254,80,253,191,0,68,251,84,251,86,255,87,255,228,250,161,249,65,1,214,1,117,250,37,251,192,255,16,1,175,250,8,255,236,1,53,2,47,253,159,253,195,0,229,1,195,253,123,255,171,1,202,0,85,255,138,255,199,0,63,2,2,0,225,255,182,2,243,2,170,250,217,255,40,2,45,2,23,254,15,1,168,2,25,2,13,0,59,254,87,3,186,3,123,255,204,255,175,255,226,2,111,251,125,2,31,4,35,4,161,255,164,2,235,4,57,4,233,1,49,1,63,254,186,3,234,253,228,3,55,252,98,3,222,251,35,4,242,250,106,2,120,250,105,2,54,254,86,5,97,255,29,7,250,252,240,253,242,255,86,4,78,251,123,252,252,252,177,1,24,251,25,251,13,252,210,254,166,253,183,253,9,253,174,249,8,253,243,249,184,252,127,248,208,252,229,253,23,249,69,247,29,255,220,255,14,248,217,248,197,247,154,251,89,246,232,248,66,250,252,0,115,245,97,254,197,253,45,254,229,5,18,6,132,8,183,7,22,9,228,7,191,248,111,249,191,248,37,249,248,247,130,251,170,247,138,249,173,249,181,251,88,249,149,251,191,250,184,249,177,250,154,249,198,250,243,250,211,250,15,251,128,249,143,249,49,250,173,252,190,250,216,248,123,250,116,247,254,250,87,253,7,249,143,249,58,252,198,251,97,251,116,249,226,251,207,251,138,251,122,251,73,251,24,253,6,251,27,252,90,252,153,250,97,252,120,250,14,252,231,250,241,252,69,252,231,251,124,252,31,252,207,252,31,253,201,252,52,252,91,251,30,253,186,251,30,253,126,251,240,252,223,252,214,252,238,252,132,252,248,253,24,252,206,252,124,253,59,252,191,253,142,252,227,253,74,253,97,253,107,252,173,253,126,253,122,253,153,253,68,252,147,253,99,252,253,253,41,253,29,254,209,252,27,254,184,252,190,253,72,254,55,253,190,253,187,254,111,253,98,253,126,254,198,253,71,254,102,253,254,253,237,252,120,254,239,253,246,253,59,254,25,254,89,254,152,253,183,253,151,253,99,255,106,253,244,254,88,253,164,254,190,254,189,254,136,253,68,254,208,254,82,254,180,254,54,254,235,254,44,254,109,253,231,252,193,254,132,253,29,255,214,253,139,254,165,254,178,254,46,255,56,254,64,255,238,253,14,255,40,255,58,255,146,254,142,254,174,254,95,255,103,254,20,253,149,255,132,254,218,254,125,253,33,255,103,253,22,255,27,253,115,255,16,254,126,255,2,254,117,255,185,254,84,255,207,254,206,254,188,253,92,255,249,254,250,254,84,255,189,255,110,254,31,0,146,254,246,255,76,254,170,255,241,253,71,0,135,254,234,255,159,253,244,255,90,253,189,255,193,254,63,0,65,255,35,0,75,255,217,255,14,255,126,0,89,255,116,255,224,253,155,0,215,254,174,0,215,254,38,0,248,255,117,0,132,254,197,0,60,254,240,0,246,253,223,0,153,255,110,0,69,255,87,0,101,255,169,0,209,255,157,0,26,0,173,255,156,255,128,0,80,0,209,0,194,255,6,0,7,0,22,0,5,0,62,1,236,255,248,0,211,255,56,255,193,255,156,0,187,255,250,0,73,255,113,1,130,255,143,255,180,255,114,255,134,255,192,255,2,255,225,255,35,0,79,255,185,255,249,255,171,0,93,0,27,0,108,0,212,0,182,254,47,255,133,255,186,255,233,254,95,0,160,255,20,0,68,255,195,255,198,254,87,0,212,254,178,255,158,254,122,255,11,0,122,0,116,255,122,0,237,254,152,0,219,254,140,0,174,255,138,0,191,254,145,255,32,254,100,255,153,254,76,0,2,255,216,255,133,253,160,255,246,253,79,0,5,254,8,0,244,254,47,1,229,253,68,0,66,254,61,0,246,253,50,1,111,0,189,0,77,254,122,0,133,254,166,0,197,253,114,254,136,253,182,255,21,253,161,255,57,254,194,0,72,252,83,0,226,252,192,0,13,253,192,0,243,252,94,255,149,253,234,0,105,253,215,254,24,254,147,255,60,252,124,255,186,252,188,255,181,252,58,0,168,251,170,255,219,252,213,254,80,252,3,255,246,252,206,255,59,252,219,253,160,254,158,255,32,252,169,254,163,251,197,254,163,251,205,254,125,251,138,254,131,253,26,255,114,251,213,255,237,250,156,255,99,252,119,254,6,251,168,253,79,253,126,255,57,250,200,254,215,250,2,255,72,250,70,254,244,250,155,253,19,251,9,254,35,250,144,254,214,250,26,0,104,250,190,255,49,249,95,255,148,249,45,254,32,249,220,253,143,250,200,253,236,249,153,252,41,250,246,251,149,250,197,253,131,248,240,253,9,249,133,255,151,248,25,255,250,247,189,254,252,247,118,252,72,248,201,253,131,248,148,253,1,248,35,252,203,251,142,254,17,248,64,253,205,246,19,253,76,245,191,251,139,248,159,0,36,248,248,0,142,253,133,255,221,246,62,252,99,253,104,254,157,250,106,251,60,254,148,254,236,251,33,253,124,255,183,0,172,249,16,253,221,253,205,254,247,252,19,251,158,255,41,0,144,252,189,251,255,254,97,0,190,249,215,248,31,0,230,255,124,253,207,253,76,255,222,253,127,254,185,251,102,254,222,252,98,254,197,252,55,254,54,252,22,254,171,251,41,255,108,252,112,255,87,252,19,254,11,251,251,253,29,250,181,0,101,0,180,254,135,252,188,252,87,252,209,253,83,254,139,253,221,253,73,255,175,254,223,253,174,255,6,255,226,254,5,0,124,255,164,254,4,255,219,254,40,254,98,255,100,0,227,255,197,0,20,255,88,254,163,252,43,255,116,255,249,255,85,254,69,254,187,0,159,255,84,253,32,253,219,254,2,1,144,254,104,255,106,255,136,1,159,253,175,0,114,255,43,1,118,255,152,0,137,255,73,1,26,254,204,255,37,1,198,0,73,255,117,0,175,0,75,1,198,255,238,254,231,0,44,1,224,254,74,1,207,254,116,1,145,255,153,1,247,255,167,1,83,0,0,1,67,0,111,1,237,255,248,0,91,0,113,0,221,255,150,1,65,255,154,0,238,0,40,1,5,0,197,0,141,0,221,0,57,1,198,0,211,0,165,1,244,0,78,1,88,0,170,1,13,255,198,1,202,0,40,2,251,255,147,1,35,1,185,0,219,0,45,1,251,0,138,0,128,0,69,0,197,0,32,1,116,255,195,255,188,0,105,1,197,0,86,2,186,1,17,1,34,1,143,0,216,1,226,1,157,0,114,1,159,1,65,1,116,1,129,1,146,1,40,2,155,0,24,0,38,2,7,1,245,255,21,0,104,1,227,0,147,0,2,255,168,1,97,0,110,1,243,255,119,1,141,0,193,1,232,0,140,1,251,1,218,1,16,1,189,2,68,1,106,1,209,255,75,2,148,0,31,2,69,0,144,1,205,255,49,2,59,0,220,0,246,255,96,1,147,0,206,0,211,0,141,2,185,0,51,2,41,1,53,2,28,1,82,2,121,0,254,2,192,0,142,1,118,0,130,2,178,1,233,0,8,1,225,1,211,1,129,0,91,255,187,2,239,0,90,0,26,0,86,1,218,1,201,255,27,0,132,1,94,0,84,255,0,0,213,2,123,1,196,255,81,1,114,1,209,1,95,0,63,1,38,3,83,2,78,0,4,1,241,1,83,3,210,0,48,2,202,1,62,2,48,254,202,0,241,1,113,2,54,255,152,0,48,0,200,2,236,255,54,2,100,0,203,2,199,1,212,1,155,1,93,2,63,1,134,2,195,0,103,2,145,1,26,2,168,2,227,2,201,0,155,2,178,1,186,3,198,1,169,1,134,2,235,1,94,2,169,2,160,1,252,1,241,1,54,3,170,1,47,3,148,2,135,2,116,2,204,2,185,2,210,1,106,2,201,1,173,2,204,1,109,1,53,1,209,2,55,2,68,3,89,2,97,2,44,1,57,3,203,1,175,3,175,2,169,2,21,2,147,3,86,2,79,2,243,0,108,3,195,1,106,3,164,1,18,3,61,1,220,2,220,0,154,3,61,1,84,4,111,1,19,2,210,1,4,4,137,2,29,4,103,2,10,4,41,2,61,3,90,2,253,3,31,3,159,3,35,3,110,3,251,2,31,3,240,1,93,5,5,3,73,2,2,3,35,3,162,3,75,4,25,3,198,4,94,3,185,4,127,3,1,4,215,2,4,3,77,3,148,4,91,4,99,3,253,3,62,3,245,3,73,3,142,3,250,1,191,2,215,4,53,4,108,2,51,3,172,4,59,4,131,4,57,4,118,4,139,3,11,6,97,4,29,5,136,2,63,5,100,2,204,5,220,3,199,5,169,3,217,3,48,5,187,3,61,5,173,1,142,3,73,3,58,5,52,2,155,4,156,1,132,4,147,5,40,5,154,5,50,5,128,2,248,2,190,6,130,5,190,0,43,2,49,4,237,3,170,1,1,1,71,3,212,3,235,0,231,0,240,5,143,4,109,0,37,1,246,3,33,6,49,1,142,0,124,4,27,2,221,254,148,255,189,4,204,3,22,0,40,255,155,2,60,3,30,254,182,1,197,1,151,5,187,253,90,254,21,3,131,1,154,254,58,254,174,0,12,3,220,255,140,254,134,1,122,255,139,253,160,0,206,254,239,2,22,251,181,254,177,0,10,2,8,255,62,2,5,255,127,2,237,253,151,1,172,253,138,1,93,254,21,3,151,253,33,3,38,252,143,1,167,252,215,2,249,255,6,2,65,253,54,1,137,251,232,255,22,252,31,1,64,252,107,1,237,250,56,1,2,250,245,0,235,249,49,1,28,0,153,0,165,252,81,255,223,255,76,1,138,250,102,255,212,0,154,1,175,253,59,255,188,251,64,253,120,252,191,255,26,1,111,1,106,252,82,253,89,1,93,0,254,254,155,254,184,2,132,2,75,253,228,255,192,1,237,1,239,254,193,0,15,2,34,2,13,255,255,253,128,1,120,255,17,1,159,254,0,2,114,255,25,2,58,255,173,3,238,2,83,0,248,0,66,2,93,3,200,255,80,2,74,3,44,0,124,3,24,0,33,0,122,3,240,255,214,3,63,3,118,5,255,5,106,7,180,6,96,5,156,7,185,5,22,252,95,252,184,251,77,251,127,253,93,252,164,253,63,252,245,252,95,253,189,252,236,252,96,254,104,253,54,254,2,253,116,253,247,253,106,253,17,254,1,252,3,254,1,252,84,254,68,254,216,253,144,254,63,254,33,254,45,255,226,251,121,252,196,254,7,255,199,253,177,253,199,253,237,254,227,253,65,255,52,253,68,255,182,252,248,254,179,254,8,255,194,254,28,255,237,254,1,0,201,253,28,255,141,255,35,255,18,255,138,254,59,255,5,254,34,255,189,253,254,254,80,254,195,255,12,255,167,254,2,0,174,254,39,0,41,255,87,255,198,255,0,0,200,255,250,255,53,255,125,255,1,0,70,255,251,255,45,255,6,0,132,254,11,0,94,254,140,255,131,0,122,255,113,0,89,0,252,255,71,0,254,255,237,255,64,255,6,1,24,0,189,0,151,0,123,255,147,255,186,0,103,255,166,0,37,255,37,0,139,0,193,0,171,0,81,1,124,0,158,0,195,255,141,0,226,0,243,255,190,0,231,0,34,0,98,1,109,0,60,1,201,0,244,0,164,0,74,1,171,255,134,1,172,255,254,0,71,1,1,1,79,1,235,1,147,0,220,1,105,0,54,0,77,0,181,1,114,1,165,1,58,1,193,1,86,1,73,1,126,0,161,2,36,1,59,2,132,1,243,0,193,0,141,2,64,1,109,2,24,1,194,0,124,1,5,2,69,2,45,0,67,1,111,0,166,1,233,1,139,1,222,2,22,2,110,2,34,2,230,1,246,1,62,1,60,2,189,0,38,2,129,1,166,1,99,255,153,0,131,255,126,1,59,255,130,1,249,254,78,1,228,0,185,2,68,255,1,0,51,0,41,1,5,254,213,0,136,254,141,1,232,255,255,0,221,253,89,0,10,254,162,255,131,1,179,0,148,253,68,0,84,253,112,0,126,253,162,254,252,254,172,0,74,254,188,254,8,1,136,2,60,252,252,255,159,251,7,0,122,255,134,0,147,251,206,254,143,0,96,0,92,254,15,254,59,251,162,254,9,250,83,253,95,255,72,0,105,3,179,2,220,2,27,1,153,3,97,1,78,1,219,1,71,4,53,3,96,3,12,2,75,3,241,1,202,2,199,2,20,3,238,2,52,4,202,2,180,4,241,2,65,2,150,2,124,245,170,192,38,3,44,7,95,251,33,228,37,12,28,4,40,248,202,208,85,16,107,5,192,249,99,218,69,9,145,5,232,249,78,219,176,12,193,7,210,251,214,230,35,7,16,9,184,252,64,236,173,3,242,12,199,254,163,248,47,9,161,11,41,254,234,244,32,14,116,9,247,252,183,237,123,13,24,12,98,254,70,246,139,11,205,16,72,0,178,1,56,7,148,17,139,0,68,3,44,15,40,21,157,1,180,9,163,4,42,28,67,3,166,19,11,12,40,35,139,4,90,27,216,28,115,3,37,247,177,202,74,23,226,5,58,250,60,221,35,20,86,8,61,252,88,233,8,31,217,7,228,251,65,231,107,25,202,8,139,252,49,235,246,29,192,10,180,253,47,242,64,23,200,11,60,254,92,245,34,19,180,14,131,255,17,253,77,27,4,14,60,255,103,251,238,31,138,15,213,255,252,254,176,23,52,17,107,0,133,2,29,30,223,19,64,1,136,7,147,21,133,23,57,2,98,13,89,30,214,27,50,3,62,19,172,23,2,31,209,3,253,22,218,21,223,44,243,5,212,35,85,41,76,5,159,249,153,217,89,35,61,6,145,250,68,223,66,38,243,7,247,251,180,231,242,34,111,9,244,252,164,237,56,40,24,10,87,253,253,239,191,36,174,10,171,253,245,241,252,33,146,12,156,254,160,247,29,38,67,13,235,254,123,249,193,39,52,15,181,255,58,254,210,35,176,17,148,0,123,3,168,39,140,19,40,1,245,6,154,35,103,22,241,1,177,11,4,41,122,24,116,2,198,14,126,39,207,29,151,3,158,21,140,34,23,34,93,4,72,26,252,34,208,48,112,6,193,38,124,50,208,3,185,247,47,206,171,44,219,6,28,251,141,226,106,47,24,9,189,252,96,236,124,44,64,9,214,252,248,236,204,41,248,11,83,254,236,245,44,48,45,11,238,253,136,243,202,45,255,12,205,254,200,248,6,44,116,14,106,255,120,252,109,42,61,17,110,0,151,2,50,47,181,17,150,0,134,3,19,44,85,20,98,1,84,8,184,46,161,24,125,2,253,14,159,43,110,29,132,3,44,21,96,47,137,32,25,4,168,24,217,42,25,42,149,5,156,33,60,40,224,67,87,8,53,50,75,54,145,6,220,250,15,225,36,49,253,7,254,251,221,231,209,51,135,9,2,253,254,237,209,54,173,11,47,254,14,245,140,52,26,12,99,254,78,246,108,48,74,14,89,255,18,252,198,52,196,14,137,255,55,253,80,50,176,16,62,0,118,1,221,52,253,18,253,0,243,5,123,49,81,21,168,1,248,9,30,54,218,23,78,2,223,13,231,50,83,25,166,2,244,15,245,52,41,30,169,3,7,22,157,50,95,36,189,4,136,28,146,53,31,45,252,5,5,36,47,49,102,59,146,7,147,45,9,59,4,6,91,250,4,222,224,58,29,9,192,252,113,236,191,56,207,9,45,253,0,239,100,57,127,12,147,254,107,247,22,60,232,13,49,255,33,251,53,55,120,15,206,255,212,254,254,58,140,16,50,0,42,1,252,55,216,18,242,0,174,5,254,57,75,21,166,1,238,9,202,59,195,23,72,2,190,13,249,55,232,26,0,3,15,18,212,58,9,30,162,3,226,21,70,56,210,36,207,4,245,28,27,60,13,38,0,5,26,30,232,57,191,55,52,7,94,43,32,53,107,97,109,10,195,62,12,64,177,7,198,251,139,230,177,65,16,11,223,253,45,243,97,61,27,11,229,253,80,243,232,62,8,13,209,254,223,248,0,64,123,15,207,255,218,254,44,66,227,17,165,0,224,3,95,61,247,17,171,0,6,4,94,63,72,21,165,1,233,9,192,65,238,24,143,2,105,15,129,61,229,27,53,3,80,19,198,63,45,29,120,3,223,20,227,64,176,33,76,4,222,25,132,66,178,40,99,5,111,32,33,62,41,46,29,6,207,36,238,65,98,57,95,7,96,44,131,64,134,81,102,9,147,56,222,70,35,8,25,252,131,232,201,75,106,12,137,254,47,247,100,68,98,13,248,254,203,249,86,78,187,15,231,255,105,255,149,70,153,16,54,0,70,1,8,74,202,19,58,1,98,7,47,69,26,21,153,1,157,9,123,77,48,24,98,2,92,14,30,70,102,27,27,3,176,18,70,83,197,30,198,3,184,22,246,69,73,36,186,4,115,28,200,74,74,36,186,4,116,28,37,80,117,44,230,5,129,35,155,70,149,56,74,7,226,43,31,78,218,69,129,8,52,51,154,73,252,127,0,12,62,72,61,42,81,112,63,11,181,67,0,80,225,10,198,253,153,242,153,73,194,25,191,2,139,16,81,24,245,28,108,3,156,20,51,67,204,40,103,5,133,32,122,84,245,4,61,249,74,215,143,82,71,17,113,0,171,2,40,44,20,6,106,250,95,222,61,74,20,50,150,6,164,39,215,67,194,9,37,253,210,238,194,69,225,18,244,0,192,5,10,39,194,9,37,253,210,238,122,68,184,30,196,3,170,22,174,55,92,7,133,251,5,229,20,62,81,12,125,254,233,246,61,26,10,7,67,251,121,227,10,71,225,78,53,9,109,55,102,70,215,11,67,254,138,245,71,65,225,22,16,2,109,12,143,34,174,15,226,255,76,255,20,62,10,35,134,4,60,27,102,70,112,5,198,249,129,218,71,65,0,16,0,0,0,0,0,32,143,2,108,245,79,192,133,59,102,54,16,7,132,42,174,55,40,12,106,254,116,246,10,55,61,18,193,0,141,4,30,21,143,10,154,253,143,241,122,52,153,25,182,2,84,16,163,48,133,3,67,247,100,203,163,48,102,10,131,253,7,241,184,14,143,2,108,245,79,192,153,57,215,91,22,10,183,60,225,74,153,9,13,253,62,238,184,78,215,19,62,1,121,7,225,26,0,16,0,0,0,0,0,80,112,33,65,4,156,25,204,76,225,2,26,246,105,196,61,74,163,16,58,0,91,1,184,30,40,8,29,252,151,232,204,44,0,48,87,6,43,38,20,62,194,5,26,250,126,220,112,61,20,18,180,0,62,4,215,35,153,5,240,249,131,219,184,62,92,27,25,3,164,18,235,57,225,2,26,246,105,196,225,58,204,8,140,252,55,235,215,19,204,4,12,249,38,214,215,51,174,67,83,8,27,50,163,64,30,9,193,252,118,236,225,58,184,22,6,2,46,12,92,15,102,14,100,255,86,252,174,55,153,33,72,4,198,25,235,65,10,3,106,246,74,198,225,58,225,14,149,255,122,253,174,23,102,2,12,245,17,190,122,36,40,36,180,4,83,28,215,51,225,6,33,251,172,226,215,51,194,13,33,255,193,250,153,9,174,7,196,251,127,230,204,44,153,21,187,1,108,10,245,40,225,2,26,246,105,196,112,45,122,12,145,254,92,247,194,5,10,3,106,246,74,198,0,64,248,65,226,67,190,69,142,71,82,73,12,75,188,76,98,78,0,80,150,81,35,83,170,84,42,86,163,87,22,89,130,90,234,91,76,93,168,94,0,96,83,97,161,98,236,99,49,101,115,102,177,103,235,104,34,106,85,107,132,108,177,109,218,110,0,112,35,113,67,114,97,115,123,116,147,117,169,118,188,119,204,120,218,121,230,122,239,123,247,124,252,125,255,126,255,127,255,127,61,10,63,10,69,10,78,10,91,10,108,10,129,10,153,10,181,10,212,10,248,10,31,11,74,11,120,11,170,11,224,11,25,12,86,12,151,12,219,12,35,13,110,13,189,13,15,14,101,14,190,14,27,15,123,15,223,15,70,16,176,16,30,17,143,17,3,18,123,18,245,18,115,19,244,19,120,20,0,21,138,21,23,22,168,22,59,23,209,23,106,24,6,25,165,25,70,26,234,26,145,27,59,28,231,28,149,29,70,30,250,30,176,31,104,32,35,33,224,33,159,34,97,35,36,36,234,36,178,37,124,38,71,39,21,40,228,40,181,41,136,42,93,43,51,44,11,45,228,45,191,46,155,47,121,48,88,49,56,50,26,51,252,51,224,52,196,53,170,54,145,55,120,56,96,57,73,58,51,59,29,60,8,61,243,61,223,62,203,63,184,64,165,65,146,66,127,67,108,68,90,69,71,70,52,71,33,72,14,73,251,73,231,74,211,75,191,76,170,77,149,78,126,79,104,80,80,81,56,82,31,83,5,84,234,84,207,85,178,86,148,87,116,88,84,89,50,90,15,91,235,91,197,92,157,93,117,94,74,95,30,96,240,96,192,97,143,98,91,99,38,100,239,100,181,101,122,102,60,103,253,103,187,104,119,105,48,106,232,106,156,107,79,108,255,108,172,109,87,110,255,110,165,111,71,112,231,112,133,113,31,114,183,114,75,115,221,115,108,116,248,116,129,117,6,118,137,118,8,119,133,119,254,119,116,120,230,120,86,121,194,121,42,122,144,122,242,122,80,123,171,123,3,124,87,124,167,124,244,124,62,125,132,125,198,125,5,126,64,126,120,126,172,126,220,126,9,127,49,127,87,127,120,127,150,127,176,127,199,127,217,127,232,127,243,127,251,127,255,127,255,127,229,127,153,127,25,127,103,126,129,125],"i8",R2,h1.GLOBAL_BASE+20480),I0([106,124,33,123,167,121,252,119,34,118,24,116,223,113,122,111,231,108,41,106,65,103,47,100,245,96,149,93,15,90,101,86,153,82,171,78,158,74,116,70,45,66,204,61,82,57,193,52,27,48,98,43,151,38,189,33,213,28,226,23,230,18,226,13,216,8,203,3,61,10,64,10,73,10,88,10,108,10,135,10,167,10,205,10,249,10,43,11,99,11,160,11,227,11,44,12,122,12,207,12,40,13,136,13,237,13,87,14,199,14,60,15,183,15,55,16,189,16,71,17,215,17,108,18,6,19,165,19,73,20,242,20,159,21,82,22,9,23,196,23,133,24,73,25,18,26,224,26,177,27,135,28,97,29,62,30,32,31,5,32,238,32,219,33,203,34,191,35,182,36,176,37,174,38,174,39,177,40,184,41,193,42,204,43,218,44,235,45,254,46,19,48,42,49,67,50,94,51,123,52,154,53,186,54,219,55,254,56,34,58,71,59,109,60,148,61,188,62,228,63,13,65,54,66,96,67,138,68,180,69,221,70,7,72,48,73,89,74,130,75,169,76,208,77,246,78,27,80,63,81,98,82,132,83,164,84,194,85,223,86,250,87,19,89,43,90,64,91,83,92,99,93,113,94,125,95,134,96,140,97,143,98,144,99,141,100,135,101,126,102,114,103,98,104,79,105,56,106,30,107,255,107,221,108,183,109,140,110,94,111,43,112,244,112,185,113,121,114,53,115,236,115,158,116,76,117,245,117,153,118,55,119,209,119,102,120,246,120,129,121,6,122,134,122,1,123,118,123,230,123,81,124,182,124,21,125,111,125,195,125,17,126,90,126,157,126,219,126,18,127,68,127,112,127,150,127,183,127,209,127,230,127,244,127,253,127,255,127,255,127,244,127,208,127,149,127,66,127,215,126,85,126,188,125,12,125,69,124,104,123,117,122,108,121,78,120,28,119,213,117,122,116,13,115,140,113,250,111,87,110,162,108,222,106,11,105,40,103,57,101,60,99,51,97,30,95,255,92,215,90,165,88,108,86,44,84,229,81,154,79,74,77,247,74,161,72,74,70,243,67,156,65,71,63,244,60,164,58,88,56,18,54,209,51,152,49,103,47,62,45,31,43,11,41,2,39,5,37,21,35,51,33,95,31,155,29,231,27,67,26,177,24,49,23,195,21,105,20,34,19,239,17,209,16,201,15,214,14,249,13,50,13,130,12,232,11,102,11,252,10,169,10,109,10,73,10,61,10,61,10,63,10,67,10,74,10,84,10,96,10,111,10,129,10,150,10,174,10,200,10,229,10,5,11,39,11,77,11,117,11,159,11,205,11,253,11,48,12,101,12,157,12,216,12,22,13,86,13,153,13,222,13,38,14,113,14,190,14,13,15,96,15,181,15,12,16,102,16,194,16,33,17,130,17,230,17,76,18,180,18,31,19,140,19,252,19,110,20,226,20,88,21,209,21,76,22,201,22,72,23,202,23,77,24,211,24,91,25,229,25,113,26,254,26,142,27,32,28,180,28,74,29,225,29,123,30,22,31,179,31,82,32,242,32,149,33,57,34,222,34,133,35,46,36,216,36,132,37,50,38,224,38,145,39,66,40,245,40,169,41,95,42,22,43,206,43,135,44,66,45,253,45,186,46,120,47,54,48,246,48,183,49,120,50,59,51,254,51,194,52,135,53,77,54,19,55,218,55,161,56,106,57,50,58,252,58,197,59,144,60,90,61,37,62,240,62,188,63,136,64,84,65,32,66,236,66,185,67,133,68,82,69,30,70,235,70,183,71,132,72,80,73,28,74,231,74,179,75,126,76,73,77,19,78,221,78,166,79,111,80,56,81,0,82,199,82,142,83,84,84,25,85,221,85,161,86,100,87,38,88,231,88,167,89,103,90,37,91,226,91,158,92,89,93,19,94,204,94,131,95,57,96,238,96,162,97,84,98,5,99,181,99,99,100,15,101,186,101,100,102,12,103,178,103,87,104,250,104,155,105,59,106,217,106,117,107,16,108,168,108,63,109,211,109,102,110,247,110,134,111,19,112,158,112,39,113,174,113,50,114,181,114,53,115,179,115,47,116,169,116,33,117,150,117,9,118,122,118,232,118,84,119,190,119,37,120,138,120,236,120,76,121,170,121,5,122,94,122,180,122,7,123,88,123,167,123,242,123,60,124,130,124,198,124,8,125,71,125,131,125,188,125,243,125,39,126,89,126,136,126,180,126,221,126,4,127,40,127,73,127,103,127,131,127,156,127,178,127,197,127,214,127,228,127,239,127,247,127,253,127,255,127,255,127,97,125,160,117,15,105,48,88,181,67,116,44,98,19,68,101,99,111,100,101,114,0,101,110,99,111,100,101,114,0],"i8",R2,h1.GLOBAL_BASE+30720);var h6=h1.alignMemory(I0(12,"i8",_2),8);function J2(p){var v=J2;v.called||(Q0=x4(Q0),v.called=!0,k1(h1.dynamicAlloc),v.alloc=h1.dynamicAlloc,h1.dynamicAlloc=function(){w2("cannot dynamically allocate, sbrk now has control")});var y=Q0;return p==0||v.alloc(p)?y:4294967295}function x2(p){return L.___errno_location&&(g0[L.___errno_location()>>2]=p),p}k1(h6%8==0);var $={EPERM:1,ENOENT:2,ESRCH:3,EINTR:4,EIO:5,ENXIO:6,E2BIG:7,ENOEXEC:8,EBADF:9,ECHILD:10,EAGAIN:11,EWOULDBLOCK:11,ENOMEM:12,EACCES:13,EFAULT:14,ENOTBLK:15,EBUSY:16,EEXIST:17,EXDEV:18,ENODEV:19,ENOTDIR:20,EISDIR:21,EINVAL:22,ENFILE:23,EMFILE:24,ENOTTY:25,ETXTBSY:26,EFBIG:27,ENOSPC:28,ESPIPE:29,EROFS:30,EMLINK:31,EPIPE:32,EDOM:33,ERANGE:34,ENOMSG:42,EIDRM:43,ECHRNG:44,EL2NSYNC:45,EL3HLT:46,EL3RST:47,ELNRNG:48,EUNATCH:49,ENOCSI:50,EL2HLT:51,EDEADLK:35,ENOLCK:37,EBADE:52,EBADR:53,EXFULL:54,ENOANO:55,EBADRQC:56,EBADSLT:57,EDEADLOCK:35,EBFONT:59,ENOSTR:60,ENODATA:61,ETIME:62,ENOSR:63,ENONET:64,ENOPKG:65,EREMOTE:66,ENOLINK:67,EADV:68,ESRMNT:69,ECOMM:70,EPROTO:71,EMULTIHOP:72,EDOTDOT:73,EBADMSG:74,ENOTUNIQ:76,EBADFD:77,EREMCHG:78,ELIBACC:79,ELIBBAD:80,ELIBSCN:81,ELIBMAX:82,ELIBEXEC:83,ENOSYS:38,ENOTEMPTY:39,ENAMETOOLONG:36,ELOOP:40,EOPNOTSUPP:95,EPFNOSUPPORT:96,ECONNRESET:104,ENOBUFS:105,EAFNOSUPPORT:97,EPROTOTYPE:91,ENOTSOCK:88,ENOPROTOOPT:92,ESHUTDOWN:108,ECONNREFUSED:111,EADDRINUSE:98,ECONNABORTED:103,ENETUNREACH:101,ENETDOWN:100,ETIMEDOUT:110,EHOSTDOWN:112,EHOSTUNREACH:113,EINPROGRESS:115,EALREADY:114,EDESTADDRREQ:89,EMSGSIZE:90,EPROTONOSUPPORT:93,ESOCKTNOSUPPORT:94,EADDRNOTAVAIL:99,ENETRESET:102,EISCONN:106,ENOTCONN:107,ETOOMANYREFS:109,EUSERS:87,EDQUOT:122,ESTALE:116,ENOTSUP:95,ENOMEDIUM:123,EILSEQ:84,EOVERFLOW:75,ECANCELED:125,ENOTRECOVERABLE:131,EOWNERDEAD:130,ESTRPIPE:86};function $4(p){switch(p){case 30:return o6;case 85:return $0/o6;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 79:return 0;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return typeof navigator=="object"&&navigator.hardwareConcurrency||1}return x2($.EINVAL),-1}function J4(p,v,y){return F0.set(F0.subarray(v,v+y),p),p}function e3(){L.abort()}L._memcpy=s3,L._memmove=o3,L._memset=n3;var i3={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"},h2={ttys:[],init:function(){},shutdown:function(){},register:function(p,v){h2.ttys[p]={input:[],output:[],ops:v},P.registerDevice(p,h2.stream_ops)},stream_ops:{open:function(p){var v=h2.ttys[p.node.rdev];if(!v)throw new P.ErrnoError($.ENODEV);p.tty=v,p.seekable=!1},close:function(p){p.tty.ops.flush(p.tty)},flush:function(p){p.tty.ops.flush(p.tty)},read:function(p,v,y,_,e){if(!p.tty||!p.tty.ops.get_char)throw new P.ErrnoError($.ENXIO);for(var a=0,x=0;x<_;x++){var D;try{D=p.tty.ops.get_char(p.tty)}catch{throw new P.ErrnoError($.EIO)}if(D===void 0&&a===0)throw new P.ErrnoError($.EAGAIN);if(D==null)break;a++,v[y+x]=D}return a&&(p.node.timestamp=Date.now()),a},write:function(p,v,y,_,e){if(!p.tty||!p.tty.ops.put_char)throw new P.ErrnoError($.ENXIO);for(var a=0;a<_;a++)try{p.tty.ops.put_char(p.tty,v[y+a])}catch{throw new P.ErrnoError($.EIO)}return _&&(p.node.timestamp=Date.now()),a}},default_tty_ops:{get_char:function(p){if(!p.input.length){var v=null;if(typeof window<"u"&&typeof window.prompt=="function"?(v=window.prompt("Input: "))!==null&&(v+=`
`):typeof readline=="function"&&(v=readline())!==null&&(v+=`
`),!v)return null;p.input=d2(v,!0)}return p.input.shift()},put_char:function(p,v){v===null||v===10?(L.print(l2(p.output,0)),p.output=[]):v!=0&&p.output.push(v)},flush:function(p){p.output&&p.output.length>0&&(L.print(l2(p.output,0)),p.output=[])}},default_tty1_ops:{put_char:function(p,v){v===null||v===10?(L.printErr(l2(p.output,0)),p.output=[]):v!=0&&p.output.push(v)},flush:function(p){p.output&&p.output.length>0&&(L.printErr(l2(p.output,0)),p.output=[])}}},g1={ops_table:null,mount:function(p){return g1.createNode(null,"/",16895,0)},createNode:function(p,v,y,_){if(P.isBlkdev(y)||P.isFIFO(y))throw new P.ErrnoError($.EPERM);g1.ops_table||(g1.ops_table={dir:{node:{getattr:g1.node_ops.getattr,setattr:g1.node_ops.setattr,lookup:g1.node_ops.lookup,mknod:g1.node_ops.mknod,rename:g1.node_ops.rename,unlink:g1.node_ops.unlink,rmdir:g1.node_ops.rmdir,readdir:g1.node_ops.readdir,symlink:g1.node_ops.symlink},stream:{llseek:g1.stream_ops.llseek}},file:{node:{getattr:g1.node_ops.getattr,setattr:g1.node_ops.setattr},stream:{llseek:g1.stream_ops.llseek,read:g1.stream_ops.read,write:g1.stream_ops.write,allocate:g1.stream_ops.allocate,mmap:g1.stream_ops.mmap,msync:g1.stream_ops.msync}},link:{node:{getattr:g1.node_ops.getattr,setattr:g1.node_ops.setattr,readlink:g1.node_ops.readlink},stream:{}},chrdev:{node:{getattr:g1.node_ops.getattr,setattr:g1.node_ops.setattr},stream:P.chrdev_stream_ops}});var e=P.createNode(p,v,y,_);return P.isDir(e.mode)?(e.node_ops=g1.ops_table.dir.node,e.stream_ops=g1.ops_table.dir.stream,e.contents={}):P.isFile(e.mode)?(e.node_ops=g1.ops_table.file.node,e.stream_ops=g1.ops_table.file.stream,e.usedBytes=0,e.contents=null):P.isLink(e.mode)?(e.node_ops=g1.ops_table.link.node,e.stream_ops=g1.ops_table.link.stream):P.isChrdev(e.mode)&&(e.node_ops=g1.ops_table.chrdev.node,e.stream_ops=g1.ops_table.chrdev.stream),e.timestamp=Date.now(),p&&(p.contents[v]=e),e},getFileDataAsRegularArray:function(p){if(p.contents&&p.contents.subarray){for(var v=[],y=0;y<p.usedBytes;++y)v.push(p.contents[y]);return v}return p.contents},getFileDataAsTypedArray:function(p){return p.contents?p.contents.subarray?p.contents.subarray(0,p.usedBytes):new Uint8Array(p.contents):new Uint8Array},expandFileStorage:function(p,v){if(p.contents&&p.contents.subarray&&v>p.contents.length&&(p.contents=g1.getFileDataAsRegularArray(p),p.usedBytes=p.contents.length),!p.contents||p.contents.subarray){var y=p.contents?p.contents.buffer.byteLength:0;if(y>=v)return;var _=1048576;v=Math.max(v,y*(y<_?2:1.125)|0),y!=0&&(v=Math.max(v,256));var e=p.contents;return p.contents=new Uint8Array(v),void(p.usedBytes>0&&p.contents.set(e.subarray(0,p.usedBytes),0))}for(!p.contents&&v>0&&(p.contents=[]);p.contents.length<v;)p.contents.push(0)},resizeFileStorage:function(p,v){if(p.usedBytes!=v){if(v==0)return p.contents=null,void(p.usedBytes=0);if(!p.contents||p.contents.subarray){var y=p.contents;return p.contents=new Uint8Array(new ArrayBuffer(v)),y&&p.contents.set(y.subarray(0,Math.min(v,p.usedBytes))),void(p.usedBytes=v)}if(p.contents||(p.contents=[]),p.contents.length>v)p.contents.length=v;else for(;p.contents.length<v;)p.contents.push(0);p.usedBytes=v}},node_ops:{getattr:function(p){var v={};return v.dev=P.isChrdev(p.mode)?p.id:1,v.ino=p.id,v.mode=p.mode,v.nlink=1,v.uid=0,v.gid=0,v.rdev=p.rdev,P.isDir(p.mode)?v.size=4096:P.isFile(p.mode)?v.size=p.usedBytes:P.isLink(p.mode)?v.size=p.link.length:v.size=0,v.atime=new Date(p.timestamp),v.mtime=new Date(p.timestamp),v.ctime=new Date(p.timestamp),v.blksize=4096,v.blocks=Math.ceil(v.size/v.blksize),v},setattr:function(p,v){v.mode!==void 0&&(p.mode=v.mode),v.timestamp!==void 0&&(p.timestamp=v.timestamp),v.size!==void 0&&g1.resizeFileStorage(p,v.size)},lookup:function(p,v){throw P.genericErrors[$.ENOENT]},mknod:function(p,v,y,_){return g1.createNode(p,v,y,_)},rename:function(p,v,y){if(P.isDir(p.mode)){var _;try{_=P.lookupNode(v,y)}catch{}if(_)for(var e in _.contents)throw new P.ErrnoError($.ENOTEMPTY)}delete p.parent.contents[p.name],p.name=y,v.contents[y]=p,p.parent=v},unlink:function(p,v){delete p.contents[v]},rmdir:function(p,v){var y=P.lookupNode(p,v);for(var _ in y.contents)throw new P.ErrnoError($.ENOTEMPTY);delete p.contents[v]},readdir:function(p){var v=[".",".."];for(var y in p.contents)p.contents.hasOwnProperty(y)&&v.push(y);return v},symlink:function(p,v,y){var _=g1.createNode(p,v,41471,0);return _.link=y,_},readlink:function(p){if(!P.isLink(p.mode))throw new P.ErrnoError($.EINVAL);return p.link}},stream_ops:{read:function(p,v,y,_,e){var a=p.node.contents;if(e>=p.node.usedBytes)return 0;var x=Math.min(p.node.usedBytes-e,_);if(k1(x>=0),x>8&&a.subarray)v.set(a.subarray(e,e+x),y);else for(var D=0;D<x;D++)v[y+D]=a[e+D];return x},write:function(p,v,y,_,e,a){if(!_)return 0;var x=p.node;if(x.timestamp=Date.now(),v.subarray&&(!x.contents||x.contents.subarray)){if(a)return x.contents=v.subarray(y,y+_),x.usedBytes=_,_;if(x.usedBytes===0&&e===0)return x.contents=new Uint8Array(v.subarray(y,y+_)),x.usedBytes=_,_;if(e+_<=x.usedBytes)return x.contents.set(v.subarray(y,y+_),e),_}if(g1.expandFileStorage(x,e+_),x.contents.subarray&&v.subarray)x.contents.set(v.subarray(y,y+_),e);else for(var D=0;D<_;D++)x.contents[e+D]=v[y+D];return x.usedBytes=Math.max(x.usedBytes,e+_),_},llseek:function(p,v,y){var _=v;if(y===1?_+=p.position:y===2&&P.isFile(p.node.mode)&&(_+=p.node.usedBytes),_<0)throw new P.ErrnoError($.EINVAL);return _},allocate:function(p,v,y){g1.expandFileStorage(p.node,v+y),p.node.usedBytes=Math.max(p.node.usedBytes,v+y)},mmap:function(p,v,y,_,e,a,x){if(!P.isFile(p.node.mode))throw new P.ErrnoError($.ENODEV);var D,G,J=p.node.contents;if(2&x||J.buffer!==v&&J.buffer!==v.buffer){if((e>0||e+_<p.node.usedBytes)&&(J=J.subarray?J.subarray(e,e+_):Array.prototype.slice.call(J,e,e+_)),G=!0,!(D=U2(_)))throw new P.ErrnoError($.ENOMEM);v.set(J,D)}else G=!1,D=J.byteOffset;return{ptr:D,allocated:G}},msync:function(p,v,y,_,e){if(!P.isFile(p.node.mode))throw new P.ErrnoError($.ENODEV);return 2&e||g1.stream_ops.write(p,v,0,_,y,!1),0}}},Q1={dbs:{},indexedDB:function(){if(typeof indexedDB<"u")return indexedDB;var p=null;return typeof window=="object"&&(p=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB),k1(p,"IDBFS used, but indexedDB not supported"),p},DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:function(p){return g1.mount.apply(null,arguments)},syncfs:function(p,v,y){Q1.getLocalSet(p,function(_,e){if(_)return y(_);Q1.getRemoteSet(p,function(a,x){if(a)return y(a);var D=v?x:e,G=v?e:x;Q1.reconcile(D,G,y)})})},getDB:function(p,v){var y,_=Q1.dbs[p];if(_)return v(null,_);try{y=Q1.indexedDB().open(p,Q1.DB_VERSION)}catch(e){return v(e)}y.onupgradeneeded=function(e){var a,x=e.target.result,D=e.target.transaction;(a=x.objectStoreNames.contains(Q1.DB_STORE_NAME)?D.objectStore(Q1.DB_STORE_NAME):x.createObjectStore(Q1.DB_STORE_NAME)).indexNames.contains("timestamp")||a.createIndex("timestamp","timestamp",{unique:!1})},y.onsuccess=function(){_=y.result,Q1.dbs[p]=_,v(null,_)},y.onerror=function(e){v(this.error),e.preventDefault()}},getLocalSet:function(p,v){var y={};function _(G){return G!=="."&&G!==".."}function e(G){return function(J){return P1.join2(G,J)}}for(var a=P.readdir(p.mountpoint).filter(_).map(e(p.mountpoint));a.length;){var x,D=a.pop();try{x=P.stat(D)}catch(G){return v(G)}P.isDir(x.mode)&&a.push.apply(a,P.readdir(D).filter(_).map(e(D))),y[D]={timestamp:x.mtime}}return v(null,{type:"local",entries:y})},getRemoteSet:function(p,v){var y={};Q1.getDB(p.mountpoint,function(_,e){if(_)return v(_);var a=e.transaction([Q1.DB_STORE_NAME],"readonly");a.onerror=function(x){v(this.error),x.preventDefault()},a.objectStore(Q1.DB_STORE_NAME).index("timestamp").openKeyCursor().onsuccess=function(x){var D=x.target.result;if(!D)return v(null,{type:"remote",db:e,entries:y});y[D.primaryKey]={timestamp:D.key},D.continue()}})},loadLocalEntry:function(p,v){var y,_;try{_=P.lookupPath(p).node,y=P.stat(p)}catch(e){return v(e)}return P.isDir(y.mode)?v(null,{timestamp:y.mtime,mode:y.mode}):P.isFile(y.mode)?(_.contents=g1.getFileDataAsTypedArray(_),v(null,{timestamp:y.mtime,mode:y.mode,contents:_.contents})):v(new Error("node type not supported"))},storeLocalEntry:function(p,v,y){try{if(P.isDir(v.mode))P.mkdir(p,v.mode);else{if(!P.isFile(v.mode))return y(new Error("node type not supported"));P.writeFile(p,v.contents,{encoding:"binary",canOwn:!0})}P.chmod(p,v.mode),P.utime(p,v.timestamp,v.timestamp)}catch(_){return y(_)}y(null)},removeLocalEntry:function(p,v){try{P.lookupPath(p);var y=P.stat(p);P.isDir(y.mode)?P.rmdir(p):P.isFile(y.mode)&&P.unlink(p)}catch(_){return v(_)}v(null)},loadRemoteEntry:function(p,v,y){var _=p.get(v);_.onsuccess=function(e){y(null,e.target.result)},_.onerror=function(e){y(this.error),e.preventDefault()}},storeRemoteEntry:function(p,v,y,_){var e=p.put(y,v);e.onsuccess=function(){_(null)},e.onerror=function(a){_(this.error),a.preventDefault()}},removeRemoteEntry:function(p,v,y){var _=p.delete(v);_.onsuccess=function(){y(null)},_.onerror=function(e){y(this.error),e.preventDefault()}},reconcile:function(p,v,y){var _=0,e=[];Object.keys(p.entries).forEach(function(Q){var C=p.entries[Q],e1=v.entries[Q];(!e1||C.timestamp>e1.timestamp)&&(e.push(Q),_++)});var a=[];if(Object.keys(v.entries).forEach(function(Q){v.entries[Q],p.entries[Q]||(a.push(Q),_++)}),!_)return y(null);var x=0,D=(p.type==="remote"?p.db:v.db).transaction([Q1.DB_STORE_NAME],"readwrite"),G=D.objectStore(Q1.DB_STORE_NAME);function J(Q){return Q?J.errored?void 0:(J.errored=!0,y(Q)):++x>=_?y(null):void 0}D.onerror=function(Q){J(this.error),Q.preventDefault()},e.sort().forEach(function(Q){v.type==="local"?Q1.loadRemoteEntry(G,Q,function(C,e1){if(C)return J(C);Q1.storeLocalEntry(Q,e1,J)}):Q1.loadLocalEntry(Q,function(C,e1){if(C)return J(C);Q1.storeRemoteEntry(G,Q,e1,J)})}),a.sort().reverse().forEach(function(Q){v.type==="local"?Q1.removeLocalEntry(Q,J):Q1.removeRemoteEntry(G,Q,J)})}},i0={DIR_MODE:16895,FILE_MODE:33279,reader:null,mount:function(p){k1(T0),i0.reader||(i0.reader=new FileReaderSync);var v=i0.createNode(null,"/",i0.DIR_MODE,0),y={};function _(a){for(var x=a.split("/"),D=v,G=0;G<x.length-1;G++){var J=x.slice(0,G+1).join("/");y[J]||(y[J]=i0.createNode(D,J,i0.DIR_MODE,0)),D=y[J]}return D}function e(a){var x=a.split("/");return x[x.length-1]}return Array.prototype.forEach.call(p.opts.files||[],function(a){i0.createNode(_(a.name),e(a.name),i0.FILE_MODE,0,a,a.lastModifiedDate)}),(p.opts.blobs||[]).forEach(function(a){i0.createNode(_(a.name),e(a.name),i0.FILE_MODE,0,a.data)}),(p.opts.packages||[]).forEach(function(a){a.metadata.files.forEach(function(x){var D=x.filename.substr(1);i0.createNode(_(D),e(D),i0.FILE_MODE,0,a.blob.slice(x.start,x.end))})}),v},createNode:function(p,v,y,_,e,a){var x=P.createNode(p,v,y);return x.mode=y,x.node_ops=i0.node_ops,x.stream_ops=i0.stream_ops,x.timestamp=(a||new Date).getTime(),k1(i0.FILE_MODE!==i0.DIR_MODE),y===i0.FILE_MODE?(x.size=e.size,x.contents=e):(x.size=4096,x.contents={}),p&&(p.contents[v]=x),x},node_ops:{getattr:function(p){return{dev:1,ino:void 0,mode:p.mode,nlink:1,uid:0,gid:0,rdev:void 0,size:p.size,atime:new Date(p.timestamp),mtime:new Date(p.timestamp),ctime:new Date(p.timestamp),blksize:4096,blocks:Math.ceil(p.size/4096)}},setattr:function(p,v){v.mode!==void 0&&(p.mode=v.mode),v.timestamp!==void 0&&(p.timestamp=v.timestamp)},lookup:function(p,v){throw new P.ErrnoError($.ENOENT)},mknod:function(p,v,y,_){throw new P.ErrnoError($.EPERM)},rename:function(p,v,y){throw new P.ErrnoError($.EPERM)},unlink:function(p,v){throw new P.ErrnoError($.EPERM)},rmdir:function(p,v){throw new P.ErrnoError($.EPERM)},readdir:function(p){throw new P.ErrnoError($.EPERM)},symlink:function(p,v,y){throw new P.ErrnoError($.EPERM)},readlink:function(p){throw new P.ErrnoError($.EPERM)}},stream_ops:{read:function(p,v,y,_,e){if(e>=p.node.size)return 0;var a=p.node.contents.slice(e,e+_),x=i0.reader.readAsArrayBuffer(a);return v.set(new Uint8Array(x),y),a.size},write:function(p,v,y,_,e){throw new P.ErrnoError($.EIO)},llseek:function(p,v,y){var _=v;if(y===1?_+=p.position:y===2&&P.isFile(p.node.mode)&&(_+=p.node.size),_<0)throw new P.ErrnoError($.EINVAL);return _}}};I0(1,"i32*",_2),I0(1,"i32*",_2),I0(1,"i32*",_2);var P={root:null,mounts:[],devices:[null],streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,handleFSError:function(p){if(!(p instanceof P.ErrnoError))throw p+" : "+d5();return x2(p.errno)},lookupPath:function(p,v){if(v=v||{},!(p=P1.resolve(P.cwd(),p)))return{path:"",node:null};var y={follow_mount:!0,recurse_count:0};for(var _ in y)v[_]===void 0&&(v[_]=y[_]);if(v.recurse_count>8)throw new P.ErrnoError($.ELOOP);for(var e=P1.normalizeArray(p.split("/").filter(function(C){return!!C}),!1),a=P.root,x="/",D=0;D<e.length;D++){var G=D===e.length-1;if(G&&v.parent)break;if(a=P.lookupNode(a,e[D]),x=P1.join2(x,e[D]),P.isMountpoint(a)&&(!G||G&&v.follow_mount)&&(a=a.mounted.root),!G||v.follow)for(var J=0;P.isLink(a.mode);){var Q=P.readlink(x);if(x=P1.resolve(P1.dirname(x),Q),a=P.lookupPath(x,{recurse_count:v.recurse_count}).node,J++>40)throw new P.ErrnoError($.ELOOP)}}return{path:x,node:a}},getPath:function(p){for(var v;;){if(P.isRoot(p)){var y=p.mount.mountpoint;return v?y[y.length-1]!=="/"?y+"/"+v:y+v:y}v=v?p.name+"/"+v:p.name,p=p.parent}},hashName:function(p,v){for(var y=0,_=0;_<v.length;_++)y=(y<<5)-y+v.charCodeAt(_)|0;return(p+y>>>0)%P.nameTable.length},hashAddNode:function(p){var v=P.hashName(p.parent.id,p.name);p.name_next=P.nameTable[v],P.nameTable[v]=p},hashRemoveNode:function(p){var v=P.hashName(p.parent.id,p.name);if(P.nameTable[v]===p)P.nameTable[v]=p.name_next;else for(var y=P.nameTable[v];y;){if(y.name_next===p){y.name_next=p.name_next;break}y=y.name_next}},lookupNode:function(p,v){var y=P.mayLookup(p);if(y)throw new P.ErrnoError(y,p);for(var _=P.hashName(p.id,v),e=P.nameTable[_];e;e=e.name_next){var a=e.name;if(e.parent.id===p.id&&a===v)return e}return P.lookup(p,v)},createNode:function(p,v,y,_){if(!P.FSNode){P.FSNode=function(D,G,J,Q){D||(D=this),this.parent=D,this.mount=D.mount,this.mounted=null,this.id=P.nextInode++,this.name=G,this.mode=J,this.node_ops={},this.stream_ops={},this.rdev=Q},P.FSNode.prototype={};var e=365,a=146;Object.defineProperties(P.FSNode.prototype,{read:{get:function(){return(this.mode&e)===e},set:function(D){D?this.mode|=e:this.mode&=~e}},write:{get:function(){return(this.mode&a)===a},set:function(D){D?this.mode|=a:this.mode&=~a}},isFolder:{get:function(){return P.isDir(this.mode)}},isDevice:{get:function(){return P.isChrdev(this.mode)}}})}var x=new P.FSNode(p,v,y,_);return P.hashAddNode(x),x},destroyNode:function(p){P.hashRemoveNode(p)},isRoot:function(p){return p===p.parent},isMountpoint:function(p){return!!p.mounted},isFile:function(p){return(61440&p)==32768},isDir:function(p){return(61440&p)==16384},isLink:function(p){return(61440&p)==40960},isChrdev:function(p){return(61440&p)==8192},isBlkdev:function(p){return(61440&p)==24576},isFIFO:function(p){return(61440&p)==4096},isSocket:function(p){return!(49152&~p)},flagModes:{r:0,rs:1052672,"r+":2,w:577,wx:705,xw:705,"w+":578,"wx+":706,"xw+":706,a:1089,ax:1217,xa:1217,"a+":1090,"ax+":1218,"xa+":1218},modeStringToFlags:function(p){var v=P.flagModes[p];if(v===void 0)throw new Error("Unknown file open mode: "+p);return v},flagsToPermissionString:function(p){var v=["r","w","rw"][3&p];return 512&p&&(v+="w"),v},nodePermissions:function(p,v){return P.ignorePermissions||(v.indexOf("r")===-1||292&p.mode)&&(v.indexOf("w")===-1||146&p.mode)&&(v.indexOf("x")===-1||73&p.mode)?0:$.EACCES},mayLookup:function(p){var v=P.nodePermissions(p,"x");return v||(p.node_ops.lookup?0:$.EACCES)},mayCreate:function(p,v){try{return P.lookupNode(p,v),$.EEXIST}catch{}return P.nodePermissions(p,"wx")},mayDelete:function(p,v,y){var _;try{_=P.lookupNode(p,v)}catch(a){return a.errno}var e=P.nodePermissions(p,"wx");if(e)return e;if(y){if(!P.isDir(_.mode))return $.ENOTDIR;if(P.isRoot(_)||P.getPath(_)===P.cwd())return $.EBUSY}else if(P.isDir(_.mode))return $.EISDIR;return 0},mayOpen:function(p,v){return p?P.isLink(p.mode)?$.ELOOP:P.isDir(p.mode)&&(2097155&v||512&v)?$.EISDIR:P.nodePermissions(p,P.flagsToPermissionString(v)):$.ENOENT},MAX_OPEN_FDS:4096,nextfd:function(p,v){p=p||0,v=v||P.MAX_OPEN_FDS;for(var y=p;y<=v;y++)if(!P.streams[y])return y;throw new P.ErrnoError($.EMFILE)},getStream:function(p){return P.streams[p]},createStream:function(p,v,y){P.FSStream||(P.FSStream=function(){},P.FSStream.prototype={},Object.defineProperties(P.FSStream.prototype,{object:{get:function(){return this.node},set:function(x){this.node=x}},isRead:{get:function(){return(2097155&this.flags)!=1}},isWrite:{get:function(){return!!(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}}));var _=new P.FSStream;for(var e in p)_[e]=p[e];p=_;var a=P.nextfd(v,y);return p.fd=a,P.streams[a]=p,p},closeStream:function(p){P.streams[p]=null},chrdev_stream_ops:{open:function(p){var v=P.getDevice(p.node.rdev);p.stream_ops=v.stream_ops,p.stream_ops.open&&p.stream_ops.open(p)},llseek:function(){throw new P.ErrnoError($.ESPIPE)}},major:function(p){return p>>8},minor:function(p){return 255&p},makedev:function(p,v){return p<<8|v},registerDevice:function(p,v){P.devices[p]={stream_ops:v}},getDevice:function(p){return P.devices[p]},getMounts:function(p){for(var v=[],y=[p];y.length;){var _=y.pop();v.push(_),y.push.apply(y,_.mounts)}return v},syncfs:function(p,v){typeof p=="function"&&(v=p,p=!1);var y=P.getMounts(P.root.mount),_=0;function e(a){if(a)return e.errored?void 0:(e.errored=!0,v(a));++_>=y.length&&v(null)}y.forEach(function(a){if(!a.type.syncfs)return e(null);a.type.syncfs(a,p,e)})},mount:function(p,v,y){var _,e=y==="/",a=!y;if(e&&P.root)throw new P.ErrnoError($.EBUSY);if(!e&&!a){var x=P.lookupPath(y,{follow_mount:!1});if(y=x.path,_=x.node,P.isMountpoint(_))throw new P.ErrnoError($.EBUSY);if(!P.isDir(_.mode))throw new P.ErrnoError($.ENOTDIR)}var D={type:p,opts:v,mountpoint:y,mounts:[]},G=p.mount(D);return G.mount=D,D.root=G,e?P.root=G:_&&(_.mounted=D,_.mount&&_.mount.mounts.push(D)),G},unmount:function(p){var v=P.lookupPath(p,{follow_mount:!1});if(!P.isMountpoint(v.node))throw new P.ErrnoError($.EINVAL);var y=v.node,_=y.mounted,e=P.getMounts(_);Object.keys(P.nameTable).forEach(function(x){for(var D=P.nameTable[x];D;){var G=D.name_next;e.indexOf(D.mount)!==-1&&P.destroyNode(D),D=G}}),y.mounted=null;var a=y.mount.mounts.indexOf(_);k1(a!==-1),y.mount.mounts.splice(a,1)},lookup:function(p,v){return p.node_ops.lookup(p,v)},mknod:function(p,v,y){var _=P.lookupPath(p,{parent:!0}).node,e=P1.basename(p);if(!e||e==="."||e==="..")throw new P.ErrnoError($.EINVAL);var a=P.mayCreate(_,e);if(a)throw new P.ErrnoError(a);if(!_.node_ops.mknod)throw new P.ErrnoError($.EPERM);return _.node_ops.mknod(_,e,v,y)},create:function(p,v){return v=v!==void 0?v:438,v&=4095,v|=32768,P.mknod(p,v,0)},mkdir:function(p,v){return v=v!==void 0?v:511,v&=1023,v|=16384,P.mknod(p,v,0)},mkdev:function(p,v,y){return y===void 0&&(y=v,v=438),v|=8192,P.mknod(p,v,y)},symlink:function(p,v){if(!P1.resolve(p))throw new P.ErrnoError($.ENOENT);var y=P.lookupPath(v,{parent:!0}).node;if(!y)throw new P.ErrnoError($.ENOENT);var _=P1.basename(v),e=P.mayCreate(y,_);if(e)throw new P.ErrnoError(e);if(!y.node_ops.symlink)throw new P.ErrnoError($.EPERM);return y.node_ops.symlink(y,_,p)},rename:function(p,v){var y,_,e=P1.dirname(p),a=P1.dirname(v),x=P1.basename(p),D=P1.basename(v);try{y=P.lookupPath(p,{parent:!0}).node,_=P.lookupPath(v,{parent:!0}).node}catch{throw new P.ErrnoError($.EBUSY)}if(!y||!_)throw new P.ErrnoError($.ENOENT);if(y.mount!==_.mount)throw new P.ErrnoError($.EXDEV);var G,J=P.lookupNode(y,x),Q=P1.relative(p,a);if(Q.charAt(0)!==".")throw new P.ErrnoError($.EINVAL);if((Q=P1.relative(v,e)).charAt(0)!==".")throw new P.ErrnoError($.ENOTEMPTY);try{G=P.lookupNode(_,D)}catch{}if(J!==G){var C=P.isDir(J.mode),e1=P.mayDelete(y,x,C);if(e1)throw new P.ErrnoError(e1);if(e1=G?P.mayDelete(_,D,C):P.mayCreate(_,D))throw new P.ErrnoError(e1);if(!y.node_ops.rename)throw new P.ErrnoError($.EPERM);if(P.isMountpoint(J)||G&&P.isMountpoint(G))throw new P.ErrnoError($.EBUSY);if(_!==y&&(e1=P.nodePermissions(y,"w")))throw new P.ErrnoError(e1);try{P.trackingDelegate.willMovePath&&P.trackingDelegate.willMovePath(p,v)}catch{}P.hashRemoveNode(J);try{y.node_ops.rename(J,_,D)}catch(s1){throw s1}finally{P.hashAddNode(J)}try{P.trackingDelegate.onMovePath&&P.trackingDelegate.onMovePath(p,v)}catch{}}},rmdir:function(p){var v=P.lookupPath(p,{parent:!0}).node,y=P1.basename(p),_=P.lookupNode(v,y),e=P.mayDelete(v,y,!0);if(e)throw new P.ErrnoError(e);if(!v.node_ops.rmdir)throw new P.ErrnoError($.EPERM);if(P.isMountpoint(_))throw new P.ErrnoError($.EBUSY);try{P.trackingDelegate.willDeletePath&&P.trackingDelegate.willDeletePath(p)}catch{}v.node_ops.rmdir(v,y),P.destroyNode(_);try{P.trackingDelegate.onDeletePath&&P.trackingDelegate.onDeletePath(p)}catch{}},readdir:function(p){var v=P.lookupPath(p,{follow:!0}).node;if(!v.node_ops.readdir)throw new P.ErrnoError($.ENOTDIR);return v.node_ops.readdir(v)},unlink:function(p){var v=P.lookupPath(p,{parent:!0}).node,y=P1.basename(p),_=P.lookupNode(v,y),e=P.mayDelete(v,y,!1);if(e)throw e===$.EISDIR&&(e=$.EPERM),new P.ErrnoError(e);if(!v.node_ops.unlink)throw new P.ErrnoError($.EPERM);if(P.isMountpoint(_))throw new P.ErrnoError($.EBUSY);try{P.trackingDelegate.willDeletePath&&P.trackingDelegate.willDeletePath(p)}catch{}v.node_ops.unlink(v,y),P.destroyNode(_);try{P.trackingDelegate.onDeletePath&&P.trackingDelegate.onDeletePath(p)}catch{}},readlink:function(p){var v=P.lookupPath(p).node;if(!v)throw new P.ErrnoError($.ENOENT);if(!v.node_ops.readlink)throw new P.ErrnoError($.EINVAL);return P1.resolve(P.getPath(v.parent),v.node_ops.readlink(v))},stat:function(p,v){var y=P.lookupPath(p,{follow:!v}).node;if(!y)throw new P.ErrnoError($.ENOENT);if(!y.node_ops.getattr)throw new P.ErrnoError($.EPERM);return y.node_ops.getattr(y)},lstat:function(p){return P.stat(p,!0)},chmod:function(p,v,y){var _;if(!(_=typeof p=="string"?P.lookupPath(p,{follow:!y}).node:p).node_ops.setattr)throw new P.ErrnoError($.EPERM);_.node_ops.setattr(_,{mode:4095&v|-4096&_.mode,timestamp:Date.now()})},lchmod:function(p,v){P.chmod(p,v,!0)},fchmod:function(p,v){var y=P.getStream(p);if(!y)throw new P.ErrnoError($.EBADF);P.chmod(y.node,v)},chown:function(p,v,y,_){var e;if(!(e=typeof p=="string"?P.lookupPath(p,{follow:!_}).node:p).node_ops.setattr)throw new P.ErrnoError($.EPERM);e.node_ops.setattr(e,{timestamp:Date.now()})},lchown:function(p,v,y){P.chown(p,v,y,!0)},fchown:function(p,v,y){var _=P.getStream(p);if(!_)throw new P.ErrnoError($.EBADF);P.chown(_.node,v,y)},truncate:function(p,v){if(v<0)throw new P.ErrnoError($.EINVAL);var y;if(!(y=typeof p=="string"?P.lookupPath(p,{follow:!0}).node:p).node_ops.setattr)throw new P.ErrnoError($.EPERM);if(P.isDir(y.mode))throw new P.ErrnoError($.EISDIR);if(!P.isFile(y.mode))throw new P.ErrnoError($.EINVAL);var _=P.nodePermissions(y,"w");if(_)throw new P.ErrnoError(_);y.node_ops.setattr(y,{size:v,timestamp:Date.now()})},ftruncate:function(p,v){var y=P.getStream(p);if(!y)throw new P.ErrnoError($.EBADF);if(!(2097155&y.flags))throw new P.ErrnoError($.EINVAL);P.truncate(y.node,v)},utime:function(p,v,y){var _=P.lookupPath(p,{follow:!0}).node;_.node_ops.setattr(_,{timestamp:Math.max(v,y)})},open:function(p,v,y,_,e){if(p==="")throw new P.ErrnoError($.ENOENT);var a;if(y=y===void 0?438:y,y=64&(v=typeof v=="string"?P.modeStringToFlags(v):v)?4095&y|32768:0,typeof p=="object")a=p;else{p=P1.normalize(p);try{a=P.lookupPath(p,{follow:!(131072&v)}).node}catch{}}var x=!1;if(64&v)if(a){if(128&v)throw new P.ErrnoError($.EEXIST)}else a=P.mknod(p,y,0),x=!0;if(!a)throw new P.ErrnoError($.ENOENT);if(P.isChrdev(a.mode)&&(v&=-513),65536&v&&!P.isDir(a.mode))throw new P.ErrnoError($.ENOTDIR);if(!x){var D=P.mayOpen(a,v);if(D)throw new P.ErrnoError(D)}512&v&&P.truncate(a,0),v&=-641;var G=P.createStream({node:a,path:P.getPath(a),flags:v,seekable:!0,position:0,stream_ops:a.stream_ops,ungotten:[],error:!1},_,e);G.stream_ops.open&&G.stream_ops.open(G),!L.logReadFiles||1&v||(P.readFiles||(P.readFiles={}),p in P.readFiles||(P.readFiles[p]=1,L.printErr("read file: "+p)));try{if(P.trackingDelegate.onOpenFile){var J=0;(2097155&v)!=1&&(J|=P.tracking.openFlags.READ),2097155&v&&(J|=P.tracking.openFlags.WRITE),P.trackingDelegate.onOpenFile(p,J)}}catch{}return G},close:function(p){p.getdents&&(p.getdents=null);try{p.stream_ops.close&&p.stream_ops.close(p)}catch(v){throw v}finally{P.closeStream(p.fd)}},llseek:function(p,v,y){if(!p.seekable||!p.stream_ops.llseek)throw new P.ErrnoError($.ESPIPE);return p.position=p.stream_ops.llseek(p,v,y),p.ungotten=[],p.position},read:function(p,v,y,_,e){if(_<0||e<0)throw new P.ErrnoError($.EINVAL);if((2097155&p.flags)==1)throw new P.ErrnoError($.EBADF);if(P.isDir(p.node.mode))throw new P.ErrnoError($.EISDIR);if(!p.stream_ops.read)throw new P.ErrnoError($.EINVAL);var a=!0;if(e===void 0)e=p.position,a=!1;else if(!p.seekable)throw new P.ErrnoError($.ESPIPE);var x=p.stream_ops.read(p,v,y,_,e);return a||(p.position+=x),x},write:function(p,v,y,_,e,a){if(_<0||e<0)throw new P.ErrnoError($.EINVAL);if(!(2097155&p.flags))throw new P.ErrnoError($.EBADF);if(P.isDir(p.node.mode))throw new P.ErrnoError($.EISDIR);if(!p.stream_ops.write)throw new P.ErrnoError($.EINVAL);1024&p.flags&&P.llseek(p,0,2);var x=!0;if(e===void 0)e=p.position,x=!1;else if(!p.seekable)throw new P.ErrnoError($.ESPIPE);var D=p.stream_ops.write(p,v,y,_,e,a);x||(p.position+=D);try{p.path&&P.trackingDelegate.onWriteToFile&&P.trackingDelegate.onWriteToFile(p.path)}catch{}return D},allocate:function(p,v,y){if(v<0||y<=0)throw new P.ErrnoError($.EINVAL);if(!(2097155&p.flags))throw new P.ErrnoError($.EBADF);if(!P.isFile(p.node.mode)&&!P.isDir(node.mode))throw new P.ErrnoError($.ENODEV);if(!p.stream_ops.allocate)throw new P.ErrnoError($.EOPNOTSUPP);p.stream_ops.allocate(p,v,y)},mmap:function(p,v,y,_,e,a,x){if((2097155&p.flags)==1)throw new P.ErrnoError($.EACCES);if(!p.stream_ops.mmap)throw new P.ErrnoError($.ENODEV);return p.stream_ops.mmap(p,v,y,_,e,a,x)},msync:function(p,v,y,_,e){return p&&p.stream_ops.msync?p.stream_ops.msync(p,v,y,_,e):0},munmap:function(p){return 0},ioctl:function(p,v,y){if(!p.stream_ops.ioctl)throw new P.ErrnoError($.ENOTTY);return p.stream_ops.ioctl(p,v,y)},readFile:function(p,v){if((v=v||{}).flags=v.flags||"r",v.encoding=v.encoding||"binary",v.encoding!=="utf8"&&v.encoding!=="binary")throw new Error('Invalid encoding type "'+v.encoding+'"');var y,_=P.open(p,v.flags),e=P.stat(p).size,a=new Uint8Array(e);return P.read(_,a,0,e,0),v.encoding==="utf8"?y=l2(a,0):v.encoding==="binary"&&(y=a),P.close(_),y},writeFile:function(p,v,y){if((y=y||{}).flags=y.flags||"w",y.encoding=y.encoding||"utf8",y.encoding!=="utf8"&&y.encoding!=="binary")throw new Error('Invalid encoding type "'+y.encoding+'"');var _=P.open(p,y.flags,y.mode);if(y.encoding==="utf8"){var e=new Uint8Array(c5(v)+1),a=Y2(v,e,0,e.length);P.write(_,e,0,a,0,y.canOwn)}else y.encoding==="binary"&&P.write(_,v,0,v.length,0,y.canOwn);P.close(_)},cwd:function(){return P.currentPath},chdir:function(p){var v=P.lookupPath(p,{follow:!0});if(!P.isDir(v.node.mode))throw new P.ErrnoError($.ENOTDIR);var y=P.nodePermissions(v.node,"x");if(y)throw new P.ErrnoError(y);P.currentPath=v.path},createDefaultDirectories:function(){P.mkdir("/tmp"),P.mkdir("/home"),P.mkdir("/home/<USER>")},createDefaultDevices:function(){var p;if(P.mkdir("/dev"),P.registerDevice(P.makedev(1,3),{read:function(){return 0},write:function(y,_,e,a,x){return a}}),P.mkdev("/dev/null",P.makedev(1,3)),h2.register(P.makedev(5,0),h2.default_tty_ops),h2.register(P.makedev(6,0),h2.default_tty1_ops),P.mkdev("/dev/tty",P.makedev(5,0)),P.mkdev("/dev/tty1",P.makedev(6,0)),typeof crypto<"u"){var v=new Uint8Array(1);p=function(){return crypto.getRandomValues(v),v[0]}}else p=function(){return 256*Math.random()|0};P.createDevice("/dev","random",p),P.createDevice("/dev","urandom",p),P.mkdir("/dev/shm"),P.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){P.mkdir("/proc"),P.mkdir("/proc/self"),P.mkdir("/proc/self/fd"),P.mount({mount:function(){var p=P.createNode("/proc/self","fd",16895,73);return p.node_ops={lookup:function(v,y){var _=+y,e=P.getStream(_);if(!e)throw new P.ErrnoError($.EBADF);var a={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return e.path}}};return a.parent=a,a}},p}},{},"/proc/self/fd")},createStandardStreams:function(){L.stdin?P.createDevice("/dev","stdin",L.stdin):P.symlink("/dev/tty","/dev/stdin"),L.stdout?P.createDevice("/dev","stdout",null,L.stdout):P.symlink("/dev/tty","/dev/stdout"),L.stderr?P.createDevice("/dev","stderr",null,L.stderr):P.symlink("/dev/tty1","/dev/stderr");var p=P.open("/dev/stdin","r");k1(p.fd===0,"invalid handle for stdin ("+p.fd+")");var v=P.open("/dev/stdout","w");k1(v.fd===1,"invalid handle for stdout ("+v.fd+")");var y=P.open("/dev/stderr","w");k1(y.fd===2,"invalid handle for stderr ("+y.fd+")")},ensureErrnoError:function(){P.ErrnoError||(P.ErrnoError=function(p,v){this.node=v,this.setErrno=function(y){for(var _ in this.errno=y,$)if($[_]===y){this.code=_;break}},this.setErrno(p),this.message=i3[p]},P.ErrnoError.prototype=new Error,P.ErrnoError.prototype.constructor=P.ErrnoError,[$.ENOENT].forEach(function(p){P.genericErrors[p]=new P.ErrnoError(p),P.genericErrors[p].stack="<generic error, no stack>"}))},staticInit:function(){P.ensureErrnoError(),P.nameTable=new Array(4096),P.mount(g1,{},"/"),P.createDefaultDirectories(),P.createDefaultDevices(),P.createSpecialDirectories(),P.filesystems={MEMFS:g1,IDBFS:Q1,NODEFS:{},WORKERFS:i0}},init:function(p,v,y){k1(!P.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),P.init.initialized=!0,P.ensureErrnoError(),L.stdin=p||L.stdin,L.stdout=v||L.stdout,L.stderr=y||L.stderr,P.createStandardStreams()},quit:function(){P.init.initialized=!1;var p=L._fflush;p&&p(0);for(var v=0;v<P.streams.length;v++){var y=P.streams[v];y&&P.close(y)}},getMode:function(p,v){var y=0;return p&&(y|=365),v&&(y|=146),y},joinPath:function(p,v){var y=P1.join.apply(null,p);return v&&y[0]=="/"&&(y=y.substr(1)),y},absolutePath:function(p,v){return P1.resolve(v,p)},standardizePath:function(p){return P1.normalize(p)},findObject:function(p,v){var y=P.analyzePath(p,v);return y.exists?y.object:(x2(y.error),null)},analyzePath:function(p,v){try{p=(_=P.lookupPath(p,{follow:!v})).path}catch{}var y={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var _=P.lookupPath(p,{parent:!0});y.parentExists=!0,y.parentPath=_.path,y.parentObject=_.node,y.name=P1.basename(p),_=P.lookupPath(p,{follow:!v}),y.exists=!0,y.path=_.path,y.object=_.node,y.name=_.node.name,y.isRoot=_.path==="/"}catch(e){y.error=e.errno}return y},createFolder:function(p,v,y,_){var e=P1.join2(typeof p=="string"?p:P.getPath(p),v),a=P.getMode(y,_);return P.mkdir(e,a)},createPath:function(p,v,y,_){p=typeof p=="string"?p:P.getPath(p);for(var e=v.split("/").reverse();e.length;){var a=e.pop();if(a){var x=P1.join2(p,a);try{P.mkdir(x)}catch{}p=x}}return x},createFile:function(p,v,y,_,e){var a=P1.join2(typeof p=="string"?p:P.getPath(p),v),x=P.getMode(_,e);return P.create(a,x)},createDataFile:function(p,v,y,_,e,a){var x=v?P1.join2(typeof p=="string"?p:P.getPath(p),v):p,D=P.getMode(_,e),G=P.create(x,D);if(y){if(typeof y=="string"){for(var J=new Array(y.length),Q=0,C=y.length;Q<C;++Q)J[Q]=y.charCodeAt(Q);y=J}P.chmod(G,146|D);var e1=P.open(G,"w");P.write(e1,y,0,y.length,0,a),P.close(e1),P.chmod(G,D)}return G},createDevice:function(p,v,y,_){var e=P1.join2(typeof p=="string"?p:P.getPath(p),v),a=P.getMode(!!y,!!_);P.createDevice.major||(P.createDevice.major=64);var x=P.makedev(P.createDevice.major++,0);return P.registerDevice(x,{open:function(D){D.seekable=!1},close:function(D){_&&_.buffer&&_.buffer.length&&_(10)},read:function(D,G,J,Q,C){for(var e1=0,s1=0;s1<Q;s1++){var _1;try{_1=y()}catch{throw new P.ErrnoError($.EIO)}if(_1===void 0&&e1===0)throw new P.ErrnoError($.EAGAIN);if(_1==null)break;e1++,G[J+s1]=_1}return e1&&(D.node.timestamp=Date.now()),e1},write:function(D,G,J,Q,C){for(var e1=0;e1<Q;e1++)try{_(G[J+e1])}catch{throw new P.ErrnoError($.EIO)}return Q&&(D.node.timestamp=Date.now()),e1}}),P.mkdev(e,a,x)},createLink:function(p,v,y,_,e){var a=P1.join2(typeof p=="string"?p:P.getPath(p),v);return P.symlink(y,a)},forceLoadFile:function(p){if(p.isDevice||p.isFolder||p.link||p.contents)return!0;var v=!0;if(typeof XMLHttpRequest<"u")throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!L.read)throw new Error("Cannot load without read() or XMLHttpRequest.");try{p.contents=d2(L.read(p.url),!0),p.usedBytes=p.contents.length}catch{v=!1}return v||x2($.EIO),v},createLazyFile:function(p,v,y,_,e){function a(){this.lengthKnown=!1,this.chunks=[]}if(a.prototype.get=function(Q){if(!(Q>this.length-1||Q<0)){var C=Q%this.chunkSize,e1=Q/this.chunkSize|0;return this.getter(e1)[C]}},a.prototype.setDataGetter=function(Q){this.getter=Q},a.prototype.cacheLength=function(){var Q=new XMLHttpRequest;if(Q.open("HEAD",y,!1),Q.send(null),!(Q.status>=200&&Q.status<300||Q.status===304))throw new Error("Couldn't load "+y+". Status: "+Q.status);var C,e1=Number(Q.getResponseHeader("Content-length")),s1=(C=Q.getResponseHeader("Accept-Ranges"))&&C==="bytes",_1=1048576;s1||(_1=e1);var N1=function(D1,d0){if(D1>d0)throw new Error("invalid range ("+D1+", "+d0+") or no bytes requested!");if(d0>e1-1)throw new Error("only "+e1+" bytes available! programmer error!");var F1=new XMLHttpRequest;if(F1.open("GET",y,!1),e1!==_1&&F1.setRequestHeader("Range","bytes="+D1+"-"+d0),typeof Uint8Array<"u"&&(F1.responseType="arraybuffer"),F1.overrideMimeType&&F1.overrideMimeType("text/plain; charset=x-user-defined"),F1.send(null),!(F1.status>=200&&F1.status<300||F1.status===304))throw new Error("Couldn't load "+y+". Status: "+F1.status);return F1.response!==void 0?new Uint8Array(F1.response||[]):d2(F1.responseText||"",!0)},I1=this;I1.setDataGetter(function(D1){var d0=D1*_1,F1=(D1+1)*_1-1;if(F1=Math.min(F1,e1-1),I1.chunks[D1]===void 0&&(I1.chunks[D1]=N1(d0,F1)),I1.chunks[D1]===void 0)throw new Error("doXHR failed!");return I1.chunks[D1]}),this._length=e1,this._chunkSize=_1,this.lengthKnown=!0},typeof XMLHttpRequest<"u"){if(!T0)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var x=new a;Object.defineProperty(x,"length",{get:function(){return this.lengthKnown||this.cacheLength(),this._length}}),Object.defineProperty(x,"chunkSize",{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}});var D={isDevice:!1,contents:x}}else D={isDevice:!1,url:y};var G=P.createFile(p,v,D,_,e);D.contents?G.contents=D.contents:D.url&&(G.contents=null,G.url=D.url),Object.defineProperty(G,"usedBytes",{get:function(){return this.contents.length}});var J={};return Object.keys(G.stream_ops).forEach(function(Q){var C=G.stream_ops[Q];J[Q]=function(){if(!P.forceLoadFile(G))throw new P.ErrnoError($.EIO);return C.apply(null,arguments)}}),J.read=function(Q,C,e1,s1,_1){if(!P.forceLoadFile(G))throw new P.ErrnoError($.EIO);var N1=Q.node.contents;if(_1>=N1.length)return 0;var I1=Math.min(N1.length-_1,s1);if(k1(I1>=0),N1.slice)for(var D1=0;D1<I1;D1++)C[e1+D1]=N1[_1+D1];else for(D1=0;D1<I1;D1++)C[e1+D1]=N1.get(_1+D1);return I1},G.stream_ops=J,G},createPreloadedFile:function(p,v,y,_,e,a,x,D,G,J){X.init();var Q=v?P1.resolve(P1.join2(p,v)):p;function C(e1){function s1(N1){J&&J(),D||P.createDataFile(p,v,N1,_,e,G),a&&a(),$2()}var _1=!1;L.preloadPlugins.forEach(function(N1){_1||N1.canHandle(Q)&&(N1.handle(e1,Q,s1,function(){x&&x(),$2()}),_1=!0)}),_1||s1(e1)}v5(),typeof y=="string"?X.asyncLoad(y,function(e1){C(e1)},x):C(y)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(p,v,y){v=v||function(){},y=y||function(){};var _=P.indexedDB();try{var e=_.open(P.DB_NAME(),P.DB_VERSION)}catch(a){return y(a)}e.onupgradeneeded=function(){e.result.createObjectStore(P.DB_STORE_NAME)},e.onsuccess=function(){var a=e.result.transaction([P.DB_STORE_NAME],"readwrite"),x=a.objectStore(P.DB_STORE_NAME),D=0,G=0,J=p.length;function Q(){G==0?v():y()}p.forEach(function(C){var e1=x.put(P.analyzePath(C).object.contents,C);e1.onsuccess=function(){++D+G==J&&Q()},e1.onerror=function(){G++,D+G==J&&Q()}}),a.onerror=y},e.onerror=y},loadFilesFromDB:function(p,v,y){v=v||function(){},y=y||function(){};var _=P.indexedDB();try{var e=_.open(P.DB_NAME(),P.DB_VERSION)}catch(a){return y(a)}e.onupgradeneeded=y,e.onsuccess=function(){var a=e.result;try{var x=a.transaction([P.DB_STORE_NAME],"readonly")}catch(e1){return void y(e1)}var D=x.objectStore(P.DB_STORE_NAME),G=0,J=0,Q=p.length;function C(){J==0?v():y()}p.forEach(function(e1){var s1=D.get(e1);s1.onsuccess=function(){P.analyzePath(e1).exists&&P.unlink(e1),P.createDataFile(P1.dirname(e1),P1.basename(e1),s1.result,!0,!0,!0),++G+J==Q&&C()},s1.onerror=function(){J++,G+J==Q&&C()}}),x.onerror=y},e.onerror=y}},P1={splitPath:function(p){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(p).slice(1)},normalizeArray:function(p,v){for(var y=0,_=p.length-1;_>=0;_--){var e=p[_];e==="."?p.splice(_,1):e===".."?(p.splice(_,1),y++):y&&(p.splice(_,1),y--)}if(v)for(;y--;y)p.unshift("..");return p},normalize:function(p){var v=p.charAt(0)==="/",y=p.substr(-1)==="/";return(p=P1.normalizeArray(p.split("/").filter(function(_){return!!_}),!v).join("/"))||v||(p="."),p&&y&&(p+="/"),(v?"/":"")+p},dirname:function(p){var v=P1.splitPath(p),y=v[0],_=v[1];return y||_?(_&&(_=_.substr(0,_.length-1)),y+_):"."},basename:function(p){if(p==="/")return"/";var v=p.lastIndexOf("/");return v===-1?p:p.substr(v+1)},extname:function(p){return P1.splitPath(p)[3]},join:function(){var p=Array.prototype.slice.call(arguments,0);return P1.normalize(p.join("/"))},join2:function(p,v){return P1.normalize(p+"/"+v)},resolve:function(){for(var p="",v=!1,y=arguments.length-1;y>=-1&&!v;y--){var _=y>=0?arguments[y]:P.cwd();if(typeof _!="string")throw new TypeError("Arguments to path.resolve must be strings");if(!_)return"";p=_+"/"+p,v=_.charAt(0)==="/"}return(v?"/":"")+(p=P1.normalizeArray(p.split("/").filter(function(e){return!!e}),!v).join("/"))||"."},relative:function(p,v){function y(J){for(var Q=0;Q<J.length&&J[Q]==="";Q++);for(var C=J.length-1;C>=0&&J[C]==="";C--);return Q>C?[]:J.slice(Q,C-Q+1)}p=P1.resolve(p).substr(1),v=P1.resolve(v).substr(1);for(var _=y(p.split("/")),e=y(v.split("/")),a=Math.min(_.length,e.length),x=a,D=0;D<a;D++)if(_[D]!==e[D]){x=D;break}var G=[];for(D=x;D<_.length;D++)G.push("..");return(G=G.concat(e.slice(x))).join("/")}};function e5(p,v){if(X.mainLoop.timingMode=p,X.mainLoop.timingValue=v,!X.mainLoop.func)return 1;if(p==0)X.mainLoop.scheduler=function(){setTimeout(X.mainLoop.runner,v)},X.mainLoop.method="timeout";else if(p==1)X.mainLoop.scheduler=function(){X.requestAnimationFrame(X.mainLoop.runner)},X.mainLoop.method="rAF";else if(p==2){if(!window.setImmediate){let e=function(a){a.source===window&&a.data===_&&(a.stopPropagation(),y.shift()())};var y=[],_="__emcc";window.addEventListener("message",e,!0),window.setImmediate=function(a){y.push(a),window.postMessage(_,"*")}}X.mainLoop.scheduler=function(){window.setImmediate(X.mainLoop.runner)},X.mainLoop.method="immediate"}return 0}function w6(p,v,y,_,e){L.noExitRuntime=!0,k1(!X.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),X.mainLoop.func=p,X.mainLoop.arg=_;var a=X.mainLoop.currentlyRunningMainloop;if(X.mainLoop.runner=function(){if(!W1){if(X.mainLoop.queue.length>0){Date.now();var x=X.mainLoop.queue.shift();if(x.func(x.arg),X.mainLoop.remainingBlockers){var D=X.mainLoop.remainingBlockers,G=D%1==0?D-1:Math.floor(D);x.counted?X.mainLoop.remainingBlockers=G:(G+=.5,X.mainLoop.remainingBlockers=(8*D+G)/9)}return X.mainLoop.updateStatus(),void setTimeout(X.mainLoop.runner,0)}a<X.mainLoop.currentlyRunningMainloop||(X.mainLoop.currentFrameNumber=X.mainLoop.currentFrameNumber+1|0,X.mainLoop.timingMode==1&&X.mainLoop.timingValue>1&&X.mainLoop.currentFrameNumber%X.mainLoop.timingValue!=0?X.mainLoop.scheduler():(X.mainLoop.method==="timeout"&&L.ctx&&(L.printErr("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),X.mainLoop.method=""),X.mainLoop.runIter(function(){_!==void 0?h1.dynCall("vi",p,[_]):h1.dynCall("v",p)}),a<X.mainLoop.currentlyRunningMainloop||(typeof SDL=="object"&&SDL.audio&&SDL.audio.queueNewAudioData&&SDL.audio.queueNewAudioData(),X.mainLoop.scheduler())))}},e||(v&&v>0?e5(0,1e3/v):e5(1,1),X.mainLoop.scheduler()),y)throw"SimulateInfiniteLoop"}var X={mainLoop:{scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){X.mainLoop.scheduler=null,X.mainLoop.currentlyRunningMainloop++},resume:function(){X.mainLoop.currentlyRunningMainloop++;var p=X.mainLoop.timingMode,v=X.mainLoop.timingValue,y=X.mainLoop.func;X.mainLoop.func=null,w6(y,0,!1,X.mainLoop.arg,!0),e5(p,v),X.mainLoop.scheduler()},updateStatus:function(){if(L.setStatus){var p=L.statusMessage||"Please wait...",v=X.mainLoop.remainingBlockers,y=X.mainLoop.expectedBlockers;v?v<y?L.setStatus(p+" ("+(y-v)+"/"+y+")"):L.setStatus(p):L.setStatus("")}},runIter:function(p){if(!W1){if(L.preMainLoop&&L.preMainLoop()===!1)return;try{p()}catch(v){if(v instanceof D2)return;throw v&&typeof v=="object"&&v.stack&&L.printErr("exception thrown: "+[v,v.stack]),v}L.postMainLoop&&L.postMainLoop()}}},isFullScreen:!1,pointerLock:!1,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(L.preloadPlugins||(L.preloadPlugins=[]),!X.initted){X.initted=!0;try{new Blob,X.hasBlobConstructor=!0}catch{X.hasBlobConstructor=!1}X.BlobBuilder=typeof MozBlobBuilder<"u"?MozBlobBuilder:typeof WebKitBlobBuilder<"u"?WebKitBlobBuilder:X.hasBlobConstructor?null:void 0,X.URLObject=typeof window<"u"?window.URL?window.URL:window.webkitURL:void 0,L.noImageDecoding||X.URLObject!==void 0||(L.noImageDecoding=!0);var p={canHandle:function(e){return!L.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(e)},handle:function(e,a,x,D){var G=null;if(X.hasBlobConstructor)try{(G=new Blob([e],{type:X.getMimetype(a)})).size!==e.length&&(G=new Blob([new Uint8Array(e).buffer],{type:X.getMimetype(a)}))}catch(e1){h1.warnOnce("Blob constructor present but fails: "+e1+"; falling back to blob builder")}if(!G){var J=new X.BlobBuilder;J.append(new Uint8Array(e).buffer),G=J.getBlob()}var Q=X.URLObject.createObjectURL(G),C=new Image;C.onload=function(){k1(C.complete,"Image "+a+" could not be decoded");var e1=document.createElement("canvas");e1.width=C.width,e1.height=C.height,e1.getContext("2d").drawImage(C,0,0),L.preloadedImages[a]=e1,X.URLObject.revokeObjectURL(Q),x&&x(e)},C.onerror=function(e1){D&&D()},C.src=Q}};L.preloadPlugins.push(p);var v={canHandle:function(e){return!L.noAudioDecoding&&e.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(e,a,x,D){var G=!1;function J(_1){G||(G=!0,L.preloadedAudios[a]=_1,x&&x(e))}function Q(){G||(G=!0,L.preloadedAudios[a]=new Audio,D&&D())}if(!X.hasBlobConstructor)return Q();try{var C=new Blob([e],{type:X.getMimetype(a)})}catch{return Q()}var e1=X.URLObject.createObjectURL(C),s1=new Audio;s1.addEventListener("canplaythrough",function(){J(s1)},!1),s1.onerror=function(_1){function N1(I1){for(var D1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d0="=",F1="",V0=0,B0=0,M0=0;M0<I1.length;M0++)for(V0=V0<<8|I1[M0],B0+=8;B0>=6;){var b6=V0>>B0-6&63;B0-=6,F1+=D1[b6]}return B0==2?(F1+=D1[(3&V0)<<4],F1+=d0+d0):B0==4&&(F1+=D1[(15&V0)<<2],F1+=d0),F1}G||(s1.src="data:audio/x-"+a.substr(-3)+";base64,"+N1(e),J(s1))},s1.src=e1,X.safeSetTimeout(function(){J(s1)},1e4)}};L.preloadPlugins.push(v);var y=L.canvas;y&&(y.requestPointerLock=y.requestPointerLock||y.mozRequestPointerLock||y.webkitRequestPointerLock||y.msRequestPointerLock||function(){},y.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||function(){},y.exitPointerLock=y.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",_,!1),document.addEventListener("mozpointerlockchange",_,!1),document.addEventListener("webkitpointerlockchange",_,!1),document.addEventListener("mspointerlockchange",_,!1),L.elementPointerLock&&y.addEventListener("click",function(e){!X.pointerLock&&y.requestPointerLock&&(y.requestPointerLock(),e.preventDefault())},!1))}function _(){X.pointerLock=document.pointerLockElement===y||document.mozPointerLockElement===y||document.webkitPointerLockElement===y||document.msPointerLockElement===y}},createContext:function(p,v,y,_){if(v&&L.ctx&&p==L.canvas)return L.ctx;var e,a;if(v){var x={antialias:!1,alpha:!1};if(_)for(var D in _)x[D]=_[D];(a=GL.createContext(p,x))&&(e=GL.getContext(a).GLctx),p.style.backgroundColor="black"}else e=p.getContext("2d");return e?(y&&(v||k1(typeof GLctx>"u","cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),L.ctx=e,v&&GL.makeContextCurrent(a),L.useWebGL=v,X.moduleContextCreatedCallbacks.forEach(function(G){G()}),X.init()),e):null},destroyContext:function(p,v,y){},fullScreenHandlersInstalled:!1,lockPointer:void 0,resizeCanvas:void 0,requestFullScreen:function(p,v,y){X.lockPointer=p,X.resizeCanvas=v,X.vrDevice=y,X.lockPointer===void 0&&(X.lockPointer=!0),X.resizeCanvas===void 0&&(X.resizeCanvas=!1),X.vrDevice===void 0&&(X.vrDevice=null);var _=L.canvas;function e(){X.isFullScreen=!1;var x=_.parentNode;(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===x?(_.cancelFullScreen=document.cancelFullScreen||document.mozCancelFullScreen||document.webkitCancelFullScreen||document.msExitFullscreen||document.exitFullscreen||function(){},_.cancelFullScreen=_.cancelFullScreen.bind(document),X.lockPointer&&_.requestPointerLock(),X.isFullScreen=!0,X.resizeCanvas&&X.setFullScreenCanvasSize()):(x.parentNode.insertBefore(_,x),x.parentNode.removeChild(x),X.resizeCanvas&&X.setWindowedCanvasSize()),L.onFullScreen&&L.onFullScreen(X.isFullScreen),X.updateCanvasDimensions(_)}X.fullScreenHandlersInstalled||(X.fullScreenHandlersInstalled=!0,document.addEventListener("fullscreenchange",e,!1),document.addEventListener("mozfullscreenchange",e,!1),document.addEventListener("webkitfullscreenchange",e,!1),document.addEventListener("MSFullscreenChange",e,!1));var a=document.createElement("div");_.parentNode.insertBefore(a,_),a.appendChild(_),a.requestFullScreen=a.requestFullScreen||a.mozRequestFullScreen||a.msRequestFullscreen||(a.webkitRequestFullScreen?function(){a.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null),y?a.requestFullScreen({vrDisplay:y}):a.requestFullScreen()},nextRAF:0,fakeRequestAnimationFrame:function(p){var v=Date.now();if(X.nextRAF===0)X.nextRAF=v+1e3/60;else for(;v+2>=X.nextRAF;)X.nextRAF+=1e3/60;var y=Math.max(X.nextRAF-v,0);setTimeout(p,y)},requestAnimationFrame:function(p){typeof window>"u"?X.fakeRequestAnimationFrame(p):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||X.fakeRequestAnimationFrame),window.requestAnimationFrame(p))},safeCallback:function(p){return function(){if(!W1)return p.apply(null,arguments)}},allowAsyncCallbacks:!0,queuedAsyncCallbacks:[],pauseAsyncCallbacks:function(){X.allowAsyncCallbacks=!1},resumeAsyncCallbacks:function(){if(X.allowAsyncCallbacks=!0,X.queuedAsyncCallbacks.length>0){var p=X.queuedAsyncCallbacks;X.queuedAsyncCallbacks=[],p.forEach(function(v){v()})}},safeRequestAnimationFrame:function(p){return X.requestAnimationFrame(function(){W1||(X.allowAsyncCallbacks?p():X.queuedAsyncCallbacks.push(p))})},safeSetTimeout:function(p,v){return L.noExitRuntime=!0,setTimeout(function(){W1||(X.allowAsyncCallbacks?p():X.queuedAsyncCallbacks.push(p))},v)},safeSetInterval:function(p,v){return L.noExitRuntime=!0,setInterval(function(){W1||X.allowAsyncCallbacks&&p()},v)},getMimetype:function(p){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[p.substr(p.lastIndexOf(".")+1)]},getUserMedia:function(p){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia),window.getUserMedia(p)},getMovementX:function(p){return p.movementX||p.mozMovementX||p.webkitMovementX||0},getMovementY:function(p){return p.movementY||p.mozMovementY||p.webkitMovementY||0},getMouseWheelDelta:function(p){var v=0;switch(p.type){case"DOMMouseScroll":v=p.detail;break;case"mousewheel":v=p.wheelDelta;break;case"wheel":v=p.deltaY;break;default:throw"unrecognized mouse wheel event: "+p.type}return v},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(p){if(X.pointerLock)p.type!="mousemove"&&"mozMovementX"in p?X.mouseMovementX=X.mouseMovementY=0:(X.mouseMovementX=X.getMovementX(p),X.mouseMovementY=X.getMovementY(p)),typeof SDL<"u"?(X.mouseX=SDL.mouseX+X.mouseMovementX,X.mouseY=SDL.mouseY+X.mouseMovementY):(X.mouseX+=X.mouseMovementX,X.mouseY+=X.mouseMovementY);else{var v=L.canvas.getBoundingClientRect(),y=L.canvas.width,_=L.canvas.height,e=window.scrollX!==void 0?window.scrollX:window.pageXOffset,a=window.scrollY!==void 0?window.scrollY:window.pageYOffset;if(p.type==="touchstart"||p.type==="touchend"||p.type==="touchmove"){var x=p.touch;if(x===void 0)return;var D=x.pageX-(e+v.left),G=x.pageY-(a+v.top),J={x:D*=y/v.width,y:G*=_/v.height};if(p.type==="touchstart")X.lastTouches[x.identifier]=J,X.touches[x.identifier]=J;else if(p.type==="touchend"||p.type==="touchmove"){var Q=X.touches[x.identifier];Q||(Q=J),X.lastTouches[x.identifier]=Q,X.touches[x.identifier]=J}return}var C=p.pageX-(e+v.left),e1=p.pageY-(a+v.top);C*=y/v.width,e1*=_/v.height,X.mouseMovementX=C-X.mouseX,X.mouseMovementY=e1-X.mouseY,X.mouseX=C,X.mouseY=e1}},xhrLoad:function(p,v,y){var _=new XMLHttpRequest;_.open("GET",p,!0),_.responseType="arraybuffer",_.onload=function(){_.status==200||_.status==0&&_.response?v(_.response):y()},_.onerror=y,_.send(null)},asyncLoad:function(p,v,y,_){X.xhrLoad(p,function(e){k1(e,'Loading data file "'+p+'" failed (no arrayBuffer).'),v(new Uint8Array(e)),_||$2()},function(e){if(!y)throw'Loading data file "'+p+'" failed.';y()}),_||v5()},resizeListeners:[],updateResizeListeners:function(){var p=L.canvas;X.resizeListeners.forEach(function(v){v(p.width,p.height)})},setCanvasSize:function(p,v,y){var _=L.canvas;X.updateCanvasDimensions(_,p,v),y||X.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullScreenCanvasSize:function(){if(typeof SDL<"u"){var p=G2[SDL.screen+0*h1.QUANTUM_SIZE>>2];p|=8388608,g0[SDL.screen+0*h1.QUANTUM_SIZE>>2]=p}X.updateResizeListeners()},setWindowedCanvasSize:function(){if(typeof SDL<"u"){var p=G2[SDL.screen+0*h1.QUANTUM_SIZE>>2];p&=-8388609,g0[SDL.screen+0*h1.QUANTUM_SIZE>>2]=p}X.updateResizeListeners()},updateCanvasDimensions:function(p,v,y){v&&y?(p.widthNative=v,p.heightNative=y):(v=p.widthNative,y=p.heightNative);var _=v,e=y;if(L.forcedAspectRatio&&L.forcedAspectRatio>0&&(_/e<L.forcedAspectRatio?_=Math.round(e*L.forcedAspectRatio):e=Math.round(_/L.forcedAspectRatio)),(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===p.parentNode&&typeof screen<"u"){var a=Math.min(screen.width/_,screen.height/e);_=Math.round(_*a),e=Math.round(e*a)}X.resizeCanvas?(p.width!=_&&(p.width=_),p.height!=e&&(p.height=e),p.style!==void 0&&(p.style.removeProperty("width"),p.style.removeProperty("height"))):(p.width!=v&&(p.width=v),p.height!=y&&(p.height=y),p.style!==void 0&&(_!=v||e!=y?(p.style.setProperty("width",_+"px","important"),p.style.setProperty("height",e+"px","important")):(p.style.removeProperty("width"),p.style.removeProperty("height"))))},wgetRequests:{},nextWgetRequestHandle:0,getNextWgetRequestHandle:function(){var p=X.nextWgetRequestHandle;return X.nextWgetRequestHandle++,p}};function t3(p){var v=Date.now()/1e3|0;return p&&(g0[p>>2]=v),v}function r3(){return 0}L.requestFullScreen=function(p,v,y){X.requestFullScreen(p,v,y)},L.requestAnimationFrame=function(p){X.requestAnimationFrame(p)},L.setCanvasSize=function(p,v,y){X.setCanvasSize(p,v,y)},L.pauseMainLoop=function(){X.mainLoop.pause()},L.resumeMainLoop=function(){X.mainLoop.resume()},L.getUserMedia=function(){X.getUserMedia()},L.createContext=function(p,v,y,_){return X.createContext(p,v,y,_)},P.staticInit(),F2.unshift(function(){L.noFSInit||P.init.initialized||P.init()}),p5.push(function(){P.ignorePermissions=!1}),Q2.push(function(){P.quit()}),L.FS_createFolder=P.createFolder,L.FS_createPath=P.createPath,L.FS_createDataFile=P.createDataFile,L.FS_createPreloadedFile=P.createPreloadedFile,L.FS_createLazyFile=P.createLazyFile,L.FS_createLink=P.createLink,L.FS_createDevice=P.createDevice,L.FS_unlink=P.unlink,F2.unshift(function(){}),Q2.push(function(){}),s6=Z0=h1.alignMemory(S2),n6=!0,h5=s6+w5,k1((Q0=h1.alignMemory(h5))<c2,"TOTAL_MEMORY not big enough for stack"),L.asmGlobalArg={Math,Int8Array,Int16Array,Int32Array,Uint8Array,Uint16Array,Uint32Array,Float32Array,Float64Array,NaN:NaN,Infinity:1/0},L.asmLibraryArg={abort:w2,assert:k1,_sysconf:$4,_pthread_self:r3,_abort:e3,___setErrNo:x2,_sbrk:J2,_time:t3,_emscripten_set_main_loop_timing:e5,_emscripten_memcpy_big:J4,_emscripten_set_main_loop:w6,STACKTOP:Z0,STACK_MAX:h5,tempDoublePtr:h6,ABORT:W1};var t0=function(p,v,y){var _=new p.Int8Array(y),e=new p.Int16Array(y),a=new p.Int32Array(y),x=new p.Uint8Array(y),D=new p.Uint16Array(y),G=new p.Uint32Array(y),J=new p.Float32Array(y),Q=new p.Float64Array(y),C=v.STACKTOP|0,e1=v.STACK_MAX|0,s1=v.tempDoublePtr|0,_1=v.ABORT|0,N1=0,I1=0,D1=0,d0=0,F1=p.NaN,V0=p.Infinity,B0=0,M0=0,b6=0,xi=0,Ui=0,zi=0,ji=0,qi=0,Hi=0,v6=0,Vi=0,Wi=0,Xi=0,Yi=0,Gi=0,Ki=0,Zi=0,Qi=0,$i=0,Ji=p.Math.floor,e7=p.Math.abs,i7=p.Math.sqrt,t7=p.Math.pow,r7=p.Math.cos,o7=p.Math.sin,n7=p.Math.tan,s7=p.Math.acos,a7=p.Math.asin,f7=p.Math.atan,l7=p.Math.atan2,u7=p.Math.exp,c7=p.Math.log,d7=p.Math.ceil,g=p.Math.imul,h7=p.Math.min,w7=p.Math.clz32,m7=v.abort,p7=v.assert,a3=v._sysconf,k7=v._pthread_self,v1=v._abort,b7=v.___setErrNo,m2=v._sbrk,f3=v._time,v7=v._emscripten_set_main_loop_timing,l3=v._emscripten_memcpy_big,E7=v._emscripten_set_main_loop,y7=0;function u3(i){i=i|0;var r=0;return r=C,C=C+i|0,C=C+15&-16,r|0}function c3(){return C|0}function d3(i){i=i|0,C=i}function h3(i,r){i=i|0,r=r|0,C=i,e1=r}function w3(i,r){i=i|0,r=r|0,N1||(N1=i,I1=r)}function g7(i){_[s1>>0]=_[i>>0],_[s1+1>>0]=_[i+1>>0],_[s1+2>>0]=_[i+2>>0],_[s1+3>>0]=_[i+3>>0]}function _7(i){_[s1>>0]=_[i>>0],_[s1+1>>0]=_[i+1>>0],_[s1+2>>0]=_[i+2>>0],_[s1+3>>0]=_[i+3>>0],_[s1+4>>0]=_[i+4>>0],_[s1+5>>0]=_[i+5>>0],_[s1+6>>0]=_[i+6>>0],_[s1+7>>0]=_[i+7>>0]}function m3(i){i=i|0,v6=i}function p3(){return v6|0}function k3(){var i=0,r=0;return r=C,C=C+16|0,i=r,a[i>>2]=0,te(i,31756)|0,C=r,a[i>>2]|0}function b3(i){i=i|0;var r=0,t=0;r=C,C=C+16|0,t=r,a[t>>2]=i,re(t),C=r}function v3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0,S3(i,o|0?15:(x[r>>0]|0)>>>3&15,r+1|0,t,2)|0}function E3(i){i=i|0;var r=0;return r=o0(8)|0,se(r,r+4|0,i)|0,r|0}function y3(i){i=i|0,ae(i,i+4|0),G1(i)}function g3(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0;return n=C,C=C+16|0,s=n,a[s>>2]=r,t=(fe(a[i>>2]|0,a[i+4>>2]|0,r,t,o,s,3)|0)<<16>>16,_[o>>0]=x[o>>0]|0|4,C=n,t|0}function _3(i){return i=i|0,i?(e[i>>1]=4096,i=0):i=-1,i|0}function E6(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0;if(d=a[s>>2]|0,k=n<<16>>16>0,k){f=0,l=0;do c=e[t+(f<<1)>>1]|0,c=g(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+l|0,(c^l|0)>0&(u^l|0)<0?(a[s>>2]=1,l=(l>>>31)+2147483647|0):l=u):(a[s>>2]=1,l=2147483647),f=f+1|0;while((f&65535)<<16>>16!=n<<16>>16);if((l|0)==2147483647){a[s>>2]=d,c=0,u=0;do l=e[t+(c<<1)>>1]>>2,l=g(l,l)|0,(l|0)!=1073741824?(f=(l<<1)+u|0,(l^u|0)>0&(f^u|0)<0?(a[s>>2]=1,u=(u>>>31)+2147483647|0):u=f):(a[s>>2]=1,u=2147483647),c=c+1|0;while((c&65535)<<16>>16!=n<<16>>16)}else h=8}else l=0,h=8;if((h|0)==8&&(u=l>>4),!u){e[i>>1]=0;return}if(w=((B1(u)|0)&65535)+65535|0,l=w<<16>>16,(w&65535)<<16>>16>0?(f=u<<l,(f>>l|0)==(u|0)?u=f:u=u>>31^2147483647):(l=0-l<<16,(l|0)<2031616?u=u>>(l>>16):u=0),m=S1(u,s)|0,f=a[s>>2]|0,k){l=0,u=0;do d=e[r+(l<<1)>>1]|0,d=g(d,d)|0,(d|0)!=1073741824?(c=(d<<1)+u|0,(d^u|0)>0&(c^u|0)<0?(a[s>>2]=1,u=(u>>>31)+2147483647|0):u=c):(a[s>>2]=1,u=2147483647),l=l+1|0;while((l&65535)<<16>>16!=n<<16>>16);if((u|0)==2147483647){a[s>>2]=f,d=0,u=0;do c=e[r+(d<<1)>>1]>>2,c=g(c,c)|0,(c|0)!=1073741824?(l=(c<<1)+u|0,(c^u|0)>0&(l^u|0)<0?(a[s>>2]=1,u=(u>>>31)+2147483647|0):u=l):(a[s>>2]=1,u=2147483647),d=d+1|0;while((d&65535)<<16>>16!=n<<16>>16)}else h=29}else u=0,h=29;if((h|0)==29&&(u=u>>4),u?(l=(B1(u)|0)<<16>>16,f=w-l|0,c=f&65535,u=(r0(m,S1(u<<l,s)|0)|0)<<16>>16,l=u<<7,f=f<<16>>16,c<<16>>16>0?f=c<<16>>16<31?l>>f:0:(h=0-f<<16>>16,f=l<<h,f=(f>>h|0)==(l|0)?f:u>>24^2147483647),c=(g(((e2(f,s)|0)<<9)+32768>>16,32767-(o&65535)<<16>>16)|0)>>>15<<16>>16):c=0,f=e[i>>1]|0,k)for(u=o<<16>>16,l=0;o=((g(f<<16>>16,u)|0)>>>15&65535)+c|0,f=o&65535,e[t>>1]=(g(e[t>>1]|0,o<<16>>16)|0)>>>12,l=l+1<<16>>16,!(l<<16>>16>=n<<16>>16);)t=t+2|0;e[i>>1]=f}function R3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0;if(f=a[o>>2]|0,n=t<<16>>16>0,n){l=0,s=0;do c=e[r+(l<<1)>>1]|0,c=g(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+s|0,(c^s|0)>0&(u^s|0)<0?(a[o>>2]=1,s=(s>>>31)+2147483647|0):s=u):(a[o>>2]=1,s=2147483647),l=l+1|0;while((l&65535)<<16>>16!=t<<16>>16);if((s|0)==2147483647){a[o>>2]=f,c=0,f=0;do u=e[r+(c<<1)>>1]>>2,u=g(u,u)|0,(u|0)!=1073741824?(l=(u<<1)+f|0,(u^f|0)>0&(l^f|0)<0?(a[o>>2]=1,f=(f>>>31)+2147483647|0):f=l):(a[o>>2]=1,f=2147483647),c=c+1|0;while((c&65535)<<16>>16!=t<<16>>16)}else w=8}else s=0,w=8;if((w|0)==8&&(f=s>>4),!!f){if(m=((B1(f)|0)&65535)+65535|0,u=m<<16>>16,(m&65535)<<16>>16>0?(l=f<<u,(l>>u|0)==(f|0)?f=l:f=f>>31^2147483647):(u=0-u<<16,(u|0)<2031616?f=f>>(u>>16):f=0),d=S1(f,o)|0,f=a[o>>2]|0,n){l=0,s=0;do c=e[i+(l<<1)>>1]|0,c=g(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+s|0,(c^s|0)>0&(u^s|0)<0?(a[o>>2]=1,s=(s>>>31)+2147483647|0):s=u):(a[o>>2]=1,s=2147483647),l=l+1|0;while((l&65535)<<16>>16!=t<<16>>16);if((s|0)==2147483647){a[o>>2]=f,f=0,l=0;do c=e[i+(f<<1)>>1]>>2,c=g(c,c)|0,(c|0)!=1073741824?(u=(c<<1)+l|0,(c^l|0)>0&(u^l|0)<0?(a[o>>2]=1,l=(l>>>31)+2147483647|0):l=u):(a[o>>2]=1,l=2147483647),f=f+1|0;while((f&65535)<<16>>16!=t<<16>>16)}else w=28}else s=0,w=28;if((w|0)==28&&(l=s>>4),l?(c=B1(l)|0,u=c<<16>>16,c<<16>>16>0?(f=l<<u,(f>>u|0)==(l|0)?l=f:l=l>>31^2147483647):(u=0-u<<16,(u|0)<2031616?l=l>>(u>>16):l=0),f=m-(c&65535)|0,u=f&65535,s=(r0(d,S1(l,o)|0)|0)<<16>>16,n=s<<7,f=f<<16>>16,u<<16>>16>0?n=u<<16>>16<31?n>>f:0:(m=0-f<<16>>16,i=n<<m,n=(i>>m|0)==(n|0)?i:s>>24^2147483647),n=e2(n,o)|0,(n|0)>4194303?n=2147483647:n=(n|0)<-4194304?-2147483648:n<<9,n=S1(n,o)|0):n=0,s=(t&65535)+65535&65535,!(s<<16>>16<=-1))for(c=n<<16>>16,u=t+-1<<16>>16<<16>>16;;){f=r+(u<<1)|0,n=g(e[f>>1]|0,c)|0;do if((n|0)!=1073741824)if(l=n<<1,(l|0)<=268435455)if((l|0)<-268435456){e[f>>1]=-32768;break}else{e[f>>1]=n>>>12;break}else w=52;else a[o>>2]=1,w=52;while(!1);if((w|0)==52&&(w=0,e[f>>1]=32767),s=s+-1<<16>>16,s<<16>>16<=-1)break;u=u+-1|0}}}function S3(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0;u=C,C=C+496|0,l=u,f=(n|0)==2;do if(f&1|(n|0)==4){s=i+1168|0,f?(ne(r,t,l,s),s=604):(Y3(r,t,l,s),s=3436),n=e[s+(r<<1)>>1]|0;do if(r>>>0>=8){if((r|0)==8){r=e[l+76>>1]<<2|(e[l+74>>1]<<1|e[l+72>>1]),s=e[l+70>>1]|0?5:4;break}if(r>>>0<15)return i=-1,C=u,i|0;r=a[i+1760>>2]|0,s=7;break}else s=0;while(!1);if(n<<16>>16==-1)return i=-1,C=u,i|0}else{if(n)return i=-1,C=u,i|0;for(f=e[t>>1]|0,r=t+490|0,n=t+2|0,s=0;e[l+(s<<1)>>1]=e[n>>1]|0,s=s+1|0,(s|0)!=244;)n=n+2|0;if(s=f<<16>>16,f<<16>>16==7){n=492,r=a[i+1760>>2]|0;break}else{n=492,r=e[r>>1]|0;break}}while(!1);return oe(i,r,l,s,o),a[i+1760>>2]=r,i=n,C=u,i|0}function D3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0;b=C,C=C+48|0,h=b+20|0,k=b,n=h,o=n+20|0;do e[n>>1]=e[i>>1]|0,n=n+2|0,i=i+2|0;while((n|0)<(o|0));i=e[h+18>>1]|0,w=(i&65535)-((i&65535)>>>15&65535)|0;e:do if(((w<<16>>31^w)&65535)<<16>>16<=4095){for(o=9,w=9;;){i=i<<16>>16,i=(i<<19>>19|0)==(i|0)?i<<3:i>>>15^32767,m=r+(o<<1)|0,e[m>>1]=i,i=i<<16>>16,i=g(i,i)|0,(i|0)==1073741824?(a[t>>2]=1,n=2147483647):n=i<<1,i=2147483647-n|0,(i&n|0)<0&&(a[t>>2]=1,i=2147483647),c=B1(i)|0,d=15-(c&65535)&65535,s=c<<16>>16,c<<16>>16>0?(n=i<<s,(n>>s|0)!=(i|0)&&(n=i>>31^2147483647)):(n=0-s<<16,(n|0)<2031616?n=i>>(n>>16):n=0),n=r0(16384,S1(n,t)|0)|0;do if(w<<16>>16>0){for(c=o+-1|0,f=n<<16>>16,l=w<<16>>16,u=0;;){if(o=D[h+(u<<1)>>1]|0,i=o<<16,s=g(e[h+(c-u<<1)>>1]|0,e[m>>1]|0)|0,(s|0)==1073741824?(a[t>>2]=1,n=2147483647):n=s<<1,s=i-n|0,((s^i)&(n^i)|0)<0&&(a[t>>2]=1,s=(o>>>15)+2147483647|0),s=g((S1(s,t)|0)<<16>>16,f)|0,(s|0)==1073741824?(a[t>>2]=1,s=2147483647):s=s<<1,s=j5(s,d,t)|0,n=s-(s>>>31)|0,(n>>31^n|0)>32767){s=24;break}if(e[k+(u<<1)>>1]=s,u=u+1|0,(l|0)<=(u|0)){s=26;break}}if((s|0)==24){s=0,n=r,o=n+20|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(o|0));i=10}else if((s|0)==26)if(s=0,w<<16>>16>0)i=w;else{s=28;break}n=i+-1<<16>>16,R0(h|0,k|0,((n&65535)<<1)+2|0)|0,o=n<<16>>16}else s=28;while(!1);if((s|0)==28)if(i=w+-1<<16>>16,i<<16>>16>-1)o=i<<16>>16,n=32767;else break;if(i=e[h+(o<<1)>>1]|0,w=(i&65535)-((i&65535)>>>15&65535)|0,((w<<16>>31^w)&65535)<<16>>16>4095)break e;w=n}C=b;return}while(!1);n=r,o=n+20|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(o|0));C=b}function A3(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,s=0,f=0;if(r<<16>>16<=0)return i=0,i|0;o=a[i>>2]|0,n=0,t=0;do f=o&1,t=f|t<<1&131070,s=o>>1,o=(f|0)==(o>>>28&1|0)?s:s|1073741824,n=n+1<<16>>16;while(n<<16>>16<r<<16>>16);return a[i>>2]=o,f=t&65535,f|0}function i5(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0;n=r,o=n+80|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(o|0));o=0,n=a[i>>2]|0;do u=n&1,l=n>>1,l=(u|0)==(n>>>28&1|0)?l:l|1073741824,s=l&1,f=l>>1,a[i>>2]=(s|0)==(l>>>28&1|0)?f:f|1073741824,s=n1((g(u<<1|s,1310720)|0)>>>17&65535,o,t)|0,u=a[i>>2]|0,f=u&1,l=u>>1,n=(f|0)==(u>>>28&1|0)?l:l|1073741824,a[i>>2]=n,e[r+(s<<16>>16<<1)>>1]=((f&65535)<<13&65535)+-4096<<16>>16,o=o+1<<16>>16;while(o<<16>>16<10)}function M3(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0;if(f=e[i>>1]|0,(f*31821|0)==1073741824?(a[s>>2]=1,l=1073741823):l=f*63642>>1,f=l+13849|0,(l|0)>-1&(f^l|0)<0&&(a[s>>2]=1,f=(l>>>31)+2147483647|0),e[i>>1]=f,!(r<<16>>16<=0))for(l=0,f=n+((f&127)<<1)|0;e[o+(l<<1)>>1]=(-65536<<e[t+(l<<1)>>1]>>>16^65535)&D[f>>1],l=l+1|0,(l&65535)<<16>>16!=r<<16>>16;)f=f+2|0}function y6(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+122|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function P3(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0;for(l=159,f=0;;)if(c=e[t+(l<<1)>>1]|0,c=g(c,c)|0,c=(c|0)==1073741824?2147483647:c<<1,s=c+f|0,(c^f|0)>-1&(s^f|0)<0?(a[n>>2]=1,f=(f>>>31)+2147483647|0):f=s,(l|0)>0)l=l+-1|0;else{l=f;break}for(n=l>>>14&65535,f=32767,s=59;c=e[i+(s<<1)>>1]|0,f=c<<16>>16<f<<16>>16?c:f,(s|0)>0;)s=s+-1|0;for(c=(l|0)>536870911?32767:n,n=f<<16>>16,s=n<<20>>16,l=f<<16>>16>0?32767:-32768,t=55,f=e[i>>1]|0;u=e[i+(t<<1)>>1]|0,f=f<<16>>16<u<<16>>16?u:f,(t|0)>1;)t=t+-1|0;t=e[i+80>>1]|0,u=e[i+82>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+84>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+86>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+88>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+90>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+92>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+94>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+96>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+98>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+100>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+102>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+104>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+106>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+108>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+110>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+112>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+114>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=e[i+116>>1]|0,t=t<<16>>16<u<<16>>16?u:t,u=i+118|0,m=e[u>>1]|0;do if((c+-21&65535)<17557&f<<16>>16>20&&((c<<16>>16|0)<(((n<<4|0)==(s|0)?s:l)|0)||(t<<16>>16<m<<16>>16?m:t)<<16>>16<1953))if(f=i+120|0,s=e[f>>1]|0,s<<16>>16>29){e[f>>1]=30,t=f,l=1;break}else{l=(s&65535)+1&65535,e[f>>1]=l,t=f,l=l<<16>>16>1&1;break}else d=14;while(!1);(d|0)==14&&(t=i+120|0,e[t>>1]=0,l=0),f=0;do m=f,f=f+1|0,e[i+(m<<1)>>1]=e[i+(f<<1)>>1]|0;while((f|0)!=59);return e[u>>1]=c,f=e[t>>1]|0,f=f<<16>>16>15?16383:f<<16>>16>8?15565:13926,s=k2(r+8|0,5)|0,(e[t>>1]|0)>20?((k2(r,9)|0)<<16>>16|0)>(f|0)&&(d=20):(s<<16>>16|0)>(f|0)&&(d=20),(d|0)==20?(e[o>>1]=0,l|0):(s=(D[o>>1]|0)+1&65535,s<<16>>16>10?(e[o>>1]=10,l|0):(e[o>>1]=s,l|0))}function _5(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+18|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function N3(i,r,t,o,n,s,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0;A=i+2|0,e[i>>1]=e[A>>1]|0,I=i+4|0,e[A>>1]=e[I>>1]|0,F=i+6|0,e[I>>1]=e[F>>1]|0,B=i+8|0,e[F>>1]=e[B>>1]|0,M=i+10|0,e[B>>1]=e[M>>1]|0,T=i+12|0,e[M>>1]=e[T>>1]|0,e[T>>1]=t,E=0,N=0;do{w=n+(N<<1)|0,k=y1(e[w>>1]|0,e[o+(N<<1)>>1]|0,m)|0,k=(k&65535)-((k&65535)>>>15&65535)|0,k=k<<16>>31^k,O=((P2(k&65535)|0)&65535)+65535|0,h=O<<16>>16,(O&65535)<<16>>16<0?(b=0-h<<16,(b|0)<983040?R=k<<16>>16>>(b>>16)&65535:R=0):(b=k<<16>>16,k=b<<h,(k<<16>>16>>h|0)==(b|0)?R=k&65535:R=(b>>>15^32767)&65535),S=P2(e[w>>1]|0)|0,k=e[w>>1]|0,h=S<<16>>16,S<<16>>16<0?(b=0-h<<16,(b|0)<983040?b=k<<16>>16>>(b>>16)&65535:b=0):(b=k<<16>>16,k=b<<h,(k<<16>>16>>h|0)==(b|0)?b=k&65535:b=(b>>>15^32767)&65535),h=r0(R,b)|0,b=(O&65535)+2-(S&65535)|0,k=b&65535;do if(b&32768){if(k<<16>>16!=-32768){if(O=0-b|0,b=O<<16>>16,(O&65535)<<16>>16<0){if(b=0-b<<16,(b|0)>=983040){b=0;break}b=h<<16>>16>>(b>>16)&65535;break}}else b=32767;k=h<<16>>16,h=k<<b,(h<<16>>16>>b|0)==(k|0)?b=h&65535:b=(k>>>15^32767)&65535}else b=X1(h,k,m)|0;while(!1);E=n1(E,b,m)|0,N=N+1|0}while((N|0)!=10);switch(b=E&65535,k=E<<16>>16>5325,E=i+14|0,k?(n=(D[E>>1]|0)+1&65535,e[E>>1]=n,n<<16>>16>10&&(e[i+16>>1]=0)):e[E>>1]=0,r|0){case 0:case 1:case 2:case 3:case 6:break;default:return T=i+16|0,m=t,t=e[T>>1]|0,t=t&65535,t=t+1|0,t=t&65535,e[T>>1]=t,m|0}return R=(f|s)<<16>>16==0,S=c<<16>>16==0,O=r>>>0<3,E=b+(O&((S|(R&(l<<16>>16==0|u<<16>>16==0)|d<<16>>16<2))^1)?61030:62259)&65535,E=E<<16>>16>0?E:0,E<<16>>16<=2048?(E=E<<16>>16,(E<<18>>18|0)==(E|0)?u=E<<2:u=E>>>15^32767):u=8192,l=i+16|0,d=k|(e[l>>1]|0)<40,E=e[I>>1]|0,(E*6554|0)==1073741824?(a[m>>2]=1,k=2147483647):k=E*13108|0,E=e[F>>1]|0,b=E*6554|0,(b|0)!=1073741824?(E=(E*13108|0)+k|0,(b^k|0)>0&(E^k|0)<0&&(a[m>>2]=1,E=(k>>>31)+2147483647|0)):(a[m>>2]=1,E=2147483647),b=e[B>>1]|0,k=b*6554|0,(k|0)!=1073741824?(b=(b*13108|0)+E|0,(k^E|0)>0&(b^E|0)<0&&(a[m>>2]=1,b=(E>>>31)+2147483647|0)):(a[m>>2]=1,b=2147483647),E=e[M>>1]|0,k=E*6554|0,(k|0)!=1073741824?(E=(E*13108|0)+b|0,(k^b|0)>0&(E^b|0)<0?(a[m>>2]=1,k=(b>>>31)+2147483647|0):k=E):(a[m>>2]=1,k=2147483647),E=e[T>>1]|0,b=E*6554|0,(b|0)!=1073741824?(E=(E*13108|0)+k|0,(b^k|0)>0&(E^k|0)<0&&(a[m>>2]=1,E=(k>>>31)+2147483647|0)):(a[m>>2]=1,E=2147483647),k=S1(E,m)|0,O&((R|S)^1)&&(E=e[i>>1]|0,(E*4681|0)==1073741824?(a[m>>2]=1,k=2147483647):k=E*9362|0,E=e[A>>1]|0,b=E*4681|0,(b|0)!=1073741824?(E=(E*9362|0)+k|0,(b^k|0)>0&(E^k|0)<0?(a[m>>2]=1,k=(k>>>31)+2147483647|0):k=E):(a[m>>2]=1,k=2147483647),E=e[I>>1]|0,b=E*4681|0,(b|0)!=1073741824?(E=(E*9362|0)+k|0,(b^k|0)>0&(E^k|0)<0?(a[m>>2]=1,k=(k>>>31)+2147483647|0):k=E):(a[m>>2]=1,k=2147483647),E=e[F>>1]|0,b=E*4681|0,(b|0)!=1073741824?(E=(E*9362|0)+k|0,(b^k|0)>0&(E^k|0)<0&&(a[m>>2]=1,E=(k>>>31)+2147483647|0)):(a[m>>2]=1,E=2147483647),b=e[B>>1]|0,k=b*4681|0,(k|0)!=1073741824?(b=(b*9362|0)+E|0,(k^E|0)>0&(b^E|0)<0?(a[m>>2]=1,E=(E>>>31)+2147483647|0):E=b):(a[m>>2]=1,E=2147483647),b=e[M>>1]|0,k=b*4681|0,(k|0)!=1073741824?(b=(b*9362|0)+E|0,(k^E|0)>0&(b^E|0)<0&&(a[m>>2]=1,b=(E>>>31)+2147483647|0)):(a[m>>2]=1,b=2147483647),k=e[T>>1]|0,w=k*4681|0,(w|0)!=1073741824?(h=(k*9362|0)+b|0,(w^b|0)>0&(h^b|0)<0&&(a[m>>2]=1,h=(b>>>31)+2147483647|0)):(a[m>>2]=1,h=2147483647),k=S1(h,m)|0),E=d?8192:u<<16>>16,w=g(E,t<<16>>16)|0,(w|0)==1073741824?(a[m>>2]=1,b=2147483647):b=w<<1,k=k<<16>>16,h=k<<13,(h|0)!=1073741824?(w=b+(k<<14)|0,(b^h|0)>0&(w^b|0)<0?(a[m>>2]=1,b=(b>>>31)+2147483647|0):b=w):(a[m>>2]=1,b=2147483647),w=g(k,E)|0,(w|0)==1073741824?(a[m>>2]=1,h=2147483647):h=w<<1,w=b-h|0,((w^b)&(h^b)|0)<0&&(a[m>>2]=1,w=(b>>>31)+2147483647|0),T=w<<2,t=l,m=S1((T>>2|0)==(w|0)?T:w>>31^2147483647,m)|0,T=e[t>>1]|0,T=T&65535,T=T+1|0,T=T&65535,e[t>>1]=T,m|0}function O3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0;o=r,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));o=0;do f=e[i+(o<<1)>>1]|0,n=((f&8)<<10&65535^8192)+-4096<<16>>16,s=o<<16,f=((e[t+((f&7)<<1)>>1]|0)*327680|0)+s>>16,e[r+(f<<1)>>1]=n,s=((e[t+((D[i+(o+5<<1)>>1]&7)<<1)>>1]|0)*327680|0)+s>>16,(s|0)<(f|0)&&(n=0-(n&65535)&65535),f=r+(s<<1)|0,e[f>>1]=(D[f>>1]|0)+(n&65535),o=o+1|0;while((o|0)!=5)}function C3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;n=r<<16>>16,o=(n<<1&2|1)+((n>>>1&7)*5|0)|0,r=n>>>4&3,r=((n>>>6&7)*5|0)+((r|0)==3?4:r)|0,n=t,s=n+80|0;do e[n>>1]=0,n=n+2|0;while((n|0)<(s|0));i=i<<16>>16,e[t+(o<<1)>>1]=(0-(i&1)&16383)+57344,e[t+(r<<1)>>1]=(0-(i>>>1&1)&16383)+57344}function L3(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0;s=t<<16>>16,l=s>>>3,i=i<<16>>16,i=((i<<17>>17|0)==(i|0)?i<<1:i>>>15^32767)+(l&8)<<16,l=(D[o+(i+65536>>16<<1)>>1]|0)+((l&7)*5|0)|0,t=r<<16>>16,f=(0-(t&1)&16383)+57344&65535,i=n+((D[o+(i>>16<<1)>>1]|0)+((s&7)*5|0)<<16>>16<<1)|0,r=n,s=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(s|0));e[i>>1]=f,e[n+(l<<16>>16<<1)>>1]=(0-(t>>>1&1)&16383)+57344}function T3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0;r=r<<16>>16,o=(r&7)*5|0,n=(r>>>2&2|1)+((r>>>4&7)*5|0)|0,r=(r>>>6&2)+2+((r>>>8&7)*5|0)|0,s=t,f=s+80|0;do e[s>>1]=0,s=s+2|0;while((s|0)<(f|0));i=i<<16>>16,e[t+(o<<1)>>1]=(0-(i&1)&16383)+57344,e[t+(n<<1)>>1]=(0-(i>>>1&1)&16383)+57344,e[t+(r<<1)>>1]=(0-(i>>>2&1)&16383)+57344}function I3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0;r=r<<16>>16,f=e[t+((r&7)<<1)>>1]|0,l=e[t+((r>>>3&7)<<1)>>1]|0,s=e[t+((r>>>6&7)<<1)>>1]|0,t=(r>>>9&1)+3+((e[t+((r>>>10&7)<<1)>>1]|0)*5|0)|0,r=o,n=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(n|0));i=i<<16>>16,e[o+(f*327680>>16<<1)>>1]=(0-(i&1)&16383)+57344,e[o+((l*327680|0)+65536>>16<<1)>>1]=(0-(i>>>1&1)&16383)+57344,e[o+((s*327680|0)+131072>>16<<1)>>1]=(0-(i>>>2&1)&16383)+57344,e[o+(t<<16>>16<<1)>>1]=(0-(i>>>3&1)&16383)+57344}function F3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0;w=C,C=C+32|0,m=w+16|0,d=w,s=r,n=s+80|0;do e[s>>1]=0,s=s+2|0;while((s|0)<(n|0));n=e[i>>1]|0,e[m>>1]=n,e[m+2>>1]=e[i+2>>1]|0,e[m+4>>1]=e[i+4>>1]|0,e[m+6>>1]=e[i+6>>1]|0,u=e[i+8>>1]|0,g6(u>>>3&65535,u&7,0,4,1,d,t),u=e[i+10>>1]|0,g6(u>>>3&65535,u&7,2,6,5,d,t),u=e[i+12>>1]|0,o=u>>2;do if((o*25|0)!=1073741824){if(s=(g(o,1638400)|0)+786432>>21,o=s*6554>>15,(o|0)>32767){a[t>>2]=1,f=1,l=1,i=163835,c=6;break}i=(o<<16>>16)*5|0,f=o&1,(i|0)==1073741824?(a[t>>2]=1,l=0,i=65535):(l=0,c=6)}else a[t>>2]=1,f=0,o=0,l=0,s=0,i=0,c=6;while(!1);for((c|0)==6&&(i=i&65535),c=s-i|0,f=f<<16>>16?4-c|0:c,c=f<<16>>16,e[d+6>>1]=n1(((f<<17>>17|0)==(c|0)?f<<1:c>>>15^32767)&65535,u&1,t)|0,l&&(a[t>>2]=1,o=32767),c=o<<16>>16,e[d+14>>1]=((o<<17>>17|0)==(c|0)?o<<1:c>>>15^32767)+(u>>>1&1),o=0;n=n<<16>>16?-8191:8191,c=(e[d+(o<<1)>>1]<<2)+o<<16,s=c>>16,(c|0)<2621440&&(e[r+(s<<1)>>1]=n),f=(e[d+(o+4<<1)>>1]<<2)+o<<16,i=f>>16,(i|0)<(s|0)&&(n=0-(n&65535)&65535),(f|0)<2621440&&(c=r+(i<<1)|0,e[c>>1]=(D[c>>1]|0)+(n&65535)),o=o+1|0,(o|0)!=4;)n=e[m+(o<<1)>>1]|0;C=w}function g6(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0;u=i<<16>>16>124?124:i,i=(u<<16>>16)*1311>>15,h=(i|0)>32767,h?(a[f>>2]=1,l=32767,w=4):(l=i<<16>>16,(l*25|0)==1073741824?(a[f>>2]=1,l=1073741823):w=4),(w|0)==4&&(l=(l*50|0)>>>1),d=(u&65535)-l|0,l=(d<<16>>16)*6554>>15,m=(l|0)>32767,m?(a[f>>2]=1,u=32767,w=9):(u=l<<16>>16,(u*5|0)==1073741824?(a[f>>2]=1,c=1073741823):w=9),(w|0)==9&&(c=(u*10|0)>>>1),d=d-c|0,w=d<<16>>16,u=r<<16>>16,c=u>>2,u=u-(c<<2)|0,e[s+(t<<16>>16<<1)>>1]=((d<<17>>17|0)==(w|0)?d<<1:w>>>15^32767)+(u&1),m&&(a[f>>2]=1,l=32767),t=l<<16>>16,e[s+(o<<16>>16<<1)>>1]=((l<<17>>17|0)==(t|0)?l<<1:t>>>15^32767)+(u<<16>>17),h&&(a[f>>2]=1,i=32767),o=i<<16>>16,e[s+(n<<16>>16<<1)>>1]=n1(c&65535,((i<<17>>17|0)==(o|0)?i<<1:o>>>15^32767)&65535,f)|0}function B3(i){i=i|0;var r=0,t=0,o=0,n=0;if(!i)return n=-1,n|0;o4(i+1168|0),e[i+460>>1]=40,a[i+1164>>2]=0,r=i+646|0,t=i+1216|0,o=i+462|0,n=o+22|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));return S5(r,a[t>>2]|0)|0,A5(i+686|0)|0,D5(i+700|0)|0,_5(i+608|0)|0,M6(i+626|0,a[t>>2]|0)|0,y6(i+484|0)|0,N6(i+730|0)|0,A6(i+748|0)|0,n2(i+714|0)|0,R5(i,0)|0,n=0,n|0}function R5(i,r){i=i|0,r=r|0;var t=0,o=0;if(!i)return i=-1,i|0;if(a[i+388>>2]=i+308,t2(i|0,0,308)|0,r=(r|0)!=8,r){t=i+412|0,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));e[i+392>>1]=3e4,e[i+394>>1]=26e3,e[i+396>>1]=21e3,e[i+398>>1]=15e3,e[i+400>>1]=8e3,e[i+402>>1]=0,e[i+404>>1]=-8e3,e[i+406>>1]=-15e3,e[i+408>>1]=-21e3,e[i+410>>1]=-26e3}if(e[i+432>>1]=0,e[i+434>>1]=40,a[i+1164>>2]=0,e[i+436>>1]=0,e[i+438>>1]=0,e[i+440>>1]=0,e[i+460>>1]=40,e[i+462>>1]=0,e[i+464>>1]=0,r){t=i+442|0,o=t+18|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));t=i+466|0,o=t+18|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));_5(i+608|0)|0,o=i+1216|0,M6(i+626|0,a[o>>2]|0)|0,S5(i+646|0,a[o>>2]|0)|0,A5(i+686|0)|0,D5(i+700|0)|0,n2(i+714|0)|0}else{t=i+466|0,o=t+18|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));_5(i+608|0)|0,S5(i+646|0,a[i+1216>>2]|0)|0,A5(i+686|0)|0,D5(i+700|0)|0}return y6(i+484|0)|0,e[i+606>>1]=21845,N6(i+730|0)|0,r?(A6(i+748|0)|0,i=0,i|0):(i=0,i|0)}function x3(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0,E1=0,T1=0,A1=0,j1=0,x1=0,L1=0,K1=0,U1=0,n0=0,z1=0,Z1=0,s0=0,e0=0,q1=0,h0=0,j0=0,Y0=0,k0=0,l0=0,a0=0,O1=0,w0=0,b2=0,v2=0,C0=0,a2=0,d4=0,h4=0,O2=0,w4=0,W5=0,W2=0,X5=0,C2=0,N0=0,m0=0,p1=0,u5=0,X2=0,Y5=0,G5=0,U0=0,E2=0,S0=0,G0=0,L2=0,b0=0,Y1=0,K5=0,Z5=0,L0=0,D0=0;if(D0=C,C=C+336|0,w=D0+236|0,m=D0+216|0,Z5=D0+112|0,K5=D0+12|0,S0=D0+256|0,L2=D0+136|0,G0=D0+32|0,U0=D0+8|0,E2=D0+6|0,Y1=D0+4|0,b0=D0+2|0,L0=D0,p1=i+1164|0,u5=i+748|0,X2=W3(u5,o,p1)|0,X2){R5(i,8)|0,H3(u5,i+412|0,i+646|0,i+714|0,i+608|0,X2,r,t,i+1168|0,n,s,p1),L0=i+666|0,p0(L0,i+392|0,10,p1),P6(i+626|0,L0,p1),L0=i+1156|0,a[L0>>2]=X2,C=D0;return}switch(o|0){case 1:{f=1,S=6;break}case 2:case 7:{M3(i+606|0,e[(a[i+1256>>2]|0)+(r<<1)>>1]|0,a[(a[i+1260>>2]|0)+(r<<2)>>2]|0,t,a[i+1276>>2]|0,p1),S=9;break}case 3:{S=9;break}default:f=0,S=6}do if((S|0)==6)if(o=i+440|0,(e[o>>1]|0)==6){e[o>>1]=5,N0=0,m0=0;break}else{e[o>>1]=0,N0=0,m0=0;break}else(S|0)==9&&(o=i+440|0,N0=(D[o>>1]|0)+1&65535,e[o>>1]=N0<<16>>16>6?6:N0,N0=1,m0=1,f=0);while(!1);switch(W5=i+1156|0,a[W5>>2]|0){case 1:{e[o>>1]=5,e[i+436>>1]=0;break}case 2:{e[o>>1]=5,e[i+436>>1]=1;break}default:}u=i+646|0,W2=i+666|0,l=Z5,c=W2,d=l+20|0;do _[l>>0]=_[c>>0]|0,l=l+1|0,c=c+1|0;while((l|0)<(d|0));X5=(r|0)!=7,C2=i+1168|0,X5?(D6(u,r,m0,t,C2,w,p1),l=i+392|0,s4(l,w,s,p1),t=t+6|0):(q3(u,m0,t,C2,m,w,p1),l=i+392|0,n4(l,m,w,s,p1),t=t+10|0),c=w,d=l+20|0;do e[l>>1]=e[c>>1]|0,l=l+2|0,c=c+2|0;while((l|0)<(d|0));for(w4=r>>>0>1,I=r>>>0<4&1,O2=(r|0)==5,h4=O2?10:5,O2=O2?19:9,M=i+434|0,T=143-O2&65535,z=i+460|0,j=i+462|0,Y=i+464|0,F=r>>>0>2,K=i+388|0,H=(r|0)==0,U=r>>>0<2,q=i+1244|0,i1=i+432|0,W=r>>>0<6,o1=i+1168|0,a1=(r|0)==6,u1=m0<<16>>16==0,d1=i+714|0,c1=i+686|0,t1=i+436|0,l1=i+700|0,w1=(r|0)==7,m1=i+482|0,Z=r>>>0<3,V=i+608|0,E1=i+626|0,T1=i+438|0,A1=r>>>0<7,j1=i+730|0,B=N0^1,x1=f<<16>>16!=0,d4=x1?m0^1:0,L1=i+442|0,K1=i+458|0,U1=i+412|0,n0=i+80|0,z1=i+1236|0,Z1=i+1240|0,s0=i+468|0,e0=i+466|0,q1=i+470|0,h0=i+472|0,j0=i+474|0,Y0=i+476|0,k0=i+478|0,l0=i+480|0,a0=i+444|0,O1=i+446|0,w0=i+448|0,b2=i+450|0,v2=i+452|0,C0=i+454|0,a2=i+456|0,O=0,N=0,h=0,k=0,A=-1;;){A=(A<<16>>16)+1|0,d=A&65535,N=1-(N<<16>>16)|0,E=N&65535,m=w4&h<<16>>16==80?0:h,b=t+2|0,w=e[t>>1]|0;e:do if(X5){if(R=e[M>>1]|0,l=(R&65535)-h4&65535,l=l<<16>>16<20?20:l,c=(l&65535)+O2&65535,u=c<<16>>16>143,U3(w,u?T:l,u?143:c,m,R,U0,E2,I,p1),m=e[U0>>1]|0,e[z>>1]=m,N0?(w=e[M>>1]|0,w<<16>>16<143&&(w=(w&65535)+1&65535,e[M>>1]=w),e[U0>>1]=w,e[E2>>1]=0,e[j>>1]|0&&!(F|(e[Y>>1]|0)<5)&&(e[U0>>1]=m,w=m),m=0):(w=m,m=e[E2>>1]|0),H2(a[K>>2]|0,w,m,40,1,p1),U){if(m=t+6|0,L3(d,e[t+4>>1]|0,e[b>>1]|0,a[q>>2]|0,S0,p1),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=H;break}c=H,w=t<<16>>16>0?32767:-32768;break}switch(r|0){case 2:{if(m=t+6|0,C3(e[t+4>>1]|0,e[b>>1]|0,S0),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=H;break e}c=H,w=t<<16>>16>0?32767:-32768;break e}case 3:{if(m=t+6|0,T3(e[t+4>>1]|0,e[b>>1]|0,S0),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=H;break e}c=H,w=t<<16>>16>0?32767:-32768;break e}default:{if(W){if(m=t+6|0,I3(e[t+4>>1]|0,e[b>>1]|0,a[o1>>2]|0,S0),t=e[i1>>1]|0,R=t<<16>>16,w=R<<1,(w|0)==(R<<17>>16|0)){c=H;break e}c=H,w=t<<16>>16>0?32767:-32768;break e}if(!a1){c=H,S=44;break e}if(F3(b,S0,p1),w=t+16|0,t=e[i1>>1]|0,R=t<<16>>16,d=R<<1,(d|0)==(R<<17>>16|0)){m=w,c=H,w=d;break e}m=w,c=H,w=t<<16>>16>0?32767:-32768;break e}}}else z3(w,18,143,m,U0,E2,p1),u1&&m<<16>>16==0|w<<16>>16<61?(w=e[U0>>1]|0,m=e[E2>>1]|0):(e[z>>1]=e[U0>>1]|0,w=e[M>>1]|0,e[U0>>1]=w,e[E2>>1]=0,m=0),H2(a[K>>2]|0,w,m,40,0,p1),c=0,S=44;while(!1);(S|0)==44&&(S=0,N0?o5(c1,e[o>>1]|0,Y1,p1):e[Y1>>1]=S6(r,e[b>>1]|0,a[Z1>>2]|0)|0,n5(c1,m0,e[t1>>1]|0,Y1,p1),O3(t+4|0,S0,a[o1>>2]|0),w=t+24|0,t=e[Y1>>1]|0,R=t<<16>>16,d=R<<1,(d|0)==(R<<17>>16|0)?(m=w,w=d):(m=w,w=t<<16>>16>0?32767:-32768)),t=e[U0>>1]|0;e:do if(t<<16>>16<40)for(l=w<<16>>16,u=t,w=t<<16>>16;;){if(d=S0+(w<<1)|0,t=(g(e[S0+(w-(u<<16>>16)<<1)>>1]|0,l)|0)>>15,(t|0)>32767&&(a[p1>>2]=1,t=32767),R=t&65535,e[L0>>1]=R,e[d>>1]=n1(e[d>>1]|0,R,p1)|0,w=w+1|0,(w&65535)<<16>>16==40)break e;u=e[U0>>1]|0}while(!1);e:do if(c)c=(N&65535|0)==0,c?(t=m,d=k):(t=m+2|0,d=e[m>>1]|0),u1?_6(d1,r,d,S0,E,Y1,b0,C2,p1):(o5(c1,e[o>>1]|0,Y1,p1),t5(l1,d1,e[o>>1]|0,b0,p1)),n5(c1,m0,e[t1>>1]|0,Y1,p1),r5(l1,m0,e[t1>>1]|0,b0,p1),m=e[Y1>>1]|0,w=m<<16>>16>13017?13017:m,c?S=80:R=d;else switch(t=m+2|0,w=e[m>>1]|0,r|0){case 1:case 2:case 3:case 4:case 6:{if(u1?_6(d1,r,w,S0,E,Y1,b0,C2,p1):(o5(c1,e[o>>1]|0,Y1,p1),t5(l1,d1,e[o>>1]|0,b0,p1)),n5(c1,m0,e[t1>>1]|0,Y1,p1),r5(l1,m0,e[t1>>1]|0,b0,p1),m=e[Y1>>1]|0,w=m<<16>>16>13017?13017:m,!a1){d=k,S=80;break e}if((e[M>>1]|0)<=45){d=k,S=80;break e}d=k,w=w<<16>>16>>>2&65535,S=80;break e}case 5:{N0?o5(c1,e[o>>1]|0,Y1,p1):e[Y1>>1]=S6(5,w,a[Z1>>2]|0)|0,n5(c1,m0,e[t1>>1]|0,Y1,p1),u1?R6(d1,5,e[t>>1]|0,S0,a[z1>>2]|0,b0,p1):t5(l1,d1,e[o>>1]|0,b0,p1),r5(l1,m0,e[t1>>1]|0,b0,p1),w=e[Y1>>1]|0,t=m+4|0,m=w,d=k,w=w<<16>>16>13017?13017:w,S=80;break e}default:{u1?R6(d1,r,w,S0,a[z1>>2]|0,b0,p1):t5(l1,d1,e[o>>1]|0,b0,p1),r5(l1,m0,e[t1>>1]|0,b0,p1),w=e[Y1>>1]|0,m=w,d=k,S=80;break e}}while(!1);(S|0)==80&&(S=0,e[i1>>1]=m<<16>>16>13017?13017:m,R=d),w=w<<16>>16,w=(w<<17>>17|0)==(w|0)?w<<1:w>>>15^32767,E=(w&65535)<<16>>16>16384;e:do if(E){if(b=w<<16>>16,w1)m=0;else for(m=0;;)if(w=(g(e[(a[K>>2]|0)+(m<<1)>>1]|0,b)|0)>>15,(w|0)>32767&&(a[p1>>2]=1,w=32767),e[L0>>1]=w,w=g(e[Y1>>1]|0,w<<16>>16)|0,(w|0)==1073741824?(a[p1>>2]=1,w=2147483647):w=w<<1,e[L2+(m<<1)>>1]=S1(w,p1)|0,m=m+1|0,(m|0)==40)break e;do w=(g(e[(a[K>>2]|0)+(m<<1)>>1]|0,b)|0)>>15,(w|0)>32767&&(a[p1>>2]=1,w=32767),e[L0>>1]=w,w=g(e[Y1>>1]|0,w<<16>>16)|0,(w|0)!=1073741824?(w=w<<1,(w|0)<0?w=~((w^-2)>>1):S=88):(a[p1>>2]=1,w=2147483647,S=88),(S|0)==88&&(S=0,w=w>>1),e[L2+(m<<1)>>1]=S1(w,p1)|0,m=m+1|0;while((m|0)!=40)}while(!1);switch(u1&&(e[e0>>1]=e[s0>>1]|0,e[s0>>1]=e[q1>>1]|0,e[q1>>1]=e[h0>>1]|0,e[h0>>1]=e[j0>>1]|0,e[j0>>1]=e[Y0>>1]|0,e[Y0>>1]=e[k0>>1]|0,e[k0>>1]=e[l0>>1]|0,e[l0>>1]=e[m1>>1]|0,e[m1>>1]=e[Y1>>1]|0),N0|(e[t1>>1]|0)!=0&&Z&(e[j>>1]|0)!=0&&(Y5=e[Y1>>1]|0,Y5<<16>>16>12288)&&(S=(((Y5<<16>>16)+118784|0)>>>1)+12288&65535,e[Y1>>1]=S<<16>>16>14745?14745:S),G3(Z5,W2,h,K5,p1),w=N3(V,r,e[b0>>1]|0,K5,E1,m0,e[t1>>1]|0,f,e[T1>>1]|0,e[j>>1]|0,e[Y>>1]|0,p1)|0,r|0){case 0:case 1:case 2:case 3:case 6:{d=e[Y1>>1]|0,b=1;break}default:w=e[b0>>1]|0,d=e[Y1>>1]|0,A1?b=1:(m=d<<16>>16,d<<16>>16<0?m=~((m^-2)>>1):m=m>>>1,d=m&65535,b=2)}l=d<<16>>16,h=b&65535,m=a[K>>2]|0,k=0;do m=m+(k<<1)|0,e[G0+(k<<1)>>1]=e[m>>1]|0,m=g(e[m>>1]|0,l)|0,(m|0)==1073741824?(a[p1>>2]=1,u=2147483647):u=m<<1,c=g(e[b0>>1]|0,e[S0+(k<<1)>>1]|0)|0,(c|0)!=1073741824?(m=(c<<1)+u|0,(c^u|0)>0&(m^u|0)<0&&(a[p1>>2]=1,m=(u>>>31)+2147483647|0)):(a[p1>>2]=1,m=2147483647),S=m<<h,S=S1((S>>h|0)==(m|0)?S:m>>31^2147483647,p1)|0,m=a[K>>2]|0,e[m+(k<<1)>>1]=S,k=k+1|0;while((k|0)!=40);Z3(j1),Z&&(e[Y>>1]|0)>3&&!((e[j>>1]|0)==0|B)&&K3(j1),Q3(j1,r,G0,w,e[Y1>>1]|0,S0,d,b,C2,p1),w=0,c=0;do m=e[G0+(c<<1)>>1]|0,m=g(m,m)|0,(m|0)!=1073741824?(d=(m<<1)+w|0,(m^w|0)>0&(d^w|0)<0?(a[p1>>2]=1,w=(w>>>31)+2147483647|0):w=d):(a[p1>>2]=1,w=2147483647),c=c+1|0;while((c|0)!=40);(w|0)<0?w=~((w^-2)>>1):w=w>>1,w=V5(w,L0,p1)|0,d=((e[L0>>1]|0)>>>1)+15|0,m=d&65535,d=d<<16>>16,m<<16>>16>0?m<<16>>16<31?(w=w>>d,S=135):(w=0,S=137):(b=0-d<<16>>16,S=w<<b,w=(S>>b|0)==(w|0)?S:w>>31^2147483647,S=135),(S|0)==135&&(S=0,(w|0)<0?w=~((w^-4)>>2):S=137),(S|0)==137&&(S=0,w=w>>>2),w=w&65535;do if(Z&&(G5=e[Y>>1]|0,G5<<16>>16>5))if(e[j>>1]|0)if((e[o>>1]|0)<4){if(x1?N0|(e[T1>>1]|0)!=0||(S=145):N0||(S=145),(S|0)==145&&!(e[t1>>1]|0)){S=147;break}X3(G0,w,L1,G5,e[t1>>1]|0,d4,p1)|0,S=147}else S=147;else S=151;else S=147;while(!1);do if((S|0)==147)if(S=0,e[j>>1]|0){if(!N0&&!(e[t1>>1]|0)){S=151;break}(e[o>>1]|0)>=4&&(S=151)}else S=151;while(!1);if((S|0)==151&&(S=0,e[L1>>1]=e[a0>>1]|0,e[a0>>1]=e[O1>>1]|0,e[O1>>1]=e[w0>>1]|0,e[w0>>1]=e[b2>>1]|0,e[b2>>1]=e[v2>>1]|0,e[v2>>1]=e[C0>>1]|0,e[C0>>1]=e[a2>>1]|0,e[a2>>1]=e[K1>>1]|0,e[K1>>1]=w),E){w=0;do E=L2+(w<<1)|0,e[E>>1]=n1(e[E>>1]|0,e[G0+(w<<1)>>1]|0,p1)|0,w=w+1|0;while((w|0)!=40);R3(G0,L2,40,p1),a[p1>>2]=0,f0(s,L2,n+(O<<1)|0,40,U1,0)}else a[p1>>2]=0,f0(s,G0,n+(O<<1)|0,40,U1,0);if(!(a[p1>>2]|0))i2(U1|0,n+(O+30<<1)|0,20)|0;else{for(d=193;;)if(m=i+(d<<1)|0,E=e[m>>1]|0,w=E<<16>>16,E<<16>>16<0?w=~((w^-4)>>2):w=w>>>2,e[m>>1]=w,(d|0)>0)d=d+-1|0;else{d=39;break}for(;m=G0+(d<<1)|0,E=e[m>>1]|0,w=E<<16>>16,E<<16>>16<0?w=~((w^-4)>>2):w=w>>>2,e[m>>1]=w,(d|0)>0;)d=d+-1|0;f0(s,G0,n+(O<<1)|0,40,U1,1)}if(i2(i|0,n0|0,308)|0,e[M>>1]=e[U0>>1]|0,w=O+40|0,h=w&65535,h<<16>>16>=160)break;O=w<<16>>16,s=s+22|0,k=R}e[j>>1]=P3(i+484|0,i+466|0,n,Y,p1)|0,V3(u5,W2,n,p1),e[t1>>1]=m0,e[T1>>1]=f,P6(i+626|0,W2,p1),L0=W5,a[L0>>2]=X2,C=D0}function _6(i,r,t,o,n,s,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0;switch(h=C,C=C+16|0,m=h+2|0,w=h,t=t<<16>>16,t=(t<<18>>18|0)==(t|0)?t<<2:t>>>15^32767,r|0){case 3:case 4:case 6:{d=t<<16>>16,t=a[l+84>>2]|0,e[s>>1]=e[t+(d<<1)>>1]|0,l=e[t+(d+1<<1)>>1]|0,c=e[t+(d+3<<1)>>1]|0,s=e[t+(d+2<<1)>>1]|0;break}case 0:{l=(t&65535)+(n<<16>>16<<1^2)|0,l=(l&65535)<<16>>16>1022?1022:l<<16>>16,e[s>>1]=e[782+(l<<1)>>1]|0,s=e[782+(l+1<<1)>>1]|0,s2(s<<16>>16,w,m,u),e[w>>1]=(D[w>>1]|0)+65524,l=V2(e[m>>1]|0,5,u)|0,d=e[w>>1]|0,d=n1(l,((d<<26>>26|0)==(d|0)?d<<10:d>>>15^32767)&65535,u)|0,l=e[m>>1]|0,t=e[w>>1]|0,(t*24660|0)==1073741824?(a[u>>2]=1,n=2147483647):n=t*49320|0,c=(l<<16>>16)*24660>>15,t=n+(c<<1)|0,(n^c|0)>0&(t^n|0)<0&&(a[u>>2]=1,t=(n>>>31)+2147483647|0),c=t<<13,l=s,c=S1((c>>13|0)==(t|0)?c:t>>31^2147483647,u)|0,s=d;break}default:d=t<<16>>16,t=a[l+80>>2]|0,e[s>>1]=e[t+(d<<1)>>1]|0,l=e[t+(d+1<<1)>>1]|0,c=e[t+(d+3<<1)>>1]|0,s=e[t+(d+2<<1)>>1]|0}if(M2(i,r,o,w,m,0,0,u),n=g((x0(14,e[m>>1]|0,u)|0)<<16>>16,l<<16>>16)|0,(n|0)==1073741824?(a[u>>2]=1,t=2147483647):t=n<<1,l=10-(D[w>>1]|0)|0,n=l&65535,l=l<<16>>16,n<<16>>16>0){w=n<<16>>16<31?t>>l:0,w=w>>>16,w=w&65535,e[f>>1]=w,W0(i,s,c),C=h;return}else{u=0-l<<16>>16,w=t<<u,w=(w>>u|0)==(t|0)?w:t>>31^2147483647,w=w>>>16,w=w&65535,e[f>>1]=w,W0(i,s,c),C=h;return}}function U3(i,r,t,o,n,s,f,l,u){if(i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,!(o<<16>>16)){if(l=i<<16>>16,i<<16>>16>=197){e[s>>1]=l+65424,e[f>>1]=0;return}n=((l<<16)+131072>>16)*10923>>15,(n|0)>32767&&(a[u>>2]=1,n=32767),i=(n&65535)+19|0,e[s>>1]=i,e[f>>1]=l+58-((i*196608|0)>>>16);return}if(!(l<<16>>16)){u=i<<16>>16<<16,i=((u+131072>>16)*21846|0)+-65536>>16,e[s>>1]=i+(r&65535),e[f>>1]=((u+-131072|0)>>>16)-((i*196608|0)>>>16);return}if((y1(n,r,u)|0)<<16>>16>5&&(n=(r&65535)+5&65535),l=t<<16>>16,l=(l-(n&65535)&65535)<<16>>16>4?l+65532&65535:n,n=i<<16>>16,i<<16>>16<4){e[s>>1]=((((l&65535)<<16)+-327680|0)>>>16)+n,e[f>>1]=0;return}if(n=n<<16,i<<16>>16<12){u=(((n+-327680>>16)*10923|0)>>>15<<16)+-65536|0,i=u>>16,e[s>>1]=(l&65535)+i,e[f>>1]=((n+-589824|0)>>>16)-(u>>>15)-i;return}else{e[s>>1]=((n+-786432+((l&65535)<<16)|0)>>>16)+1,e[f>>1]=0;return}}function z3(i,r,t,o,n,s,f){if(i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,o<<16>>16){f=(D[n>>1]|0)+65531|0,f=(f<<16>>16|0)<(r<<16>>16|0)?r:f&65535,t=t<<16>>16,r=i<<16>>16<<16,i=((r+327680>>16)*10924|0)+-65536>>16,e[n>>1]=(((((f&65535)<<16)+589824>>16|0)>(t|0)?t+65527&65535:f)&65535)+i,e[s>>1]=((r+-196608|0)>>>16)-((i*393216|0)>>>16);return}if(o=i<<16>>16,i<<16>>16<463){i=((((o<<16)+327680>>16)*10924|0)>>>16)+17|0,e[n>>1]=i,e[s>>1]=o+105-((i*393216|0)>>>16);return}else{e[n>>1]=o+65168,e[s>>1]=0;return}}function R6(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0;d=C,C=C+16|0,u=d+6|0,l=d+4|0,M2(i,r,o,u,l,d+2|0,d,f),c=(t&31)*3|0,o=n+(c<<1)|0,(y1(r&65535,7,f)|0)<<16>>16?(t=x0(14,e[l>>1]|0,f)|0,t=g(t<<16>>16,e[o>>1]|0)|0,(t|0)==1073741824?(a[f>>2]=1,o=2147483647):o=t<<1,t=y1(9,e[u>>1]|0,f)|0,l=t<<16>>16,t<<16>>16>0?l=t<<16>>16<31?o>>l:0:(f=0-l<<16>>16,l=o<<f,l=(l>>f|0)==(o|0)?l:o>>31^2147483647),l=l>>>16):(u=x0(e[u>>1]|0,e[l>>1]|0,f)|0,l=u<<16>>16,l=(g(((u<<20>>20|0)==(l|0)?u<<4:l>>>15^32767)<<16>>16,e[o>>1]|0)|0)>>15,(l|0)>32767&&(a[f>>2]=1,l=32767),o=l<<16,t=o>>16,(l<<17>>17|0)==(t|0)?l=o>>15:l=t>>>15^32767),e[s>>1]=l,W0(i,e[n+(c+1<<1)>>1]|0,e[n+(c+2<<1)>>1]|0),C=d}function S6(i,r,t){return i=i|0,r=r|0,t=t|0,r=e[t+(r<<16>>16<<1)>>1]|0,(i|0)!=7?(i=r,i|0):(i=r&65532,i|0)}function D6(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(R=C,C=C+48|0,h=R+20|0,E=R,b=a[n+44>>2]|0,k=a[n+64>>2]|0,l=a[n+4>>2]|0,w=a[n+12>>2]|0,c=a[n+20>>2]|0,u=a[n+56>>2]|0,t<<16>>16){l=0;do t=(e[i+20+(l<<1)>>1]|0)*29491>>15,(t|0)>32767&&(a[f>>2]=1,t=32767),u=(e[b+(l<<1)>>1]|0)*3277>>15,(u|0)>32767&&(a[f>>2]=1,u=32767),e[E+(l<<1)>>1]=n1(u&65535,t&65535,f)|0,l=l+1|0;while((l|0)!=10);if((r|0)==8){l=0;do k=i+(l<<1)|0,h=n1(e[b+(l<<1)>>1]|0,e[k>>1]|0,f)|0,e[k>>1]=y1(e[E+(l<<1)>>1]|0,h,f)|0,l=l+1|0;while((l|0)!=10);P0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));p0(E,s,10,f),C=R;return}else l=0;do u=i+(l<<1)|0,t=(g(e[k+(l<<1)>>1]|0,e[u>>1]|0)|0)>>15,(t|0)>32767&&(a[f>>2]=1,t=32767),h=n1(e[b+(l<<1)>>1]|0,t&65535,f)|0,e[u>>1]=y1(e[E+(l<<1)>>1]|0,h,f)|0,l=l+1|0;while((l|0)!=10);P0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));p0(E,s,10,f),C=R;return}else{if(d=r>>>0<2,d?(t=765,m=508,c=a[n+52>>2]|0):(n=(r|0)==5,t=n?1533:765,m=2044,l=n?u:l),u=e[o>>1]|0,t=((u*196608>>16|0)>(t&65535|0)?t:u*3&65535)<<16>>16,u=e[l+(t<<1)>>1]|0,e[h>>1]=u,e[h+2>>1]=e[l+(t+1<<1)>>1]|0,e[h+4>>1]=e[l+(t+2<<1)>>1]|0,t=e[o+2>>1]|0,d&&(t=t<<16>>16<<1&65535),d=(t<<16>>16)*196608|0,d=(d|0)>100466688?1533:d>>16,e[h+6>>1]=e[w+(d<<1)>>1]|0,e[h+8>>1]=e[w+(d+1<<1)>>1]|0,e[h+10>>1]=e[w+(d+2<<1)>>1]|0,o=e[o+4>>1]|0,o=((o<<18>>16|0)>(m&65535|0)?m:o<<2&65535)<<16>>16,e[h+12>>1]=e[c+(o<<1)>>1]|0,e[h+14>>1]=e[c+((o|1)<<1)>>1]|0,e[h+16>>1]=e[c+((o|2)<<1)>>1]|0,e[h+18>>1]=e[c+((o|3)<<1)>>1]|0,(r|0)==8){for(t=0;k=i+(t<<1)|0,e[E+(t<<1)>>1]=n1(u,n1(e[b+(t<<1)>>1]|0,e[k>>1]|0,f)|0,f)|0,e[k>>1]=u,t=t+1|0,(t|0)!=10;)u=e[h+(t<<1)>>1]|0;P0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));p0(E,s,10,f),C=R;return}else l=0;do u=i+(l<<1)|0,t=(g(e[k+(l<<1)>>1]|0,e[u>>1]|0)|0)>>15,(t|0)>32767&&(a[f>>2]=1,t=32767),o=n1(e[b+(l<<1)>>1]|0,t&65535,f)|0,r=e[h+(l<<1)>>1]|0,e[E+(l<<1)>>1]=n1(r,o,f)|0,e[u>>1]=r,l=l+1|0;while((l|0)!=10);P0(E,205,10,f),l=i+20|0,u=E,t=l+20|0;do _[l>>0]=_[u>>0]|0,l=l+1|0,u=u+1|0;while((l|0)<(t|0));p0(E,s,10,f),C=R;return}}function j3(i,r,t){i=i|0,r=r|0,t=t|0,i2(i|0,t+((r<<16>>16)*10<<1)|0,20)|0}function q3(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(R=C,C=C+80|0,w=R+60|0,h=R+40|0,b=R+20|0,E=R,k=a[o+48>>2]|0,c=a[o+24>>2]|0,d=a[o+28>>2]|0,m=a[o+32>>2]|0,r<<16>>16){l=0;do w=k+(l<<1)|0,t=n1(((e[w>>1]|0)*1639|0)>>>15&65535,((e[i+20+(l<<1)>>1]|0)*31128|0)>>>15&65535,f)|0,e[b+(l<<1)>>1]=t,e[E+(l<<1)>>1]=t,h=i+(l<<1)|0,e[h>>1]=y1(t,n1(e[w>>1]|0,((e[h>>1]|0)*21299|0)>>>15&65535,f)|0,f)|0,l=l+1|0;while((l|0)!=10);P0(b,205,10,f),P0(E,205,10,f),l=i+20|0,o=E,r=l+20|0;do _[l>>0]=_[o>>0]|0,l=l+1|0,o=o+1|0;while((l|0)<(r|0));p0(b,n,10,f),p0(E,s,10,f),C=R;return}r=a[o+16>>2]|0,o=a[o+8>>2]|0,u=e[t>>1]|0,u=((u<<18>>18|0)==(u|0)?u<<2:u>>>15^32767)<<16>>16,e[w>>1]=e[o+(u<<1)>>1]|0,e[w+2>>1]=e[o+(u+1<<1)>>1]|0,e[h>>1]=e[o+(u+2<<1)>>1]|0,e[h+2>>1]=e[o+(u+3<<1)>>1]|0,u=e[t+2>>1]|0,u=((u<<18>>18|0)==(u|0)?u<<2:u>>>15^32767)<<16>>16,e[w+4>>1]=e[r+(u<<1)>>1]|0,e[w+6>>1]=e[r+(u+1<<1)>>1]|0,e[h+4>>1]=e[r+(u+2<<1)>>1]|0,e[h+6>>1]=e[r+(u+3<<1)>>1]|0,u=e[t+4>>1]|0,o=u<<16>>16,u<<16>>16<0?r=~((o^-2)>>1):r=o>>>1,u=r<<16>>16,u=((r<<18>>18|0)==(u|0)?r<<2:u>>>15^32767)<<16>>16,l=c+(u+1<<1)|0,r=e[c+(u<<1)>>1]|0,o&1?(r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[w+8>>1]=r,r=e[l>>1]|0,r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[w+10>>1]=r,r=e[c+(u+2<<1)>>1]|0,r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[h+8>>1]=r,r=e[c+(u+3<<1)>>1]|0,r<<16>>16==-32768?r=32767:r=0-(r&65535)&65535,e[h+10>>1]=r):(e[w+8>>1]=r,e[w+10>>1]=e[l>>1]|0,e[h+8>>1]=e[c+(u+2<<1)>>1]|0,e[h+10>>1]=e[c+(u+3<<1)>>1]|0),l=e[t+6>>1]|0,l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16,e[w+12>>1]=e[d+(l<<1)>>1]|0,e[w+14>>1]=e[d+(l+1<<1)>>1]|0,e[h+12>>1]=e[d+(l+2<<1)>>1]|0,e[h+14>>1]=e[d+(l+3<<1)>>1]|0,l=e[t+8>>1]|0,l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)<<16>>16,e[w+16>>1]=e[m+(l<<1)>>1]|0,e[w+18>>1]=e[m+(l+1<<1)>>1]|0,e[h+16>>1]=e[m+(l+2<<1)>>1]|0,e[h+18>>1]=e[m+(l+3<<1)>>1]|0,l=0;do o=i+(l<<1)|0,r=(e[o>>1]|0)*21299>>15,(r|0)>32767&&(a[f>>2]=1,r=32767),m=n1(e[k+(l<<1)>>1]|0,r&65535,f)|0,e[b+(l<<1)>>1]=n1(e[w+(l<<1)>>1]|0,m,f)|0,t=e[h+(l<<1)>>1]|0,e[E+(l<<1)>>1]=n1(t,m,f)|0,e[o>>1]=t,l=l+1|0;while((l|0)!=10);P0(b,205,10,f),P0(E,205,10,f),l=i+20|0,o=E,r=l+20|0;do _[l>>0]=_[o>>0]|0,l=l+1|0,o=o+1|0;while((l|0)<(r|0));p0(b,n,10,f),p0(E,s,10,f),C=R}function S5(i,r){i=i|0,r=r|0;var t=0,o=0;if(!i)return o=-1,o|0;t=i,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return i2(i+20|0,r|0,20)|0,o=0,o|0}function A6(i){i=i|0;var r=0,t=0,o=0,n=0,s=0;if(!i)return s=-1,s|0;e[i>>1]=0,e[i+2>>1]=8192,r=i+4|0,e[r>>1]=3500,e[i+6>>1]=3500,a[i+8>>2]=1887529304,e[i+12>>1]=3e4,e[i+14>>1]=26e3,e[i+16>>1]=21e3,e[i+18>>1]=15e3,e[i+20>>1]=8e3,e[i+22>>1]=0,e[i+24>>1]=-8e3,e[i+26>>1]=-15e3,e[i+28>>1]=-21e3,e[i+30>>1]=-26e3,e[i+32>>1]=3e4,e[i+34>>1]=26e3,e[i+36>>1]=21e3,e[i+38>>1]=15e3,e[i+40>>1]=8e3,e[i+42>>1]=0,e[i+44>>1]=-8e3,e[i+46>>1]=-15e3,e[i+48>>1]=-21e3,e[i+50>>1]=-26e3,e[i+212>>1]=0,e[i+374>>1]=0,e[i+392>>1]=0,t=i+52|0,e[t>>1]=1384,e[i+54>>1]=2077,e[i+56>>1]=3420,e[i+58>>1]=5108,e[i+60>>1]=6742,e[i+62>>1]=8122,e[i+64>>1]=9863,e[i+66>>1]=11092,e[i+68>>1]=12714,e[i+70>>1]=13701,o=i+72|0,n=t,s=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(s|0));o=i+92|0,n=t,s=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(s|0));o=i+112|0,n=t,s=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(s|0));o=i+132|0,n=t,s=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(s|0));o=i+152|0,n=t,s=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(s|0));o=i+172|0,n=t,s=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(s|0));o=i+192|0,n=t,s=o+20|0;do _[o>>0]=_[n>>0]|0,o=o+1|0,n=n+1|0;while((o|0)<(s|0));return t2(i+214|0,0,160)|0,e[i+376>>1]=3500,e[i+378>>1]=3500,s=e[r>>1]|0,e[i+380>>1]=s,e[i+382>>1]=s,e[i+384>>1]=s,e[i+386>>1]=s,e[i+388>>1]=s,e[i+390>>1]=s,e[i+394>>1]=0,e[i+396>>1]=7,e[i+398>>1]=32767,e[i+400>>1]=0,e[i+402>>1]=0,e[i+404>>1]=0,a[i+408>>2]=1,e[i+412>>1]=0,s=0,s|0}function H3(i,r,t,o,n,s,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0;if(V=C,C=C+304|0,a1=V+192|0,i1=V+168|0,d1=V+148|0,w1=V+216|0,c1=V+146|0,t1=V+144|0,W=V+124|0,o1=V+104|0,u1=V+84|0,l1=V+60|0,U=V+40|0,H=V,Z=i+404|0,m1=i+400|0,e[Z>>1]|0&&e[m1>>1]|0){K=i+394|0,e[K>>1]=e[636+(f<<1)>>1]|0,N=e[i+212>>1]|0,O=N+10|0,i2(i+52+(((O&65535|0)==80?0:O<<16>>16)<<1)|0,i+52+(N<<1)|0,20)|0,N=e[i+392>>1]|0,O=N+1|0,e[i+376+(((O&65535|0)==8?0:O<<16>>16)<<1)>>1]=e[i+376+(N<<1)>>1]|0,O=i+4|0,e[O>>1]=0,N=H+36|0,A=H+32|0,I=H+28|0,F=H+24|0,B=H+20|0,M=H+16|0,T=H+12|0,z=H+8|0,j=H+4|0,Y=i+52|0,k=H,q=k+40|0;do a[k>>2]=0,k=k+4|0;while((k|0)<(q|0));for(h=0,w=7;;){for(q=e[i+376+(w<<1)>>1]|0,S=q<<16>>16,q<<16>>16<0?S=~((S^-8)>>3):S=S>>>3,h=n1(h,S&65535,m)|0,e[O>>1]=h,E=w*10|0,k=9;b=H+(k<<2)|0,R=a[b>>2]|0,q=e[i+52+(k+E<<1)>>1]|0,S=q+R|0,(q^R|0)>-1&(S^R|0)<0&&(a[m>>2]=1,S=(R>>>31)+2147483647|0),a[b>>2]=S,(k|0)>0;)k=k+-1|0;if((w|0)<=0)break;w=w+-1|0}for(e[U+18>>1]=(a[N>>2]|0)>>>3,e[U+16>>1]=(a[A>>2]|0)>>>3,e[U+14>>1]=(a[I>>2]|0)>>>3,e[U+12>>1]=(a[F>>2]|0)>>>3,e[U+10>>1]=(a[B>>2]|0)>>>3,e[U+8>>1]=(a[M>>2]|0)>>>3,e[U+6>>1]=(a[T>>2]|0)>>>3,e[U+4>>1]=(a[z>>2]|0)>>>3,e[U+2>>1]=(a[j>>2]|0)>>>3,e[U>>1]=(a[H>>2]|0)>>>3,p0(U,i+12|0,10,m),e[O>>1]=y1(e[O>>1]|0,e[K>>1]|0,m)|0,R0(i+214|0,Y|0,160)|0,U=9;;){for(q=e[i+214+(U+70<<1)>>1]|0,b=q<<16>>16,H=e[i+214+(U+60<<1)>>1]|0,k=(H<<16>>16)+b|0,(H^q)<<16>>16>-1&(k^b|0)<0&&(a[m>>2]=1,k=(b>>>31)+2147483647|0),q=e[i+214+(U+50<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0&&(a[m>>2]=1,b=(k>>>31)+2147483647|0),q=e[i+214+(U+40<<1)>>1]|0,k=q+b|0,(q^b|0)>-1&(k^b|0)<0&&(a[m>>2]=1,k=(b>>>31)+2147483647|0),q=e[i+214+(U+30<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0&&(a[m>>2]=1,b=(k>>>31)+2147483647|0),q=e[i+214+(U+20<<1)>>1]|0,k=q+b|0,(q^b|0)>-1&(k^b|0)<0&&(a[m>>2]=1,k=(b>>>31)+2147483647|0),q=e[i+214+(U+10<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0?(a[m>>2]=1,k=(k>>>31)+2147483647|0):k=b,q=e[i+214+(U<<1)>>1]|0,b=q+k|0,(q^k|0)>-1&(b^k|0)<0&&(a[m>>2]=1,b=(k>>>31)+2147483647|0),(b|0)<0?b=~((b^-8)>>3):b=b>>>3,S=b&65535,E=e[654+(U<<1)>>1]|0,R=7;w=i+214+((R*10|0)+U<<1)|0,b=y1(e[w>>1]|0,S,m)|0,e[w>>1]=b,b=(g(E,b<<16>>16)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[w>>1]=b,h=(b&65535)-(b>>>15&1)|0,h=h<<16>>31^h,k=h&65535,k<<16>>16>655&&(k=(((h<<16>>16)+261489|0)>>>2)+655&65535),k=k<<16>>16>1310?1310:k,b&32768?b=0-(k&65535)&65535:b=k,e[w>>1]=b,(R|0)>0;)R=R+-1|0;if((U|0)>0)U=U+-1|0;else break}}if(e[m1>>1]|0){S=i+32|0,R=i+12|0,k=S,E=R,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));E=i+4|0,h=e[E>>1]|0,w=i+6|0,e[w>>1]=h;do if(e[i+402>>1]|0){k=e[i>>1]|0,e[i>>1]=0,k=k<<16>>16<32?k:32,q=k<<16>>16,b=q<<10,(b|0)!=(q<<26>>16|0)&&(a[m>>2]=1,b=k<<16>>16>0?32767:-32768),k<<16>>16>1?b=r0(1024,b&65535)|0:b=16384,e[i+2>>1]=b,j3(t,e[l>>1]|0,a[u+60>>2]|0),D6(t,8,0,l+2|0,u,R,m),k=t,q=k+20|0;do _[k>>0]=0,k=k+1|0;while((k|0)<(q|0));if(h=e[l+8>>1]|0,h=h<<16>>16?((h+64&65535)>127?h<<16>>16>0?32767:32768:h<<16>>16<<9)+60416&65535:-32768,e[E>>1]=h,e[i+412>>1]|0&&a[i+408>>2]|0)break;k=S,E=R,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));e[w>>1]=h}while(!1);k=h<<16>>16,h<<16>>16<0?k=~((k^-2)>>1):k=k>>>1,k=k+56536|0,b=k<<16,(b|0)>0?k=0:k=(b|0)<-946077696?-14436:k&65535,e[o>>1]=k,e[o+2>>1]=k,e[o+4>>1]=k,e[o+6>>1]=k,l=((k<<16>>16)*5443|0)>>>15&65535,e[o+8>>1]=l,e[o+10>>1]=l,e[o+12>>1]=l,e[o+14>>1]=l}for(k=((e[636+(f<<1)>>1]|0)*104864|0)>>>15<<16,(k|0)<0?k=~((k>>16^-32)>>5):k=k>>21,f=i+394|0,e[f>>1]=n1(((e[f>>1]|0)*29491|0)>>>15&65535,k&65535,m)|0,o=(D[i>>1]<<16)+65536|0,k=o>>16,u=i+2|0,k=(g(((o<<10>>26|0)==(k|0)?o>>>6:k>>>15^32767)<<16>>16,e[u>>1]|0)|0)>>15,(k|0)>32767&&(a[m>>2]=1,k=32767),h=k&65535,h<<16>>16<=1024?h<<16>>16<-2048?R=-32768:R=k<<4&65535:R=16384,l=i+4|0,S=R<<16>>16,b=g(e[l>>1]|0,S)|0,(b|0)==1073741824?(a[m>>2]=1,U=2147483647):U=b<<1,b=(g(e[i+30>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),O=b&65535,e[a1+18>>1]=O,b=(g(e[i+28>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+16>>1]=b,b=(g(e[i+26>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+14>>1]=b,b=(g(e[i+24>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+12>>1]=b,b=(g(e[i+22>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+10>>1]=b,b=(g(e[i+20>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+8>>1]=b,b=(g(e[i+18>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+6>>1]=b,b=(g(e[i+16>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+4>>1]=b,b=(g(e[i+14>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1+2>>1]=b,b=(g(e[i+12>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[a1>>1]=b,o=i+6|0,S=16384-(R&65535)<<16>>16,b=g(e[o>>1]|0,S)|0,(b|0)!=1073741824?(k=(b<<1)+U|0,(b^U|0)>0&(k^U|0)<0?(a[m>>2]=1,H=(U>>>31)+2147483647|0):H=k):(a[m>>2]=1,H=2147483647),k=O,E=9;h=a1+(E<<1)|0,b=(g(e[i+32+(E<<1)>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),k=n1(k,b&65535,m)|0,e[h>>1]=k,q=k<<16>>16,b=q<<1,(b|0)!=(q<<17>>16|0)&&(a[m>>2]=1,b=k<<16>>16>0?32767:-32768),e[h>>1]=b,b=E+-1|0,!((E|0)<=0);)k=e[a1+(b<<1)>>1]|0,E=b;U=i+374|0,b=((D[U>>1]<<16)+-161021952>>16)*9830>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),b=4096-(b&65535)|0,k=b<<16,(k|0)>268369920?S=32767:S=(k|0)<0?0:b<<19>>16,K=i+8|0,b=A3(K,3)|0,q2(a1,W,10,m),k=o1,E=W,q=k+20|0;do e[k>>1]=e[E>>1]|0,k=k+2|0,E=E+2|0;while((k|0)<(q|0));for(k=(b<<16>>16)*10|0,E=9;h=o1+(E<<1)|0,w=e[h>>1]|0,b=(g(e[i+214+(E+k<<1)>>1]|0,S)|0)>>15,(b|0)>32767&&(a[m>>2]=1,b=32767),e[h>>1]=n1(w,b&65535,m)|0,(E|0)>0;)E=E+-1|0;P0(W,205,10,m),P0(o1,205,10,m),k=t+20|0,E=W,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));p0(W,a1,10,m),p0(o1,u1,10,m),_0(a1,i1,m),_0(u1,l1,m),k=d,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));k=d+22|0,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));k=d+44|0,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));k=d+66|0,E=i1,q=k+22|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));D3(i1+2|0,d1,m),b=0,k=32767;do h=e[d1+(b<<1)>>1]|0,h=g(h,h)|0,h>>>0<1073741824?h=32767-(h>>>15)|0:(a[m>>2]=1,h=0),k=(g(h<<16>>16,k<<16>>16)|0)>>15,(k|0)>32767&&(a[m>>2]=1,k=32767),b=b+1|0;while((b|0)!=10);for(s2(k<<16>>16,c1,t1,m),k=(D[c1>>1]<<16)+-983040|0,h=k>>16,h=X1(y1(0,n1(((k<<12>>28|0)==(h|0)?k>>>4:h>>>15^32767)&65535,X1(e[t1>>1]|0,3,m)|0,m)|0,m)|0,1,m)|0,k=(e[U>>1]|0)*29491>>15,(k|0)>32767&&(a[m>>2]=1,k=32767),b=h<<16>>16,h=b*3277>>15,(h|0)>32767&&(a[m>>2]=1,h=32767),e[U>>1]=n1(k&65535,h&65535,m)|0,h=H>>10,w=h+262144|0,(h|0)>-1&(w^h|0)<0&&(a[m>>2]=1,w=(h>>>31)+2147483647|0),t1=b<<4,h=w-t1|0,((h^w)&(w^t1)|0)<0?(a[m>>2]=1,w=(w>>>31)+2147483647|0):w=h,t1=e[f>>1]<<5,h=t1+w|0,(t1^w|0)>-1&(h^w|0)<0&&(a[m>>2]=1,h=(w>>>31)+2147483647|0),b=(x0(h>>>16&65535,h>>>1&32767,m)|0)<<16>>16,i5(K,w1,m),w=39;k=w1+(w<<1)|0,h=(g(e[k>>1]|0,b)|0)>>15,(h|0)>32767&&(a[m>>2]=1,h=32767),e[k>>1]=h,(w|0)>0;)w=w+-1|0;for(f0(l1,w1,c,40,r,1),i5(K,w1,m),w=39;k=w1+(w<<1)|0,h=(g(e[k>>1]|0,b)|0)>>15,(h|0)>32767&&(a[m>>2]=1,h=32767),e[k>>1]=h,(w|0)>0;)w=w+-1|0;for(f0(l1,w1,c+80|0,40,r,1),i5(K,w1,m),w=39;k=w1+(w<<1)|0,h=(g(e[k>>1]|0,b)|0)>>15,(h|0)>32767&&(a[m>>2]=1,h=32767),e[k>>1]=h,(w|0)>0;)w=w+-1|0;for(f0(l1,w1,c+160|0,40,r,1),i5(K,w1,m),k=39;w=w1+(k<<1)|0,h=(g(e[w>>1]|0,b)|0)>>15,(h|0)>32767&&(a[m>>2]=1,h=32767),e[w>>1]=h,(k|0)>0;)k=k+-1|0;if(f0(l1,w1,c+240|0,40,r,1),e[n+14>>1]=20,e[n+16>>1]=0,(s|0)==2){h=e[i>>1]|0,h=h<<16>>16>32?32:h<<16>>16<1?8:h,c=h<<16>>16,w=c<<10,(w|0)!=(c<<26>>16|0)&&(a[m>>2]=1,w=h<<16>>16>0?32767:-32768),e[u>>1]=r0(1024,w&65535)|0,e[i>>1]=0,k=i+32|0,E=i+12|0,q=k+20|0;do _[k>>0]=_[E>>0]|0,k=k+1|0,E=E+1|0;while((k|0)<(q|0));m=e[l>>1]|0,e[o>>1]=m,e[l>>1]=(m&65535)+65280}if(!(e[m1>>1]|0)){C=V;return}do if(!(e[i+402>>1]|0)){if(e[Z>>1]|0)break;C=V;return}while(!1);e[i>>1]=0,e[i+412>>1]=1,C=V}function V3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0;for(u=C,C=C+16|0,f=u+2|0,l=u,e[l>>1]=0,s=i+212|0,n=(D[s>>1]|0)+10|0,n=(n&65535|0)==80?0:n&65535,e[s>>1]=n,i2(i+52+(n<<16>>16<<1)|0,r|0,20)|0,n=0,s=159;c=e[t+(s<<1)>>1]|0,c=g(c,c)|0,c=(c|0)==1073741824?2147483647:c<<1,r=c+n|0,(c^n|0)>-1&(r^n|0)<0?(a[o>>2]=1,n=(n>>>31)+2147483647|0):n=r,(s|0)>0;)s=s+-1|0;s2(n,f,l,o),n=e[f>>1]|0,c=n<<16>>16,r=c<<10,(r|0)!=(c<<26>>16|0)&&(a[o>>2]=1,r=n<<16>>16>0?32767:-32768),e[f>>1]=r,c=e[l>>1]|0,n=c<<16>>16,c<<16>>16<0?n=~((n^-32)>>5):n=n>>>5,l=i+392|0,c=(D[l>>1]|0)+1|0,c=(c&65535|0)==8?0:c&65535,e[l>>1]=c,e[i+376+(c<<16>>16<<1)>>1]=n+57015+r,C=u}function W3(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0;u=(r|0)==4,c=(r|0)==5,d=(r|0)==6,o=a[i+408>>2]|0;e:do if((r+-4|0)>>>0<3)l=4;else{if((o+-1|0)>>>0<2)switch(r|0){case 2:case 3:case 7:{l=4;break e}default:}e[i>>1]=0,f=0}while(!1);if((l|0)==4){e:do if((o|0)==2){switch(r|0){case 2:case 4:case 6:case 7:break;default:{n=1;break e}}n=2}else n=1;while(!1);f=(D[i>>1]|0)+1&65535,e[i>>1]=f,f=(r|0)!=5&f<<16>>16>50?2:n}s=i+398|0,c&(e[i+412>>1]|0)==0?(e[s>>1]=0,n=0):n=e[s>>1]|0,n=n1(n,1,t)|0,e[s>>1]=n,t=i+404|0,e[t>>1]=0;e:do switch(r|0){case 2:case 4:case 5:case 6:case 7:{if((r|0)==7&(f|0)==0)l=14;else{if(n<<16>>16>30){e[t>>1]=1,e[s>>1]=0,e[i+396>>1]=0;break e}if(n=i+396|0,o=e[n>>1]|0,o<<16>>16){e[n>>1]=(o&65535)+65535;break e}else{e[s>>1]=0;break e}}break}default:l=14}while(!1);return(l|0)==14&&(e[i+396>>1]=7),f?(n=i+400|0,e[n>>1]=0,o=i+402|0,e[o>>1]=0,u?(e[n>>1]=1,f|0):c?(e[n>>1]=1,e[o>>1]=1,f|0):(d&&(e[n>>1]=1,e[t>>1]=0),f|0)):f|0}function D5(i){return i=i|0,i?(e[i>>1]=1,e[i+2>>1]=1,e[i+4>>1]=1,e[i+6>>1]=1,e[i+8>>1]=1,e[i+10>>1]=0,e[i+12>>1]=1,i=0,i|0):(i=-1,i|0)}function t5(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0;u=C,C=C+16|0,l=u+2|0,f=u,s=k2(i,5)|0,i=i+10|0,(y1(s,e[i>>1]|0,n)|0)<<16>>16>0&&(s=e[i>>1]|0),s=(g(e[674+(t<<16>>16<<1)>>1]|0,s<<16>>16)|0)>>15,(s|0)>32767&&(a[n>>2]=1,s=32767),e[o>>1]=s,Ri(r,l,f,n),W0(r,e[l>>1]|0,e[f>>1]|0),C=u}function r5(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,r<<16>>16||(t<<16>>16?(r=i+12|0,(y1(e[o>>1]|0,e[r>>1]|0,n)|0)<<16>>16>0&&(e[o>>1]=e[r>>1]|0)):r=i+12|0,e[r>>1]=e[o>>1]|0),e[i+10>>1]=e[o>>1]|0,n=i+2|0,e[i>>1]=e[n>>1]|0,t=i+4|0,e[n>>1]=e[t>>1]|0,n=i+6|0,e[t>>1]=e[n>>1]|0,i=i+8|0,e[n>>1]=e[i>>1]|0,e[i>>1]=e[o>>1]|0}function o5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0;if(n=k2(i,5)|0,i=i+10|0,(y1(n,e[i>>1]|0,o)|0)<<16>>16>0&&(n=e[i>>1]|0),n=(g(e[688+(r<<16>>16<<1)>>1]|0,n<<16>>16)|0)>>15,(n|0)<=32767){o=n,o=o&65535,e[t>>1]=o;return}a[o>>2]=1,o=32767,o=o&65535,e[t>>1]=o}function A5(i){return i=i|0,i?(e[i>>1]=1640,e[i+2>>1]=1640,e[i+4>>1]=1640,e[i+6>>1]=1640,e[i+8>>1]=1640,e[i+10>>1]=0,e[i+12>>1]=16384,i=0,i|0):(i=-1,i|0)}function n5(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,r<<16>>16||(t<<16>>16?(r=i+12|0,(y1(e[o>>1]|0,e[r>>1]|0,n)|0)<<16>>16>0&&(e[o>>1]=e[r>>1]|0)):r=i+12|0,e[r>>1]=e[o>>1]|0),o=e[o>>1]|0,r=i+10|0,e[r>>1]=o,(y1(o,16384,n)|0)<<16>>16>0?(e[r>>1]=16384,r=16384):r=e[r>>1]|0,n=i+2|0,e[i>>1]=e[n>>1]|0,o=i+4|0,e[n>>1]=e[o>>1]|0,n=i+6|0,e[o>>1]=e[n>>1]|0,i=i+8|0,e[n>>1]=e[i>>1]|0,e[i>>1]=r}function X3(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0;if(u=k2(t,9)|0,c=e[t+16>>1]|0,l=c<<16>>16,t=(l+(e[t+14>>1]|0)|0)>>>1,t=(l|0)<(t<<16>>16|0)?c:t&65535,!(r<<16>>16>5&&u<<16>>16>r<<16>>16))return 0;l=t<<16>>16,l=((l<<18>>18|0)==(l|0)?l<<2:l>>>15^32767)&65535,o<<16>>16>6&n<<16>>16==0||(l=y1(l,t,f)|0),u=u<<16>>16>l<<16>>16?l:u,c=P2(r)|0,l=c<<16>>16,c<<16>>16<0?(t=0-l<<16,(t|0)<983040?l=r<<16>>16>>(t>>16)&65535:l=0):(t=r<<16>>16,n=t<<l,(n<<16>>16>>l|0)==(t|0)?l=n&65535:l=(t>>>15^32767)&65535),o=g((r0(16383,l)|0)<<16>>16,u<<16>>16)|0,(o|0)==1073741824?(a[f>>2]=1,n=2147483647):n=o<<1,o=y1(20,c,f)|0,l=o<<16>>16,o<<16>>16>0?o=o<<16>>16<31?n>>l:0:(r=0-l<<16>>16,o=n<<r,o=(o>>r|0)==(n|0)?o:n>>31^2147483647),o=(o|0)>32767?32767:o&65535,o=s<<16>>16!=0&o<<16>>16>3072?3072:o<<16>>16,t=0;do n=i+(t<<1)|0,l=g(e[n>>1]|0,o)|0,(l|0)==1073741824?(a[f>>2]=1,l=2147483647):l=l<<1,e[n>>1]=l>>>11,t=t+1|0;while((t|0)!=40);return 0}function Y3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0;if(n=a[o+104>>2]|0,s=a[o+96>>2]|0,i>>>0>=8){if(e[t>>1]=(x[r>>0]|0)>>>4&1,e[t+2>>1]=(x[r>>0]|0)>>>5&1,e[t+4>>1]=(x[r>>0]|0)>>>6&1,e[t+6>>1]=(x[r>>0]|0)>>>7&255,n=n+(i<<1)|0,(e[n>>1]|0)>1)i=1,o=1,s=4;else return;for(;f=r+i|0,i=s|1,e[t+(s<<16>>16<<1)>>1]=x[f>>0]&1,e[t+(i<<16>>16<<1)>>1]=(x[f>>0]|0)>>>1&1,l=s|3,e[t+(i+1<<16>>16<<16>>16<<1)>>1]=(x[f>>0]|0)>>>2&1,e[t+(l<<16>>16<<1)>>1]=(x[f>>0]|0)>>>3&1,e[t+(l+1<<16>>16<<16>>16<<1)>>1]=(x[f>>0]|0)>>>4&1,e[t+(l+2<<16>>16<<16>>16<<1)>>1]=(x[f>>0]|0)>>>5&1,e[t+(l+3<<16>>16<<16>>16<<1)>>1]=(x[f>>0]|0)>>>6&1,e[t+(l+4<<16>>16<<16>>16<<1)>>1]=(x[f>>0]|0)>>>7&255,o=o+1<<16>>16,o<<16>>16<(e[n>>1]|0);)i=o<<16>>16,s=s+8<<16>>16;return}if(l=a[(a[o+100>>2]|0)+(i<<2)>>2]|0,e[t+(e[l>>1]<<1)>>1]=(x[r>>0]|0)>>>4&1,e[t+(e[l+2>>1]<<1)>>1]=(x[r>>0]|0)>>>5&1,e[t+(e[l+4>>1]<<1)>>1]=(x[r>>0]|0)>>>6&1,e[t+(e[l+6>>1]<<1)>>1]=(x[r>>0]|0)>>>7&255,f=n+(i<<1)|0,!((e[f>>1]|0)<=1))for(o=s+(i<<1)|0,n=1,i=1,s=4;n=r+n|0,s=s<<16>>16,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=x[n>>0]&1,s=s+1|0,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=(x[n>>0]|0)>>>1&1,s=s+1|0,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=(x[n>>0]|0)>>>2&1,s=s+1|0,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=(x[n>>0]|0)>>>3&1,s=s+1|0,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=(x[n>>0]|0)>>>4&1,s=s+1|0,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=(x[n>>0]|0)>>>5&1,s=s+1|0,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=(x[n>>0]|0)>>>6&1,s=s+1|0,(s|0)<(e[o>>1]|0)&&(e[t+(e[l+(s<<1)>>1]<<1)>>1]=(x[n>>0]|0)>>>7&1,s=s+1|0)))))))),i=i+1<<16>>16,i<<16>>16<(e[f>>1]|0);)n=i<<16>>16}function G3(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0;switch(t<<16>>16){case 0:{for(u=9;l=e[i+(u<<1)>>1]|0,t=l<<16>>16,l<<16>>16<0?t=~((t^-4)>>2):t=t>>>2,f=e[r+(u<<1)>>1]|0,s=f<<16>>16,f<<16>>16<0?f=~((s^-4)>>2):f=s>>>2,e[o+(u<<1)>>1]=n1((l&65535)-t&65535,f&65535,n)|0,(u|0)>0;)u=u+-1|0;return}case 40:{for(f=9;n=e[i+(f<<1)>>1]|0,t=n<<16>>16,n<<16>>16<0?s=~((t^-2)>>1):s=t>>>1,n=e[r+(f<<1)>>1]|0,t=n<<16>>16,n<<16>>16<0?t=~((t^-2)>>1):t=t>>>1,e[o+(f<<1)>>1]=t+s,(f|0)>0;)f=f+-1|0;return}case 80:{for(u=9;l=e[i+(u<<1)>>1]|0,t=l<<16>>16,l<<16>>16<0?l=~((t^-4)>>2):l=t>>>2,t=e[r+(u<<1)>>1]|0,s=t<<16>>16,t<<16>>16<0?f=~((s^-4)>>2):f=s>>>2,e[o+(u<<1)>>1]=n1(l&65535,(t&65535)-f&65535,n)|0,(u|0)>0;)u=u+-1|0;return}case 120:{e[o+18>>1]=e[r+18>>1]|0,e[o+16>>1]=e[r+16>>1]|0,e[o+14>>1]=e[r+14>>1]|0,e[o+12>>1]=e[r+12>>1]|0,e[o+10>>1]=e[r+10>>1]|0,e[o+8>>1]=e[r+8>>1]|0,e[o+6>>1]=e[r+6>>1]|0,e[o+4>>1]=e[r+4>>1]|0,e[o+2>>1]=e[r+2>>1]|0,e[o>>1]=e[r>>1]|0;return}default:return}}function M6(i,r){return i=i|0,r=r|0,i?(i2(i|0,r|0,20)|0,i=0,i|0):(i=-1,i|0)}function P6(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0,c=0;c=0;do u=i+(c<<1)|0,o=e[u>>1]|0,f=o&65535,l=f<<16,o=o<<16>>16,(o*5243|0)==1073741824?(a[t>>2]=1,s=2147483647):s=o*10486|0,n=l-s|0,((n^l)&(s^l)|0)<0?(a[t>>2]=1,s=(f>>>15)+2147483647|0):s=n,o=e[r+(c<<1)>>1]|0,n=o*5243|0,(n|0)!=1073741824?(o=(o*10486|0)+s|0,(n^s|0)>0&(o^s|0)<0&&(a[t>>2]=1,o=(s>>>31)+2147483647|0)):(a[t>>2]=1,o=2147483647),e[u>>1]=S1(o,t)|0,c=c+1|0;while((c|0)!=10)}function N6(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+18|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function K3(i){i=i|0,e[i+14>>1]=1}function Z3(i){i=i|0,e[i+14>>1]=0}function Q3(i,r,t,o,n,s,f,l,u,c){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0;var d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0;M=C,C=C+160|0,I=M+80|0,F=M,S=a[u+120>>2]|0,O=a[u+124>>2]|0,N=a[u+128>>2]|0,R=a[u+132>>2]|0,m=i+6|0,E=i+8|0,e[E>>1]=e[m>>1]|0,k=i+4|0,e[m>>1]=e[k>>1]|0,b=i+2|0,e[k>>1]=e[b>>1]|0,e[b>>1]=e[i>>1]|0,e[i>>1]=n,u=n<<16>>16<14746?n<<16>>16>9830&1:2,d=i+12|0,n=e[d>>1]|0,w=n<<15;do if((w|0)<=536870911)if((w|0)<-536870912){a[c>>2]=1,n=-2147483648;break}else{n=n<<17;break}else a[c>>2]=1,n=2147483647;while(!1);switch(A=o<<16>>16,h=i+16|0,(S1(n,c)|0)<<16>>16>=o<<16>>16?(w=e[h>>1]|0,w<<16>>16>0&&(w=(w&65535)+65535&65535,e[h>>1]=w),w<<16>>16||(n=(e[i>>1]|0)<9830,n=(e[b>>1]|0)<9830?n?2:1:n&1,(e[k>>1]|0)<9830&&(n=(n&65535)+1&65535),(e[m>>1]|0)<9830&&(n=(n&65535)+1&65535),(e[E>>1]|0)<9830&&(n=(n&65535)+1&65535),w=0,u=n<<16>>16>2?0:u)):(e[h>>1]=2,w=2),b=u<<16>>16,E=i+10|0,b=!(w<<16>>16)&&(b|0)>((e[E>>1]|0)+1|0)?b+65535&65535:u,i=(e[i+14>>1]|0)==1?0:o<<16>>16<10?2:b<<16>>16<2&w<<16>>16>0?(b&65535)+1&65535:b,e[E>>1]=i,e[d>>1]=o,r|0){case 4:case 6:case 7:break;default:if(i<<16>>16<2){for(w=0,u=0,m=s,d=I;;)if(e[m>>1]|0?(u=u<<16>>16,e[F+(u<<1)>>1]=w,n=e[m>>1]|0,u=u+1&65535):n=0,e[d>>1]=n,e[m>>1]=0,w=w+1<<16>>16,w<<16>>16>=40){E=u;break}else m=m+2|0,d=d+2|0;if(b=i<<16>>16==0,b=(r|0)==5?b?S:O:b?N:R,E<<16>>16>0){k=0;do{if(h=e[F+(k<<1)>>1]|0,u=h<<16>>16,i=e[I+(u<<1)>>1]|0,h<<16>>16<40){for(w=i<<16>>16,m=39-h&65535,d=h,u=s+(u<<1)|0,n=b;r=(g(e[n>>1]|0,w)|0)>>>15&65535,e[u>>1]=n1(e[u>>1]|0,r,c)|0,d=d+1<<16>>16,!(d<<16>>16>=40);)u=u+2|0,n=n+2|0;h<<16>>16>0&&(u=b+(m+1<<1)|0,B=36)}else u=b,B=36;if((B|0)==36)for(B=0,n=i<<16>>16,w=0,m=s;r=(g(e[u>>1]|0,n)|0)>>>15&65535,e[m>>1]=n1(e[m>>1]|0,r,c)|0,w=w+1<<16>>16,!(w<<16>>16>=h<<16>>16);)m=m+2|0,u=u+2|0;k=k+1|0}while((k&65535)<<16>>16!=E<<16>>16)}}}if(k=f<<16>>16,b=A<<1,n=l<<16>>16,d=0-n<<16,u=d>>16,l<<16>>16>0){for(w=0,m=t;i=g(e[t+(w<<1)>>1]|0,k)|0,(i|0)==1073741824?(a[c>>2]=1,d=2147483647):d=i<<1,l=g(b,e[s>>1]|0)|0,i=l+d|0,(l^d|0)>-1&(i^d|0)<0&&(a[c>>2]=1,i=(d>>>31)+2147483647|0),l=i<<n,e[m>>1]=S1((l>>n|0)==(i|0)?l:i>>31^2147483647,c)|0,w=w+1|0,(w|0)!=40;)s=s+2|0,m=m+2|0;C=M;return}if((d|0)<2031616){for(w=0,m=t;i=g(e[t+(w<<1)>>1]|0,k)|0,(i|0)==1073741824?(a[c>>2]=1,d=2147483647):d=i<<1,l=g(b,e[s>>1]|0)|0,i=l+d|0,(l^d|0)>-1&(i^d|0)<0&&(a[c>>2]=1,i=(d>>>31)+2147483647|0),e[m>>1]=S1(i>>u,c)|0,w=w+1|0,(w|0)!=40;)s=s+2|0,m=m+2|0;C=M;return}else{for(m=0,d=t;i=g(e[t+(m<<1)>>1]|0,k)|0,(i|0)==1073741824?(a[c>>2]=1,i=2147483647):i=i<<1,l=g(b,e[s>>1]|0)|0,(l^i|0)>-1&(l+i^i|0)<0&&(a[c>>2]=1),e[d>>1]=S1(0,c)|0,m=m+1|0,(m|0)!=40;)s=s+2|0,d=d+2|0;C=M;return}}function O6(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,i=0,i|0):(i=-1,i|0)}function $3(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0;if(!(t<<16>>16<=0))for(n=i+10|0,u=i+8|0,d=i+4|0,m=i+6|0,w=i+2|0,s=e[d>>1]|0,f=e[m>>1]|0,l=e[i>>1]|0,c=e[w>>1]|0,h=0;k=e[n>>1]|0,b=e[u>>1]|0,e[n>>1]=b,E=e[r>>1]|0,e[u>>1]=E,k=((E<<16>>16)*7699|0)+((g(l<<16>>16,-7667)|0)+(((s<<16>>16)*15836|0)+((f<<16>>16)*15836>>15))+((g(c<<16>>16,-7667)|0)>>15))+(g(b<<16>>16,-15398)|0)+((k<<16>>16)*7699|0)|0,b=k<<3,k=(b>>3|0)==(k|0)?b:k>>31^2147483647,b=k<<1,e[r>>1]=S1((b>>1|0)==(k|0)?b:k>>31^2147483647,o)|0,l=e[d>>1]|0,e[i>>1]=l,c=e[m>>1]|0,e[w>>1]=c,s=k>>>16&65535,e[d>>1]=s,f=(k>>>1)-(k>>16<<15)&65535,e[m>>1]=f,h=h+1<<16>>16,!(h<<16>>16>=t<<16>>16);)r=r+2|0}function J3(i){return i=i|0,i?(e[i>>1]=0,i=0):i=-1,i|0}function C6(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0;if(l=o<<16>>16,s=r+(l+-1<<1)|0,l=l+-2|0,u=e[s>>1]|0,o<<16>>16<2)o=t<<16>>16;else for(o=t<<16>>16,f=0,r=r+(l<<1)|0;t=(g(e[r>>1]|0,o)|0)>>15,(t|0)>32767&&(a[n>>2]=1,t=32767),e[s>>1]=y1(e[s>>1]|0,t&65535,n)|0,s=s+-2|0,f=f+1<<16>>16,!((f<<16>>16|0)>(l|0));)r=r+-2|0;if(o=(g(e[i>>1]|0,o)|0)>>15,(o|0)<=32767){l=o,l=l&65535,f=e[s>>1]|0,n=y1(f,l,n)|0,e[s>>1]=n,e[i>>1]=u;return}a[n>>2]=1,l=32767,l=l&65535,f=e[s>>1]|0,n=y1(f,l,n)|0,e[s>>1]=n,e[i>>1]=u}function ee(i){i=i|0;var r=0,t=0,o=0;if(!i)return o=-1,o|0;t2(i+104|0,0,340)|0,r=i+102|0,t=i,o=t+100|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return _3(r)|0,J3(i+100|0)|0,o=0,o|0}function ie(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0;if(S=C,C=C+96|0,k=S+22|0,b=S,E=S+44|0,i2(i+124|0,t|0,320)|0,d=E+22|0,m=i+100|0,w=i+80|0,h=i+102|0,(r&-2|0)==6){for(c=0;;){X0(o,702,k),X0(o,722,b),u=i+104+(c+10<<1)|0,N2(k,u,i,40),f=E,s=k,r=f+22|0;do e[f>>1]=e[s>>1]|0,f=f+2|0,s=s+2|0;while((f|0)<(r|0));f=d,r=f+22|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(r|0));f0(b,E,E,22,d,0),r=0,f=21;do{if(s=e[E+(f<<16>>16<<1)>>1]|0,s=g(s,s)|0,(s|0)==1073741824){R=7;break}l=s<<1,s=l+r|0,(l^r|0)>-1&(s^r|0)<0?(a[n>>2]=1,r=(r>>>31)+2147483647|0):r=s,f=f+-1<<16>>16}while(f<<16>>16>-1);for((R|0)==7&&(R=0,a[n>>2]=1),l=r>>>16&65535,s=20,r=0,f=20;;){if(s=g(e[E+(s+1<<1)>>1]|0,e[E+(s<<1)>>1]|0)|0,(s|0)==1073741824){R=13;break}if(O=s<<1,s=O+r|0,(O^r|0)>-1&(s^r|0)<0?(a[n>>2]=1,r=(r>>>31)+2147483647|0):r=s,s=(f&65535)+-1<<16>>16,s<<16>>16>-1)s=s<<16>>16,f=f+-1|0;else break}if((R|0)==13&&(R=0,a[n>>2]=1),r=r>>16,(r|0)<1?r=0:r=r0((r*26214|0)>>>15&65535,l)|0,C6(m,i,r,40,n),r=t+(c<<1)|0,f0(b,i,r,40,w,1),E6(h,u,r,29491,40,n),r=(c<<16)+2621440|0,(r|0)<10485760)c=r>>16,o=o+22|0;else break}f=i+104|0,s=i+424|0,r=f+20|0;do _[f>>0]=_[s>>0]|0,f=f+1|0,s=s+1|0;while((f|0)<(r|0));C=S;return}else{for(c=0;;){X0(o,742,k),X0(o,762,b),u=i+104+(c+10<<1)|0,N2(k,u,i,40),f=E,s=k,r=f+22|0;do e[f>>1]=e[s>>1]|0,f=f+2|0,s=s+2|0;while((f|0)<(r|0));f=d,r=f+22|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(r|0));f0(b,E,E,22,d,0),r=0,f=21;do{if(s=e[E+(f<<16>>16<<1)>>1]|0,s=g(s,s)|0,(s|0)==1073741824){R=22;break}O=s<<1,s=O+r|0,(O^r|0)>-1&(s^r|0)<0?(a[n>>2]=1,r=(r>>>31)+2147483647|0):r=s,f=f+-1<<16>>16}while(f<<16>>16>-1);for((R|0)==22&&(R=0,a[n>>2]=1),l=r>>>16&65535,s=20,r=0,f=20;;){if(s=g(e[E+(s+1<<1)>>1]|0,e[E+(s<<1)>>1]|0)|0,(s|0)==1073741824){R=28;break}if(O=s<<1,s=O+r|0,(O^r|0)>-1&(s^r|0)<0?(a[n>>2]=1,r=(r>>>31)+2147483647|0):r=s,s=(f&65535)+-1<<16>>16,s<<16>>16>-1)s=s<<16>>16,f=f+-1|0;else break}if((R|0)==28&&(R=0,a[n>>2]=1),r=r>>16,(r|0)<1?r=0:r=r0((r*26214|0)>>>15&65535,l)|0,C6(m,i,r,40,n),r=t+(c<<1)|0,f0(b,i,r,40,w,1),E6(h,u,r,29491,40,n),r=(c<<16)+2621440|0,(r|0)<10485760)c=r>>16,o=o+22|0;else break}f=i+104|0,s=i+424|0,r=f+20|0;do _[f>>0]=_[s>>0]|0,f=f+1|0,s=s+1|0;while((f|0)<(r|0));C=S;return}}function te(i,r){i=i|0,r=r|0;var t=0,o=0;return!i||(a[i>>2]=0,t=o0(1764)|0,!t)?(i=-1,i|0):!((B3(t)|0)<<16>>16)&&(o=t+1748|0,(O6(o)|0)<<16>>16==0)?(R5(t,0)|0,ee(t+1304|0)|0,O6(o)|0,a[t+1760>>2]=0,a[i>>2]=t,i=0,i|0):(r=a[t>>2]|0,r?(G1(r),a[t>>2]=0,i=-1,i|0):(i=-1,i|0))}function re(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function oe(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(R=C,C=C+208|0,E=R+88|0,b=R,k=i+1164|0,s=a[i+1256>>2]|0,(o+-5|0)>>>0<2){if(h=s+16|0,(e[h>>1]|0)>0)for(w=a[(a[i+1260>>2]|0)+32>>2]|0,m=0,s=0;;){if(d=w+(m<<1)|0,u=e[d>>1]|0,u<<16>>16>0){for(l=t,c=0,f=0;f=D[l>>1]|f<<1&131070,c=c+1<<16>>16,!(c<<16>>16>=u<<16>>16);)l=l+2|0;f=f&65535}else f=0;if(e[E+(m<<1)>>1]=f,s=s+1<<16>>16,s<<16>>16<(e[h>>1]|0))t=t+(e[d>>1]<<1)|0,m=s<<16>>16;else break}}else if(w=s+(r<<1)|0,(e[w>>1]|0)>0)for(h=a[(a[i+1260>>2]|0)+(r<<2)>>2]|0,d=0,s=0;;){if(m=h+(d<<1)|0,u=e[m>>1]|0,u<<16>>16>0){for(l=t,c=0,f=0;f=D[l>>1]|f<<1&131070,c=c+1<<16>>16,!(c<<16>>16>=u<<16>>16);)l=l+2|0;f=f&65535}else f=0;if(e[E+(d<<1)>>1]=f,s=s+1<<16>>16,s<<16>>16<(e[w>>1]|0))t=t+(e[m>>1]<<1)|0,d=s<<16>>16;else break}x3(i,r,E,o,n,b),ie(i+1304|0,r,n,b,k),$3(i+1748|0,n,160,k),s=0;do i=n+(s<<1)|0,e[i>>1]=D[i>>1]&65528,s=s+1|0;while((s|0)!=160);C=R}function ne(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;if(s=a[o+100>>2]|0,f=(D[(a[o+96>>2]|0)+(i<<1)>>1]|0)+65535|0,o=f&65535,n=o<<16>>16>-1,i>>>0<8){if(!n)return;for(s=a[s+(i<<2)>>2]|0,n=f<<16>>16;e[t+(e[s+(n<<1)>>1]<<1)>>1]=(x[r+(n>>3)>>0]|0)>>>(n&7^7)&1,o=o+-1<<16>>16,o<<16>>16>-1;)n=o<<16>>16;return}else{if(!n)return;for(n=f<<16>>16;e[t+(n<<1)>>1]=(x[r+(n>>3)>>0]|0)>>>(n&7^7)&1,o=o+-1<<16>>16,o<<16>>16>-1;)n=o<<16>>16;return}}function se(i,r,t){return i=i|0,r=r|0,t=t|0,i=ci(i,t,31764)|0,((li(r)|0|i)<<16>>16!=0)<<31>>31|0}function ae(i,r){i=i|0,r=r|0,di(i),ui(r)}function fe(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0;if(m=C,C=C+512|0,l=m+8|0,u=m+4|0,c=m,a[c>>2]=0,d=f<<16>>16==3,!((f&65535)<2|d&1)){if(f<<16>>16!=2)return n=-1,C=m,n|0;e4(i,t,o,l+2|0,c),i=a[c>>2]|0,a[s>>2]=i,J6(r,i,u),r=a[u>>2]|0,e[l>>1]=r,e[l+490>>1]=(r|0)==3?-1:t&65535,_[n>>0]=r,r=1;do l=l+1|0,_[n+r>>0]=_[l>>0]|0,r=r+1|0;while((r|0)!=492);return l=492,C=m,l|0}if(e4(i,t,o,l,c),J6(r,a[c>>2]|0,u),o=a[u>>2]|0,(o|0)!=3){if(r=a[c>>2]|0,a[s>>2]=r,(r|0)==8){switch(o|0){case 1:{e[l+70>>1]=0;break}case 2:{c=l+70|0,e[c>>1]=D[c>>1]|0|1;break}default:}e[l+72>>1]=t&1,e[l+74>>1]=t>>>1&1,e[l+76>>1]=t>>>2&1,r=8}}else a[s>>2]=15,r=15;if(d)return Le(r,l,n,(a[i+4>>2]|0)+2392|0),n=e[3404+(a[s>>2]<<16>>16<<1)>>1]|0,C=m,n|0;switch(f<<16>>16){case 0:return Ce(r,l,n,(a[i+4>>2]|0)+2392|0),n=e[3404+(a[s>>2]<<16>>16<<1)>>1]|0,C=m,n|0;case 1:return Oe(r,l,n,(a[i+4>>2]|0)+2392|0),n=e[3436+(a[s>>2]<<16>>16<<1)>>1]|0,C=m,n|0;default:return n=-1,C=m,n|0}return 0}function M5(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0;for(I=C,C=C+480|0,A=I,s=240,c=n,u=i,l=A,f=0;;){if(N=((g(e[c>>1]|0,e[u>>1]|0)|0)+16384|0)>>>15,e[l>>1]=N,N=N<<16,f=(g(N>>15,N>>16)|0)+f|0,(f|0)<0){d=4;break}if(s=s+-1|0,(s&65535)<<16>>16)c=c+2|0,u=u+2|0,l=l+2|0;else{s=0;break}}if((d|0)==4){if(f=s&65535,l=240-s|0,!(f<<16>>16))s=0;else for(c=f,u=n+(l<<1)|0,s=i+(l<<1)|0,f=A+(l<<1)|0;;)if(e[f>>1]=((g(e[u>>1]|0,e[s>>1]|0)|0)+16384|0)>>>15,c=c+-1<<16>>16,c<<16>>16)u=u+2|0,s=s+2|0,f=f+2|0;else{s=0;break}do{for(u=s&65535,s=120,l=A,f=0;N=(e[l>>1]|0)>>>2,S=l+2|0,e[l>>1]=N,N=N<<16>>16,N=g(N,N)|0,O=(e[S>>1]|0)>>>2,e[S>>1]=O,O=O<<16>>16,f=((g(O,O)|0)+N<<1)+f|0,s=s+-1<<16>>16,s<<16>>16;)l=l+4|0;s=u+4|0}while((f|0)<1)}if(N=f+1|0,O=(B1(N)|0)<<16>>16,N=N<<O,e[t>>1]=N>>>16,e[o>>1]=(N>>>1)-(N>>16<<15),N=A+478|0,c=r<<16>>16,r<<16>>16<=0)return A=O-s|0,A=A&65535,C=I,A|0;for(b=A+476|0,E=O+1|0,R=239-c|0,S=A+(236-c<<1)|0,r=c,t=t+(c<<1)|0,o=o+(c<<1)|0;;){if(d=g((R>>>1)+65535&65535,-2)|0,u=A+(d+236<<1)|0,d=S+(d<<1)|0,n=240-r|0,k=n+-1|0,l=A+(k<<1)|0,i=k>>>1&65535,n=A+(n+-2<<1)|0,c=g(e[N>>1]|0,e[l>>1]|0)|0,!(i<<16>>16))d=n,u=b;else for(h=b,w=N;f=l+-4|0,m=w+-4|0,c=(g(e[h>>1]|0,e[n>>1]|0)|0)+c|0,i=i+-1<<16>>16,c=(g(e[m>>1]|0,e[f>>1]|0)|0)+c|0,i<<16>>16;)n=l+-6|0,h=w+-6|0,l=f,w=m;if(k&1&&(c=(g(e[u>>1]|0,e[d>>1]|0)|0)+c|0),k=c<<E,e[t>>1]=k>>>16,e[o>>1]=(k>>>1)-(k>>16<<15),(r&65535)+-1<<16>>16<<16>>16>0)R=R+1|0,S=S+2|0,r=r+-1|0,t=t+-2|0,o=o+-2|0;else break}return A=O-s|0,A=A&65535,C=I,A|0}function L6(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0;z=C,C=C+3440|0,T=z+3420|0,I=z+3400|0,F=z+3224|0,M=z,N=z+3320|0,B=z+3240|0,A=z+24|0,z2(t,i,N,2,l),$6(N,r,B,F,5,I,5,l),A2(t,B,A,l),Q6(10,5,5,N,A,I,F,M,l),r=o,l=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(l|0));e[s>>1]=65535,e[s+2>>1]=65535,e[s+4>>1]=65535,e[s+6>>1]=65535,e[s+8>>1]=65535,w=0,h=M,k=T;do{i=e[h>>1]|0,h=h+2|0,u=(i*6554|0)>>>15,c=u<<16>>16,r=o+(i<<1)|0,l=e[r>>1]|0,(e[B+(i<<1)>>1]|0)>0?(e[r>>1]=l+4096,e[k>>1]=8192,d=u):(e[r>>1]=l+61440,e[k>>1]=-8192,d=c+8|0),k=k+2|0,m=d&65535,r=i-(u<<2)-c<<16>>16,u=s+(r<<1)|0,l=e[u>>1]|0,i=l<<16>>16;do if(l<<16>>16>=0)if(c=d<<16>>16,(c^i)&8)if(r=s+(r+5<<1)|0,(i&7)>>>0>(c&7)>>>0){e[r>>1]=m;break}else{e[r>>1]=l,e[u>>1]=m;break}else if(r=s+(r+5<<1)|0,(i|0)>(c|0)){e[r>>1]=l,e[u>>1]=m;break}else{e[r>>1]=m;break}else e[u>>1]=m;while(!1);w=w+1<<16>>16}while(w<<16>>16<10);for(k=T+2|0,w=T+4|0,d=T+6|0,c=T+8|0,u=T+10|0,r=T+12|0,l=T+14|0,i=T+16|0,b=T+18|0,E=40,R=t+(0-(e[M>>1]|0)<<1)|0,S=t+(0-(e[M+2>>1]|0)<<1)|0,O=t+(0-(e[M+4>>1]|0)<<1)|0,N=t+(0-(e[M+6>>1]|0)<<1)|0,A=t+(0-(e[M+8>>1]|0)<<1)|0,I=t+(0-(e[M+10>>1]|0)<<1)|0,F=t+(0-(e[M+12>>1]|0)<<1)|0,B=t+(0-(e[M+14>>1]|0)<<1)|0,o=t+(0-(e[M+16>>1]|0)<<1)|0,h=t+(0-(e[M+18>>1]|0)<<1)|0,m=n;q=(g(e[T>>1]|0,e[R>>1]|0)|0)>>7,U=(g(e[k>>1]|0,e[S>>1]|0)|0)>>7,H=(g(e[w>>1]|0,e[O>>1]|0)|0)>>7,K=(g(e[d>>1]|0,e[N>>1]|0)|0)>>7,Y=(g(e[c>>1]|0,e[A>>1]|0)|0)>>7,j=(g(e[u>>1]|0,e[I>>1]|0)|0)>>7,M=(g(e[r>>1]|0,e[F>>1]|0)|0)>>7,t=(g(e[l>>1]|0,e[B>>1]|0)|0)>>>7,n=(g(e[i>>1]|0,e[o>>1]|0)|0)>>>7,e[m>>1]=(q+128+U+H+K+Y+j+M+t+n+((g(e[b>>1]|0,e[h>>1]|0)|0)>>>7)|0)>>>8,E=E+-1<<16>>16,E<<16>>16;)R=R+2|0,S=S+2|0,O=O+2|0,N=N+2|0,A=A+2|0,I=I+2|0,F=F+2|0,B=B+2|0,o=o+2|0,h=h+2|0,m=m+2|0;r=0;do l=s+(r<<1)|0,i=e[l>>1]|0,(r|0)<5?i=(D[f+((i&7)<<1)>>1]|i&8)&65535:i=e[f+((i&7)<<1)>>1]|0,e[l>>1]=i,r=r+1|0;while((r|0)!=10);C=z}function le(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0;if(o1=C,C=C+3456|0,H=o1+3448|0,Y=o1+3360|0,z=o1+3368|0,w=o1+3280|0,K=o1+3200|0,j=o1,q=(o&65535)<<17,W=t<<16>>16,U=t<<16>>16<40,U){o=q>>16,t=W;do c=(g(e[r+(t-W<<1)>>1]|0,o)|0)>>15,(c|0)>32767&&(a[l>>2]=1,c=32767),T=r+(t<<1)|0,e[T>>1]=n1(e[T>>1]|0,c&65535,l)|0,t=t+1|0;while((t&65535)<<16>>16!=40)}z2(r,i,z,1,l),a5(z,K,w,8),A2(r,K,j,l),T=Y+2|0,e[Y>>1]=0,e[T>>1]=1,i=1,c=0,m=1,w=0,d=-1;do{B=e[2830+(w<<1)>>1]|0,M=B<<16>>16,F=0;do{for(A=e[2834+(F<<1)>>1]|0,I=A<<16>>16,N=i,S=M,R=m,O=B,E=d;;){for(u=e[z+(S<<1)>>1]|0,k=e[j+(S*80|0)+(S<<1)>>1]|0,t=I,m=1,b=A,i=A,d=-1;o=n1(u,e[z+(t<<1)>>1]|0,l)|0,o=o<<16>>16,o=(g(o,o)|0)>>>15,h=(e[j+(S*80|0)+(t<<1)>>1]<<15)+32768+((e[j+(t*80|0)+(t<<1)>>1]|0)+k<<14)|0,((g(o<<16>>16,m<<16>>16)|0)-(g(h>>16,d<<16>>16)|0)<<1|0)>0&&(m=h>>>16&65535,i=b,d=o&65535),h=t+5|0,b=h&65535,!(b<<16>>16>=40);)t=h<<16>>16;if(((g(d<<16>>16,R<<16>>16)|0)-(g(m<<16>>16,E<<16>>16)|0)<<1|0)>0?(e[Y>>1]=O,e[T>>1]=i,c=O):(i=N,m=R,d=E),h=S+5|0,O=h&65535,O<<16>>16>=40)break;N=i,S=h<<16>>16,R=m,E=d}F=F+1|0}while((F|0)!=4);w=w+1|0}while((w|0)!=2);k=i,b=c,o=n,t=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(t|0));for(m=b,t=0,h=0,o=0;;){switch(c=m<<16>>16,u=e[K+(c<<1)>>1]|0,i=(c*6554|0)>>>15,m=i<<16,w=m>>15,d=c-(w+(i<<3)<<16>>17)|0,d<<16>>16|0){case 0:{w=m>>10,i=1;break}case 1:{(t&65535)<<16>>16?(w=i<<22>>16|16,i=1):i=0;break}case 2:{w=i<<22>>16|32,i=1;break}case 3:{w=i<<17>>16|1,i=0;break}case 4:{w=i<<22>>16|48,i=1;break}default:w=i,i=d&65535}if(w=w&65535,d=n+(c<<1)|0,u<<16>>16>0?(e[d>>1]=8191,e[H+(t<<1)>>1]=32767,c=i<<16>>16,i<<16>>16<0?(c=0-c<<16,(c|0)<983040?c=1>>>(c>>16)&65535:c=0):(j=1<<c,c=(j<<16>>16>>c|0)==1?j&65535:32767),o=n1(o,c,l)|0):(e[d>>1]=-8192,e[H+(t<<1)>>1]=-32768),c=n1(h,w,l)|0,t=t+1|0,(t|0)==2){h=c;break}m=e[Y+(t<<1)>>1]|0,h=c}e[f>>1]=o,w=H+2|0,m=e[H>>1]|0,i=0,d=r+(0-(b<<16>>16)<<1)|0,c=r+(0-(k<<16>>16)<<1)|0;do o=g(e[d>>1]|0,m)|0,d=d+2|0,(o|0)!=1073741824&&(i1=o<<1,!((o|0)>0&(i1|0)<0))?u=i1:(a[l>>2]=1,u=2147483647),t=g(e[w>>1]|0,e[c>>1]|0)|0,c=c+2|0,(t|0)!=1073741824?(o=(t<<1)+u|0,(t^u|0)>0&(o^u|0)<0&&(a[l>>2]=1,o=(u>>>31)+2147483647|0)):(a[l>>2]=1,o=2147483647),e[s+(i<<1)>>1]=S1(o,l)|0,i=i+1|0;while((i|0)!=40);if(!U)return C=o1,h|0;t=q>>16,o=W;do u=(g(e[n+(o-W<<1)>>1]|0,t)|0)>>15,(u|0)>32767&&(a[l>>2]=1,u=32767),s=n+(o<<1)|0,e[s>>1]=n1(e[s>>1]|0,u&65535,l)|0,o=o+1|0;while((o&65535)<<16>>16!=40);return C=o1,h|0}function ue(i,r,t,o,n,s,f,l,u,c){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0;var d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0;if(N=C,C=C+3456|0,k=N+3360|0,b=N+3368|0,E=N+3280|0,R=N+3200|0,S=N,O=n<<16>>16,w=O<<1,(w|0)==(O<<17>>16|0)?h=w:(a[c>>2]=1,h=n<<16>>16>0?32767:-32768),O=o<<16>>16,d=o<<16>>16<40,d){n=h<<16>>16,m=O;do o=t+(m<<1)|0,w=(g(e[t+(m-O<<1)>>1]|0,n)|0)>>15,(w|0)>32767&&(a[c>>2]=1,w=32767),e[o>>1]=n1(e[o>>1]|0,w&65535,c)|0,m=m+1|0;while((m&65535)<<16>>16!=40)}if(z2(t,r,b,1,c),a5(b,R,E,8),A2(t,R,S,c),ce(i,b,S,u,k),w=de(i,k,R,s,t,f,l,c)|0,!d)return C=N,w|0;m=h<<16>>16,n=O;do o=s+(n<<1)|0,d=(g(e[s+(n-O<<1)>>1]|0,m)|0)>>15,(d|0)>32767&&(a[c>>2]=1,d=32767),e[o>>1]=n1(e[o>>1]|0,d&65535,c)|0,n=n+1|0;while((n&65535)<<16>>16!=40);return C=N,w|0}function ce(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0;A=n+2|0,e[n>>1]=0,e[A>>1]=1,O=i<<16>>16<<1,s=1,N=0,i=-1;do{S=(N<<3)+O<<16>>16,u=e[o+(S<<1)>>1]|0,S=e[o+((S|1)<<1)>>1]|0,f=u<<16>>16;e:do if(u<<16>>16<40){if(R=S<<16>>16,S<<16>>16<40)E=s;else for(;;)if((i<<16>>16|0)<(0-(s<<16>>16)|0)?(e[n>>1]=u,e[A>>1]=S,l=1,i=-1):l=s,s=f+5|0,u=s&65535,u<<16>>16>=40){s=l;break e}else f=s<<16>>16,s=l;for(;;){for(k=e[t+(f*80|0)+(f<<1)>>1]|0,h=D[r+(f<<1)>>1]|0,w=R,s=1,b=S,l=S,c=-1;m=(D[r+(w<<1)>>1]|0)+h<<16>>16,m=(g(m,m)|0)>>>15,d=(e[t+(f*80|0)+(w<<1)>>1]<<15)+32768+((e[t+(w*80|0)+(w<<1)>>1]|0)+k<<14)|0,((g(m<<16>>16,s<<16>>16)|0)-(g(d>>16,c<<16>>16)|0)<<1|0)>0&&(s=d>>>16&65535,l=b,c=m&65535),d=w+5|0,b=d&65535,!(b<<16>>16>=40);)w=d<<16>>16;if(((g(c<<16>>16,E<<16>>16)|0)-(g(s<<16>>16,i<<16>>16)|0)<<1|0)>0?(e[n>>1]=u,e[A>>1]=l,i=c):s=E,f=f+5|0,u=f&65535,u<<16>>16>=40)break;f=f<<16>>16,E=s}}while(!1);N=N+1|0}while((N|0)!=2)}function de(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0;u=o,c=u+80|0;do e[u>>1]=0,u=u+2|0;while((u|0)<(c|0));u=e[r>>1]|0,w=(u*6554|0)>>>15,c=w<<16>>16,m=(748250>>>((u+(g(c,-5)|0)<<16>>16)+((i<<16>>16)*5|0)|0)&1|0)==0,d=(e[t+(u<<1)>>1]|0)>0,h=d?32767:-32768,e[o+(u<<1)>>1]=d?8191:-8192,u=r+2|0,i=e[u>>1]|0,o=o+(i<<1)|0,(e[t+(i<<1)>>1]|0)>0?(e[o>>1]=8191,t=32767,o=(d&1|2)&65535):(e[o>>1]=-8192,t=-32768,o=d&1),w=((i*6554|0)>>>15<<3)+(m?w:c+64|0)&65535,e[f>>1]=o,m=0,d=n+(0-(e[r>>1]|0)<<1)|0,o=n+(0-(e[u>>1]|0)<<1)|0;do u=g(h,e[d>>1]|0)|0,d=d+2|0,(u|0)==1073741824?(a[l>>2]=1,i=2147483647):i=u<<1,c=g(t,e[o>>1]|0)|0,o=o+2|0,(c|0)!=1073741824?(u=(c<<1)+i|0,(c^i|0)>0&(u^i|0)<0&&(a[l>>2]=1,u=(i>>>31)+2147483647|0)):(a[l>>2]=1,u=2147483647),e[s+(m<<1)>>1]=S1(u,l)|0,m=m+1|0;while((m|0)!=40);return w|0}function he(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0;if(l1=C,C=C+3440|0,i1=l1+3360|0,W=l1+3280|0,a1=l1+3200|0,o1=l1,d1=(o&65535)<<17,t1=t<<16>>16,u1=t<<16>>16<40,u1){t=d1>>16,u=t1;do o=(g(e[r+(u-t1<<1)>>1]|0,t)|0)>>15,(o|0)>32767&&(a[l>>2]=1,o=32767),q=r+(u<<1)|0,e[q>>1]=n1(e[q>>1]|0,o&65535,l)|0,u=u+1|0;while((u&65535)<<16>>16!=40)}for(z2(r,i,i1,1,l),a5(i1,a1,W,6),A2(r,a1,o1,l),q=1,c=2,d=1,o=0,u=1,i=-1,m=1;;){for(U=2,k=2;;){for(Y=0,K=0,H=m,j=k;;){if(K<<16>>16<40)for(B=H<<16>>16,M=H<<16>>16<40,T=j<<16>>16,z=j<<16>>16<40,I=K<<16>>16,F=K;;){if((e[W+(I<<1)>>1]|0)>-1){if(O=e[o1+(I*80|0)+(I<<1)>>1]|0,M)for(N=D[i1+(I<<1)>>1]|0,S=B,h=1,A=H,t=H,k=0,w=-1;E=(D[i1+(S<<1)>>1]|0)+N|0,R=E<<16>>16,R=(g(R,R)|0)>>>15,b=(e[o1+(I*80|0)+(S<<1)>>1]<<15)+32768+((e[o1+(S*80|0)+(S<<1)>>1]|0)+O<<14)|0,((g(R<<16>>16,h<<16>>16)|0)-(g(b>>16,w<<16>>16)|0)<<1|0)>0&&(h=b>>>16&65535,t=A,k=E&65535,w=R&65535),b=S+5|0,A=b&65535,!(A<<16>>16>=40);)S=b<<16>>16;else h=1,t=H,k=0;if(z)for(N=k&65535,A=t<<16>>16,S=(h<<16>>16<<14)+32768|0,R=T,k=1,O=j,w=j,h=-1;;)if(E=(D[i1+(R<<1)>>1]|0)+N<<16>>16,E=(g(E,E)|0)>>>15,b=S+(e[o1+(R*80|0)+(R<<1)>>1]<<12)+((e[o1+(I*80|0)+(R<<1)>>1]|0)+(e[o1+(A*80|0)+(R<<1)>>1]|0)<<13)|0,((g(E<<16>>16,k<<16>>16)|0)-(g(b>>16,h<<16>>16)|0)<<1|0)>0&&(k=b>>>16&65535,w=O,h=E&65535),b=R+5|0,O=b&65535,O<<16>>16>=40){S=k,R=h;break}else R=b<<16>>16;else S=1,w=j,R=-1;k=g(R<<16>>16,u<<16>>16)|0,(k|0)==1073741824?(a[l>>2]=1,b=2147483647):b=k<<1,k=g(S<<16>>16,i<<16>>16)|0,(k|0)==1073741824?(a[l>>2]=1,h=2147483647):h=k<<1,k=b-h|0,((k^b)&(h^b)|0)<0&&(a[l>>2]=1,k=(b>>>31)+2147483647|0),A=(k|0)>0,c=A?w:c,d=A?t:d,o=A?F:o,u=A?S:u,i=A?R:i}if(k=I+5|0,F=k&65535,F<<16>>16>=40)break;I=k<<16>>16}if(Y=Y+1<<16>>16,Y<<16>>16>=3)break;z=j,j=H,H=K,K=z}if(t=U+2|0,k=t&65535,k<<16>>16>=5)break;U=t&65535}if(t=q+2|0,m=t&65535,m<<16>>16<4)q=t&65535;else{k=c,c=d;break}}t=n,u=t+80|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(u|0));switch(R=o<<16>>16,i=e[a1+(R<<1)>>1]|0,o=(R*6554|0)>>>15,t=o<<16,u=R-(((t>>16)*327680|0)>>>16)|0,u<<16>>16|0){case 1:{o=t>>12;break}case 2:{o=t>>8,u=2;break}case 3:{o=o<<20>>16|8,u=1;break}case 4:{o=o<<24>>16|128,u=2;break}default:}switch(t=n+(R<<1)|0,i<<16>>16>0?(e[t>>1]=8191,A=32767,d=65536<<(u<<16>>16)>>>16&65535):(e[t>>1]=-8192,A=-32768,d=0),b=c<<16>>16,c=e[a1+(b<<1)>>1]|0,t=(b*6554|0)>>>15,u=t<<16,i=b-(((u>>16)*327680|0)>>>16)|0,i<<16>>16|0){case 1:{t=u>>12;break}case 2:{t=u>>8,i=2;break}case 3:{t=t<<20>>16|8,i=1;break}case 4:{t=t<<24>>16|128,i=2;break}default:}switch(u=n+(b<<1)|0,c<<16>>16>0?(e[u>>1]=8191,E=32767,d=(65536<<(i<<16>>16)>>>16)+(d&65535)&65535):(e[u>>1]=-8192,E=-32768),m=t+o|0,h=k<<16>>16,c=e[a1+(h<<1)>>1]|0,o=(h*6554|0)>>>15,t=o<<16,u=h-(((t>>16)*327680|0)>>>16)|0,u<<16>>16|0){case 1:{t=t>>12;break}case 2:{t=t>>8,u=2;break}case 3:{t=o<<20>>16|8,u=1;break}case 4:{t=o<<24>>16|128,u=2;break}default:t=o}o=n+(h<<1)|0,c<<16>>16>0?(e[o>>1]=8191,k=32767,o=(65536<<(u<<16>>16)>>>16)+(d&65535)&65535):(e[o>>1]=-8192,k=-32768,o=d),w=m+t|0,e[f>>1]=o,d=0,m=r+(0-R<<1)|0,i=r+(0-b<<1)|0,c=r+(0-h<<1)|0;do o=g(e[m>>1]|0,A)|0,m=m+2|0,(o|0)!=1073741824&&(c1=o<<1,!((o|0)>0&(c1|0)<0))?u=c1:(a[l>>2]=1,u=2147483647),o=g(e[i>>1]|0,E)|0,i=i+2|0,(o|0)!=1073741824?(t=(o<<1)+u|0,(o^u|0)>0&(t^u|0)<0&&(a[l>>2]=1,t=(u>>>31)+2147483647|0)):(a[l>>2]=1,t=2147483647),u=g(e[c>>1]|0,k)|0,c=c+2|0,(u|0)!=1073741824?(o=(u<<1)+t|0,(u^t|0)>0&(o^t|0)<0&&(a[l>>2]=1,o=(t>>>31)+2147483647|0)):(a[l>>2]=1,o=2147483647),e[s+(d<<1)>>1]=S1(o,l)|0,d=d+1|0;while((d|0)!=40);if(o=w&65535,!u1)return C=l1,o|0;u=d1>>16,t=t1;do i=(g(e[n+(t-t1<<1)>>1]|0,u)|0)>>15,(i|0)>32767&&(a[l>>2]=1,i=32767),s=n+(t<<1)|0,e[s>>1]=n1(e[s>>1]|0,i&65535,l)|0,t=t+1|0;while((t&65535)<<16>>16!=40);return C=l1,o|0}function we(i,r,t,o,n,s,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0,E1=0,T1=0,A1=0,j1=0,x1=0,L1=0;if(L1=C,C=C+3456|0,E1=L1+3448|0,Z=L1+3360|0,l1=L1+3368|0,w1=L1+3280|0,V=L1+3200|0,m1=L1,A1=(o&65535)<<17,x1=t<<16>>16,T1=t<<16>>16<40,T1){t=A1>>16,c=x1;do o=(g(e[r+(c-x1<<1)>>1]|0,t)|0)>>15,(o|0)>32767&&(a[u>>2]=1,o=32767),t1=r+(c<<1)|0,e[t1>>1]=n1(e[t1>>1]|0,o&65535,u)|0,c=c+1|0;while((c&65535)<<16>>16!=40)}z2(r,i,l1,1,u),a5(l1,V,w1,4),A2(r,V,m1,u),d1=Z+2|0,e[Z>>1]=0,c1=Z+4|0,e[d1>>1]=1,t1=Z+6|0,e[c1>>1]=2,e[t1>>1]=3,h=3,m=2,d=1,o=0,t=1,c=-1,w=3;do{for(i1=0,W=0,o1=w,a1=1,u1=2;;){if(W<<16>>16<40)for(j=a1<<16>>16,Y=a1<<16>>16<40,K=u1<<16>>16,H=u1<<16>>16<40,U=o1<<16>>16,q=o1<<16>>16<40,z=W<<16>>16,T=m,B=d,F=t,M=W;;){if((e[w1+(z<<1)>>1]|0)>-1){if(b=e[m1+(z*80|0)+(z<<1)>>1]|0,Y)for(k=D[l1+(z<<1)>>1]|0,E=j,A=1,m=a1,d=a1,O=0,N=-1;S=(D[l1+(E<<1)>>1]|0)+k|0,R=S<<16>>16,R=(g(R,R)|0)>>>15,I=(e[m1+(z*80|0)+(E<<1)>>1]<<15)+32768+((e[m1+(E*80|0)+(E<<1)>>1]|0)+b<<14)|0,((g(R<<16>>16,A<<16>>16)|0)-(g(I>>16,N<<16>>16)|0)<<1|0)>0&&(A=I>>>16&65535,d=m,O=S&65535,N=R&65535),I=E+5|0,m=I&65535,!(m<<16>>16>=40);)E=I<<16>>16;else A=1,d=a1,O=0;if(H)for(t=O&65535,i=d<<16>>16,b=(A<<16>>16<<14)+32768|0,E=K,I=1,k=u1,m=u1,N=0,O=-1;S=(D[l1+(E<<1)>>1]|0)+t|0,R=S<<16>>16,R=(g(R,R)|0)>>>15,A=b+(e[m1+(E*80|0)+(E<<1)>>1]<<12)+((e[m1+(z*80|0)+(E<<1)>>1]|0)+(e[m1+(i*80|0)+(E<<1)>>1]|0)<<13)|0,((g(R<<16>>16,I<<16>>16)|0)-(g(A>>16,O<<16>>16)|0)<<1|0)>0&&(I=A>>>16&65535,m=k,N=S&65535,O=R&65535),A=E+5|0,k=A&65535,!(k<<16>>16>=40);)E=A<<16>>16;else I=1,m=u1,N=0;if(q)for(b=N&65535,k=m<<16>>16,i=d<<16>>16,R=(I&65535)<<16|32768,S=U,t=1,E=o1,A=o1,I=-1;O=(D[l1+(S<<1)>>1]|0)+b<<16>>16,O=(g(O,O)|0)>>>15,N=(e[m1+(S*80|0)+(S<<1)>>1]<<12)+R+((e[m1+(i*80|0)+(S<<1)>>1]|0)+(e[m1+(k*80|0)+(S<<1)>>1]|0)+(e[m1+(z*80|0)+(S<<1)>>1]|0)<<13)|0,((g(O<<16>>16,t<<16>>16)|0)-(g(N>>16,I<<16>>16)|0)<<1|0)>0&&(t=N>>>16&65535,A=E,I=O&65535),N=S+5|0,E=N&65535,!(E<<16>>16>=40);)S=N<<16>>16;else t=1,A=o1,I=-1;((g(I<<16>>16,F<<16>>16)|0)-(g(t<<16>>16,c<<16>>16)|0)<<1|0)>0?(e[Z>>1]=M,e[d1>>1]=d,e[c1>>1]=m,e[t1>>1]=A,h=A,o=M,c=I):(m=T,d=B,t=F)}else m=T,d=B,t=F;if(S=z+5|0,M=S&65535,M<<16>>16>=40)break;z=S<<16>>16,T=m,B=d,F=t}if(i1=i1+1<<16>>16,i1<<16>>16>=4)break;U=u1,q=o1,u1=a1,a1=W,o1=U,W=q}w=w+1<<16>>16}while(w<<16>>16<5);I=h,A=m,N=d,O=o,o=n,t=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(t|0));for(i=O,t=0,c=0,o=0;;){switch(m=i<<16>>16,w=e[V+(m<<1)>>1]|0,i=m*13108>>16,d=m-((i*327680|0)>>>16)|0,i=e[l+(i<<1)>>1]|0,d<<16>>16|0){case 1:{h=i<<16>>16<<3&65535;break}case 2:{h=i<<16>>16<<6&65535;break}case 3:{h=i<<16>>16<<10&65535;break}case 4:{h=((i&65535)<<10|512)&65535,d=3;break}default:h=i}if(i=n+(m<<1)|0,w<<16>>16>0?(e[i>>1]=8191,i=32767,o=(65536<<(d<<16>>16)>>>16)+(o&65535)&65535):(e[i>>1]=-8192,i=-32768),e[E1+(t<<1)>>1]=i,c=(h&65535)+(c&65535)|0,t=t+1|0,(t|0)==4){S=c;break}i=e[Z+(t<<1)>>1]|0}e[f>>1]=o,b=E1+2|0,E=E1+4|0,R=E1+6|0,i=e[E1>>1]|0,k=0,d=r+(0-(O<<16>>16)<<1)|0,m=r+(0-(N<<16>>16)<<1)|0,w=r+(0-(A<<16>>16)<<1)|0,h=r+(0-(I<<16>>16)<<1)|0;do o=g(e[d>>1]|0,i)|0,d=d+2|0,(o|0)!=1073741824&&(j1=o<<1,!((o|0)>0&(j1|0)<0))?c=j1:(a[u>>2]=1,c=2147483647),o=g(e[b>>1]|0,e[m>>1]|0)|0,m=m+2|0,(o|0)!=1073741824?(t=(o<<1)+c|0,(o^c|0)>0&(t^c|0)<0&&(a[u>>2]=1,t=(c>>>31)+2147483647|0)):(a[u>>2]=1,t=2147483647),o=g(e[E>>1]|0,e[w>>1]|0)|0,w=w+2|0,(o|0)!=1073741824?(c=(o<<1)+t|0,(o^t|0)>0&(c^t|0)<0&&(a[u>>2]=1,c=(t>>>31)+2147483647|0)):(a[u>>2]=1,c=2147483647),t=g(e[R>>1]|0,e[h>>1]|0)|0,h=h+2|0,(t|0)!=1073741824?(o=(t<<1)+c|0,(t^c|0)>0&(o^c|0)<0&&(a[u>>2]=1,o=(c>>>31)+2147483647|0)):(a[u>>2]=1,o=2147483647),e[s+(k<<1)>>1]=S1(o,u)|0,k=k+1|0;while((k|0)!=40);if(o=S&65535,((x1<<16)+-2621440|0)>-1|T1^1)return C=L1,o|0;c=A1>>16,t=x1;do i=(g(e[n+(t-x1<<1)>>1]|0,c)|0)>>15,(i|0)>32767&&(a[u>>2]=1,i=32767),s=n+(t<<1)|0,e[s>>1]=n1(e[s>>1]|0,i&65535,u)|0,t=t+1|0;while((t&65535)<<16>>16!=40);return C=L1,o|0}function T6(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0;q=C,C=C+3440|0,b=q+3424|0,j=q+3408|0,Y=q+3240|0,E=q+3224|0,T=q+3328|0,k=q+3248|0,z=q+24|0,U=q+16|0,H=q,ge(t,i,T,2,4,4,f),$6(T,r,k,Y,4,j,4,f),A2(t,k,z,f),Q6(8,4,4,T,z,j,Y,E,f),r=o,i=r+80|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(i|0));e[H>>1]=-1,e[U>>1]=-1,B=H+2|0,e[B>>1]=-1,M=U+2|0,e[M>>1]=-1,T=H+4|0,e[T>>1]=-1,z=U+4|0,e[z>>1]=-1,Y=H+6|0,e[Y>>1]=-1,j=U+6|0,e[j>>1]=-1,w=0;do{d=e[E+(w<<1)>>1]|0,r=d>>>2,u=r&65535,i=d&3,c=(e[k+(d<<1)>>1]|0)>0,d=o+(d<<1)|0,h=c&1^1,e[d>>1]=(D[d>>1]|0)+(c?8191:57345),e[b+(w<<1)>>1]=c?32767:-32768,c=H+(i<<1)|0,d=e[c>>1]|0;do if(d<<16>>16>=0)if(m=U+(i<<1)|0,l=(d<<16>>16|0)<=(r<<16>>16|0),r=H+((i|4)<<1)|0,(h&65535|0)==(D[m>>1]&1|0))if(l){e[r>>1]=u;break}else{e[r>>1]=d,e[c>>1]=u,e[m>>1]=h;break}else if(l){e[r>>1]=d,e[c>>1]=u,e[m>>1]=h;break}else{e[r>>1]=u;break}else e[c>>1]=u,e[U+(i<<1)>>1]=h;while(!1);w=w+1|0}while((w|0)!=8);R=b+2|0,S=b+4|0,O=b+6|0,N=b+8|0,A=b+10|0,I=b+12|0,F=b+14|0,b=e[b>>1]|0,w=0,m=t+(0-(e[E>>1]|0)<<1)|0,d=t+(0-(e[E+2>>1]|0)<<1)|0,c=t+(0-(e[E+4>>1]|0)<<1)|0,u=t+(0-(e[E+6>>1]|0)<<1)|0,r=t+(0-(e[E+8>>1]|0)<<1)|0,i=t+(0-(e[E+10>>1]|0)<<1)|0,l=t+(0-(e[E+12>>1]|0)<<1)|0,t=t+(0-(e[E+14>>1]|0)<<1)|0;do h=g(e[m>>1]|0,b)|0,m=m+2|0,(h|0)!=1073741824&&(K=h<<1,!((h|0)>0&(K|0)<0))?h=K:(a[f>>2]=1,h=2147483647),k=g(e[R>>1]|0,e[d>>1]|0)|0,d=d+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0?(a[f>>2]=1,h=(h>>>31)+2147483647|0):h=o):(a[f>>2]=1,h=2147483647),k=g(e[S>>1]|0,e[c>>1]|0)|0,c=c+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0&&(a[f>>2]=1,o=(h>>>31)+2147483647|0)):(a[f>>2]=1,o=2147483647),k=g(e[O>>1]|0,e[u>>1]|0)|0,u=u+2|0,(k|0)!=1073741824?(h=(k<<1)+o|0,(k^o|0)>0&(h^o|0)<0&&(a[f>>2]=1,h=(o>>>31)+2147483647|0)):(a[f>>2]=1,h=2147483647),k=g(e[N>>1]|0,e[r>>1]|0)|0,r=r+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0&&(a[f>>2]=1,o=(h>>>31)+2147483647|0)):(a[f>>2]=1,o=2147483647),k=g(e[A>>1]|0,e[i>>1]|0)|0,i=i+2|0,(k|0)!=1073741824?(h=(k<<1)+o|0,(k^o|0)>0&(h^o|0)<0&&(a[f>>2]=1,h=(o>>>31)+2147483647|0)):(a[f>>2]=1,h=2147483647),k=g(e[I>>1]|0,e[l>>1]|0)|0,l=l+2|0,(k|0)!=1073741824?(o=(k<<1)+h|0,(k^h|0)>0&(o^h|0)<0&&(a[f>>2]=1,o=(h>>>31)+2147483647|0)):(a[f>>2]=1,o=2147483647),k=g(e[F>>1]|0,e[t>>1]|0)|0,t=t+2|0,(k|0)!=1073741824?(h=(k<<1)+o|0,(k^o|0)>0&(h^o|0)<0&&(a[f>>2]=1,h=(o>>>31)+2147483647|0)):(a[f>>2]=1,h=2147483647),e[n+(w<<1)>>1]=S1(h,f)|0,w=w+1|0;while((w|0)!=40);if(e[s>>1]=e[U>>1]|0,e[s+2>>1]=e[M>>1]|0,e[s+4>>1]=e[z>>1]|0,e[s+6>>1]=e[j>>1]|0,i=e[H>>1]|0,r=e[H+8>>1]|0,l=e[B>>1]|0,e[s+8>>1]=r<<1&2|i&1|l<<2&4|(((r>>1)*327680|0)+(i>>>1<<16)+(g(l>>1,1638400)|0)|0)>>>13&65528,l=e[T>>1]|0,i=e[H+12>>1]|0,r=e[H+10>>1]|0,e[s+10>>1]=i<<1&2|l&1|r<<2&4|(((i>>1)*327680|0)+(l>>>1<<16)+(g(r>>1,1638400)|0)|0)>>>13&65528,r=e[H+14>>1]|0,l=e[Y>>1]|0,i=l<<16>>16>>>1,!(r&2)){n=i,f=r<<16>>16,U=f>>1,U=U*327680|0,n=n<<16,U=n+U|0,U=U<<5,U=U>>16,U=U|12,U=U*2622|0,U=U>>>16,n=l&65535,n=n&1,f=f<<17,f=f&131072,U=U<<18,f=U|f,f=f>>>16,n=f|n,n=n&65535,s=s+12|0,e[s>>1]=n,C=q;return}n=4-(i<<16>>16)|0,f=r<<16>>16,U=f>>1,U=U*327680|0,n=n<<16,U=n+U|0,U=U<<5,U=U>>16,U=U|12,U=U*2622|0,U=U>>>16,n=l&65535,n=n&1,f=f<<17,f=f&131072,U=U<<18,f=U|f,f=f>>>16,n=f|n,n=n&65535,s=s+12|0,e[s>>1]=n,C=q}function I6(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0;if(b=t<<16>>16,s=0-b|0,t=n+(s<<2)|0,n=((b-(o<<16>>16)|0)>>>2)+1&65535,!(n<<16>>16<=0)){if(b=r<<16>>16>>>1&65535,!(b<<16>>16)){for(;a[t>>2]=0,a[t+4>>2]=0,a[t+8>>2]=0,a[t+12>>2]=0,n<<16>>16>1;)t=t+16|0,n=n+-1<<16>>16;return}for(k=i+(s<<1)|0;;){for(c=k+4|0,m=e[c>>1]|0,l=e[k>>1]|0,d=m,u=b,w=i,h=k,k=k+8|0,f=0,s=0,o=0,r=0;R=e[w>>1]|0,E=(g(l<<16>>16,R)|0)+f|0,f=e[h+2>>1]|0,s=(g(f,R)|0)+s|0,l=(g(d<<16>>16,R)|0)+o|0,o=e[h+6>>1]|0,d=(g(o,R)|0)+r|0,r=e[w+2>>1]|0,f=E+(g(r,f)|0)|0,s=s+(g(m<<16>>16,r)|0)|0,c=c+4|0,o=l+(g(r,o)|0)|0,l=e[c>>1]|0,r=d+(g(l<<16>>16,r)|0)|0,u=u+-1<<16>>16,!!(u<<16>>16);)R=m,d=l,m=e[h+8>>1]|0,w=w+4|0,h=h+4|0,l=R;if(a[t>>2]=f<<1,a[t+4>>2]=s<<1,a[t+8>>2]=o<<1,a[t+12>>2]=r<<1,n<<16>>16<=1)break;t=t+16|0,n=n+-1<<16>>16}}}function me(i,r,t,o,n,s,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0;N=C,C=C+16|0,S=N+2|0,O=N;do if(n<<16>>16>0){for(k=o<<16>>16,E=0,m=0,o=0,d=0,b=0;;)if(c=e[i+(E<<1)>>1]|0,w=c<<16>>16,m=(g(w,w)|0)+m|0,w=e[r+(E<<1)>>1]|0,o=(g(w,w)|0)+o|0,d=(g(e[t+(E<<1)>>1]|0,w)|0)+d|0,w=g(w,k)|0,(w|0)==1073741824?(a[u>>2]=1,h=2147483647):h=w<<1,w=h<<1,w=(y1(c,S1((w>>1|0)==(h|0)?w:h>>31^2147483647,u)|0,u)|0)<<16>>16,w=g(w,w)|0,(w|0)!=1073741824?(c=(w<<1)+b|0,(w^b|0)>0&(c^b|0)<0&&(a[u>>2]=1,c=(b>>>31)+2147483647|0)):(a[u>>2]=1,c=2147483647),E=E+1|0,(E&65535)<<16>>16==n<<16>>16){b=c;break}else b=c;if(m=m<<1,o=o<<1,d=d<<1,(m|0)>=0){if((m|0)<400){c=b,R=14;break}}else a[u>>2]=1,m=2147483647;h=B1(m)|0,w=h<<16>>16,h<<16>>16>0?(c=m<<w,(c>>w|0)!=(m|0)&&(c=m>>31^2147483647)):(c=0-w<<16,(c|0)<2031616?c=m>>(c>>16):c=0),e[s>>1]=c>>>16,m=o,k=d,c=b,o=15-(h&65535)&65535}else o=0,d=0,c=0,R=14;while(!1);if((R|0)==14&&(e[s>>1]=0,m=o,k=d,o=-15),e[f>>1]=o,(m|0)<0&&(a[u>>2]=1,m=2147483647),w=B1(m)|0,d=w<<16>>16,w<<16>>16>0?(o=m<<d,(o>>d|0)!=(m|0)&&(o=m>>31^2147483647)):(o=0-d<<16,(o|0)<2031616?o=m>>(o>>16):o=0),e[s+2>>1]=o>>>16,e[f+2>>1]=15-(w&65535),m=B1(k)|0,d=m<<16>>16,m<<16>>16>0?(o=k<<d,(o>>d|0)!=(k|0)&&(o=k>>31^2147483647)):(o=0-d<<16,(o|0)<2031616?o=k>>(o>>16):o=0),e[s+4>>1]=o>>>16,e[f+4>>1]=2-(m&65535),m=B1(c)|0,o=m<<16>>16,m<<16>>16>0?(d=c<<o,(d>>o|0)!=(c|0)&&(d=c>>31^2147483647)):(o=0-o<<16,(o|0)<2031616?d=c>>(o>>16):d=0),o=d>>>16&65535,c=15-(m&65535)&65535,e[s+6>>1]=o,e[f+6>>1]=c,(d>>16|0)<=0){u=0,e[l>>1]=u,C=N;return}if(d=e[s>>1]|0,!(d<<16>>16)){u=0,e[l>>1]=u,C=N;return}o=r0(X1(d,1,u)|0,o)|0,o=(o&65535)<<16,d=((y1(c,e[f>>1]|0,u)|0)&65535)+3|0,c=d&65535,d=d<<16>>16,c<<16>>16>0?c=c<<16>>16<31?o>>d:0:(f=0-d<<16>>16,c=o<<f,c=(c>>f|0)==(o|0)?c:o>>31^2147483647),s2(c,S,O,u),O=qe((D[S>>1]|0)+65509&65535,e[O>>1]|0,u)|0,S=O<<13,u=S1((S>>13|0)==(O|0)?S:O>>31^2147483647,u)|0,e[l>>1]=u,C=N}function P5(i,r,t,o,n,s,f,l,u,c,d){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0;var m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0;switch(N=C,C=C+80|0,R=N,e[f>>1]=e[s>>1]|0,e[l>>1]=e[s+2>>1]|0,h=e[s+4>>1]|0,h<<16>>16==-32768?h=32767:h=0-(h&65535)&65535,e[f+2>>1]=h,e[l+2>>1]=(D[s+6>>1]|0)+1,i|0){case 0:case 5:{E=0,w=0,m=0,b=0;break}default:E=0,w=1,m=1,b=1}for(;;)if(k=(e[n+(E<<1)>>1]|0)>>>3,e[R+(E<<1)>>1]=k,k=k<<16>>16,h=g(k,k)|0,(h|0)!=1073741824?(s=(h<<1)+w|0,(h^w|0)>0&(s^w|0)<0?(a[d>>2]=1,w=(w>>>31)+2147483647|0):w=s):(a[d>>2]=1,w=2147483647),h=g(e[r+(E<<1)>>1]|0,k)|0,(h|0)!=1073741824?(s=(h<<1)+m|0,(h^m|0)>0&(s^m|0)<0?(a[d>>2]=1,m=(m>>>31)+2147483647|0):m=s):(a[d>>2]=1,m=2147483647),h=g(e[o+(E<<1)>>1]|0,k)|0,(h|0)!=1073741824?(s=(h<<1)+b|0,(h^b|0)>0&(s^b|0)<0&&(a[d>>2]=1,s=(b>>>31)+2147483647|0)):(a[d>>2]=1,s=2147483647),E=E+1|0,(E|0)==40){o=s,k=m;break}else b=s;switch(m=B1(w)|0,s=m<<16>>16,m<<16>>16>0?(h=w<<s,(h>>s|0)!=(w|0)&&(h=w>>31^2147483647)):(h=0-s<<16,(h|0)<2031616?h=w>>(h>>16):h=0),n=f+4|0,e[n>>1]=h>>>16,r=l+4|0,e[r>>1]=-3-(m&65535),w=B1(k)|0,s=w<<16>>16,w<<16>>16>0?(h=k<<s,(h>>s|0)!=(k|0)&&(h=k>>31^2147483647)):(h=0-s<<16,(h|0)<2031616?h=k>>(h>>16):h=0),s=h>>>16,e[f+6>>1]=(s|0)==32768?32767:0-s&65535,e[l+6>>1]=7-(w&65535),w=B1(o)|0,s=w<<16>>16,w<<16>>16>0?(h=o<<s,(h>>s|0)!=(o|0)&&(h=o>>31^2147483647)):(h=0-s<<16,(h|0)<2031616?h=o>>(h>>16):h=0),e[f+8>>1]=h>>>16,e[l+8>>1]=7-(w&65535),i|0){case 0:case 5:{h=0,m=0;break}default:{C=N;return}}do m=(g(e[R+(h<<1)>>1]|0,e[t+(h<<1)>>1]|0)|0)+m|0,h=h+1|0;while((h|0)!=40);if(s=m<<1,h=B1(s)|0,w=h<<16>>16,h<<16>>16>0?(m=s<<w,(m>>w|0)==(s|0)?(S=m,O=40):(S=s>>31^2147483647,O=40)):(m=0-w<<16,(m|0)<2031616&&(S=s>>(m>>16),O=40)),(O|0)==40&&(S>>16|0)>=1){d=X1(S>>>16&65535,1,d)|0,e[u>>1]=r0(d,e[n>>1]|0)|0,e[c>>1]=65528-(h&65535)-(D[r>>1]|0),C=N;return}e[u>>1]=0,e[c>>1]=0,C=N}function F6(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;s=0,n=0;do f=e[i+(s<<1)>>1]|0,n=(g(f,f)|0)+n|0,s=s+1|0;while((s|0)!=40);(n|0)<0&&(a[o>>2]=1,n=2147483647),o=B1(n)|0,i=o<<16>>16,o<<16>>16>0?(s=n<<i,(s>>i|0)==(n|0)?n=s:n=n>>31^2147483647):(i=0-i<<16,(i|0)<2031616?n=n>>(i>>16):n=0),e[t>>1]=n>>>16,e[r>>1]=16-(o&65535)}function pe(i,r,t,o,n,s,f,l,u,c,d,m,w){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0;var h=0,k=0,b=0,E=0;if(k=C,C=C+16|0,h=k,c>>>0<2){f=ue(d,i,r,t,o,f,l,h,a[m+76>>2]|0,w)|0,w=a[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,a[u>>2]=w+4,e[w+2>>1]=f,C=k;return}switch(c|0){case 2:{f=le(i,r,t,o,f,l,h,w)|0,w=a[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,a[u>>2]=w+4,e[w+2>>1]=f,C=k;return}case 3:{f=he(i,r,t,o,f,l,h,w)|0,w=a[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,a[u>>2]=w+4,e[w+2>>1]=f,C=k;return}default:{if((c&-2|0)==4){f=we(i,r,t,o,f,l,h,a[m+36>>2]|0,w)|0,w=a[u>>2]|0,e[w>>1]=f,f=e[h>>1]|0,a[u>>2]=w+4,e[w+2>>1]=f,C=k;return}if((c|0)!=6){if(d=n<<16>>16,d=(d<<17>>17|0)==(d|0)?d<<1:d>>>15^32767,n=t<<16>>16<40,!n){L6(i,s,r,f,l,a[u>>2]|0,a[m+36>>2]|0,w),a[u>>2]=(a[u>>2]|0)+20,C=k;return}h=t<<16>>16,c=d<<16>>16,o=h;do E=(g(e[r+(o-h<<1)>>1]|0,c)|0)>>>15&65535,b=r+(o<<1)|0,e[b>>1]=n1(e[b>>1]|0,E,w)|0,o=o+1|0;while((o&65535)<<16>>16!=40);if(L6(i,s,r,f,l,a[u>>2]|0,a[m+36>>2]|0,w),a[u>>2]=(a[u>>2]|0)+20,!n){C=k;return}n=t<<16>>16,c=d<<16>>16,h=n;do o=(g(e[f+(h-n<<1)>>1]|0,c)|0)>>15,(o|0)>32767&&(a[w>>2]=1,o=32767),E=f+(h<<1)|0,e[E>>1]=n1(e[E>>1]|0,o&65535,w)|0,h=h+1|0;while((h&65535)<<16>>16!=40);C=k;return}if(m=o<<16>>16,m=(m<<17>>17|0)==(m|0)?m<<1:m>>>15^32767,d=t<<16>>16<40,!d){T6(i,s,r,f,l,a[u>>2]|0,w),a[u>>2]=(a[u>>2]|0)+14,C=k;return}h=t<<16>>16,c=m<<16>>16,o=h;do n=(g(e[r+(o-h<<1)>>1]|0,c)|0)>>15,(n|0)>32767&&(a[w>>2]=1,n=32767),E=r+(o<<1)|0,e[E>>1]=n1(e[E>>1]|0,n&65535,w)|0,o=o+1|0;while((o&65535)<<16>>16!=40);if(T6(i,s,r,f,l,a[u>>2]|0,w),a[u>>2]=(a[u>>2]|0)+14,!d){C=k;return}n=t<<16>>16,c=m<<16>>16,h=n;do o=(g(e[f+(h-n<<1)>>1]|0,c)|0)>>15,(o|0)>32767&&(a[w>>2]=1,o=32767),E=f+(h<<1)|0,e[E>>1]=n1(e[E>>1]|0,o&65535,w)|0,h=h+1|0;while((h&65535)<<16>>16!=40);C=k;return}}}function ke(i){i=i|0;var r=0;return!i||(a[i>>2]=0,r=o0(4)|0,!r)?(i=-1,i|0):(Ge(r)|0)<<16>>16?(Y6(r),G1(r),i=-1,i|0):(X6(a[r>>2]|0)|0,a[i>>2]=r,i=0,i|0)}function be(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(Y6(r),G1(a[i>>2]|0),a[i>>2]=0))}function B6(i){return i=i|0,i?(X6(a[i>>2]|0)|0,i=0,i|0):(i=-1,i|0)}function ve(i,r,t,o,n,s,f,l,u,c,d,m,w,h,k,b,E,R,S,O){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0,O=O|0;var N=0,A=0,I=0,F=0;for(A=C,C=C+16|0,F=A+2|0,I=A,e[w>>1]=Ke(a[i>>2]|0,t,n,f,u,s,40,o,h,I,F,O)|0,i=e[F>>1]|0,o=a[E>>2]|0,a[E>>2]=o+2,e[o>>1]=i,H2(f,e[w>>1]|0,e[h>>1]|0,40,e[I>>1]|0,O),s5(f,s,m,40),e[k>>1]=je(t,u,m,b,40,O)|0,e[R>>1]=32767,c<<16>>16&&(N=e[k>>1]|0,N<<16>>16>15565)?N=ki(r,N,O)|0:N=0,t>>>0<2?(F=e[k>>1]|0,e[k>>1]=F<<16>>16>13926?13926:F,N<<16>>16&&(e[R>>1]=15565)):(N<<16>>16&&(e[R>>1]=15565,e[k>>1]=15565),(t|0)==7&&(I=Z6(7,e[R>>1]|0,k,0,0,S,O)|0,F=a[E>>2]|0,a[E>>2]=F+2,e[F>>1]=I)),w=e[k>>1]|0,N=0;I=g(e[m>>1]|0,w)|0,e[d>>1]=(D[u>>1]|0)-(I>>>14),I=(g(e[f>>1]|0,w)|0)>>>14,F=l+(N<<1)|0,e[F>>1]=(D[F>>1]|0)-I,N=N+1|0,(N|0)!=40;)f=f+2|0,u=u+2|0,d=d+2|0,m=m+2|0;C=A}function Ee(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,s=0;return s=C,C=C+16|0,n=s,!i||(a[i>>2]=0,t=o0(2532)|0,a[n>>2]=t,!t)?(i=-1,C=s,i|0):(o4(t+2392|0),a[t+2188>>2]=0,a[(a[n>>2]|0)+2192>>2]=0,a[(a[n>>2]|0)+2196>>2]=0,a[(a[n>>2]|0)+2200>>2]=0,a[(a[n>>2]|0)+2204>>2]=0,a[(a[n>>2]|0)+2208>>2]=0,a[(a[n>>2]|0)+2212>>2]=0,a[(a[n>>2]|0)+2220>>2]=0,o=a[n>>2]|0,a[o+2216>>2]=r,a[o+2528>>2]=0,t=o,!((ke(o+2196|0)|0)<<16>>16)&&!((Ai(o+2192|0)|0)<<16>>16)&&!((Fe(o+2200|0)|0)<<16>>16)&&!((Ze(o+2204|0)|0)<<16>>16)&&!((hi(o+2208|0)|0)<<16>>16)&&!((vi(o+2212|0)|0)<<16>>16)&&!((_e(o+2220|0,a[o+2432>>2]|0)|0)<<16>>16)&&!((Ve(o+2188|0)|0)<<16>>16)?(x6(o)|0,a[i>>2]=t,i=0,C=s,i|0):(N5(n),i=-1,C=s,i|0))}function N5(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(We(r+2188|0),Mi((a[i>>2]|0)+2192|0),Be((a[i>>2]|0)+2200|0),be((a[i>>2]|0)+2196|0),$e((a[i>>2]|0)+2204|0),mi((a[i>>2]|0)+2208|0),yi((a[i>>2]|0)+2212|0),Se((a[i>>2]|0)+2220|0),G1(a[i>>2]|0),a[i>>2]=0))}function x6(i){i=i|0;var r=0,t=0,o=0,n=0;if(!i)return n=-1,n|0;a[i+652>>2]=i+320,a[i+640>>2]=i+240,a[i+644>>2]=i+160,a[i+648>>2]=i+80,a[i+1264>>2]=i+942,a[i+1912>>2]=i+1590,o=i+1938|0,a[i+2020>>2]=o,a[i+2384>>2]=i+2304,r=i+2028|0,a[i+2024>>2]=i+2108,a[i+2528>>2]=0,t2(i|0,0,640)|0,t2(i+1282|0,0,308)|0,t2(i+656|0,0,286)|0,t=i+2224|0,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));o=r,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));r=i+1268|0,o=t,n=o+80|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(n|0));return e[r>>1]=40,e[i+1270>>1]=40,e[i+1272>>1]=40,e[i+1274>>1]=40,e[i+1276>>1]=40,Xe(a[i+2188>>2]|0)|0,f4(a[i+2192>>2]|0)|0,B6(a[i+2196>>2]|0)|0,xe(a[i+2200>>2]|0)|0,Qe(a[i+2204>>2]|0)|0,wi(a[i+2208>>2]|0)|0,Ei(a[i+2212>>2]|0)|0,Re(a[i+2220>>2]|0,a[i+2432>>2]|0)|0,e[i+2388>>1]=0,n=0,n|0}function ye(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0,E1=0,T1=0,A1=0,j1=0,x1=0,L1=0,K1=0,U1=0,n0=0,z1=0,Z1=0,s0=0,e0=0,q1=0,h0=0,j0=0,Y0=0,k0=0,l0=0,a0=0,O1=0;if(O1=C,C=C+1184|0,l1=O1,d=O1+1096|0,m=O1+1008|0,u=O1+904|0,h0=O1+928|0,j0=O1+824|0,V=O1+744|0,k0=O1+664|0,l0=O1+584|0,T1=O1+328|0,s0=O1+504|0,e0=O1+424|0,Y0=O1+344|0,a0=O1+248|0,E1=O1+168|0,U1=O1+88|0,z1=O1+68|0,Z1=O1+48|0,n0=O1+28|0,q1=O1+24|0,L1=O1+22|0,j1=O1+20|0,Z=O1+16|0,w1=O1+12|0,m1=O1+10|0,x1=O1+8|0,A1=O1+6|0,K1=O1+4|0,a[l1>>2]=o,t1=i+2528|0,f=i+652|0,R0(a[f>>2]|0,t|0,320)|0,a[n>>2]=r,c=i+2216|0,a[c>>2]|0?(o=_i(a[i+2212>>2]|0,a[f>>2]|0,t1)|0,c1=i+2220|0,t=c1,o=Me(a[c1>>2]|0,o,n,t1)|0):(t=i+2220|0,o=0),c1=i+2392|0,Ye(a[i+2188>>2]|0,r,a[i+644>>2]|0,a[i+648>>2]|0,d,c1,t1),l=i+2192|0,Pi(a[l>>2]|0,r,a[n>>2]|0,d,m,u,l1,t1),Ae(a[t>>2]|0,u,a[f>>2]|0,t1),(a[n>>2]|0)==8){De(a[t>>2]|0,o,a[(a[l>>2]|0)+40>>2]|0,(a[i+2200>>2]|0)+32|0,l1,t1),t2(i+1282|0,0,308)|0,f=i+2244|0,h=f+20|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));f=i+2284|0,h=f+20|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));f=a[i+2020>>2]|0,h=f+80|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));f=i+2028|0,h=f+80|0;do e[f>>1]=0,f=f+2|0;while((f|0)<(h|0));f4(a[l>>2]|0)|0,f=a[l>>2]|0,t=u,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=(a[l>>2]|0)+20|0,t=u,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));B6(a[i+2196>>2]|0)|0,e[i+2388>>1]=0,d1=0}else d1=pi(a[i+2208>>2]|0,a[l>>2]|0,t1)|0;if(o1=i+640|0,l=i+2264|0,f=i+1264|0,t=i+2204|0,o=i+2212|0,a1=i+1268|0,u1=i+1278|0,F5(r,2842,2862,2882,d,0,a[o1>>2]|0,l,a[f>>2]|0,t1),r>>>0>1?(L5(a[t>>2]|0,a[o>>2]|0,r,a[f>>2]|0,Z,a1,u1,0,a[c>>2]|0,t1),F5(r,2842,2862,2882,d,80,a[o1>>2]|0,l,a[f>>2]|0,t1),L5(a[t>>2]|0,a[o>>2]|0,r,(a[f>>2]|0)+160|0,Z+2|0,a1,u1,1,a[c>>2]|0,t1)):(F5(r,2842,2862,2882,d,80,a[o1>>2]|0,l,a[f>>2]|0,t1),L5(a[t>>2]|0,a[o>>2]|0,r,a[f>>2]|0,Z,a1,u1,1,a[c>>2]|0,t1),e[Z+2>>1]=e[Z>>1]|0),a[c>>2]|0&&gi(a[o>>2]|0,Z,t1),(a[n>>2]|0)==8)return l0=i+656|0,a0=i+976|0,R0(l0|0,a0|0,286)|0,a0=i+320|0,R0(i|0,a0|0,320)|0,C=O1,0;for(I=i+2224|0,F=i+2244|0,B=i+2284|0,M=i+2388|0,T=i+2020|0,z=i+1916|0,j=i+1912|0,Y=i+2024|0,K=i+2384|0,H=i+2196|0,U=i+2208|0,q=i+2464|0,i1=i+2200|0,W=i+2224|0,O=i+2244|0,N=i+1270|0,A=i+1280|0,S=0,c=0,u=0,b=0,E=0,l=0,R=-1;;){w=R,R=R+1<<16>>16,b=1-(b<<16>>16)|0,o=b&65535,k=(b&65535|0)!=0,t=a[n>>2]|0,f=(t|0)==0;do if(k)if(f){f=z1,t=I,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=Z1,t=F,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=n0,t=B,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));e[q1>>1]=e[M>>1]|0,r=(a[o1>>2]|0)+(S<<1)|0,f=20;break}else{r=(a[o1>>2]|0)+(S<<1)|0,f=19;break}else r=(a[o1>>2]|0)+(S<<1)|0,f?f=20:f=19;while(!1);if((f|0)==19)B5(t,2842,2862,2882,d,m,r,B,O,a[T>>2]|0,z,(a[j>>2]|0)+(S<<1)|0,a[Y>>2]|0,h0,s0,a[K>>2]|0);else if((f|0)==20&&(B5(0,2842,2862,2882,d,m,r,B,Z1,a[T>>2]|0,z,(a[j>>2]|0)+(S<<1)|0,a[Y>>2]|0,h0,s0,a[K>>2]|0),k)){f=U1,t=a[Y>>2]|0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0))}f=e0,t=s0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));switch(ve(a[H>>2]|0,a[U>>2]|0,a[n>>2]|0,E,Z,a[Y>>2]|0,(a[j>>2]|0)+(S<<1)|0,e0,h0,d1,j0,k0,w1,m1,x1,T1,l1,K1,a[q>>2]|0,t1),w<<16>>16){case-1:{(e[u1>>1]|0)>0&&(e[N>>1]=e[w1>>1]|0);break}case 2:{(e[A>>1]|0)>0&&(e[a1>>1]=e[w1>>1]|0);break}default:}pe(j0,a[Y>>2]|0,e[w1>>1]|0,e[M>>1]|0,e[x1>>1]|0,e0,V,l0,l1,a[n>>2]|0,R,c1,t1),Ue(a[i1>>2]|0,a[n>>2]|0,s0,(a[j>>2]|0)+(S<<1)|0,V,h0,j0,k0,l0,T1,o,e[K1>>1]|0,L1,j1,x1,A1,l1,c1,t1),bi(a[U>>2]|0,e[x1>>1]|0,t1),r=a[n>>2]|0;do if(r)f5(a[o1>>2]|0,r,E,e[x1>>1]|0,e[A1>>1]|0,m,s,h0,V,k0,l0,W,B,O,a[j>>2]|0,M,t1);else if(k){f=Y0,t=h0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=a0,t=l0,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));f=E1,t=V,h=f+80|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));u=e[w1>>1]|0,c=e[m1>>1]|0,f5(a[o1>>2]|0,0,E,e[x1>>1]|0,e[A1>>1]|0,m,s,h0,V,k0,l0,z1,B,Z1,a[j>>2]|0,M,t1),e[M>>1]=e[q1>>1]|0,l=E;break}else{f=B,t=n0,h=f+20|0;do e[f>>1]=e[t>>1]|0,f=f+2|0,t=t+2|0;while((f|0)<(h|0));k=l<<16>>16,H2((a[j>>2]|0)+(k<<1)|0,u,c,40,1,t1),s5((a[j>>2]|0)+(k<<1)|0,U1,k0,40),f5(a[o1>>2]|0,a[n>>2]|0,l,e[L1>>1]|0,e[j1>>1]|0,m+-22|0,s,Y0,E1,k0,a0,W,B,O,a[j>>2]|0,q1,t1),B5(a[n>>2]|0,2842,2862,2882,d,m,(a[o1>>2]|0)+(S<<1)|0,B,O,a[T>>2]|0,z,(a[j>>2]|0)+(S<<1)|0,a[Y>>2]|0,h0,s0,a[K>>2]|0),H2((a[j>>2]|0)+(S<<1)|0,e[w1>>1]|0,e[m1>>1]|0,40,1,t1),s5((a[j>>2]|0)+(S<<1)|0,a[Y>>2]|0,k0,40),f5(a[o1>>2]|0,a[n>>2]|0,E,e[x1>>1]|0,e[A1>>1]|0,m,s,h0,V,k0,l0,W,B,O,a[j>>2]|0,M,t1);break}while(!1);if(r=S+40|0,E=r&65535,E<<16>>16>=160)break;S=r<<16>>16,d=d+22|0,m=m+22|0}return R0(i+1282|0,i+1602|0,308)|0,l0=i+656|0,a0=i+976|0,R0(l0|0,a0|0,286)|0,a0=i+320|0,R0(i|0,a0|0,320)|0,C=O1,0}function s5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0;if(h=o<<16>>16,o<<16>>16>1)w=1;else return;for(;;){if(n=e[i>>1]|0,l=r+(w+-1<<1)|0,o=g(e[r+(w<<1)>>1]|0,n)|0,c=e[l>>1]|0,n=g(c<<16>>16,n)|0,f=(w+131071|0)>>>1,u=f&65535,s=e[i+2>>1]|0,!(u<<16>>16))r=l,f=c;else{d=(f<<1)+131070&131070,m=w-d|0,f=i;do b=(g(c<<16>>16,s)|0)+o|0,k=f,f=f+4|0,o=e[l+-2>>1]|0,s=(g(o,s)|0)+n|0,n=e[f>>1]|0,l=l+-4|0,o=b+(g(n,o)|0)|0,c=e[l>>1]|0,n=s+(g(c<<16>>16,n)|0)|0,u=u+-1<<16>>16,s=e[k+6>>1]|0;while(u<<16>>16);f=r+(m+-3<<1)|0,i=i+(d+2<<1)|0,r=f,f=e[f>>1]|0}if(o=(g(f<<16>>16,s)|0)+o|0,e[t>>1]=n>>>12,e[t+2>>1]=o>>>12,o=(w<<16)+131072>>16,(o|0)<(h|0))t=t+4|0,i=i+(1-w<<1)|0,w=o;else break}}function A2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0;for(F=C,C=C+80|0,I=F,f=20,s=i,n=1;A=e[s>>1]|0,A=(g(A,A)|0)+n|0,n=e[s+2>>1]|0,n=A+(g(n,n)|0)|0,f=f+-1<<16>>16,f<<16>>16;)s=s+4|0;if(n=n<<1,(n|0)<0)for(s=20,n=i,o=I;;)if(e[o>>1]=(e[n>>1]|0)>>>1,e[o+2>>1]=(e[n+2>>1]|0)>>>1,s=s+-1<<16>>16,s<<16>>16)n=n+4|0,o=o+4|0;else{A=I;break}else for(n=e2(n>>1,o)|0,(n|0)<16777215?n=((n>>9)*32440|0)>>>15<<16>>16:n=32440,f=20,s=i,o=I;;)if(e[o>>1]=((g(e[s>>1]|0,n)|0)+32|0)>>>6,e[o+2>>1]=((g(e[s+2>>1]|0,n)|0)+32|0)>>>6,f=f+-1<<16>>16,f<<16>>16)s=s+4|0,o=o+4|0;else{A=I;break}for(f=20,s=A,o=t+3198|0,n=0;N=e[s>>1]|0,N=(g(N,N)|0)+n|0,e[o>>1]=(N+16384|0)>>>15,O=e[s+2>>1]|0,n=(g(O,O)|0)+N|0,e[o+-82>>1]=(n+16384|0)>>>15,f=f+-1<<16>>16,f<<16>>16;)s=s+4|0,o=o+-164|0;for(N=r+78|0,O=1;;){if(n=39-O|0,i=t+3120+(n<<1)|0,o=t+(n*80|0)+78|0,n=r+(n<<1)|0,u=I+(O<<1)|0,s=65575-O|0,l=s&65535,f=e[A>>1]|0,!(l<<16>>16))l=N,s=0;else{for(b=s+65535&65535,R=b*41|0,S=(g(O,-40)|0)-R|0,E=0-O|0,R=E-R|0,E=E-b|0,k=O+b|0,h=e[u>>1]|0,m=A,w=N,c=t+((38-O|0)*80|0)+78|0,s=0,d=0;u=u+2|0,s=(g(h<<16>>16,f)|0)+s|0,m=m+2|0,h=e[u>>1]|0,d=(g(h<<16>>16,f)|0)+d|0,M=n,n=n+-2|0,f=e[n>>1]|0,B=e[w>>1]<<1,M=(g((g(B,e[M>>1]|0)|0)>>16,(s<<1)+32768>>16)|0)>>>15&65535,e[o>>1]=M,e[i>>1]=M,f=(g((g(B,f)|0)>>16,(d<<1)+32768>>16)|0)>>>15&65535,e[i+-2>>1]=f,e[c>>1]=f,l=l+-1<<16>>16,f=e[m>>1]|0,l<<16>>16;)w=w+-2|0,i=i+-82|0,o=o+-82|0,c=c+-82|0;u=I+(k+1<<1)|0,l=r+(38-b<<1)|0,n=r+(E+38<<1)|0,i=t+3040+(R+38<<1)|0,o=t+3040+(S+38<<1)|0}if(M=(g(e[u>>1]|0,f)|0)+s|0,M=(g((M<<1)+32768>>16,(g(e[l>>1]<<1,e[n>>1]|0)|0)>>16)|0)>>>15&65535,e[i>>1]=M,e[o>>1]=M,o=(O<<16)+131072|0,(o|0)<2621440)O=o>>16;else break}C=F}function ge(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0;if(k=C,C=C+160|0,h=k,n<<16>>16>0){m=s&65535,w=0,l=5;do{if((w|0)<40)for(d=w,c=w&65535,s=0;;){if(c<<16>>16<40){c=c<<16>>16,u=0;do u=(g(e[i+(c-d<<1)>>1]|0,e[r+(c<<1)>>1]|0)|0)+u|0,c=c+1|0;while((c&65535)<<16>>16!=40)}else u=0;if(u=u<<1,a[h+(d<<2)>>2]=u,u=p2(u)|0,s=(u|0)>(s|0)?u:s,u=d+m|0,c=u&65535,c<<16>>16>=40)break;d=u<<16>>16}else s=0;l=(s>>1)+l|0,w=w+1|0}while((w&65535)<<16>>16!=n<<16>>16)}else l=5;if(o=((B1(l)|0)&65535)-(o&65535)|0,s=o<<16>>16,u=0-s<<16,l=(u|0)<2031616,u=u>>16,(o&65535)<<16>>16>0)if(l){l=0;do o=a[h+(l<<2)>>2]|0,r=o<<s,e[t+(l<<1)>>1]=S1((r>>s|0)==(o|0)?r:o>>31^2147483647,f)|0,l=l+1|0;while((l|0)!=40);C=k;return}else{l=0;do o=a[h+(l<<2)>>2]|0,r=o<<s,e[t+(l<<1)>>1]=S1((r>>s|0)==(o|0)?r:o>>31^2147483647,f)|0,l=l+1|0;while((l|0)!=40);C=k;return}else if(l){l=0;do e[t+(l<<1)>>1]=S1(a[h+(l<<2)>>2]>>u,f)|0,l=l+1|0;while((l|0)!=40);C=k;return}else{l=0;do e[t+(l<<1)>>1]=S1(0,f)|0,l=l+1|0;while((l|0)!=40);C=k;return}}function z2(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0;I=C,C=C+160|0,A=I,S=i+2|0,O=e[i>>1]|0,N=0,n=5;do{for(R=N,l=0;;){if(d=r+(R<<1)|0,E=40-R|0,s=(E+131071|0)>>>1&65535,u=r+(R+1<<1)|0,f=g(e[d>>1]<<1,O)|0,!(s<<16>>16))s=S;else{for(b=131111-R+131070&131070,k=R+b|0,h=S,w=i,m=d;c=m+4|0,d=w+4|0,f=(g(e[u>>1]<<1,e[h>>1]|0)|0)+f|0,s=s+-1<<16>>16,f=(g(e[c>>1]<<1,e[d>>1]|0)|0)+f|0,s<<16>>16;)u=m+6|0,h=w+6|0,w=d,m=c;u=r+(k+3<<1)|0,s=i+(b+3<<1)|0}if(E&1||(f=(g(e[u>>1]<<1,e[s>>1]|0)|0)+f|0),a[A+(R<<2)>>2]=f,f=(f|0)<0?0-f|0:f,l=(f|0)>(l|0)?f:l,f=R+5|0,(f&65535)<<16>>16<40)R=f<<16>>16;else break}n=(l>>1)+n|0,N=N+1|0}while((N|0)!=5);if(o=((B1(n)|0)&65535)-(o&65535)|0,f=o<<16>>16,n=0-f<<16,l=n>>16,(o&65535)<<16>>16>0){for(s=20,n=A;A=a[n>>2]|0,o=A<<f,e[t>>1]=(((o>>f|0)==(A|0)?o:A>>31^2147483647)+32768|0)>>>16,A=a[n+4>>2]|0,o=A<<f,e[t+2>>1]=(((o>>f|0)==(A|0)?o:A>>31^2147483647)+32768|0)>>>16,s=s+-1<<16>>16,s<<16>>16;)t=t+4|0,n=n+8|0;C=I;return}if((n|0)<2031616){for(s=20,n=A;e[t>>1]=((a[n>>2]>>l)+32768|0)>>>16,e[t+2>>1]=((a[n+4>>2]>>l)+32768|0)>>>16,s=s+-1<<16>>16,s<<16>>16;)t=t+4|0,n=n+8|0;C=I;return}else{e[t>>1]=0,A=t+4|0,e[t+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,o=A+4|0,e[A+2>>1]=0,e[o>>1]=0,A=o+4|0,e[o+2>>1]=0,e[A>>1]=0,e[A+2>>1]=0,C=I;return}}function U6(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;return f=(r0(16383,r)|0)<<16>>16,r=g(f,r<<16>>16)|0,(r|0)==1073741824?(a[o>>2]=1,n=2147483647):n=r<<1,s=(g(f,t<<16>>16)|0)>>15,r=n+(s<<1)|0,(n^s|0)>0&(r^n|0)<0&&(a[o>>2]=1,r=(n>>>31)+2147483647|0),n=2147483647-r|0,t=n>>16,r=g(t,f)|0,(r|0)==1073741824?(a[o>>2]=1,s=2147483647):s=r<<1,f=(g((n>>>1)-(t<<15)<<16>>16,f)|0)>>15,r=s+(f<<1)|0,(s^f|0)>0&(r^s|0)<0&&(a[o>>2]=1,r=(s>>>31)+2147483647|0),s=r>>16,f=i>>16,t=g(s,f)|0,t=(t|0)==1073741824?2147483647:t<<1,n=(g((r>>>1)-(s<<15)<<16>>16,f)|0)>>15,o=(n<<1)+t|0,o=(n^t|0)>0&(o^t|0)<0?(t>>>31)+2147483647|0:o,f=(g(s,(i>>>1)-(f<<15)<<16>>16)|0)>>15,i=o+(f<<1)|0,i=(o^f|0)>0&(i^o|0)<0?(o>>>31)+2147483647|0:i,o=i<<2,((o>>2|0)==(i|0)?o:i>>31^2147483647)|0}function _e(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,s=0;if(!i||(a[i>>2]=0,t=o0(192)|0,!t))return s=-1,s|0;o=t+176|0,e[o>>1]=0,e[o+2>>1]=0,e[o+4>>1]=0,e[o+6>>1]=0,e[o+8>>1]=0,e[o+10>>1]=0,o=t,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+20|0,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+40|0,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+60|0,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+80|0,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+100|0,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+120|0,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+140|0,n=r,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=t+160|0,s=o+20|0;do e[o>>1]=0,o=o+2|0;while((o|0)<(s|0));return e[t+188>>1]=7,e[t+190>>1]=32767,a[i>>2]=t,s=0,s|0}function Re(i,r){i=i|0,r=r|0;var t=0,o=0,n=0;if(!i)return n=-1,n|0;t=i+176|0,e[t>>1]=0,e[t+2>>1]=0,e[t+4>>1]=0,e[t+6>>1]=0,e[t+8>>1]=0,e[t+10>>1]=0,t=i,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+20|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+40|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+60|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+80|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+100|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+120|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+140|0,o=r,n=t+20|0;do e[t>>1]=e[o>>1]|0,t=t+2|0,o=o+2|0;while((t|0)<(n|0));t=i+160|0,n=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(n|0));return e[i+188>>1]=7,e[i+190>>1]=32767,n=1,n|0}function Se(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function De(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0;if(M=C,C=C+112|0,I=M+80|0,F=M+60|0,B=M+40|0,A=M,!(r<<16>>16)&&(f=i+178|0,(e[f>>1]|0)!=0)){B=i+180|0,s=i+182|0,t=f,B=e[B>>1]|0,o=a[n>>2]|0,F=o+2|0,e[o>>1]=B,s=e[s>>1]|0,B=o+4|0,e[F>>1]=s,F=i+184|0,F=e[F>>1]|0,s=o+6|0,e[B>>1]=F,B=i+186|0,B=e[B>>1]|0,i=o+8|0,e[s>>1]=B,t=e[t>>1]|0,o=o+10|0,a[n>>2]=o,e[i>>1]=t,C=M;return}b=A+36|0,E=A+32|0,R=A+28|0,S=A+24|0,O=A+20|0,N=A+16|0,w=A+12|0,h=A+8|0,k=A+4|0,r=A,f=r+40|0;do a[r>>2]=0,r=r+4|0;while((r|0)<(f|0));for(m=7,r=0;;){for(d=e[i+160+(m<<1)>>1]|0,f=d<<16>>16,d<<16>>16<0?f=~((f^-4)>>2):f=f>>>2,r=n1(r,f&65535,s)|0,u=m*10|0,d=9;c=A+(d<<2)|0,l=a[c>>2]|0,T=e[i+(d+u<<1)>>1]|0,f=T+l|0,(T^l|0)>-1&(f^l|0)<0&&(a[s>>2]=1,f=(l>>>31)+2147483647|0),a[c>>2]=f,(d|0)>0;)d=d+-1|0;if((m|0)>0)m=m+-1|0;else break}f=r<<16>>16,r<<16>>16<0?f=~((f^-2)>>1):f=f>>>1,e[F+18>>1]=(a[b>>2]|0)>>>3,e[F+16>>1]=(a[E>>2]|0)>>>3,e[F+14>>1]=(a[R>>2]|0)>>>3,e[F+12>>1]=(a[S>>2]|0)>>>3,e[F+10>>1]=(a[O>>2]|0)>>>3,e[F+8>>1]=(a[N>>2]|0)>>>3,e[F+6>>1]=(a[w>>2]|0)>>>3,e[F+4>>1]=(a[h>>2]|0)>>>3,e[F+2>>1]=(a[k>>2]|0)>>>3,e[F>>1]=(a[A>>2]|0)>>>3,r=i+178|0,f=(((f<<16)+167772160|0)>>>16)+128|0,e[r>>1]=f,f=f<<16,(f|0)<0?f=~((f>>16^-256)>>8):f=f>>24,e[r>>1]=f,(f|0)<=63?(f|0)<0&&(e[r>>1]=0,f=0):(e[r>>1]=63,f=63),T=y1(f<<8&65535,11560,s)|0,T=T<<16>>16>0?0:T<<16>>16<-14436?-14436:T,e[o>>1]=T,e[o+2>>1]=T,e[o+4>>1]=T,e[o+6>>1]=T,T=((T<<16>>16)*5443|0)>>>15&65535,e[o+8>>1]=T,e[o+10>>1]=T,e[o+12>>1]=T,e[o+14>>1]=T,q2(F,I,10,s),P0(I,205,10,s),p0(I,F,10,s),o=i+182|0,T=i+180|0,l4(t,8,F,B,o,T,s),s=o,o=r,T=e[T>>1]|0,t=a[n>>2]|0,B=t+2|0,e[t>>1]=T,s=e[s>>1]|0,T=t+4|0,e[B>>1]=s,B=i+184|0,B=e[B>>1]|0,s=t+6|0,e[T>>1]=B,i=i+186|0,i=e[i>>1]|0,T=t+8|0,e[s>>1]=i,i=e[o>>1]|0,t=t+10|0,a[n>>2]=t,e[T>>1]=i,C=M}function Ae(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0;c=C,C=C+16|0,f=c+2|0,u=c,l=i+176|0,s=(D[l>>1]|0)+1|0,s=(s&65535|0)==8?0:s&65535,e[l>>1]=s,s=i+((s<<16>>16)*10<<1)|0,n=s+20|0;do e[s>>1]=e[r>>1]|0,s=s+2|0,r=r+2|0;while((s|0)<(n|0));for(r=0,n=160;;){if(s=e[t>>1]|0,r=(g(s<<1,s)|0)+r|0,(r|0)<0){r=2147483647;break}if(n=n+-1<<16>>16,n<<16>>16)t=t+2|0;else break}s2(r,f,u,o),r=e[f>>1]|0,f=r<<16>>16,t=f<<10,(t|0)!=(f<<26>>16|0)&&(a[o>>2]=1,t=r<<16>>16>0?32767:-32768),e[i+160+(e[l>>1]<<1)>>1]=(((e[u>>1]|0)>>>5)+t<<16)+-558432256>>17,C=c}function Me(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;s=i+190|0,f=n1(e[s>>1]|0,1,o)|0,e[s>>1]=f,n=i+188|0;do if(r<<16>>16)e[n>>1]=7,i=0;else{if(i=e[n>>1]|0,!(i<<16>>16)){e[s>>1]=0,a[t>>2]=8,i=1;break}s=(i&65535)+65535&65535,e[n>>1]=s,(n1(f,s,o)|0)<<16>>16<30&&(a[t>>2]=8),i=0}while(!1);return i|0}function Pe(i,r,t,o,n,s,f,l){return i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,s<<16>>16?f<<16>>16?(o=o<<16>>16,o=(((t&65535)-o<<16)+-327680|0)>0?o+5&65535:t,n=n<<16>>16,t=i<<16>>16,o=(((n-(o&65535)<<16)+-262144|0)>0?n+65532&65535:o)<<16>>16,n=o*196608|0,i=n+-393216>>16,s=((r&65535)<<16)+(t*196608|0)>>16,i-s&32768?(n+196608>>16|0)>(s|0)?(r=s+3-i|0,r=r&65535,r|0):(r=t+11-o|0,r=r&65535,r|0):(r=t+5-o|0,r=r&65535,r|0)):(l=(i&65535)-(o&65535)<<16,r=(r<<16>>16)+2+(l>>15)+(l>>16)|0,r=r&65535,r|0):(s=i<<16>>16,((s<<16)+-5570560|0)<65536?(r=(s*3|0)+-58+(r<<16>>16)|0,r=r&65535,r|0):(r=s+112|0,r=r&65535,r|0))}function Ne(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,n=i<<16>>16;do if(o<<16>>16)n=((((n-(t&65535)|0)*393216|0)+196608|0)>>>16)+(r&65535)|0;else if(i<<16>>16<95){n=((n*393216|0)+-6881280>>16)+(r<<16>>16)|0;break}else{n=n+368|0;break}while(!1);return n&65535|0}function Oe(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0;if(n=a[o+96>>2]|0,i>>>0<8){if(u=(a[o+100>>2]|0)+(i<<2)|0,l=a[u>>2]|0,_[t>>0]=e[r+(e[l>>1]<<1)>>1]<<4|i|e[r+(e[l+2>>1]<<1)>>1]<<5|e[r+(e[l+4>>1]<<1)>>1]<<6|e[r+(e[l+6>>1]<<1)>>1]<<7,l=n+(i<<1)|0,o=e[l>>1]|0,(o+-7|0)>4)for(n=4,f=4,i=1;c=e[r+(e[(a[u>>2]|0)+(n<<1)>>1]<<1)>>1]|0,o=t+(i<<16>>16)|0,_[o>>0]=c,c=D[r+(e[(a[u>>2]|0)+((f|1)<<16>>16<<1)>>1]<<1)>>1]<<1|c&65535,_[o>>0]=c,c=D[r+(e[(a[u>>2]|0)+((f|2)<<16>>16<<1)>>1]<<1)>>1]<<2|c,_[o>>0]=c,c=D[r+(e[(a[u>>2]|0)+((f|3)<<16>>16<<1)>>1]<<1)>>1]<<3|c,_[o>>0]=c,c=D[r+(e[(a[u>>2]|0)+(f+4<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<4|c,_[o>>0]=c,c=D[r+(e[(a[u>>2]|0)+(f+5<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<5|c,_[o>>0]=c,c=D[r+(e[(a[u>>2]|0)+(f+6<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<6|c,_[o>>0]=c,s=f+8<<16>>16,i=i+1<<16>>16,_[o>>0]=D[r+(e[(a[u>>2]|0)+(f+7<<16>>16<<16>>16<<1)>>1]<<1)>>1]<<7|c,n=s<<16>>16,o=e[l>>1]|0,!((n|0)>=(o+-7|0));)f=s;else s=4,i=1;if(l=o+4&7,!l)return;for(n=t+(i<<16>>16)|0,_[n>>0]=0,o=0,f=0,i=0;f=(D[r+(e[(a[u>>2]|0)+(s<<16>>16<<1)>>1]<<1)>>1]&255)<<o|f&255,_[n>>0]=f,i=i+1<<16>>16,o=i<<16>>16,!((o|0)>=(l|0));)s=s+1<<16>>16;return}if((i|0)==15){_[t>>0]=15;return}if(_[t>>0]=e[r>>1]<<4|i|e[r+2>>1]<<5|e[r+4>>1]<<6|e[r+6>>1]<<7,o=n+(i<<1)|0,i=e[o>>1]|0,n=((i&65535)<<16)+262144>>16,u=n&-8,f=(u+524281|0)>>>3&65535,f<<16>>16>0){for(n=((n&-8)+524281|0)>>>3,l=((n<<3)+524280&524280)+12|0,s=1,i=r+8|0;_[t+(s<<16>>16)>>0]=D[i+2>>1]<<1|D[i>>1]|D[i+4>>1]<<2|D[i+6>>1]<<3|D[i+8>>1]<<4|D[i+10>>1]<<5|D[i+12>>1]<<6|D[i+14>>1]<<7,f<<16>>16>1;)f=f+-1<<16>>16,s=s+1<<16>>16,i=i+16|0;i=e[o>>1]|0,s=(n<<16)+65536>>16}else l=4,s=1;if(i=(0-u|4)+(i&65535)<<16,f=i>>16,!!f){if(s=t+s|0,_[s>>0]=0,(i|0)>0)i=0,n=0,o=0;else return;do n=n&255|e[r+(l+i<<1)>>1]<<i,_[s>>0]=n,o=o+1<<16>>16,i=o<<16>>16;while((i|0)<(f|0))}}function Ce(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0;if(d=a[o+100>>2]|0,c=a[o+96>>2]|0,_[t>>0]=i&15,c=c+(i<<1)|0,n=e[c>>1]|0,i>>>0>=8){if(l=((n&65535)<<16)+-458752|0,(l|0)>0){for(u=1,f=r;r=f+16|0,o=u+1<<16>>16,_[t+(u<<16>>16)>>0]=D[f+14>>1]|D[f+12>>1]<<1|((D[f+2>>1]<<6|D[f>>1]<<7|D[f+4>>1]<<5|D[f+6>>1]<<4)&240|D[f+8>>1]<<3|D[f+10>>1]<<2)&252,l=l+-524288&-65536,!((l|0)<=0);)u=o,f=r;n=e[c>>1]|0}else o=1;if(u=n&7,n=t+(o<<16>>16)|0,_[n>>0]=0,u)s=0,f=0,l=0,o=r;else return;for(;f=f&255|e[o>>1]<<7-s,_[n>>0]=f,l=l+1<<16>>16,s=l<<16>>16,!((s|0)>=(u|0));)o=o+2|0;return}if(f=n<<16>>16,n<<16>>16>7)for(n=d+(i<<2)|0,o=0,u=0,s=1;m=D[r+(e[(a[n>>2]|0)+(o<<1)>>1]<<1)>>1]<<7,f=t+(s<<16>>16)|0,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|1)<<16>>16<<1)>>1]<<1)>>1]<<6|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|2)<<16>>16<<1)>>1]<<1)>>1]<<5|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|3)<<16>>16<<1)>>1]<<1)>>1]<<4|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|4)<<16>>16<<1)>>1]<<1)>>1]<<3|m&240,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|5)<<16>>16<<1)>>1]<<1)>>1]<<2|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|6)<<16>>16<<1)>>1]<<1)>>1]<<1|m,_[f>>0]=m,l=u+8<<16>>16,s=s+1<<16>>16,_[f>>0]=m&254|D[r+(e[(a[n>>2]|0)+((u|7)<<16>>16<<1)>>1]<<1)>>1],o=l<<16>>16,f=e[c>>1]|0,!((o|0)>=(f+-7|0));)u=l;else l=0,s=1;if(c=f&7,u=t+(s<<16>>16)|0,_[u>>0]=0,!!c)for(s=d+(i<<2)|0,n=0,o=0,f=0;o=(D[r+(e[(a[s>>2]|0)+(l<<16>>16<<1)>>1]<<1)>>1]&255)<<7-n|o&255,_[u>>0]=o,f=f+1<<16>>16,n=f<<16>>16,!((n|0)>=(c|0));)l=l+1<<16>>16}function Le(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0;if(d=a[o+100>>2]|0,c=a[o+96>>2]|0,_[t>>0]=i<<3,c=c+(i<<1)|0,n=e[c>>1]|0,i>>>0>=8){if(l=((n&65535)<<16)+-458752|0,(l|0)>0){for(u=1,f=r;r=f+16|0,o=u+1<<16>>16,_[t+(u<<16>>16)>>0]=D[f+14>>1]|D[f+12>>1]<<1|((D[f+2>>1]<<6|D[f>>1]<<7|D[f+4>>1]<<5|D[f+6>>1]<<4)&240|D[f+8>>1]<<3|D[f+10>>1]<<2)&252,l=l+-524288&-65536,!((l|0)<=0);)u=o,f=r;n=e[c>>1]|0}else o=1;if(u=n&7,n=t+(o<<16>>16)|0,_[n>>0]=0,u)s=0,f=0,l=0,o=r;else return;for(;f=f&255|e[o>>1]<<7-s,_[n>>0]=f,l=l+1<<16>>16,s=l<<16>>16,!((s|0)>=(u|0));)o=o+2|0;return}if(f=n<<16>>16,n<<16>>16>7)for(n=d+(i<<2)|0,o=0,u=0,s=1;m=D[r+(e[(a[n>>2]|0)+(o<<1)>>1]<<1)>>1]<<7,f=t+(s<<16>>16)|0,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|1)<<16>>16<<1)>>1]<<1)>>1]<<6|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|2)<<16>>16<<1)>>1]<<1)>>1]<<5|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|3)<<16>>16<<1)>>1]<<1)>>1]<<4|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|4)<<16>>16<<1)>>1]<<1)>>1]<<3|m&240,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|5)<<16>>16<<1)>>1]<<1)>>1]<<2|m,_[f>>0]=m,m=D[r+(e[(a[n>>2]|0)+((u|6)<<16>>16<<1)>>1]<<1)>>1]<<1|m,_[f>>0]=m,l=u+8<<16>>16,s=s+1<<16>>16,_[f>>0]=m&254|D[r+(e[(a[n>>2]|0)+((u|7)<<16>>16<<1)>>1]<<1)>>1],o=l<<16>>16,f=e[c>>1]|0,!((o|0)>=(f+-7|0));)u=l;else l=0,s=1;if(c=f&7,u=t+(s<<16>>16)|0,_[u>>0]=0,!!c)for(s=d+(i<<2)|0,n=0,o=0,f=0;o=(D[r+(e[(a[s>>2]|0)+(l<<16>>16<<1)>>1]<<1)>>1]&255)<<7-n|o&255,_[u>>0]=o,f=f+1<<16>>16,n=f<<16>>16,!((n|0)>=(c|0));)l=l+1<<16>>16}function Te(i){i=i|0;var r=0;return!i||(a[i>>2]=0,r=o0(16)|0,!r)?(i=-1,i|0):(e[r>>1]=0,e[r+2>>1]=0,e[r+4>>1]=0,e[r+6>>1]=0,e[r+8>>1]=0,e[r+10>>1]=0,e[r+12>>1]=0,e[r+14>>1]=0,a[i>>2]=r,i=0,i|0)}function z6(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,e[i+12>>1]=0,e[i+14>>1]=0,i=0,i|0):(i=-1,i|0)}function j6(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function Ie(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0;l=r<<16>>16<2722?0:r<<16>>16<5444?1:2,f=V2(t,1,n)|0,c=i+4|0,t<<16>>16>200&&f<<16>>16>(e[c>>1]|0)?(e[i>>1]=8,s=1,u=5):(f=e[i>>1]|0,f<<16>>16&&(s=f+-1<<16>>16,e[i>>1]=s,s=s<<16>>16!=0,u=5)),(u|0)==5&&(l&65535)<2&s&&(l=(l&65535)+1&65535),u=i+6|0,e[u>>1]=r,s=k2(u,5)|0,l<<16>>16!=0|s<<16>>16>5443?s=0:s<<16>>16<0?s=16384:(s=s<<16>>16,s=(((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16)*24660>>15,(s|0)>32767&&(a[n>>2]=1,s=32767),s=16384-s&65535),f=i+2|0,e[f>>1]|0||(s=X1(s,1,n)|0),e[o>>1]=s,e[f>>1]=s,e[c>>1]=t,o=i+12|0,e[i+14>>1]=e[o>>1]|0,t=i+10|0,e[o>>1]=e[t>>1]|0,i=i+8|0,e[t>>1]=e[i>>1]|0,e[i>>1]=e[u>>1]|0}function Fe(i){i=i|0;var r=0,t=0,o=0,n=0,s=0,f=0;if(!i||(a[i>>2]=0,r=o0(68)|0,o=r,!r))return i=-1,i|0;if(a[r+28>>2]=0,n=r+64|0,a[n>>2]=0,s=r+32|0,!((n2(s)|0)<<16>>16)&&(f=r+48|0,(n2(f)|0)<<16>>16==0)&&!((Te(n)|0)<<16>>16)){t=r+32|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(t|0));return n2(s)|0,n2(f)|0,z6(a[n>>2]|0)|0,a[i>>2]=o,i=0,i|0}return j6(n),G1(r),i=-1,i|0}function Be(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(j6(r+64|0),G1(a[i>>2]|0),a[i>>2]=0))}function xe(i){i=i|0;var r=0,t=0,o=0;if(!i)return o=-1,o|0;r=i+32|0,t=i,o=t+32|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return n2(r)|0,n2(i+48|0)|0,z6(a[i+64>>2]|0)|0,o=0,o|0}function Ue(i,r,t,o,n,s,f,l,u,c,d,m,w,h,k,b,E,R,S){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0;var O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0;if(Y=C,C=C+48|0,N=Y+34|0,I=Y+32|0,B=Y+30|0,F=Y+28|0,A=Y+18|0,O=Y+8|0,M=Y+6|0,T=Y+4|0,z=Y+2|0,j=Y,r){d=i+32|0,M2(d,r,n,N,I,M,T,S);do if((r|0)!=7)if(P5(r,s,f,l,u,c,A,O,j,z,S),(r|0)==5){si(a[i+64>>2]|0,t,o,n,A,O,e[M>>1]|0,e[T>>1]|0,e[N>>1]|0,e[I>>1]|0,40,e[j>>1]|0,e[z>>1]|0,m,k,b,B,F,E,R,S);break}else{i=fi(r,e[N>>1]|0,e[I>>1]|0,A,O,m,k,b,B,F,R,S)|0,s=a[E>>2]|0,a[E>>2]=s+2,e[s>>1]=i;break}else e[b>>1]=ze(f,u,S)|0,i=ai(7,e[N>>1]|0,e[I>>1]|0,b,B,F,a[R+68>>2]|0,S)|0,s=a[E>>2]|0,a[E>>2]=s+2,e[s>>1]=i;while(!1);W0(d,e[B>>1]|0,e[F>>1]|0),C=Y;return}if(!(d<<16>>16)){M2(i+48|0,0,n,N,I,M,T,S),P5(0,s,f,l,u,c,A,O,j,z,S),F6(s,M,T,S),s=ni(i+32|0,e[i>>1]|0,e[i+2>>1]|0,i+8|0,i+18|0,e[i+4>>1]|0,e[i+6>>1]|0,n,e[N>>1]|0,e[I>>1]|0,O,A,e[M>>1]|0,e[T>>1]|0,m,w,h,k,b,S)|0,e[a[i+28>>2]>>1]=s,C=Y;return}d=a[E>>2]|0,a[E>>2]=d+2,a[i+28>>2]=d,d=i+48|0,t=i+32|0,w=t,w=D[w>>1]|D[w+2>>1]<<16,t=t+4|0,t=D[t>>1]|D[t+2>>1]<<16,E=d,h=E,e[h>>1]=w,e[h+2>>1]=w>>>16,E=E+4|0,e[E>>1]=t,e[E+2>>1]=t>>>16,E=i+40|0,t=E,t=D[t>>1]|D[t+2>>1]<<16,E=E+4|0,E=D[E>>1]|D[E+2>>1]<<16,h=i+56|0,w=h,e[w>>1]=t,e[w+2>>1]=t>>>16,h=h+4|0,e[h>>1]=E,e[h+2>>1]=E>>>16,h=i+2|0,M2(d,0,n,i,h,M,T,S),P5(0,s,f,l,u,c,i+18|0,i+8|0,j,z,S),l=(D[z>>1]|0)+1|0,E=e[j>>1]|0,w=l<<16>>16,(l&65535)<<16>>16<0?(R=0-w<<16,(R|0)<983040?R=E<<16>>16>>(R>>16)&65535:R=0):(E=E<<16>>16,R=E<<w,(R<<16>>16>>w|0)==(E|0)?R=R&65535:R=(E>>>15^32767)&65535),e[b>>1]=R,F6(s,i+4|0,i+6|0,S),oi(d,e[i>>1]|0,e[h>>1]|0,e[z>>1]|0,e[j>>1]|0,S),C=Y}function ze(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;for(n=10,t=i,o=r,i=0;i=(g(e[o>>1]>>1,e[t>>1]|0)|0)+i|0,i=i+(g(e[o+2>>1]>>1,e[t+2>>1]|0)|0)|0,i=i+(g(e[o+4>>1]>>1,e[t+4>>1]|0)|0)|0,i=i+(g(e[o+6>>1]>>1,e[t+6>>1]|0)|0)|0,n=n+-1<<16>>16,n<<16>>16;)t=t+8|0,o=o+8|0;if(t=i<<1,n=B1(t|1)|0,s=n<<16>>16,t=(n<<16>>16<17?t>>17-s:t<<s+-17)&65535,t<<16>>16<1)return r=0,r|0;for(n=20,o=r,i=0;r=e[o>>1]>>1,r=((g(r,r)|0)>>>2)+i|0,i=e[o+2>>1]>>1,i=r+((g(i,i)|0)>>>2)|0,n=n+-1<<16>>16,n<<16>>16;)o=o+4|0;return i=i<<3,n=B1(i)|0,r=n<<16>>16,t=r0(t,(n<<16>>16<16?i>>16-r:i<<r+-16)&65535)|0,r=(s<<16)+327680-(r<<16)|0,i=r>>16,(r|0)>65536?i=t<<16>>16>>i+-1:i=t<<16>>16<<1-i,r=i&65535,r|0}function je(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0;if(a[s>>2]=0,d=n<<16>>16,u=d>>>2&65535,w=u<<16>>16==0,w)l=0;else for(c=u,f=t,l=0;h=e[f>>1]|0,h=(g(h,h)|0)+l|0,l=e[f+2>>1]|0,l=h+(g(l,l)|0)|0,h=e[f+4>>1]|0,h=l+(g(h,h)|0)|0,l=e[f+6>>1]|0,l=h+(g(l,l)|0)|0,c=c+-1<<16>>16,c<<16>>16;)f=f+8|0;if((l>>>31^1)&(l|0)<1073741824)d=l<<1|1,h=B1(d)|0,m=h,h=S1(d<<(h<<16>>16),s)|0;else{if(l=d>>>1&65535,!(l<<16>>16))l=1;else{for(f=l,c=t,l=0;h=e[c>>1]>>2,h=(g(h,h)|0)+l|0,l=e[c+2>>1]>>2,l=h+(g(l,l)|0)|0,f=f+-1<<16>>16,f<<16>>16;)c=c+4|0;l=l<<1|1}h=(B1(l)|0)<<16>>16,m=h+65532&65535,h=S1(l<<h,s)|0}a[s>>2]=0;do if(!(n<<16>>16))l=1,k=14;else{for(d=n,c=r,l=t,n=0;b=g(e[l>>1]|0,e[c>>1]|0)|0,f=b+n|0,!((b^n|0)>0&(f^n|0)<0);)if(d=d+-1<<16>>16,d<<16>>16)c=c+2|0,l=l+2|0,n=f;else{k=13;break}if((k|0)==13){l=f<<1|1,k=14;break}if(a[s>>2]=1,w)l=1;else{for(l=r,f=0;f=(g(e[t>>1]>>2,e[l>>1]|0)|0)+f|0,f=f+(g(e[t+2>>1]>>2,e[l+2>>1]|0)|0)|0,f=f+(g(e[t+4>>1]>>2,e[l+4>>1]|0)|0)|0,f=f+(g(e[t+6>>1]>>2,e[l+6>>1]|0)|0)|0,u=u+-1<<16>>16,u<<16>>16;)l=l+8|0,t=t+8|0;l=f<<1|1}t=(B1(l)|0)<<16>>16,f=t+65532&65535,t=S1(l<<t,s)|0}while(!1);return(k|0)==14&&(t=B1(l)|0,f=t,t=S1(l<<(t<<16>>16),s)|0),e[o>>1]=h,l=m<<16>>16,e[o+2>>1]=15-l,e[o+4>>1]=t,f=f<<16>>16,e[o+6>>1]=15-f,t<<16>>16<4?(b=0,b|0):(f=X1(r0(t<<16>>16>>>1&65535,h)|0,f-l&65535,s)|0,f=f<<16>>16>19661?19661:f,(i|0)!=7?(b=f,b|0):(b=f&65532,b|0))}function q6(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0;if(u=(o&65535)+65535&65535,u<<16>>16>n<<16>>16)for(m=o+-1<<16>>16<<16>>16,o=-2147483648;;)if(c=a[i+(0-m<<2)>>2]|0,l=c<<1,c=(l>>1|0)==(c|0)?l:c>>31^2147483647,l=a[i+(~m<<2)>>2]|0,d=c-l|0,((d^c)&(c^l)|0)<0&&(a[f>>2]=1,d=(c>>>31)+2147483647|0),c=a[i+(1-m<<2)>>2]|0,l=d-c|0,((l^d)&(c^d)|0)<0&&(a[f>>2]=1,l=(d>>>31)+2147483647|0),d=p2(l)|0,o=(d|0)<(o|0)?o:d,u=u+-1<<16>>16,u<<16>>16<=n<<16>>16){n=o;break}else m=m+-1|0;else n=-2147483648;if(i=t<<16>>16>0,i){for(o=0,l=r,u=0;d=e[l>>1]|0,d=g(d,d)|0,(d|0)!=1073741824?(c=(d<<1)+u|0,(d^u|0)>0&(c^u|0)<0?(a[f>>2]=1,u=(u>>>31)+2147483647|0):u=c):(a[f>>2]=1,u=2147483647),o=o+1<<16>>16,!(o<<16>>16>=t<<16>>16);)l=l+2|0;if(i)for(i=0,m=r,o=r+-2|0,l=0;d=g(e[o>>1]|0,e[m>>1]|0)|0,(d|0)!=1073741824?(c=(d<<1)+l|0,(d^l|0)>0&(c^l|0)<0?(a[f>>2]=1,l=(l>>>31)+2147483647|0):l=c):(a[f>>2]=1,l=2147483647),i=i+1<<16>>16,!(i<<16>>16>=t<<16>>16);)m=m+2|0,o=o+2|0;else l=0}else u=0,l=0;if(o=u<<1,o=(o>>1|0)==(u|0)?o:u>>31^2147483647,t=l<<1,t=(t>>1|0)==(l|0)?t:l>>31^2147483647,u=o-t|0,((u^o)&(t^o)|0)<0&&(a[f>>2]=1,u=(o>>>31)+2147483647|0),i=p2(u)|0,m=((B1(n)|0)&65535)+65535|0,u=m<<16>>16,(m&65535)<<16>>16>0?(o=n<<u,(o>>u|0)!=(n|0)&&(o=n>>31^2147483647)):(u=0-u<<16,(u|0)<2031616?o=n>>(u>>16):o=0),d=B1(i)|0,l=d<<16>>16,d<<16>>16>0?(u=i<<l,(u>>l|0)==(i|0)||(u=i>>31^2147483647),w=33):(u=0-l<<16,(u|0)<2031616?(u=i>>(u>>16),w=33):c=0),(w|0)==33&&(u>>>0>65535?c=r0(o>>>16&65535,u>>>16&65535)|0:c=0),u=d&65535,w=(m&65535)-u|0,o=w&65535,!(w&32768))return f=X1(c,o,f)|0,e[s>>1]=f,0;if(o<<16>>16!=-32768){if(f=u-m|0,l=f<<16>>16,(f&65535)<<16>>16<0)return l=0-l<<16,(l|0)>=983040?(f=0,e[s>>1]=f,0):(f=c<<16>>16>>(l>>16)&65535,e[s>>1]=f,0)}else l=32767;return o=c<<16>>16,u=o<<l,(u<<16>>16>>l|0)==(o|0)?(f=u&65535,e[s>>1]=f,0):(f=(o>>>15^32767)&65535,e[s>>1]=f,0)}function o2(i,r,t,o){return i=i|0,r=r|0,t=t|0,o=o|0,t<<16>>16&&(r=r<<16>>16<<1&65535),r<<16>>16<0&&(i=i+-2|0,r=(r&65535)+6&65535),t=r<<16>>16,o=6-t<<16>>16,r=(g(e[3468+(t<<1)>>1]|0,e[i>>1]|0)|0)+16384|0,r=r+(g(e[3468+(o<<1)>>1]|0,e[i+2>>1]|0)|0)|0,r=r+(g(e[3468+(t+6<<1)>>1]|0,e[i+-2>>1]|0)|0)|0,r=r+(g(e[3468+(o+6<<1)>>1]|0,e[i+4>>1]|0)|0)|0,r=(g(e[3468+(t+12<<1)>>1]|0,e[i+-4>>1]|0)|0)+r|0,r=r+(g(e[3468+(o+12<<1)>>1]|0,e[i+6>>1]|0)|0)|0,t=r+(g(e[3468+(t+18<<1)>>1]|0,e[i+-6>>1]|0)|0)|0,(t+(g(e[3468+(o+18<<1)>>1]|0,e[i+8>>1]|0)|0)|0)>>>15&65535|0}function p2(i){return i=i|0,i=i-(i>>>31)|0,i>>31^i|0}function O5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0;if(i<<16>>16)n=3518,s=3538,o=t;else return;for(;o=o+2|0,r=r+2|0,u=e[r>>1]|0,l=e[n>>1]|0,t=g(l,u)|0,t=(t|0)==1073741824?2147483647:t<<1,u=(g(e[s>>1]|0,u)|0)>>15,f=(u<<1)+t|0,f=(t^u|0)>0&(f^t|0)<0?(t>>>31)+2147483647|0:f,l=(g(l,e[o>>1]|0)|0)>>15,t=f+(l<<1)|0,t=(f^l|0)>0&(t^f|0)<0?(f>>>31)+2147483647|0:t,e[r>>1]=t>>>16,e[o>>1]=(t>>>1)-(t>>16<<15),i=i+-1<<16>>16,i<<16>>16;)n=n+2|0,s=s+2|0}function qe(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0;return o=i&65535,n=o<<16,r=r<<16>>16,i=(r<<1)+n|0,(r^n|0)>0&(i^n|0)<0?(a[t>>2]=1,n=(o>>>15)+2147483647|0,n|0):(n=i,n|0)}function He(i){i=i|0;var r=0,t=0,o=0;if(!i||(a[i>>2]=0,r=o0(22)|0,!r))return o=-1,o|0;e[r>>1]=4096,t=r+2|0,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return a[i>>2]=r,o=0,o|0}function H6(i){i=i|0;var r=0;if(!i)return r=-1,r|0;e[i>>1]=4096,i=i+2|0,r=i+20|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function V6(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function C5(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0;for(q=C,C=C+96|0,H=q+66|0,U=q+44|0,K=q+22|0,l=q,T=r+2|0,Y=t+2|0,j=(e[Y>>1]<<1)+(D[T>>1]<<16)|0,f=p2(j)|0,f=U6(f,e[r>>1]|0,e[t>>1]|0,s)|0,(j|0)>0&&(f=W6(f)|0),B=f>>16,e[n>>1]=S1(f,s)|0,S=f>>20,z=H+2|0,e[z>>1]=S,j=U+2|0,e[j>>1]=(f>>>5)-(S<<15),S=g(B,B)|0,S=(S|0)==1073741824?2147483647:S<<1,B=(g((f>>>1)-(B<<15)<<16>>16,B)|0)>>15,M=B<<1,F=M+S|0,F=(B^S|0)>0&(F^S|0)<0?(S>>>31)+2147483647|0:F,M=F+M|0,M=2147483647-(p2((F^B|0)>0&(M^F|0)<0?(F>>>31)+2147483647|0:M)|0)|0,F=M>>16,B=e[r>>1]|0,S=g(F,B)|0,S=(S|0)==1073741824?2147483647:S<<1,B=(g((M>>>1)-(F<<15)<<16>>16,B)|0)>>15,M=(B<<1)+S|0,M=(B^S|0)>0&(M^S|0)<0?(S>>>31)+2147483647|0:M,F=(g(e[t>>1]|0,F)|0)>>15,S=M+(F<<1)|0,S=(M^F|0)>0&(S^M|0)<0?(M>>>31)+2147483647|0:S,M=B1(S)|0,S=S<<(M<<16>>16),F=K+2|0,B=l+2|0,u=S,S=(S>>>1)-(S>>16<<15)|0,O=l+4|0,N=K+4|0,A=2,I=2;;){for(R=u>>>16,f=R&65535,k=S&65535,b=I+-1|0,d=H+(b<<1)|0,E=U+(b<<1)|0,h=1,w=d,m=E,c=T,l=Y,u=0;i1=e[c>>1]|0,W=((g(e[m>>1]|0,i1)|0)>>15)+u|0,u=e[w>>1]|0,u=W+(g(u,i1)|0)+((g(u,e[l>>1]|0)|0)>>15)|0,h=h+1<<16>>16,!((h<<16>>16|0)>=(I|0));)w=w+-2|0,m=m+-2|0,c=c+2|0,l=l+2|0;if(W=(D[r+(I<<1)>>1]<<16)+(u<<5)+(e[t+(I<<1)>>1]<<1)|0,u=U6(p2(W)|0,f,k,s)|0,(W|0)>0&&(u=W6(u)|0),l=M<<16>>16,M<<16>>16>0?(f=u<<l,(f>>l|0)!=(u|0)&&(f=u>>31^2147483647)):(l=0-l<<16,(l|0)<2031616?f=u>>(l>>16):f=0),h=f>>16,(I|0)<5&&(e[n+(b<<1)>>1]=(f+32768|0)>>>16),W=(f>>>16)-(f>>>31)|0,((W<<16>>31^W)&65535)<<16>>16>32750){f=16;break}for(m=(f>>>1)-(h<<15)<<16>>16,w=1,u=E,l=F,c=B;i1=(g(e[u>>1]|0,h)|0)>>15,E=e[d>>1]|0,W=(g(E,m)|0)>>15,E=g(E,h)|0,W=E+i1+(e[U+(w<<1)>>1]|0)+(e[H+(w<<1)>>1]<<15)+W|0,e[l>>1]=W>>>15,e[c>>1]=W&32767,w=w+1|0,(w&65535)<<16>>16!=A<<16>>16;)d=d+-2|0,u=u+-2|0,l=l+2|0,c=c+2|0;if(e[N>>1]=f>>20,e[O>>1]=(f>>>5)-(e[K+(I<<1)>>1]<<15),i1=g(h,h)|0,i1=(i1|0)==1073741824?2147483647:i1<<1,f=(g(m,h)|0)>>15,W=f<<1,l=W+i1|0,l=(f^i1|0)>0&(l^i1|0)<0?(i1>>>31)+2147483647|0:l,W=l+W|0,W=2147483647-(p2((l^f|0)>0&(W^l|0)<0?(l>>>31)+2147483647|0:W)|0)|0,l=W>>16,f=R<<16>>16,f=((g(l,S<<16>>16)|0)>>15)+(g(l,f)|0)+((g((W>>>1)-(l<<15)<<16>>16,f)|0)>>15)<<1,l=(B1(f)|0)<<16>>16,f=f<<l,W=I<<1,R0(z|0,F|0,W|0)|0,R0(j|0,B|0,W|0)|0,I=I+1|0,(I|0)>=11){f=20;break}else M=l+(M&65535)&65535,u=f,S=(f>>1)-(f>>16<<15)|0,O=O+2|0,N=N+2|0,A=A+1<<16>>16}if((f|0)==16){f=o+22|0;do e[o>>1]=e[i>>1]|0,o=o+2|0,i=i+2|0;while((o|0)<(f|0));return W=n,i1=W,e[i1>>1]=0,e[i1+2>>1]=0,W=W+4|0,e[W>>1]=0,e[W+2>>1]=0,C=q,0}else if((f|0)==20)return e[o>>1]=4096,W=((e[j>>1]|0)+8192+(e[z>>1]<<15)|0)>>>14&65535,e[o+2>>1]=W,e[i+2>>1]=W,W=((e[U+4>>1]|0)+8192+(e[H+4>>1]<<15)|0)>>>14&65535,e[o+4>>1]=W,e[i+4>>1]=W,W=((e[U+6>>1]|0)+8192+(e[H+6>>1]<<15)|0)>>>14&65535,e[o+6>>1]=W,e[i+6>>1]=W,W=((e[U+8>>1]|0)+8192+(e[H+8>>1]<<15)|0)>>>14&65535,e[o+8>>1]=W,e[i+8>>1]=W,W=((e[U+10>>1]|0)+8192+(e[H+10>>1]<<15)|0)>>>14&65535,e[o+10>>1]=W,e[i+10>>1]=W,W=((e[U+12>>1]|0)+8192+(e[H+12>>1]<<15)|0)>>>14&65535,e[o+12>>1]=W,e[i+12>>1]=W,W=((e[U+14>>1]|0)+8192+(e[H+14>>1]<<15)|0)>>>14&65535,e[o+14>>1]=W,e[i+14>>1]=W,W=((e[U+16>>1]|0)+8192+(e[H+16>>1]<<15)|0)>>>14&65535,e[o+16>>1]=W,e[i+16>>1]=W,W=((e[U+18>>1]|0)+8192+(e[H+18>>1]<<15)|0)>>>14&65535,e[o+18>>1]=W,e[i+18>>1]=W,W=((e[U+20>>1]|0)+8192+(e[H+20>>1]<<15)|0)>>>14&65535,e[o+20>>1]=W,e[i+20>>1]=W,C=q,0;return 0}function O0(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0,o=i>>16,e[r>>1]=o,e[t>>1]=(i>>>1)-(o<<15)}function W6(i){return i=i|0,((i|0)==-2147483648?2147483647:0-i|0)|0}function Ve(i){i=i|0;var r=0;return!i||(a[i>>2]=0,r=o0(4)|0,!r)?(i=-1,i|0):(a[r>>2]=0,(He(r)|0)<<16>>16?(V6(r),G1(r),i=-1,i|0):(H6(a[r>>2]|0)|0,a[i>>2]=r,i=0,i|0))}function We(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(V6(r),G1(a[i>>2]|0),a[i>>2]=0))}function Xe(i){return i=i|0,i?(H6(a[i>>2]|0)|0,i=0,i|0):(i=-1,i|0)}function Ye(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0;if(d=C,C=C+64|0,c=d+48|0,u=d+22|0,l=d,(r|0)==7){t=a[s+116>>2]|0,M5(o,10,l,u,a[s+112>>2]|0,f)|0,O5(10,l,u,f),C5(a[i>>2]|0,l,u,n+22|0,c,f)|0,M5(o,10,l,u,t,f)|0,O5(10,l,u,f),C5(a[i>>2]|0,l,u,n+66|0,c,f)|0,C=d;return}else{M5(t,10,l,u,a[s+108>>2]|0,f)|0,O5(10,l,u,f),C5(a[i>>2]|0,l,u,n+66|0,c,f)|0,C=d;return}}function L5(i,r,t,o,n,s,f,l,u,c){if(i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,(t|0)==6){e[n>>1]=Je(i,r,o,20,143,80,s,f,l,u,c)|0;return}if(e[f>>1]=0,e[f+2>>1]=0,t>>>0<2){e[n>>1]=T5(r,t,o,20,143,160,l,u,c)|0;return}if(t>>>0<6){e[n>>1]=T5(r,t,o,20,143,80,l,u,c)|0;return}else{e[n>>1]=T5(r,t,o,18,143,80,l,u,c)|0;return}}function Ge(i){i=i|0;var r=0;return i|0&&(a[i>>2]=0,r=o0(2)|0,(r|0)!=0)?(e[r>>1]=0,a[i>>2]=r,r=0):r=-1,r|0}function X6(i){return i=i|0,i?(e[i>>1]=0,i=0):i=-1,i|0}function Y6(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function Ke(i,r,t,o,n,s,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0;w1=C,C=C+240|0,R=w1+160|0,S=w1+80|0,a1=w1,o1=e[3558+(r*18|0)>>1]|0,l1=e[3558+(r*18|0)+2>>1]|0,w=e[3558+(r*18|0)+4>>1]|0,u1=e[3558+(r*18|0)+6>>1]|0,b=e[3558+(r*18|0)+12>>1]|0,k=e[3558+(r*18|0)+14>>1]|0,h=e[3558+(r*18|0)+16>>1]|0;e:do switch(l<<16>>16){case 0:case 80:if(r>>>0<2&l<<16>>16==80){d1=(D[i>>1]|0)-(b&65535)|0,d1=(d1<<16>>16|0)<(h<<16>>16|0)?h:d1&65535,W=k<<16>>16,c1=(d1&65535)+W&65535,t1=c1<<16>>16>143,d1=t1?143-W&65535:d1,c1=t1?143:c1,t1=1;break e}else{d1=(D[t+((l<<16>>16!=0&1)<<1)>>1]|0)-(D[3558+(r*18|0)+8>>1]|0)|0,d1=(d1<<16>>16|0)<(h<<16>>16|0)?h:d1&65535,W=e[3558+(r*18|0)+10>>1]|0,c1=(d1&65535)+W&65535,t1=c1<<16>>16>143,d1=t1?143-W&65535:d1,c1=t1?143:c1,t1=0;break e}default:d1=(D[i>>1]|0)-(b&65535)|0,d1=(d1<<16>>16|0)<(h<<16>>16|0)?h:d1&65535,W=k<<16>>16,c1=(d1&65535)+W&65535,t1=c1<<16>>16>143,d1=t1?143-W&65535:d1,c1=t1?143:c1,t1=1}while(!1);if(i1=d1&65535,l=i1+65532|0,E=l&65535,q=(c1&65535)+4&65535,W=l<<16>>16,l=0-(l&65535)|0,b=l&65535,s5(o+(l<<16>>16<<1)|0,s,R,f),l=f<<16>>16,B=l>>>1&65535,O=B<<16>>16==0,O)f=1;else{for(f=B,h=R,t=S,k=0;U=e[h>>1]|0,e[t>>1]=U>>>2,U=(g(U,U)|0)+k|0,k=e[h+2>>1]|0,e[t+2>>1]=k>>>2,k=U+(g(k,k)|0)|0,f=f+-1<<16>>16,f<<16>>16;)h=h+4|0,t=t+4|0;f=(k|0)<33554433}U=f?0:2,F=f?R:S,N=f?R:S;e:do if(E<<16>>16<=q<<16>>16){if(A=l+-1|0,Y=F+(A<<1)|0,K=s+(A<<1)|0,H=F+(l+-2<<1)|0,T=A>>>1,z=T&65535,I=z<<16>>16==0,j=f?12:14,T=(T<<1)+131070&131070,t=l+-3-T|0,M=F+(t<<1)|0,T=F+(l+-4-T<<1)|0,s=s+(t<<1)|0,!O)for(O=W;;){for(S=B,R=N,h=n,k=0,f=0;S=S+-1<<16>>16,l=e[R>>1]|0,k=(g(l,e[h>>1]|0)|0)+k|0,l=(g(l,l)|0)+f|0,f=e[R+2>>1]|0,k=k+(g(f,e[h+2>>1]|0)|0)|0,f=l+(g(f,f)|0)|0,S<<16>>16;)R=R+4|0,h=h+4|0;if(R=e2(f<<1,m)|0,f=R>>16,h=k<<1>>16,S=g(f,h)|0,S=(S|0)==1073741824?2147483647:S<<1,h=(g((R>>>1)-(f<<15)<<16>>16,h)|0)>>15,R=(h<<1)+S|0,R=(h^S|0)>0&(R^S|0)<0?(S>>>31)+2147483647|0:R,f=(g(f,k&32767)|0)>>15,S=R+(f<<1)|0,e[a1+(O-W<<1)>>1]=(R^f|0)>0&(S^R|0)<0?(R>>>31)+65535|0:S,E<<16>>16!=q<<16>>16){if(b=b+-1<<16>>16,S=e[o+(b<<16>>16<<1)>>1]|0,I)R=A,f=H,k=K,h=Y;else for(R=z,f=H,k=K,h=Y;;)if(O=(g(e[k>>1]|0,S)|0)>>j,e[h>>1]=O+(D[f>>1]|0),O=(g(e[k+-2>>1]|0,S)|0)>>j,e[h+-2>>1]=O+(D[f+-2>>1]|0),R=R+-1<<16>>16,R<<16>>16)f=f+-4|0,k=k+-4|0,h=h+-4|0;else{R=t,f=T,k=s,h=M;break}O=(g(e[k>>1]|0,S)|0)>>j,e[h>>1]=O+(D[f>>1]|0),e[F+(R+-1<<1)>>1]=S>>U}if(E=E+1<<16>>16,E<<16>>16>q<<16>>16)break e;O=E<<16>>16}if(I)for(f=F+(l+-2<<1)|0,k=W;;){if(e2(0,m)|0,e[a1+(k-W<<1)>>1]=0,E<<16>>16!=q<<16>>16&&(b=b+-1<<16>>16,n=e[o+(b<<16>>16<<1)>>1]|0,z=(g(e[K>>1]|0,n)|0)>>j,e[Y>>1]=z+(D[H>>1]|0),e[f>>1]=n>>U),E=E+1<<16>>16,E<<16>>16>q<<16>>16)break e;k=E<<16>>16}for(R=F+(t+-1<<1)|0,f=W;;){if(e2(0,m)|0,e[a1+(f-W<<1)>>1]=0,E<<16>>16!=q<<16>>16){for(b=b+-1<<16>>16,f=e[o+(b<<16>>16<<1)>>1]|0,k=z,h=H,t=K,l=Y;n=(g(e[t>>1]|0,f)|0)>>j,e[l>>1]=n+(D[h>>1]|0),n=(g(e[t+-2>>1]|0,f)|0)>>j,e[l+-2>>1]=n+(D[h+-2>>1]|0),k=k+-1<<16>>16,k<<16>>16;)h=h+-4|0,t=t+-4|0,l=l+-4|0;n=(g(e[s>>1]|0,f)|0)>>j,e[M>>1]=n+(D[T>>1]|0),e[R>>1]=f>>U}if(E=E+1<<16>>16,E<<16>>16>q<<16>>16)break;f=E<<16>>16}}while(!1);if(E=d1<<16>>16,t=i1+1&65535,t<<16>>16>c1<<16>>16)s=d1;else for(b=d1,l=e[a1+(E-W<<1)>>1]|0;;)if(k=e[a1+((t<<16>>16)-W<<1)>>1]|0,h=k<<16>>16<l<<16>>16,b=h?b:t,t=t+1<<16>>16,t<<16>>16>c1<<16>>16){s=b;break}else l=h?l:k;e:do if(!(t1<<16>>16)&&s<<16>>16>o1<<16>>16)t=s,w=0;else{if(!(r>>>0<4&t1<<16>>16!=0)){if(b=a1+((s<<16>>16)-W<<1)|0,k=o2(b,w,l1,m)|0,t=(w&65535)+1&65535,t<<16>>16<=u1<<16>>16)for(;h=o2(b,t,l1,m)|0,l=h<<16>>16>k<<16>>16,w=l?t:w,t=t+1<<16>>16,!(t<<16>>16>u1<<16>>16);)k=l?h:k;if((r+-7|0)>>>0<2){u1=w<<16>>16==-3,t=(u1<<31>>31)+s<<16>>16,w=u1?3:w;break}switch(w<<16>>16){case-2:{t=s+-1<<16>>16,w=1;break e}case 2:{t=s+1<<16>>16,w=-1;break e}default:{t=s;break e}}}if(o1=e[i>>1]|0,o1=((o1<<16>>16)-E|0)>5?E+5&65535:o1,l=c1<<16>>16,o1=(l-(o1<<16>>16)|0)>4?l+65532&65535:o1,l=s<<16>>16,t=o1<<16>>16,(l|0)==(t+-1|0)||s<<16>>16==o1<<16>>16){if(b=a1+(l-W<<1)|0,l=o2(b,w,l1,m)|0,t=(w&65535)+1&65535,t<<16>>16<=u1<<16>>16)for(;k=o2(b,t,l1,m)|0,h=k<<16>>16>l<<16>>16,w=h?t:w,t=t+1<<16>>16,!(t<<16>>16>u1<<16>>16);)l=h?k:l;if((r+-7|0)>>>0<2){u1=w<<16>>16==-3,t=(u1<<31>>31)+s<<16>>16,w=u1?3:w;break}switch(w<<16>>16){case-2:{t=s+-1<<16>>16,w=1;break e}case 2:{t=s+1<<16>>16,w=-1;break e}default:{t=s;break e}}}if((l|0)==(t+-2|0)){if(t=a1+(l-W<<1)|0,l=o2(t,0,l1,m)|0,(r|0)!=8){for(w=0,b=1;k=o2(t,b,l1,m)|0,h=k<<16>>16>l<<16>>16,w=h?b:w,b=b+1<<16>>16,!(b<<16>>16>u1<<16>>16);)l=h?k:l;if((r+-7|0)>>>0>=2)switch(w<<16>>16){case-2:{t=s+-1<<16>>16,w=1;break e}case 2:{t=s+1<<16>>16,w=-1;break e}default:{t=s;break e}}}else w=0;u1=w<<16>>16==-3,t=(u1<<31>>31)+s<<16>>16,w=u1?3:w;break}if((l|0)==(t+1|0)){if(b=a1+(l-W<<1)|0,t=o2(b,w,l1,m)|0,l=(w&65535)+1&65535,l<<16>>16<=0)for(;h=o2(b,l,l1,m)|0,k=h<<16>>16>t<<16>>16,w=k?l:w,l=l+1<<16>>16,!(l<<16>>16>0);)t=k?h:t;if((r+-7|0)>>>0<2){u1=w<<16>>16==-3,t=(u1<<31>>31)+s<<16>>16,w=u1?3:w;break}switch(w<<16>>16){case-2:{t=s+-1<<16>>16,w=1;break e}case 2:{t=s+1<<16>>16,w=-1;break e}default:{t=s;break e}}}else t=s,w=0}while(!1);return(r+-7|0)>>>0>1?(u1=i,i=Pe(t,w,e[i>>1]|0,d1,c1,t1,r>>>0<4&1,m)|0,e[d>>1]=i,e[u1>>1]=t,e[c>>1]=l1,e[u>>1]=w,C=w1,t|0):(m=Ne(t,w,d1,t1,m)|0,e[d>>1]=m,e[i>>1]=t,e[c>>1]=l1,e[u>>1]=w,C=w1,t|0)}function T5(i,r,t,o,n,s,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0;T=C,C=C+1200|0,B=T+1188|0,F=T+580|0,M=T+578|0,I=T+576|0,S=T,N=T+582|0,A=(l|0)!=0;do if(A)if(r>>>0<2){x5(i,1,u);break}else{x5(i,0,u);break}while(!1);O=n<<16>>16,m=0-O|0,d=t+(m<<1)|0,m=m&65535,b=s<<16>>16;do if(m<<16>>16<s<<16>>16){for(k=m,h=d,m=0;E=e[h>>1]|0,m=(g(E<<1,E)|0)+m|0,!((m|0)<0);)if(k=k+1<<16>>16,k<<16>>16>=s<<16>>16){R=14;break}else h=h+2|0;if((R|0)==14){if((m|0)<1048576){R=15;break}R0(N|0,d|0,b+O<<1|0)|0,E=0;break}if(c=b+O|0,w=c>>>1,k=w&65535,!(k<<16>>16))m=N;else{for(E=((w<<1)+131070&131070)+2|0,b=E-O|0,h=N;e[h>>1]=(e[d>>1]|0)>>>3,e[h+2>>1]=(e[d+2>>1]|0)>>>3,k=k+-1<<16>>16,k<<16>>16;)d=d+4|0,h=h+4|0;d=t+(b<<1)|0,m=N+(E<<1)|0}c&1&&(e[m>>1]=(e[d>>1]|0)>>>3),E=3}else R=15;while(!1);if((R|0)==15){if(E=b+O|0,m=E>>>1,w=m&65535,!(w<<16>>16))m=N;else{for(b=((m<<1)+131070&131070)+2|0,h=b-O|0,k=N;e[k>>1]=e[d>>1]<<3,e[k+2>>1]=e[d+2>>1]<<3,w=w+-1<<16>>16,w<<16>>16;)d=d+4|0,k=k+4|0;d=t+(h<<1)|0,m=N+(b<<1)|0}E&1&&(e[m>>1]=e[d>>1]<<3),E=-3}return b=S+(O<<2)|0,h=N+(O<<1)|0,I6(h,s,n,o,b),c=(r|0)==7&1,m=o<<16>>16,d=m<<2,(d|0)!=(m<<18>>16|0)&&(a[u>>2]=1,d=o<<16>>16>0?32767:-32768),k=I5(i,b,h,E,c,s,n,d&65535,B,l,u)|0,m=m<<1,w=I5(i,b,h,E,c,s,d+65535&65535,m&65535,F,l,u)|0,m=I5(i,b,h,E,c,s,m+65535&65535,o,M,l,u)|0,f<<16>>16==1&A&&(q6(b,h,s,n,o,I,u)|0,i4(i,e[I>>1]|0)),d=e[B>>1]|0,c=e[F>>1]|0,((d<<16>>16)*55706>>16|0)>=(c<<16>>16|0)?(F=d,B=k,F=F<<16>>16,F=F*55706|0,F=F>>16,M=e[M>>1]|0,M=M<<16>>16,M=(F|0)<(M|0),M=M?m:B,C=T,M|0):(e[B>>1]=c,F=c,B=w,F=F<<16>>16,F=F*55706|0,F=F>>16,M=e[M>>1]|0,M=M<<16>>16,M=(F|0)<(M|0),M=M?m:B,C=T,M|0)}function I5(i,r,t,o,n,s,f,l,u,c,d){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0;var m=0,w=0,h=0,k=0,b=0;if(f<<16>>16<l<<16>>16)l=-2147483648,h=f;else for(h=f,m=-2147483648,w=r+(0-(f<<16>>16)<<2)|0,r=f;;)if(f=a[w>>2]|0,b=(f|0)<(m|0),r=b?r:h,m=b?m:f,h=h+-1<<16>>16,h<<16>>16<l<<16>>16){l=m,h=r;break}else w=w+4|0;if(r=s<<16>>16>>>2&65535,!(r<<16>>16))r=0;else{for(m=r,f=t+(0-(h<<16>>16)<<1)|0,r=0;b=e[f>>1]|0,b=(g(b,b)|0)+r|0,r=e[f+2>>1]|0,r=b+(g(r,r)|0)|0,b=e[f+4>>1]|0,b=r+(g(b,b)|0)|0,r=e[f+6>>1]|0,r=b+(g(r,r)|0)|0,m=m+-1<<16>>16,m<<16>>16;)f=f+8|0;r=r<<1}if(c&&t4(i,l,r,d),r=e2(r,d)|0,f=n<<16>>16!=0,f&&(r=(r|0)>1073741823?2147483647:r<<1),n=l>>16,i=r>>16,d=g(i,n)|0,d=(d|0)==1073741824?2147483647:d<<1,r=(g((r>>>1)-(i<<15)<<16>>16,n)|0)>>15,b=(r<<1)+d|0,b=(r^d|0)>0&(b^d|0)<0?(d>>>31)+2147483647|0:b,n=(g(i,(l>>>1)-(n<<15)<<16>>16)|0)>>15,r=b+(n<<1)|0,r=(b^n|0)>0&(r^b|0)<0?(b>>>31)+2147483647|0:r,!f)return e[u>>1]=r,h|0;if(f=o<<16>>16,o<<16>>16>0?o<<16>>16<31?(f=r>>f,k=16):f=0:(k=0-f<<16>>16,f=r<<k,f=(f>>k|0)==(r|0)?f:r>>31^2147483647,k=16),(k|0)==16){if((f|0)>65535)return e[u>>1]=32767,h|0;if((f|0)<-65536)return e[u>>1]=-32768,h|0}return e[u>>1]=f>>>1,h|0}function Ze(i){i=i|0;var r=0;return!i||(a[i>>2]=0,r=o0(6)|0,!r)?(i=-1,i|0):(e[r>>1]=40,e[r+2>>1]=0,e[r+4>>1]=0,a[i>>2]=r,i=0,i|0)}function Qe(i){return i=i|0,i?(e[i>>1]=40,e[i+2>>1]=0,e[i+4>>1]=0,i=0,i|0):(i=-1,i|0)}function $e(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function Je(i,r,t,o,n,s,f,l,u,c,d){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0;var m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0;if(j=C,C=C+1200|0,O=j+1186|0,N=j+1184|0,z=j+1182|0,S=j,I=j+576|0,A=n<<16>>16,T=I+(A<<1)|0,m=(0-A&65535)<<16>>16<s<<16>>16,m){b=0-n<<16>>16<<16>>16,w=0;do k=e[t+(b<<1)>>1]|0,k=g(k,k)|0,(k|0)!=1073741824?(h=(k<<1)+w|0,(k^w|0)>0&(h^w|0)<0?(a[d>>2]=1,w=(w>>>31)+2147483647|0):w=h):(a[d>>2]=1,w=2147483647),b=b+1|0;while((b&65535)<<16>>16!=s<<16>>16)}else w=0;if((2147483646-w&w|0)>=0)if((w|0)==2147483647){if(m){w=0-n<<16>>16<<16>>16;do e[I+(w+A<<1)>>1]=X1(e[t+(w<<1)>>1]|0,3,d)|0,w=w+1|0;while((w&65535)<<16>>16!=s<<16>>16)}}else E=14;else a[d>>2]=1,E=14;do if((E|0)==14){if((1048575-w&w|0)<0?(a[d>>2]=1,w=(w>>>31)+2147483647|0):w=w+-1048576|0,(w|0)>=0){if(!m)break;M=0-n<<16>>16<<16>>16,R0(I+(A+M<<1)|0,t+(M<<1)|0,(((s+n<<16>>16)+-1&65535)<<1)+2|0)|0;break}if(m){w=0-n<<16>>16<<16>>16;do M=e[t+(w<<1)>>1]|0,e[I+(w+A<<1)>>1]=(M<<19>>19|0)==(M|0)?M<<3:M>>>15^32767,w=w+1|0;while((w&65535)<<16>>16!=s<<16>>16)}}while(!1);B=S+(A<<2)|0,I6(T,s,n,o,B),b=e[i>>1]|0,M=i+4|0,F=l+(u<<16>>16<<1)|0;e:do if(n<<16>>16<o<<16>>16)R=n;else{if((e[M>>1]|0)<=0)for(t=n,l=-2147483648,k=n,E=3402;;)if(O0(a[S+(A-(t<<16>>16)<<2)>>2]|0,O,N,d),h=e[N>>1]|0,w=e[E>>1]|0,b=g(w,e[O>>1]|0)|0,(b|0)==1073741824?(a[d>>2]=1,m=2147483647):m=b<<1,R=(g(w,h<<16>>16)|0)>>15,b=m+(R<<1)|0,(m^R|0)>0&(b^m|0)<0&&(a[d>>2]=1,b=(m>>>31)+2147483647|0),h=(b|0)<(l|0),k=h?k:t,t=t+-1<<16>>16,t<<16>>16<o<<16>>16){R=k;break e}else l=h?l:b,E=E+-2|0;for(l=n,m=-2147483648,k=n,R=2902+(A+123-(b<<16>>16)<<1)|0,t=3402;;)if(O0(a[S+(A-(l<<16>>16)<<2)>>2]|0,O,N,d),E=e[N>>1]|0,h=e[t>>1]|0,b=g(h,e[O>>1]|0)|0,(b|0)==1073741824?(a[d>>2]=1,w=2147483647):w=b<<1,E=(g(h,E<<16>>16)|0)>>15,b=w+(E<<1)|0,(w^E|0)>0&(b^w|0)<0&&(a[d>>2]=1,b=(w>>>31)+2147483647|0),O0(b,O,N,d),E=e[N>>1]|0,h=e[R>>1]|0,b=g(h,e[O>>1]|0)|0,(b|0)==1073741824?(a[d>>2]=1,w=2147483647):w=b<<1,E=(g(h,E<<16>>16)|0)>>15,b=w+(E<<1)|0,(w^E|0)>0&(b^w|0)<0&&(a[d>>2]=1,b=(w>>>31)+2147483647|0),h=(b|0)<(m|0),k=h?k:l,l=l+-1<<16>>16,l<<16>>16<o<<16>>16){R=k;break}else m=h?m:b,R=R+-2|0,t=t+-2|0}while(!1);if(s<<16>>16>0)for(l=0,t=T,E=I+(A-(R<<16>>16)<<1)|0,k=0,w=0;b=e[E>>1]|0,h=g(b,e[t>>1]|0)|0,(h|0)!=1073741824?(m=(h<<1)+k|0,(h^k|0)>0&(m^k|0)<0?(a[d>>2]=1,k=(k>>>31)+2147483647|0):k=m):(a[d>>2]=1,k=2147483647),m=g(b,b)|0,(m|0)!=1073741824?(h=(m<<1)+w|0,(m^w|0)>0&(h^w|0)<0?(a[d>>2]=1,w=(w>>>31)+2147483647|0):w=h):(a[d>>2]=1,w=2147483647),l=l+1<<16>>16,!(l<<16>>16>=s<<16>>16);)t=t+2|0,E=E+2|0;else k=0,w=0;return h=(c|0)==0,h||(x5(r,0,d),t4(r,k,w,d)),m=(S1(w,d)|0)<<16>>16,(m*13107|0)==1073741824?(a[d>>2]=1,w=2147483647):w=m*26214|0,m=k-w|0,((m^k)&(w^k)|0)<0&&(a[d>>2]=1,m=(k>>>31)+2147483647|0),c=S1(m,d)|0,e[F>>1]=c,c<<16>>16>0?(m=f+6|0,e[f+8>>1]=e[m>>1]|0,c=f+4|0,e[m>>1]=e[c>>1]|0,m=f+2|0,e[c>>1]=e[m>>1]|0,e[m>>1]=e[f>>1]|0,e[f>>1]=R,e[i>>1]=k2(f,5)|0,e[i+2>>1]=32767,m=32767):(e[i>>1]=R,i=i+2|0,m=((e[i>>1]|0)*29491|0)>>>15&65535,e[i>>1]=m),e[M>>1]=((y1(m,9830,d)|0)&65535)>>>15^1,h||(y1(u,1,d)|0)<<16>>16?(C=j,R|0):(q6(B,T,s,n,o,z,d)|0,i4(r,e[z>>1]|0),C=j,R|0)}function F5(i,r,t,o,n,s,f,l,u,c){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0;var d=0,m=0;c=C,C=C+48|0,m=c+22|0,d=c,r=i>>>0<6?r:t,t=s<<16>>16>0?22:0,i=n+(t<<1)|0,X0(i,r,m),X0(i,o,d),i=s<<16>>16,s=u+(i<<1)|0,N2(m,f+(i<<1)|0,s,40),f0(d,s,s,40,l,1),t=n+(((t<<16)+720896|0)>>>16<<1)|0,X0(t,r,m),X0(t,o,d),i=(i<<16)+2621440>>16,u=u+(i<<1)|0,N2(m,f+(i<<1)|0,u,40),f0(d,u,u,40,l,1),C=c}function ei(i){i=i|0;var r=0;return!i||(a[i>>2]=0,r=o0(12)|0,!r)?(i=-1,i|0):(e[r>>1]=0,e[r+2>>1]=0,e[r+4>>1]=0,e[r+6>>1]=0,e[r+8>>1]=0,e[r+10>>1]=0,a[i>>2]=r,i=0,i|0)}function ii(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,i=0,i|0):(i=-1,i|0)}function G6(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function ti(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0;if(m=i+10|0,n=e[m>>1]|0,w=i+8|0,o=e[w>>1]|0,!(t<<16>>16)){i=o,d=n,e[m>>1]=d,e[w>>1]=i;return}for(l=i+4|0,u=i+6|0,c=i+2|0,f=e[u>>1]|0,d=e[l>>1]|0,s=t,t=n;h=(g(e[i>>1]|0,-3733)|0)+(((d<<16>>16)*7807|0)+((f<<16>>16)*7807>>15))|0,e[i>>1]=d,h=h+((g(e[c>>1]|0,-3733)|0)>>15)|0,e[c>>1]=f,h=((t<<16>>16)*1899|0)+h+(g(o<<16>>16,-3798)|0)|0,t=e[r>>1]|0,h=h+((t<<16>>16)*1899|0)|0,e[r>>1]=(h+2048|0)>>>12,n=h>>>12,d=n&65535,e[l>>1]=d,f=(h<<3)-(n<<15)&65535,e[u>>1]=f,s=s+-1<<16>>16,s<<16>>16;)h=o,r=r+2|0,o=t,t=h;e[m>>1]=o,e[w>>1]=t}function ri(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0;if(n=e[(a[o+88>>2]|0)+(i<<1)>>1]|0,!!(n<<16>>16))for(l=t,f=a[(a[o+92>>2]|0)+(i<<2)>>2]|0;;){if(t=e[f>>1]|0,!(t<<16>>16))t=0;else{for(i=e[r>>1]|0,s=t,o=l+((t<<16>>16)+-1<<1)|0;t=i<<16>>16,e[o>>1]=t&1,s=s+-1<<16>>16,s<<16>>16;)i=t>>>1&65535,o=o+-2|0;t=e[f>>1]|0}if(r=r+2|0,n=n+-1<<16>>16,n<<16>>16)l=l+(t<<16>>16<<1)|0,f=f+2|0;else break}}function oi(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0;if(d=C,C=C+16|0,u=d+2|0,c=d,f=n<<16>>16,n<<16>>16<1){s=-5443,c=-32768,W0(i,c,s),C=d;return}if(l=x0(14,t,s)|0,(f|0)<(l<<16>>16|0)?t=o:(t=(o&65535)+1&65535,n=f>>>1&65535),o=r0(n,l&65535)|0,e[c>>1]=o,s2(o<<16>>16,u,c,s),e[u>>1]=((((t&65535)-(r&65535)<<16)+-65536|0)>>>16)+(D[u>>1]|0),o=V2(e[c>>1]|0,5,s)|0,f=e[u>>1]|0,o=((f&65535)<<10)+(o&65535)&65535,o<<16>>16>18284){s=3037,c=18284,W0(i,c,s),C=d;return}n=e[c>>1]|0,f=f<<16>>16,(f*24660|0)==1073741824?(a[s>>2]=1,t=2147483647):t=f*49320|0,c=(n<<16>>16)*24660>>15,f=t+(c<<1)|0,(t^c|0)>0&(f^t|0)<0&&(a[s>>2]=1,f=(t>>>31)+2147483647|0),c=f<<13,s=S1((c>>13|0)==(f|0)?c:f>>31^2147483647,s)|0,c=o,W0(i,c,s),C=d}function ni(i,r,t,o,n,s,f,l,u,c,d,m,w,h,k,b,E,R,S,O){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0,O=O|0;var N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0,E1=0,T1=0,A1=0,j1=0,x1=0,L1=0,K1=0,U1=0,n0=0,z1=0;for(z1=C,C=C+80|0,L1=z1+66|0,K1=z1+64|0,U1=z1+62|0,n0=z1+60|0,o1=z1+40|0,a1=z1+20|0,i1=z1,e[L1>>1]=r,e[K1>>1]=u,e[U1>>1]=c,q=x0(14,t,O)|0,x1=q&65535,e[n0>>1]=x1,W=x0(14,c,O)|0,U=(D[o>>1]|0)+65523|0,e[i1>>1]=U,T=(D[o+2>>1]|0)+65522|0,z=i1+2|0,e[z>>1]=T,j=((r&65535)<<16)+-720896|0,F=j>>16,j=(j>>>15)+15+(D[o+4>>1]|0)|0,Y=i1+4|0,e[Y>>1]=j,K=(D[o+6>>1]|0)+F|0,H=i1+6|0,e[H>>1]=K,F=F+1+(D[o+8>>1]|0)|0,B=i1+8|0,e[B>>1]=F,N=(D[d>>1]|0)+65523&65535,e[i1+10>>1]=N,M=(D[d+2>>1]|0)+65522&65535,e[i1+12>>1]=M,A=((u&65535)<<16)+-720896|0,o=A>>16,A=(A>>>15)+15+(D[d+4>>1]|0)&65535,e[i1+14>>1]=A,I=(D[d+6>>1]|0)+o&65535,e[i1+16>>1]=I,o=o+1+(D[d+8>>1]|0)&65535,e[i1+18>>1]=o,A1=(s&65535)-(w&65535)<<16,u=A1>>16,(A1|0)>0?(c=f,t=h<<16>>16>>u&65535):(c=f<<16>>16>>0-u&65535,t=h),(V2(t,1,O)|0)<<16>>16>c<<16>>16?t=1:t=(((c<<16>>16)+3>>2|0)>(t<<16>>16|0))<<31>>31,d=U+t&65535,e[i1>>1]=d,A1=T+t&65535,e[z>>1]=A1,T1=j+t&65535,e[Y>>1]=T1,E1=K+t&65535,e[H>>1]=E1,V=F+t&65535,e[B>>1]=V,u=o<<16>>16>d<<16>>16?o:d,u=I<<16>>16>u<<16>>16?I:u,u=A<<16>>16>u<<16>>16?A:u,u=M<<16>>16>u<<16>>16?M:u,u=N<<16>>16>u<<16>>16?N:u,u=V<<16>>16>u<<16>>16?V:u,u=E1<<16>>16>u<<16>>16?E1:u,u=T1<<16>>16>u<<16>>16?T1:u,u=(A1<<16>>16>u<<16>>16?A1:u)+1&65535,o=0;;){if(t=u-(d&65535)|0,d=t&65535,c=D[n>>1]<<16,t=t<<16>>16,d<<16>>16>0?d=d<<16>>16<31?c>>t:0:(A1=0-t<<16>>16,d=c<<A1,d=(d>>A1|0)==(c|0)?d:c>>31^2147483647),A1=d>>16,e[o1+(o<<1)>>1]=A1,e[a1+(o<<1)>>1]=(d>>>1)-(A1<<15),o=o+1|0,(o|0)==5){t=5,c=m;break}d=e[i1+(o<<1)>>1]|0,n=n+2|0}for(;o=u-(N&65535)|0,N=o&65535,d=D[c>>1]<<16,o=o<<16>>16,N<<16>>16>0?d=N<<16>>16<31?d>>o:0:(T1=0-o<<16>>16,A1=d<<T1,d=(A1>>T1|0)==(d|0)?A1:d>>31^2147483647),A1=d>>16,e[o1+(t<<1)>>1]=A1,e[a1+(t<<1)>>1]=(d>>>1)-(A1<<15),d=t+1|0,(d&65535)<<16>>16!=10;)N=e[i1+(d<<1)>>1]|0,t=d,c=c+2|0;u1=q<<16>>16,d1=e[o1>>1]|0,c1=e[a1>>1]|0,t1=e[o1+2>>1]|0,l1=e[a1+2>>1]|0,w1=e[o1+4>>1]|0,m1=e[a1+4>>1]|0,Z=e[o1+6>>1]|0,V=e[a1+6>>1]|0,E1=e[o1+8>>1]|0,T1=e[a1+8>>1]|0,A1=k&65535,w=W<<16>>16,s=e[o1+10>>1]|0,I=e[a1+10>>1]|0,A=e[o1+12>>1]|0,n=e[a1+12>>1]|0,t=e[o1+14>>1]|0,c=e[a1+14>>1]|0,o=e[o1+16>>1]|0,N=e[a1+16>>1]|0,F=e[o1+18>>1]|0,a1=e[a1+18>>1]|0,u=2147483647,o1=0,d=0,B=782;do i1=e[B>>1]|0,K=(g(u1,e[B+2>>1]|0)|0)>>>15<<16,m=K>>16,j=i1<<1,U=(g(j,i1)|0)>>16,h=g(U,d1)|0,(h|0)==1073741824?(a[O>>2]=1,H=2147483647):H=h<<1,W=(g(c1,U)|0)>>15,h=H+(W<<1)|0,(H^W|0)>0&(h^H|0)<0&&(a[O>>2]=1,h=(H>>>31)+2147483647|0),U=g(t1,i1)|0,(U|0)==1073741824?(a[O>>2]=1,H=2147483647):H=U<<1,W=(g(l1,i1)|0)>>15,U=H+(W<<1)|0,(H^W|0)>0&(U^H|0)<0&&(a[O>>2]=1,U=(H>>>31)+2147483647|0),K=(g(K>>15,m)|0)>>16,H=g(w1,K)|0,(H|0)==1073741824?(a[O>>2]=1,Y=2147483647):Y=H<<1,W=(g(m1,K)|0)>>15,H=Y+(W<<1)|0,(Y^W|0)>0&(H^Y|0)<0&&(a[O>>2]=1,H=(Y>>>31)+2147483647|0),K=g(Z,m)|0,(K|0)==1073741824?(a[O>>2]=1,Y=2147483647):Y=K<<1,W=(g(V,m)|0)>>15,K=Y+(W<<1)|0,(Y^W|0)>0&(K^Y|0)<0?(a[O>>2]=1,W=(Y>>>31)+2147483647|0):W=K,Y=(g(j,m)|0)>>16,K=g(E1,Y)|0,(K|0)==1073741824?(a[O>>2]=1,j=2147483647):j=K<<1,q=(g(T1,Y)|0)>>15,K=j+(q<<1)|0,(j^q|0)>0&(K^j|0)<0&&(a[O>>2]=1,K=(j>>>31)+2147483647|0),Y=e[B+4>>1]|0,j=e[B+6>>1]|0,B=B+8|0,(i1-A1&65535)<<16>>16<1&&(j1=Y<<16>>16,Y<<16>>16<=k<<16>>16)&&(T=(g(j<<16>>16,w)|0)>>>15<<16,i1=T>>16,M=j1<<1,j=(g(M,j1)|0)>>16,Y=g(s,j)|0,(Y|0)==1073741824?(a[O>>2]=1,z=2147483647):z=Y<<1,q=(g(I,j)|0)>>15,Y=z+(q<<1)|0,(z^q|0)>0&(Y^z|0)<0&&(a[O>>2]=1,Y=(z>>>31)+2147483647|0),j=g(A,j1)|0,(j|0)==1073741824?(a[O>>2]=1,z=2147483647):z=j<<1,q=(g(n,j1)|0)>>15,j=z+(q<<1)|0,(z^q|0)>0&(j^z|0)<0?(a[O>>2]=1,q=(z>>>31)+2147483647|0):q=j,z=(g(T>>15,i1)|0)>>16,j=g(t,z)|0,(j|0)==1073741824?(a[O>>2]=1,T=2147483647):T=j<<1,m=(g(c,z)|0)>>15,j=T+(m<<1)|0,(T^m|0)>0&(j^T|0)<0?(a[O>>2]=1,m=(T>>>31)+2147483647|0):m=j,j=g(o,i1)|0,(j|0)==1073741824?(a[O>>2]=1,z=2147483647):z=j<<1,T=(g(N,i1)|0)>>15,j=z+(T<<1)|0,(z^T|0)>0&(j^z|0)<0?(a[O>>2]=1,f=(z>>>31)+2147483647|0):f=j,z=(g(M,i1)|0)>>16,j=g(F,z)|0,(j|0)==1073741824?(a[O>>2]=1,T=2147483647):T=j<<1,i1=(g(a1,z)|0)>>15,j=T+(i1<<1)|0,(T^i1|0)>0&(j^T|0)<0&&(a[O>>2]=1,j=(T>>>31)+2147483647|0),i1=U+h+H+W+K+Y+q+m+f+j|0,W=(i1|0)<(u|0),u=W?i1:u,d=W?o1:d),o1=o1+1<<16>>16;while(o1<<16>>16<256);return k=(d&65535)<<18>>16,K6(i,782+(k<<1)|0,x1,r,b,E,O),M2(i,0,l,K1,U1,L1,n0,O),l=(x0(14,e[U1>>1]|0,O)|0)&65535,K6(i,782+((k|2)<<1)|0,l,e[K1>>1]|0,R,S,O),C=z1,d|0}function K6(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0;if(d=C,C=C+16|0,u=d+2|0,c=d,e[n>>1]=e[r>>1]|0,l=e[r+2>>1]|0,t=g(t<<16>>16<<1,l)|0,n=10-(o&65535)|0,r=n&65535,n=n<<16>>16,r<<16>>16>0?r=r<<16>>16<31?t>>n:0:(n=0-n<<16>>16,r=t<<n,r=(r>>n|0)==(t|0)?r:t>>31^2147483647),e[s>>1]=r>>>16,s2(l,u,c,f),e[u>>1]=(D[u>>1]|0)+65524,n=V2(e[c>>1]|0,5,f)|0,o=e[u>>1]|0,n=((o&65535)<<10)+(n&65535)&65535,t=e[c>>1]|0,o=o<<16>>16,(o*24660|0)==1073741824?(a[f>>2]=1,r=2147483647):r=o*49320|0,c=(t<<16>>16)*24660>>15,o=r+(c<<1)|0,!((r^c|0)>0&(o^r|0)<0)){f=o,f=f<<13,f=f+32768|0,f=f>>>16,f=f&65535,W0(i,n,f),C=d;return}a[f>>2]=1,f=(r>>>31)+2147483647|0,f=f<<13,f=f+32768|0,f=f>>>16,f=f&65535,W0(i,n,f),C=d}function si(i,r,t,o,n,s,f,l,u,c,d,m,w,h,k,b,E,R,S,O,N){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0,R=R|0,S=S|0,O=O|0,N=N|0;var A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0,E1=0,T1=0,A1=0,j1=0,x1=0,L1=0,K1=0,U1=0,n0=0,z1=0,Z1=0,s0=0,e0=0,q1=0;for(q1=C,C=C+80|0,Z1=q1+72|0,s0=q1+70|0,e0=q1+68|0,n0=q1+66|0,z1=q1+56|0,E1=q1+24|0,V=q1+12|0,m1=q1+48|0,Z=q1+40|0,d1=q1+34|0,t1=q1+22|0,a1=q1+6|0,u1=q1,Z6(5,h,k,a1,u1,a[O+72>>2]|0,N)|0,F=x0(14,c,N)|0,c1=O+68|0,o1=a[c1>>2]|0,w1=u<<16>>16,l1=w1+65526|0,h=(D[s>>1]|0)+65523&65535,e[z1>>1]=h,O=(D[s+2>>1]|0)+65522&65535,e[z1+2>>1]=O,L1=l1<<16>>16,K1=((l1<<17>>17|0)==(L1|0)?l1<<1:L1>>>15^32767)+15+(D[s+4>>1]|0)&65535,e[z1+4>>1]=K1,U1=(D[s+6>>1]|0)+L1&65535,e[z1+6>>1]=U1,s=L1+1+(D[s+8>>1]|0)&65535,e[z1+8>>1]=s,O=O<<16>>16>h<<16>>16?O:h,O=K1<<16>>16>O<<16>>16?K1:O,O=U1<<16>>16>O<<16>>16?U1:O,O=(n1(s<<16>>16>O<<16>>16?s:O,1,N)|0)&65535,s=h,h=0;c=O-(s&65535)|0,s=c&65535,I=D[n+(h<<1)>>1]<<16,c=c<<16>>16,s<<16>>16>0?c=s<<16>>16<31?I>>c:0:(U1=0-c<<16>>16,c=I<<U1,c=(c>>U1|0)==(I|0)?c:I>>31^2147483647),O0(c,E1+(h<<1)|0,V+(h<<1)|0,N),c=h+1|0,(c|0)!=5;)s=e[z1+(c<<1)>>1]|0,h=c;for(i1=E1+2|0,W=V+2|0,U1=F<<16>>16,T1=E1+4|0,A1=V+4|0,j1=E1+6|0,x1=V+6|0,L1=E1+8|0,K1=V+8|0,T=0,s=2147483647,n=0,c=0;;){q=e[a1+(n<<1)>>1]|0,F=g(q,q)|0,F>>>0>1073741823?(a[N>>2]=1,F=32767):F=F>>>15,O=e[V>>1]|0,I=F<<16>>16,F=g(I,e[E1>>1]|0)|0,(F|0)==1073741824?(a[N>>2]=1,h=2147483647):h=F<<1,U=(g(O<<16>>16,I)|0)>>15,F=h+(U<<1)|0,(h^U|0)>0&(F^h|0)<0&&(a[N>>2]=1,F=(h>>>31)+2147483647|0),O=e[W>>1]|0,I=g(e[i1>>1]|0,q)|0,(I|0)!=1073741824?(h=(I<<1)+F|0,(I^F|0)>0&(h^F|0)<0&&(a[N>>2]=1,h=(F>>>31)+2147483647|0)):(a[N>>2]=1,h=2147483647),F=(g(O<<16>>16,q)|0)>>15,(F|0)>32767&&(a[N>>2]=1,F=32767),U=F<<16,F=(U>>15)+h|0,(U>>16^h|0)>0&(F^h|0)<0?(a[N>>2]=1,U=(h>>>31)+2147483647|0):U=F,K=(U>>>31)+2147483647|0,H=n&65535,F=T,j=0,Y=o1;do{I=(g(e[Y>>1]|0,U1)|0)>>15,Y=Y+6|0,(I|0)>32767&&(a[N>>2]=1,I=32767),z=I<<16>>16,I=g(z,z)|0,(I|0)==1073741824?(a[N>>2]=1,M=2147483647):M=I<<1,O0(M,Z1,s0,N),I=g(z,q)|0,(I|0)==1073741824?(a[N>>2]=1,M=2147483647):M=I<<1,O0(M,e0,n0,N),h=e[A1>>1]|0,B=e[s0>>1]|0,I=e[T1>>1]|0,O=e[Z1>>1]|0,T=g(O,I)|0,(T|0)!=1073741824?(M=(T<<1)+U|0,(T^U|0)>0&(M^U|0)<0&&(a[N>>2]=1,M=K)):(a[N>>2]=1,M=2147483647),T=(g(B<<16>>16,I)|0)>>15,(T|0)>32767&&(a[N>>2]=1,T=32767),B=T<<16,T=(B>>15)+M|0,(B>>16^M|0)>0&(T^M|0)<0&&(a[N>>2]=1,T=(M>>>31)+2147483647|0),M=(g(O,h<<16>>16)|0)>>15,(M|0)>32767&&(a[N>>2]=1,M=32767),B=M<<16,M=(B>>15)+T|0,(B>>16^T|0)>0&(M^T|0)<0&&(a[N>>2]=1,M=(T>>>31)+2147483647|0),I=e[x1>>1]|0,T=g(e[j1>>1]|0,z)|0,(T|0)!=1073741824?(B=(T<<1)+M|0,(T^M|0)>0&(B^M|0)<0&&(a[N>>2]=1,B=(M>>>31)+2147483647|0)):(a[N>>2]=1,B=2147483647),I=(g(I<<16>>16,z)|0)>>15,(I|0)>32767&&(a[N>>2]=1,I=32767),z=I<<16,I=(z>>15)+B|0,(z>>16^B|0)>0&(I^B|0)<0&&(a[N>>2]=1,I=(B>>>31)+2147483647|0),O=e[K1>>1]|0,B=e[n0>>1]|0,h=e[L1>>1]|0,A=e[e0>>1]|0,T=g(A,h)|0;do if((T|0)==1073741824)a[N>>2]=1,T=2147483647;else{if(M=(T<<1)+I|0,!((T^I|0)>0&(M^I|0)<0)){T=M;break}a[N>>2]=1,T=(I>>>31)+2147483647|0}while(!1);M=(g(B<<16>>16,h)|0)>>15,(M|0)>32767&&(a[N>>2]=1,M=32767),z=M<<16,M=(z>>15)+T|0,(z>>16^T|0)>0&(M^T|0)<0&&(a[N>>2]=1,M=(T>>>31)+2147483647|0),I=(g(A,O<<16>>16)|0)>>15,(I|0)>32767&&(a[N>>2]=1,I=32767),z=I<<16,I=(z>>15)+M|0,(z>>16^M|0)>0&(I^M|0)<0&&(a[N>>2]=1,I=(M>>>31)+2147483647|0),z=(I|0)<(s|0),F=z?j:F,c=z?H:c,s=z?I:s,j=j+1<<16>>16}while(j<<16>>16<32);if(n=n+1|0,(n|0)==3){I=F,n=c;break}else T=F}if(W=(I<<16>>16)*3|0,s=e[o1+(W<<1)>>1]|0,e[E>>1]=e[o1+(W+1<<1)>>1]|0,e[R>>1]=e[o1+(W+2<<1)>>1]|0,s=g(s<<16>>16,U1)|0,(s|0)==1073741824?(a[N>>2]=1,F=2147483647):F=s<<1,W=9-w1|0,o1=W&65535,W=W<<16>>16,i1=o1<<16>>16>0,i1?F=o1<<16>>16<31?F>>W:0:(U=0-W<<16>>16,q=F<<U,F=(q>>U|0)==(F|0)?q:F>>31^2147483647),e[b>>1]=F>>>16,q=n<<16>>16,a1=e[a1+(q<<1)>>1]|0,e[k>>1]=a1,u1=e[u1+(q<<1)>>1]|0,me(r,t,o,a1,d,m1,Z,d1,N),Ie(i,e[d1>>1]|0,e[b>>1]|0,t1,N),!((e[m1>>1]|0)!=0&(e[t1>>1]|0)>0)){N=I,E=a[S>>2]|0,b=E+2|0,e[E>>1]=u1,E=E+4|0,a[S>>2]=E,e[b>>1]=N,C=q1;return}z=m1+6|0,e[z>>1]=l,M=Z+6|0,e[M>>1]=f,u=((y1(w,u,N)|0)&65535)+10|0,O=u<<16>>16,(u&65535)<<16>>16<0?(c=0-O<<16,(c|0)<983040?m=m<<16>>16>>(c>>16)&65535:m=0):(c=m<<16>>16,h=c<<O,(h<<16>>16>>O|0)==(c|0)?m=h&65535:m=(c>>>15^32767)&65535),s=e[k>>1]|0,F=e[t1>>1]|0,c1=a[c1>>2]|0,h=e[b>>1]|0,t1=10-w1|0,O=t1<<16>>16,(t1&65535)<<16>>16<0?(c=0-O<<16,(c|0)<983040?l=h<<16>>16>>(c>>16)&65535:l=0):(c=h<<16>>16,h=c<<O,(h<<16>>16>>O|0)==(c|0)?l=h&65535:l=(c>>>15^32767)&65535),n=s<<16>>16,c=g(n,n)|0,c>>>0>1073741823?(a[N>>2]=1,s=32767):s=c>>>15,I=n1(32767-(F&65535)&65535,1,N)|0,F=F<<16>>16,c=g(e[m1+2>>1]|0,F)|0,(c|0)==1073741824?(a[N>>2]=1,c=2147483647):c=c<<1,t1=c<<1,c=g(((t1>>1|0)==(c|0)?t1:c>>31^2147418112)>>16,s<<16>>16)|0,(c|0)==1073741824?(a[N>>2]=1,T=2147483647):T=c<<1,B=(D[Z+2>>1]|0)+65521|0,O=B&65535,c=g(e[m1+4>>1]|0,F)|0,(c|0)==1073741824?(a[N>>2]=1,s=2147483647):s=c<<1,c=s<<1,c=(g(((c>>1|0)==(s|0)?c:s>>31^2147418112)>>16,n)|0)>>15,(c|0)>32767&&(a[N>>2]=1,c=32767),e[T1>>1]=c,s=l1&65535,e[Z1>>1]=s,s=n1(e[Z+4>>1]|0,s,N)|0,c=g(e[z>>1]|0,F)|0,(c|0)==1073741824?(a[N>>2]=1,c=2147483647):c=c<<1,A=c<<1,e[j1>>1]=((A>>1|0)==(c|0)?A:c>>31^2147418112)>>>16,A=((w1<<17>>17|0)==(w1|0)?w1<<1:w1>>>15^32767)+65529&65535,e[Z1>>1]=A,A=n1(e[M>>1]|0,A,N)|0,c=(g(e[z>>1]|0,I<<16>>16)|0)>>15,(c|0)>32767&&(a[N>>2]=1,c=32767),e[L1>>1]=c,I=n1(A,1,N)|0,h=g(e[m1>>1]|0,F)|0,(h|0)==1073741824?(a[N>>2]=1,c=2147483647):c=h<<1,M=V5(c,Z1,N)|0,n=(D[Z1>>1]|0)+47|0,e[Z1>>1]=n,n=(D[Z>>1]|0)-(n&65535)|0,F=n+31&65535,F=O<<16>>16>F<<16>>16?O:F,F=s<<16>>16>F<<16>>16?s:F,F=A<<16>>16>F<<16>>16?A:F,F=(I<<16>>16>F<<16>>16?I:F)<<16>>16,h=F-(B&65535)|0,c=h&65535,h=h<<16>>16,c<<16>>16>0?U=c<<16>>16<31?T>>h:0:(Z=0-h<<16>>16,U=T<<Z,U=(U>>Z|0)==(T|0)?U:T>>31^2147483647),O=F-(s&65535)|0,c=O&65535,h=D[T1>>1]<<16,O=O<<16>>16,c<<16>>16>0?h=c<<16>>16<31?h>>O:0:(m1=0-O<<16>>16,Z=h<<m1,h=(Z>>m1|0)==(h|0)?Z:h>>31^2147483647),O0(h,T1,A1,N),A=F-(A&65535)|0,h=A&65535,O=D[j1>>1]<<16,A=A<<16>>16,h<<16>>16>0?h=h<<16>>16<31?O>>A:0:(Z=0-A<<16>>16,h=O<<Z,h=(h>>Z|0)==(O|0)?h:O>>31^2147483647),O0(h,j1,x1,N),A=F-(I&65535)|0,h=A&65535,O=D[L1>>1]<<16,A=A<<16>>16,h<<16>>16>0?h=h<<16>>16<31?O>>A:0:(Z=0-A<<16>>16,h=O<<Z,h=(h>>Z|0)==(O|0)?h:O>>31^2147483647),O0(h,L1,K1,N),A=F+65505|0,e[Z1>>1]=A,A=A-(n&65535)|0,h=X1(A&65535,1,N)|0,O=h<<16>>16,h<<16>>16>0?O=h<<16>>16<31?M>>O:0:(Z=0-O<<16>>16,O=M<<Z,O=(O>>Z|0)==(M|0)?O:M>>31^2147483647);do if(!(A&1))T=O;else{if(O0(O,E1,V,N),h=e[V>>1]|0,O=e[E1>>1]|0,(O*23170|0)==1073741824?(a[N>>2]=1,A=2147483647):A=O*46340|0,E1=(h<<16>>16)*23170>>15,O=A+(E1<<1)|0,!((A^E1|0)>0&(O^A|0)<0)){T=O;break}a[N>>2]=1,T=(A>>>31)+2147483647|0}while(!1);for(z=(U>>>31)+2147483647|0,M=2147483647,B=0,O=0,j=c1;h=(g(e[j>>1]|0,U1)|0)>>15,j=j+6|0,(h|0)>32767&&(a[N>>2]=1,h=32767),A=h&65535,!(A<<16>>16>=l<<16>>16);){s=h<<16>>16,h=g(s,s)|0,(h|0)==1073741824?(a[N>>2]=1,c=2147483647):c=h<<1,O0(c,s0,e0,N),h=(y1(A,m,N)|0)<<16>>16,h=g(h,h)|0,(h|0)==1073741824?(a[N>>2]=1,h=2147483647):h=h<<1,O0(h,n0,z1,N),A=e[A1>>1]|0,c=g(e[T1>>1]|0,s)|0;do if((c|0)==1073741824)a[N>>2]=1,c=2147483647;else{if(h=(c<<1)+U|0,!((c^U|0)>0&(h^U|0)<0)){c=h;break}a[N>>2]=1,c=z}while(!1);h=(g(A<<16>>16,s)|0)>>15,(h|0)>32767&&(a[N>>2]=1,h=32767),E1=h<<16,h=(E1>>15)+c|0,(E1>>16^c|0)>0&(h^c|0)<0&&(a[N>>2]=1,h=(c>>>31)+2147483647|0),n=e[x1>>1]|0,I=e[e0>>1]|0,s=e[j1>>1]|0,F=e[s0>>1]|0,c=g(F,s)|0;do if((c|0)==1073741824)a[N>>2]=1,A=2147483647;else{if(A=(c<<1)+h|0,!((c^h|0)>0&(A^h|0)<0))break;a[N>>2]=1,A=(h>>>31)+2147483647|0}while(!1);c=(g(I<<16>>16,s)|0)>>15,(c|0)>32767&&(a[N>>2]=1,c=32767),E1=c<<16,c=(E1>>15)+A|0,(E1>>16^A|0)>0&(c^A|0)<0&&(a[N>>2]=1,c=(A>>>31)+2147483647|0),h=(g(F,n<<16>>16)|0)>>15,(h|0)>32767&&(a[N>>2]=1,h=32767),E1=h<<16,h=(E1>>15)+c|0,(E1>>16^c|0)>0&(h^c|0)<0&&(a[N>>2]=1,h=(c>>>31)+2147483647|0),h=V5(h,Z1,N)|0,A=X1(e[Z1>>1]|0,1,N)|0,c=A<<16>>16,A<<16>>16>0?A=A<<16>>16<31?h>>c:0:(E1=0-c<<16>>16,A=h<<E1,A=(A>>E1|0)==(h|0)?A:h>>31^2147483647),h=A-T|0,((h^A)&(A^T)|0)<0&&(a[N>>2]=1,h=(A>>>31)+2147483647|0),h=(S1(h,N)|0)<<16>>16,h=g(h,h)|0,(h|0)==1073741824?(a[N>>2]=1,A=2147483647):A=h<<1,F=e[K1>>1]|0,s=e[z1>>1]|0,I=e[L1>>1]|0,n=e[n0>>1]|0,c=g(n,I)|0;do if((c|0)==1073741824)a[N>>2]=1,h=2147483647;else{if(h=(c<<1)+A|0,!((c^A|0)>0&(h^A|0)<0))break;a[N>>2]=1,h=(A>>>31)+2147483647|0}while(!1);if(c=(g(s<<16>>16,I)|0)>>15,(c|0)>32767&&(a[N>>2]=1,c=32767),E1=c<<16,c=(E1>>15)+h|0,(E1>>16^h|0)>0&(c^h|0)<0&&(a[N>>2]=1,c=(h>>>31)+2147483647|0),h=(g(n,F<<16>>16)|0)>>15,(h|0)>32767&&(a[N>>2]=1,h=32767),E1=h<<16,h=(E1>>15)+c|0,(E1>>16^c|0)>0&(h^c|0)<0&&(a[N>>2]=1,h=(c>>>31)+2147483647|0),c=(h|0)<(M|0),O=c?B:O,B=B+1<<16>>16,B<<16>>16>=32)break;M=c?h:M}e0=(O<<16>>16)*3|0,A=e[c1+(e0<<1)>>1]|0,e[E>>1]=e[c1+(e0+1<<1)>>1]|0,e[R>>1]=e[c1+(e0+2<<1)>>1]|0,A=g(A<<16>>16,U1)|0,(A|0)==1073741824?(a[N>>2]=1,A=2147483647):A=A<<1,i1?A=o1<<16>>16<31?A>>W:0:(E=0-W<<16>>16,N=A<<E,A=(N>>E|0)==(A|0)?N:A>>31^2147483647),e[b>>1]=A>>>16,N=O,E=a[S>>2]|0,b=E+2|0,e[E>>1]=u1,E=E+4|0,a[S>>2]=E,e[b>>1]=N,C=q1}function ai(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0;for(w=(i|0)==7,u=e[o>>1]|0,w?(u=u<<16>>16>>>1&65535,m=x0(r,t,l)|0,r=m<<16,i=r>>16,(m<<20>>20|0)==(i|0)?i=r>>12:i=i>>>15^32767):(m=x0(r,t,l)|0,r=m<<16,i=r>>16,(m<<21>>21|0)==(i|0)?i=r>>11:i=i>>>15^32767),m=i<<16>>16,l=u<<16>>16,r=l-((g(m,e[f>>1]|0)|0)>>>15&65535)|0,r=(r&32768|0?0-r|0:r)&65535,c=1,i=0,d=f;d=d+6|0,u=l-((g(e[d>>1]|0,m)|0)>>>15&65535)|0,t=u<<16,u=(t|0)<0?0-(t>>16)|0:u,t=(u<<16>>16|0)<(r<<16>>16|0),i=t?c:i,c=c+1<<16>>16,!(c<<16>>16>=32);)r=t?u&65535:r;return d=(i<<16>>16)*196608>>16,e[o>>1]=(g(e[f+(d<<1)>>1]|0,m)|0)>>>15<<(w&1),e[n>>1]=e[f+(d+1<<1)>>1]|0,e[s>>1]=e[f+(d+2<<1)>>1]|0,i|0}function Z6(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0;for(l=y1(e[t>>1]|0,e[s>>1]|0,f)|0,l=(l&65535)-((l&65535)>>>15&65535)|0,l=(l<<16>>31^l)&65535,c=0,d=1;u=e[s+(d<<1)>>1]|0,u<<16>>16>r<<16>>16?u=l:(u=y1(e[t>>1]|0,u,f)|0,u=(u&65535)-((u&65535)>>>15&65535)|0,u=(u<<16>>31^u)&65535,w=u<<16>>16<l<<16>>16,u=w?u:l,c=w?d&65535:c),d=d+1|0,(d|0)!=16;)l=u;if((i|0)!=5)return l=e[s+(c<<16>>16<<1)>>1]|0,(i|0)==7?(e[t>>1]=l&65532,c|0):(e[t>>1]=l,c|0);switch(u=c<<16>>16,c<<16>>16){case 0:{l=0;break}case 15:{m=8;break}default:(e[s+(u+1<<1)>>1]|0)>r<<16>>16?m=8:l=u+65535&65535}return(m|0)==8&&(l=u+65534&65535),e[n>>1]=l,w=l<<16>>16,e[o>>1]=e[s+(w<<1)>>1]|0,w=w+1|0,e[n+2>>1]=w,w=w<<16>>16,e[o+2>>1]=e[s+(w<<1)>>1]|0,w=w+1|0,e[n+4>>1]=w,e[o+4>>1]=e[s+(w<<16>>16<<1)>>1]|0,e[t>>1]=e[s+(u<<1)>>1]|0,c|0}function fi(i,r,t,o,n,s,f,l,u,c,d,m){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0;var w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0;switch(U=C,C=C+32|0,k=U+20|0,b=U+10|0,h=U,i|0){case 3:case 4:case 6:{d=d+84|0,H=128;break}default:d=d+80|0,H=64}for(K=a[d>>2]|0,w=x0(14,t,m)|0,Y=r<<16>>16,j=Y+65525|0,i=(D[n>>1]|0)+65523&65535,e[h>>1]=i,r=(D[n+2>>1]|0)+65522&65535,e[h+2>>1]=r,z=j<<16>>16,z=n1(e[n+4>>1]|0,((j<<17>>17|0)==(z|0)?j<<1:z>>>15^32767)+15&65535,m)|0,e[h+4>>1]=z,j=n1(e[n+6>>1]|0,j&65535,m)|0,e[h+6>>1]=j,n=n1(e[n+8>>1]|0,Y+65526&65535,m)|0,e[h+8>>1]=n,r=r<<16>>16>i<<16>>16?r:i,r=z<<16>>16>r<<16>>16?z:r,r=j<<16>>16>r<<16>>16?j:r,r=(n<<16>>16>r<<16>>16?n:r)+1&65535,n=0;t=r-(i&65535)|0,d=t&65535,i=D[o+(n<<1)>>1]<<16,t=t<<16>>16,d<<16>>16>0?d=d<<16>>16<31?i>>t:0:(j=0-t<<16>>16,d=i<<j,d=(d>>j|0)==(i|0)?d:i>>31^2147483647),O0(d,k+(n<<1)|0,b+(n<<1)|0,m),d=n+1|0,(d|0)!=5;)i=e[h+(d<<1)>>1]|0,n=d;for(j=w<<16>>16,N=e[k>>1]|0,A=e[b>>1]|0,I=e[k+2>>1]|0,F=e[b+2>>1]|0,B=e[k+4>>1]|0,M=e[b+4>>1]|0,T=e[k+6>>1]|0,z=e[b+6>>1]|0,O=e[k+8>>1]|0,E=e[b+8>>1]|0,r=2147483647,R=0,d=0,S=K;n=e[S>>1]|0,n<<16>>16>s<<16>>16?w=r:(w=(g(e[S+2>>1]|0,j)|0)>>15,(w|0)>32767&&(a[m>>2]=1,w=32767),b=n<<16>>16,n=g(b,b)|0,n>>>0>1073741823?(a[m>>2]=1,h=32767):h=n>>>15,t=w<<16>>16,w=g(t,t)|0,w>>>0>1073741823?(a[m>>2]=1,k=32767):k=w>>>15,o=(g(t,b)|0)>>15,(o|0)>32767&&(a[m>>2]=1,o=32767),w=h<<16>>16,h=g(N,w)|0,(h|0)==1073741824?(a[m>>2]=1,n=2147483647):n=h<<1,w=(g(A,w)|0)>>15,h=n+(w<<1)|0,(n^w|0)>0&(h^n|0)<0&&(a[m>>2]=1,h=(n>>>31)+2147483647|0),w=g(I,b)|0,(w|0)==1073741824?(a[m>>2]=1,n=2147483647):n=w<<1,b=(g(F,b)|0)>>15,w=n+(b<<1)|0,(n^b|0)>0&(w^n|0)<0&&(a[m>>2]=1,w=(n>>>31)+2147483647|0),n=w+h|0,(w^h|0)>-1&(n^h|0)<0&&(a[m>>2]=1,n=(h>>>31)+2147483647|0),w=k<<16>>16,h=g(B,w)|0,(h|0)==1073741824?(a[m>>2]=1,i=2147483647):i=h<<1,b=(g(M,w)|0)>>15,h=i+(b<<1)|0,(i^b|0)>0&(h^i|0)<0&&(a[m>>2]=1,h=(i>>>31)+2147483647|0),w=h+n|0,(h^n|0)>-1&(w^n|0)<0?(a[m>>2]=1,i=(n>>>31)+2147483647|0):i=w,w=g(T,t)|0,(w|0)==1073741824?(a[m>>2]=1,h=2147483647):h=w<<1,b=(g(z,t)|0)>>15,w=h+(b<<1)|0,(h^b|0)>0&(w^h|0)<0&&(a[m>>2]=1,w=(h>>>31)+2147483647|0),n=w+i|0,(w^i|0)>-1&(n^i|0)<0?(a[m>>2]=1,h=(i>>>31)+2147483647|0):h=n,n=o<<16>>16,w=g(O,n)|0,(w|0)==1073741824?(a[m>>2]=1,i=2147483647):i=w<<1,b=(g(E,n)|0)>>15,w=i+(b<<1)|0,(i^b|0)>0&(w^i|0)<0?(a[m>>2]=1,n=(i>>>31)+2147483647|0):n=w,w=n+h|0,(n^h|0)>-1&(w^h|0)<0&&(a[m>>2]=1,w=(h>>>31)+2147483647|0),b=(w|0)<(r|0),w=b?w:r,d=b?R:d),S=S+8|0,R=R+1<<16>>16,!((R<<16>>16|0)>=(H|0));)r=w;return s=d<<16>>16,s=((s<<18>>18|0)==(s|0)?s<<2:s>>>15^32767)<<16>>16,e[f>>1]=e[K+(s<<1)>>1]|0,r=e[K+(s+1<<1)>>1]|0,e[u>>1]=e[K+(s+2<<1)>>1]|0,e[c>>1]=e[K+(s+3<<1)>>1]|0,r=g(r<<16>>16,j)|0,(r|0)==1073741824?(a[m>>2]=1,i=2147483647):i=r<<1,t=10-Y|0,r=t&65535,t=t<<16>>16,r<<16>>16>0?(m=r<<16>>16<31?i>>t:0,m=m>>>16,m=m&65535,e[l>>1]=m,C=U,d|0):(u=0-t<<16>>16,m=i<<u,m=(m>>u|0)==(i|0)?m:i>>31^2147483647,m=m>>>16,m=m&65535,e[l>>1]=m,C=U,d|0)}function Q6(i,r,t,o,n,s,f,l,u){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0;var c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0,E1=0,T1=0,A1=0,j1=0,x1=0,L1=0,K1=0,U1=0,n0=0,z1=0,Z1=0,s0=0,e0=0,q1=0,h0=0,j0=0,Y0=0,k0=0,l0=0,a0=0,O1=0,w0=0,b2=0,v2=0,C0=0,a2=0;if(a2=C,C=C+160|0,C0=a2,d=i<<16>>16,b2=i<<16>>16==10,v2=e[f+(e[s>>1]<<1)>>1]|0,i<<16>>16>0)for(u=0,c=l;e[c>>1]=u,u=u+1<<16>>16,!(u<<16>>16>=i<<16>>16);)c=c+2|0;if(t<<16>>16<=1){C=a2;return}for(O1=s+2|0,w0=v2<<16>>16,k0=o+(w0<<1)|0,l0=n+(w0*80|0)+(w0<<1)|0,a0=s+6|0,V=r&65535,E1=s+4|0,T1=s+10|0,A1=s+8|0,j1=s+14|0,x1=s+12|0,L1=s+18|0,K1=s+16|0,U1=l+2|0,n0=l+4|0,z1=l+6|0,Z1=l+8|0,s0=l+10|0,e0=l+12|0,q1=l+14|0,h0=l+16|0,j0=l+18|0,Y0=i<<16>>16>2,m1=s+(d+-1<<1)|0,l1=1,Z=1,o1=0,a1=0,w1=-1;;){if(t1=e[f+(e[O1>>1]<<1)>>1]|0,c1=t1<<16>>16,r=(D[o+(c1<<1)>>1]|0)+(D[k0>>1]|0)|0,c=(e[n+(w0*80|0)+(c1<<1)>>1]<<13)+32768+((e[n+(c1*80|0)+(c1<<1)>>1]|0)+(e[l0>>1]|0)<<12)|0,d=e[a0>>1]|0,d<<16>>16<40){for(d=d<<16>>16,m=C0;u1=(e[n+(d*80|0)+(d<<1)>>1]|0)>>>1,W=e[n+(d*80|0)+(w0<<1)>>1]|0,d1=e[n+(d*80|0)+(c1<<1)>>1]|0,e[m>>1]=r+(D[o+(d<<1)>>1]|0),e[m+2>>1]=(W+2+u1+d1|0)>>>2,d=d+V|0,(d&65535)<<16>>16<40;)d=d<<16>>16,m=m+4|0;B=e[a0>>1]|0}else B=d;r=e[E1>>1]|0,F=c>>12,d=r<<16>>16;e:do if(r<<16>>16<40){if(I=B<<16>>16,B<<16>>16<40)m=1,h=r,b=B,k=0,w=-1;else for(;;)if(d=d+V|0,(d&65535)<<16>>16<40)d=d<<16>>16;else{m=1,d1=r,u1=B,d=0;break e}for(;;){for(A=((e[n+(d*80|0)+(d<<1)>>1]|0)+F>>1)+(e[n+(d*80|0)+(w0<<1)>>1]|0)+(e[n+(d*80|0)+(c1<<1)>>1]|0)|0,N=D[o+(d<<1)>>1]|0,S=I,O=B,R=C0,E=k;c=(D[R>>1]|0)+N|0,u=c<<16>>16,u=(g(u,u)|0)>>>15,k=(A+(e[n+(d*80|0)+(S<<1)>>1]|0)>>2)+(e[R+2>>1]|0)>>1,(g(u<<16>>16,m<<16>>16)|0)>(g(k,w<<16>>16)|0)?(m=k&65535,h=r,b=O,k=c&65535,w=u&65535):k=E,c=S+V|0,O=c&65535,!(O<<16>>16>=40);)S=c<<16>>16,R=R+4|0,E=k;if(d=d+V|0,r=d&65535,r<<16>>16<40)d=d<<16>>16;else{d1=h,u1=b,d=k;break}}}else m=1,d1=r,u1=B,d=0;while(!1);if(h=m<<16>>16<<15,m=e[T1>>1]|0,m<<16>>16<40){for(c=d1<<16>>16,u=u1<<16>>16,r=d&65535,m=m<<16>>16,d=C0;U=e[n+(m*80|0)+(m<<1)>>1]>>1,H=e[n+(m*80|0)+(w0<<1)>>1]|0,q=e[n+(m*80|0)+(c1<<1)>>1]|0,i1=e[n+(m*80|0)+(c<<1)>>1]|0,W=e[n+(m*80|0)+(u<<1)>>1]|0,e[d>>1]=(D[o+(m<<1)>>1]|0)+r,e[d+2>>1]=(H+2+U+q+i1+W|0)>>>2,m=m+V|0,(m&65535)<<16>>16<40;)m=m<<16>>16,d=d+4|0;U=e[T1>>1]|0}else U=m;w=e[A1>>1]|0,m=w<<16>>16;e:do if(w<<16>>16<40){if(M=d1<<16>>16,T=u1<<16>>16,z=U<<16>>16,B=h+32768|0,U<<16>>16<40)k=1,h=w,r=U,b=w,d=0,w=-1;else for(;;)if(m=m+V|0,(m&65535)<<16>>16<40)m=m<<16>>16;else{m=1,W=w,i1=U,d=0;break e}for(;;){for(u=D[o+(m<<1)>>1]|0,F=(e[n+(m*80|0)+(c1<<1)>>1]|0)+(e[n+(m*80|0)+(w0<<1)>>1]|0)+(e[n+(m*80|0)+(M<<1)>>1]|0)+(e[n+(m*80|0)+(T<<1)>>1]|0)|0,I=B+(e[n+(m*80|0)+(m<<1)>>1]<<11)|0,N=z,S=U,A=C0;;)if(E=(D[A>>1]|0)+u|0,c=I+(e[A+2>>1]<<14)+(F+(e[n+(m*80|0)+(N<<1)>>1]|0)<<12)|0,R=E<<16>>16,R=(g(R,R)|0)>>>15,(g(R<<16>>16,k<<16>>16)|0)>(g(c>>16,w<<16>>16)|0)?(k=c>>>16&65535,O=b,r=S,d=E&65535,w=R&65535):O=h,h=N+V|0,S=h&65535,S<<16>>16>=40){h=O;break}else N=h<<16>>16,h=O,A=A+4|0;if(m=m+V|0,b=m&65535,b<<16>>16<40)m=m<<16>>16;else{m=k,W=h,i1=r;break}}}else m=1,W=w,i1=U,d=0;while(!1);if(k=m<<16>>16<<15,m=e[j1>>1]|0,m<<16>>16<40){for(c=d1<<16>>16,u=u1<<16>>16,w=W<<16>>16,h=i1<<16>>16,r=d&65535,m=m<<16>>16,d=C0;j=e[n+(m*80|0)+(m<<1)>>1]>>1,z=e[n+(w0*80|0)+(m<<1)>>1]|0,Y=e[n+(c1*80|0)+(m<<1)>>1]|0,K=e[n+(c*80|0)+(m<<1)>>1]|0,H=e[n+(u*80|0)+(m<<1)>>1]|0,U=e[n+(w*80|0)+(m<<1)>>1]|0,q=e[n+(h*80|0)+(m<<1)>>1]|0,e[d>>1]=(D[o+(m<<1)>>1]|0)+r,e[d+2>>1]=(z+4+j+Y+K+H+U+q|0)>>>3,m=m+V|0,(m&65535)<<16>>16<40;)m=m<<16>>16,d=d+4|0;r=e[j1>>1]|0}else r=m;if(b=e[x1>>1]|0,b<<16>>16<40)for(U=d1<<16>>16,j=u1<<16>>16,z=W<<16>>16,T=i1<<16>>16,M=r<<16>>16,B=r<<16>>16<40,Y=k+32768|0,H=b<<16>>16,u=1,O=b,S=r,K=b,h=0,m=-1;;){if(B)for(k=D[o+(H<<1)>>1]|0,d=(e[n+(H*80|0)+(c1<<1)>>1]|0)+(e[n+(H*80|0)+(w0<<1)>>1]|0)+(e[n+(H*80|0)+(U<<1)>>1]|0)+(e[n+(H*80|0)+(j<<1)>>1]|0)+(e[n+(H*80|0)+(z<<1)>>1]|0)+(e[n+(H*80|0)+(T<<1)>>1]|0)|0,w=Y+(e[n+(H*80|0)+(H<<1)>>1]<<10)|0,R=M,b=r,I=S,F=C0;A=(D[F>>1]|0)+k|0,S=w+(e[F+2>>1]<<14)+(d+(e[n+(H*80|0)+(R<<1)>>1]|0)<<11)|0,N=A<<16>>16,N=(g(N,N)|0)>>>15,(g(N<<16>>16,u<<16>>16)|0)>(g(S>>16,m<<16>>16)|0)?(u=S>>>16&65535,O=K,S=b,h=A&65535,m=N&65535):S=I,E=R+V|0,b=E&65535,!(b<<16>>16>=40);)R=E<<16>>16,I=S,F=F+4|0;if(b=H+V|0,K=b&65535,K<<16>>16>=40){q=S;break}else H=b<<16>>16}else u=1,O=b,q=r,h=0,m=-1;if(b2){if(R=u<<16>>16<<15,m=e[L1>>1]|0,m<<16>>16<40){for(d=d1<<16>>16,r=u1<<16>>16,c=W<<16>>16,u=i1<<16>>16,k=O<<16>>16,b=q<<16>>16,w=h&65535,m=m<<16>>16,h=C0;z=e[n+(m*80|0)+(m<<1)>>1]>>1,T=e[n+(w0*80|0)+(m<<1)>>1]|0,j=e[n+(c1*80|0)+(m<<1)>>1]|0,Y=e[n+(d*80|0)+(m<<1)>>1]|0,K=e[n+(r*80|0)+(m<<1)>>1]|0,H=e[n+(c*80|0)+(m<<1)>>1]|0,U=e[n+(u*80|0)+(m<<1)>>1]|0,o1=e[n+(k*80|0)+(m<<1)>>1]|0,a1=e[n+(b*80|0)+(m<<1)>>1]|0,e[h>>1]=(D[o+(m<<1)>>1]|0)+w,e[h+2>>1]=(T+4+z+j+Y+K+H+U+o1+a1|0)>>>3,m=m+V|0,(m&65535)<<16>>16<40;)m=m<<16>>16,h=h+4|0;U=e[L1>>1]|0}else U=m;if(k=e[K1>>1]|0,k<<16>>16<40)for(z=d1<<16>>16,T=u1<<16>>16,M=W<<16>>16,c=i1<<16>>16,j=O<<16>>16,Y=q<<16>>16,K=U<<16>>16,H=U<<16>>16<40,B=R+32768|0,d=k<<16>>16,u=1,b=k,h=U,r=k,m=-1;;){if(H)for(R=D[o+(d<<1)>>1]|0,w=(e[n+(c1*80|0)+(d<<1)>>1]|0)+(e[n+(w0*80|0)+(d<<1)>>1]|0)+(e[n+(z*80|0)+(d<<1)>>1]|0)+(e[n+(T*80|0)+(d<<1)>>1]|0)+(e[n+(M*80|0)+(d<<1)>>1]|0)+(e[n+(c*80|0)+(d<<1)>>1]|0)+(e[n+(j*80|0)+(d<<1)>>1]|0)+(e[n+(Y*80|0)+(d<<1)>>1]|0)|0,k=B+(e[n+(d*80|0)+(d<<1)>>1]<<9)|0,F=K,N=U,I=C0;A=(D[I>>1]|0)+R<<16>>16,A=(g(A,A)|0)>>>15,S=k+(e[I+2>>1]<<13)+(w+(e[n+(d*80|0)+(F<<1)>>1]|0)<<10)|0,(g(A<<16>>16,u<<16>>16)|0)>(g(S>>16,m<<16>>16)|0)&&(u=S>>>16&65535,b=r,h=N,m=A&65535),E=F+V|0,N=E&65535,!(N<<16>>16>=40);)F=E<<16>>16,I=I+4|0;if(k=d+V|0,r=k&65535,r<<16>>16>=40)break;d=k<<16>>16}else u=1,b=k,h=U,m=-1}else b=o1,h=a1;if((g(m<<16>>16,l1<<16>>16)|0)>(g(u<<16>>16,w1<<16>>16)|0)?(e[l>>1]=v2,e[U1>>1]=t1,e[n0>>1]=d1,e[z1>>1]=u1,e[Z1>>1]=W,e[s0>>1]=i1,e[e0>>1]=O,e[q1>>1]=q,b2&&(e[h0>>1]=b,e[j0>>1]=h)):(u=l1,m=w1),d=e[O1>>1]|0,Y0)for(r=1,c=2;e[s+(r<<1)>>1]=e[s+(c<<1)>>1]|0,c=c+1|0,(c&65535)<<16>>16!=i<<16>>16;)r=r+1|0;if(e[m1>>1]=d,Z=Z+1<<16>>16,Z<<16>>16>=t<<16>>16)break;l1=u,o1=b,a1=h,w1=m}C=a2}function a5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0;for(l=39;f=i+(l<<1)|0,s=e[f>>1]|0,n=r+(l<<1)|0,s<<16>>16>-1?e[n>>1]=32767:(e[n>>1]=-32767,s<<16>>16==-32768?s=32767:s=0-(s&65535)&65535,e[f>>1]=s),e[t+(l<<1)>>1]=s,(l|0)>0;)l=l+-1|0;if(c=8-(o<<16>>16)|0,(c|0)>0)u=0,n=0;else return;do{for(o=0,i=0,f=32767;r=e[t+(o<<1)>>1]|0,l=r<<16>>16>-1?r<<16>>16<f<<16>>16:0,n=l?i:n,s=o+5|0,i=s&65535,!(i<<16>>16>=40);)o=s<<16>>16,f=l?r:f;e[t+(n<<16>>16<<1)>>1]=-1,u=u+1<<16>>16}while((u<<16>>16|0)<(c|0));u=0;do{for(r=1,i=1,s=32767;o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<s<<16>>16:0,n=l?i:n,f=r+5|0,i=f&65535,!(i<<16>>16>=40);)r=f<<16>>16,s=l?o:s;e[t+(n<<16>>16<<1)>>1]=-1,u=u+1<<16>>16}while((u<<16>>16|0)<(c|0));u=0;do{for(r=2,i=2,s=32767;o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<s<<16>>16:0,n=l?i:n,f=r+5|0,i=f&65535,!(i<<16>>16>=40);)r=f<<16>>16,s=l?o:s;e[t+(n<<16>>16<<1)>>1]=-1,u=u+1<<16>>16}while((u<<16>>16|0)<(c|0));for(u=0;;){for(r=3,i=3,s=32767;;)if(o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<s<<16>>16:0,n=l?i:n,f=r+5|0,i=f&65535,i<<16>>16>=40){s=n;break}else r=f<<16>>16,s=l?o:s;if(e[t+(s<<16>>16<<1)>>1]=-1,u=u+1<<16>>16,(u<<16>>16|0)>=(c|0)){n=0;break}else n=s}do{for(r=4,i=4,u=32767;o=e[t+(r<<1)>>1]|0,l=o<<16>>16>-1?o<<16>>16<u<<16>>16:0,s=l?i:s,f=r+5|0,i=f&65535,!(i<<16>>16>=40);)r=f<<16>>16,u=l?o:u;e[t+(s<<16>>16<<1)>>1]=-1,n=n+1<<16>>16}while((n<<16>>16|0)<(c|0))}function $6(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0;for(A=C,C=C+80|0,N=A,w=40,h=r,k=i,c=256,d=256;u=e[h>>1]|0,h=h+2|0,u=g(u,u)|0,(u|0)!=1073741824?(m=(u<<1)+c|0,(u^c|0)>0&(m^c|0)<0?(a[l>>2]=1,c=(c>>>31)+2147483647|0):c=m):(a[l>>2]=1,c=2147483647),O=e[k>>1]|0,d=(g(O<<1,O)|0)+d|0,w=w+-1<<16>>16,w<<16>>16;)k=k+2|0;for(O=e2(c,l)|0,R=O<<5,O=((R>>5|0)==(O|0)?R:O>>31^2147418112)>>16,R=(e2(d,l)|0)<<5>>16,S=39,b=r+78|0,E=N+78|0,u=t+78|0;k=g(e[b>>1]|0,O)|0,b=b+-2|0,h=k<<1,r=i+(S<<1)|0,c=e[r>>1]|0,w=g(c<<16>>16,R)|0,(w|0)!=1073741824?(m=(w<<1)+h|0,(w^h|0)>0&(m^h|0)<0&&(a[l>>2]=1,m=(k>>>30&1)+2147483647|0)):(a[l>>2]=1,m=2147483647),d=m<<10,d=S1((d>>10|0)==(m|0)?d:m>>31^2147483647,l)|0,d<<16>>16>-1?e[u>>1]=32767:(e[u>>1]=-32767,d<<16>>16==-32768?d=32767:d=0-(d&65535)&65535,c<<16>>16==-32768?m=32767:m=0-(c&65535)&65535,e[r>>1]=m),u=u+-2|0,e[E>>1]=d,!((S|0)<=0);)S=S+-1|0,E=E+-2|0;if(r=n<<16>>16,n<<16>>16<=0){e[s+(r<<1)>>1]=e[s>>1]|0,C=A;return}for(k=f&65535,h=0,w=-1,u=0;;){if((h|0)<40)for(d=h,m=h&65535,c=-1;l=e[N+(d<<1)>>1]|0,f=l<<16>>16>c<<16>>16,c=f?l:c,u=f?m:u,d=d+k|0,m=d&65535,!(m<<16>>16>=40);)d=d<<16>>16;else c=-1;if(e[o+(h<<1)>>1]=u,c<<16>>16>w<<16>>16?e[s>>1]=h:c=w,h=h+1|0,(h&65535)<<16>>16==n<<16>>16)break;w=c}if(u=e[s>>1]|0,e[s+(r<<1)>>1]=u,n<<16>>16>1)c=1;else{C=A;return}do o=u+1<<16>>16,u=o<<16>>16>=n<<16>>16?0:o,e[s+(c<<1)>>1]=u,e[s+(c+r<<1)>>1]=u,c=c+1|0;while((c&65535)<<16>>16!=n<<16>>16);C=A}function li(i){i=i|0;var r=0;return!i||(a[i>>2]=0,r=o0(12)|0,!r)?(i=-1,i|0):(e[r>>1]=8,a[i>>2]=r,e[r+2>>1]=3,e[r+4>>1]=0,a[r+8>>2]=0,i=0,i|0)}function ui(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function J6(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;do if((r|0)==8){if(o=i+2|0,n=(e[o>>1]|0)+-1<<16>>16,e[o>>1]=n,r=i+8|0,!(a[r>>2]|0)){a[t>>2]=1,e[o>>1]=3;break}if(s=i+4|0,n<<16>>16>2&(e[s>>1]|0)>0){a[t>>2]=2,e[s>>1]=(e[s>>1]|0)+-1<<16>>16;break}if(n<<16>>16){a[t>>2]=3;break}else{a[t>>2]=2,e[o>>1]=e[i>>1]|0;break}}else e[i+2>>1]=e[i>>1]|0,a[t>>2]=0,r=i+8|0;while(!1);a[r>>2]=a[t>>2]}function ci(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;return!i||(a[i>>2]=0,t=o0(12)|0,o=t,!t)?(i=-1,i|0):(a[t>>2]=0,n=t+4|0,a[n>>2]=0,s=t+8|0,a[s>>2]=r,!((ei(t)|0)<<16>>16)&&!((Ee(n,a[s>>2]|0)|0)<<16>>16)?(ii(a[t>>2]|0)|0,x6(a[n>>2]|0)|0,a[i>>2]=o,i=0,i|0):(G6(t),N5(n),G1(t),i=-1,i|0))}function di(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G6(r),N5((a[i>>2]|0)+4|0),G1(a[i>>2]|0),a[i>>2]=0))}function e4(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0;u=C,C=C+448|0,f=u+320|0,l=u,t2(o|0,0,488)|0,s=0;do c=t+(s<<1)|0,e[c>>1]=(D[c>>1]|0)&65528,s=s+1|0;while((s|0)!=160);ti(a[i>>2]|0,t,160),c=i+4|0,ye(a[c>>2]|0,r,t,f,n,l)|0,ri(a[n>>2]|0,f,o,(a[c>>2]|0)+2392|0),C=u}function B5(i,r,t,o,n,s,f,l,u,c,d,m,w,h,k,b){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0;var E=0,R=0,S=0;S=C,C=C+48|0,E=S+22|0,R=S,X0(n,(i&-2|0)==6?t:r,E),X0(n,o,R),t=d,r=E,n=t+22|0;do e[t>>1]=e[r>>1]|0,t=t+2|0,r=r+2|0;while((t|0)<(n|0));f0(s,d,w,40,c,0),f0(R,w,w,40,c,0),N2(s,f,k,40),t=m,r=k,n=t+80|0;do e[t>>1]=e[r>>1]|0,t=t+2|0,r=r+2|0;while((t|0)<(n|0));f0(s,m,b,40,l,0),N2(E,b,h,40),f0(R,h,h,40,u,0),C=S}function f5(i,r,t,o,n,s,f,l,u,c,d,m,w,h,k,b,E){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0,u=u|0,c=c|0,d=d|0,m=m|0,w=w|0,h=h|0,k=k|0,b=b|0,E=E|0;var R=0,S=0,O=0,N=0,A=0;for((r|0)==7?(O=11,r=o<<16>>16>>>1&65535,R=2):(O=13,r=o,R=1),e[b>>1]=o<<16>>16<13017?o:13017,S=t<<16>>16,k=k+(S<<1)|0,b=r<<16>>16,n=n<<16>>16,t=20,r=u,E=k;u=E+2|0,A=g(e[E>>1]|0,b)|0,N=g(e[u>>1]|0,b)|0,A=(g(e[r>>1]|0,n)|0)+A<<1,N=(g(e[r+2>>1]|0,n)|0)+N<<1<<R,e[E>>1]=((A<<R)+32768|0)>>>16,e[u>>1]=(N+32768|0)>>>16,t=t+-1<<16>>16,t<<16>>16;)r=r+4|0,E=E+4|0;for(r=o<<16>>16,f0(s,k,f+(S<<1)|0,40,m,1),t=30,E=0;N=t+S|0,e[w+(E<<1)>>1]=(D[i+(N<<1)>>1]|0)-(D[f+(N<<1)>>1]|0),N=g(e[c+(t<<1)>>1]|0,r)|0,A=(g(e[d+(t<<1)>>1]|0,n)|0)>>O,e[h+(E<<1)>>1]=(D[l+(t<<1)>>1]|0)-(N>>>14)-A,E=E+1|0,(E|0)!=10;)t=t+1|0}function hi(i){i=i|0;var r=0;return!i||(a[i>>2]=0,r=o0(16)|0,!r)?(i=-1,i|0):(e[r>>1]=0,e[r+2>>1]=0,e[r+4>>1]=0,e[r+6>>1]=0,e[r+8>>1]=0,e[r+10>>1]=0,e[r+12>>1]=0,e[r+14>>1]=0,a[i>>2]=r,i=0,i|0)}function wi(i){return i=i|0,i?(e[i>>1]=0,e[i+2>>1]=0,e[i+4>>1]=0,e[i+6>>1]=0,e[i+8>>1]=0,e[i+10>>1]=0,e[i+12>>1]=0,e[i+14>>1]=0,i=0,i|0):(i=-1,i|0)}function mi(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function pi(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0;return o=D[r+6>>1]|0,t=D[r+8>>1]|0,n=o-t|0,n=(n&65535|0)!=32767?n&65535:32767,s=D[r+10>>1]|0,t=t-s|0,n=(t<<16>>16|0)<(n<<16>>16|0)?t&65535:n,t=D[r+12>>1]|0,s=s-t|0,n=(s<<16>>16|0)<(n<<16>>16|0)?s&65535:n,s=D[r+14>>1]|0,t=t-s|0,n=(t<<16>>16|0)<(n<<16>>16|0)?t&65535:n,s=s-(D[r+16>>1]|0)|0,t=e[r+2>>1]|0,f=D[r+4>>1]|0,r=(t&65535)-f|0,r=(r&65535|0)!=32767?r&65535:32767,o=f-o|0,((s<<16>>16|0)<(n<<16>>16|0)?s&65535:n)<<16>>16<1500||(((o<<16>>16|0)<(r<<16>>16|0)?o&65535:r)<<16>>16|0)<((t<<16>>16>32e3?600:t<<16>>16>30500?800:1100)|0)?(s=(e[i>>1]|0)+1<<16>>16,f=s<<16>>16>11,e[i>>1]=f?12:s,f&1|0):(e[i>>1]=0,0)}function ki(i,r,t){return i=i|0,r=r|0,t=t|0,r=X1(r,3,t)|0,r=n1(r,e[i+2>>1]|0,t)|0,r=n1(r,e[i+4>>1]|0,t)|0,r=n1(r,e[i+6>>1]|0,t)|0,r=n1(r,e[i+8>>1]|0,t)|0,r=n1(r,e[i+10>>1]|0,t)|0,r=n1(r,e[i+12>>1]|0,t)|0,(n1(r,e[i+14>>1]|0,t)|0)<<16>>16>15565|0}function bi(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;t=i+4|0,e[i+2>>1]=e[t>>1]|0,o=i+6|0,e[t>>1]=e[o>>1]|0,t=i+8|0,e[o>>1]=e[t>>1]|0,o=i+10|0,e[t>>1]=e[o>>1]|0,t=i+12|0,e[o>>1]=e[t>>1]|0,i=i+14|0,e[t>>1]=e[i>>1]|0,e[i>>1]=r<<16>>16>>>3}function vi(i){i=i|0;var r=0,t=0,o=0;if(!i||(a[i>>2]=0,r=o0(128)|0,!r))return o=-1,o|0;t=r+72|0,o=t+46|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return e[r>>1]=150,e[r+36>>1]=150,e[r+18>>1]=150,e[r+54>>1]=0,e[r+2>>1]=150,e[r+38>>1]=150,e[r+20>>1]=150,e[r+56>>1]=0,e[r+4>>1]=150,e[r+40>>1]=150,e[r+22>>1]=150,e[r+58>>1]=0,e[r+6>>1]=150,e[r+42>>1]=150,e[r+24>>1]=150,e[r+60>>1]=0,e[r+8>>1]=150,e[r+44>>1]=150,e[r+26>>1]=150,e[r+62>>1]=0,e[r+10>>1]=150,e[r+46>>1]=150,e[r+28>>1]=150,e[r+64>>1]=0,e[r+12>>1]=150,e[r+48>>1]=150,e[r+30>>1]=150,e[r+66>>1]=0,e[r+14>>1]=150,e[r+50>>1]=150,e[r+32>>1]=150,e[r+68>>1]=0,e[r+16>>1]=150,e[r+52>>1]=150,e[r+34>>1]=150,e[r+70>>1]=0,e[r+118>>1]=13106,e[r+120>>1]=0,e[r+122>>1]=0,e[r+124>>1]=0,e[r+126>>1]=13106,a[i>>2]=r,o=0,o|0}function Ei(i){i=i|0;var r=0,t=0;if(!i)return t=-1,t|0;r=i+72|0,t=r+46|0;do e[r>>1]=0,r=r+2|0;while((r|0)<(t|0));return e[i>>1]=150,e[i+36>>1]=150,e[i+18>>1]=150,e[i+54>>1]=0,e[i+2>>1]=150,e[i+38>>1]=150,e[i+20>>1]=150,e[i+56>>1]=0,e[i+4>>1]=150,e[i+40>>1]=150,e[i+22>>1]=150,e[i+58>>1]=0,e[i+6>>1]=150,e[i+42>>1]=150,e[i+24>>1]=150,e[i+60>>1]=0,e[i+8>>1]=150,e[i+44>>1]=150,e[i+26>>1]=150,e[i+62>>1]=0,e[i+10>>1]=150,e[i+46>>1]=150,e[i+28>>1]=150,e[i+64>>1]=0,e[i+12>>1]=150,e[i+48>>1]=150,e[i+30>>1]=150,e[i+66>>1]=0,e[i+14>>1]=150,e[i+50>>1]=150,e[i+32>>1]=150,e[i+68>>1]=0,e[i+16>>1]=150,e[i+52>>1]=150,e[i+34>>1]=150,e[i+70>>1]=0,e[i+118>>1]=13106,e[i+120>>1]=0,e[i+122>>1]=0,e[i+124>>1]=0,e[i+126>>1]=13106,t=0,t|0}function yi(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function i4(i,r){i=i|0,r=r|0,e[i+118>>1]=r}function t4(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0;t=S1(t,o)|0,!(t<<16>>16<=0)&&(t=t<<16>>16,(t*21298|0)==1073741824?(a[o>>2]=1,n=2147483647):n=t*42596|0,t=r-n|0,((t^r)&(n^r)|0)<0&&(a[o>>2]=1,t=(r>>>31)+2147483647|0),!((t|0)<=0)&&(i=i+104|0,e[i>>1]=D[i>>1]|0|16384))}function x5(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;i=i+104|0,o=X1(e[i>>1]|0,1,t)|0,e[i>>1]=o,r<<16>>16&&(e[i>>1]=(X1(o,1,t)|0)&65535|8192)}function gi(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;if(n=i+112|0,o=y1(e[n>>1]|0,e[r>>1]|0,t)|0,o=(o&65535)-((o&65535)>>>15&65535)|0,o=((o<<16>>31^o)&65535)<<16>>16<4,s=e[r>>1]|0,e[n>>1]=s,r=r+2|0,s=y1(s,e[r>>1]|0,t)|0,s=(s&65535)-((s&65535)>>>15&65535)|0,o=((s<<16>>31^s)&65535)<<16>>16<4?o?2:1:o&1,e[n>>1]=e[r>>1]|0,n=i+102|0,e[n>>1]=X1(e[n>>1]|0,1,t)|0,r=i+110|0,(n1(e[r>>1]|0,o,t)|0)<<16>>16<=3){e[r>>1]=o;return}e[n>>1]=D[n>>1]|0|16384,e[r>>1]=o}function _i(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0;M=C,C=C+352|0,c=M+24|0,F=M,f=0,n=0;do o=e[r+(f+-40<<1)>>1]|0,o=g(o,o)|0,(o|0)!=1073741824?(s=(o<<1)+n|0,(o^n|0)>0&(s^n|0)<0?(a[t>>2]=1,n=(n>>>31)+2147483647|0):n=s):(a[t>>2]=1,n=2147483647),f=f+1|0;while((f|0)!=160);d=n,(343039-d&d|0)<0?(a[t>>2]=1,n=(d>>>31)+2147483647|0):n=d+-343040|0,(n|0)<0&&(I=i+102|0,e[I>>1]=D[I>>1]&16383),u=d+-15e3|0,m=(14999-d&d|0)<0,m?(a[t>>2]=1,s=(d>>>31)+2147483647|0):s=u,(s|0)<0&&(I=i+108|0,e[I>>1]=D[I>>1]&16383),o=i+72|0,l=i+74|0,s=e[o>>1]|0,f=e[l>>1]|0,n=0;do I=n<<2,N=y1((e[r+(I<<1)>>1]|0)>>>2&65535,((s<<16>>16)*21955|0)>>>15&65535,t)|0,R=((N<<16>>16)*21955|0)>>>15&65535,E=n1(s,R,t)|0,O=I|1,A=y1((e[r+(O<<1)>>1]|0)>>>2&65535,((f<<16>>16)*6390|0)>>>15&65535,t)|0,S=((A<<16>>16)*6390|0)>>>15&65535,s=n1(f,S,t)|0,e[c+(I<<1)>>1]=n1(E,s,t)|0,e[c+(O<<1)>>1]=y1(E,s,t)|0,O=I|2,s=y1((e[r+(O<<1)>>1]|0)>>>2&65535,R,t)|0,N=n1(N,((s<<16>>16)*21955|0)>>>15&65535,t)|0,I=I|3,f=y1((e[r+(I<<1)>>1]|0)>>>2&65535,S,t)|0,A=n1(A,((f<<16>>16)*6390|0)>>>15&65535,t)|0,e[c+(O<<1)>>1]=n1(N,A,t)|0,e[c+(I<<1)>>1]=y1(N,A,t)|0,n=n+1|0;while((n|0)!=40);e[o>>1]=s,e[l>>1]=f,f=i+76|0,s=i+80|0,n=0;do I=n<<2,r4(c+(I<<1)|0,c+((I|2)<<1)|0,f,t),r4(c+((I|1)<<1)|0,c+((I|3)<<1)|0,s,t),n=n+1|0;while((n|0)!=40);f=i+84|0,s=i+86|0,n=i+92|0,o=0;do I=o<<3,j2(c+(I<<1)|0,c+((I|4)<<1)|0,f,t),j2(c+((I|2)<<1)|0,c+((I|6)<<1)|0,s,t),j2(c+((I|3)<<1)|0,c+((I|7)<<1)|0,n,t),o=o+1|0;while((o|0)!=20);f=i+88|0,s=i+90|0,n=0;do I=n<<4,j2(c+(I<<1)|0,c+((I|8)<<1)|0,f,t),j2(c+((I|4)<<1)|0,c+((I|12)<<1)|0,s,t),n=n+1|0;while((n|0)!=10);b=J0(c,i+70|0,32,40,4,1,15,t)|0,e[F+16>>1]=b,E=J0(c,i+68|0,16,20,8,7,16,t)|0,e[F+14>>1]=E,R=J0(c,i+66|0,16,20,8,3,16,t)|0,e[F+12>>1]=R,S=J0(c,i+64|0,16,20,8,2,16,t)|0,e[F+10>>1]=S,O=J0(c,i+62|0,16,20,8,6,16,t)|0,e[F+8>>1]=O,N=J0(c,i+60|0,8,10,16,4,16,t)|0,e[F+6>>1]=N,A=J0(c,i+58|0,8,10,16,12,16,t)|0,e[F+4>>1]=A,I=J0(c,i+56|0,8,10,16,8,16,t)|0,e[F+2>>1]=I,k=J0(c,i+54|0,8,10,16,0,16,t)|0,e[F>>1]=k,f=0,o=0;do s=i+(o<<1)|0,r=P2(e[s>>1]|0)|0,s=e[s>>1]|0,n=r<<16>>16,r<<16>>16<0?(l=0-n<<16,(l|0)<983040?l=s<<16>>16>>(l>>16)&65535:l=0):(l=s<<16>>16,s=l<<n,(s<<16>>16>>n|0)==(l|0)?l=s&65535:l=(l>>>15^32767)&65535),s=r0(X1(e[F+(o<<1)>>1]|0,1,t)|0,l)|0,h=y1(r,5,t)|0,n=h<<16>>16,h<<16>>16<0?(l=0-n<<16,(l|0)<983040?l=s<<16>>16>>(l>>16):l=0):(s=s<<16>>16,l=s<<n,(l<<16>>16>>n|0)!=(s|0)&&(l=s>>>15^32767)),l=l<<16>>16,l=g(l,l)|0,(l|0)!=1073741824?(s=(l<<1)+f|0,(l^f|0)>0&(s^f|0)<0?(a[t>>2]=1,f=(f>>>31)+2147483647|0):f=s):(a[t>>2]=1,f=2147483647),o=o+1|0;while((o|0)!=9);h=f<<6,f=(((h>>6|0)==(f|0)?h:f>>31^2147418112)>>16)*3641>>15,(f|0)>32767&&(a[t>>2]=1,f=32767),h=e[i>>1]|0,l=h<<16>>16,w=e[i+2>>1]|0,s=(w<<16>>16)+l|0,(w^h)<<16>>16>-1&(s^l|0)<0&&(a[t>>2]=1,s=(l>>>31)+2147483647|0),h=e[i+4>>1]|0,l=h+s|0,(h^s|0)>-1&(l^s|0)<0&&(a[t>>2]=1,l=(s>>>31)+2147483647|0),h=e[i+6>>1]|0,s=h+l|0,(h^l|0)>-1&(s^l|0)<0&&(a[t>>2]=1,s=(l>>>31)+2147483647|0),h=e[i+8>>1]|0,l=h+s|0,(h^s|0)>-1&(l^s|0)<0&&(a[t>>2]=1,l=(s>>>31)+2147483647|0),h=e[i+10>>1]|0,s=h+l|0,(h^l|0)>-1&(s^l|0)<0&&(a[t>>2]=1,s=(l>>>31)+2147483647|0),h=e[i+12>>1]|0,l=h+s|0,(h^s|0)>-1&(l^s|0)<0&&(a[t>>2]=1,l=(s>>>31)+2147483647|0),h=e[i+14>>1]|0,s=h+l|0,(h^l|0)>-1&(s^l|0)<0&&(a[t>>2]=1,s=(l>>>31)+2147483647|0),h=e[i+16>>1]|0,l=h+s|0,(h^s|0)>-1&(l^s|0)<0&&(a[t>>2]=1,l=(s>>>31)+2147483647|0),w=l<<13,w=((w>>13|0)==(l|0)?w:l>>31^2147418112)>>>16&65535,l=(g((y1(w,0,t)|0)<<16>>16,-2808)|0)>>15,(l|0)>32767&&(a[t>>2]=1,l=32767),c=n1(l&65535,1260,t)|0,h=i+100|0,l=X1(e[h>>1]|0,1,t)|0,(f<<16>>16|0)>((c<<16>>16<720?720:c<<16>>16)|0)&&(l=(l&65535|16384)&65535),e[h>>1]=l,m&&(a[t>>2]=1,u=(d>>>31)+2147483647|0),n=e[i+118>>1]|0,m=i+126|0,l=e[m>>1]|0,o=l<<16>>16<19660,o=n<<16>>16<l<<16>>16?o?2621:6553:o?2621:655,r=l&65535,f=r<<16,l=g(o,l<<16>>16)|0,(l|0)==1073741824?(a[t>>2]=1,l=2147483647):l=l<<1,s=f-l|0,((s^f)&(l^f)|0)<0&&(a[t>>2]=1,s=(r>>>15)+2147483647|0),f=g(o,n<<16>>16)|0;do if((f|0)==1073741824)a[t>>2]=1,l=2147483647;else{if(l=s+(f<<1)|0,!((s^f|0)>0&(l^s|0)<0))break;a[t>>2]=1,l=(s>>>31)+2147483647|0}while(!1);r=S1(l,t)|0,d=(u|0)>-1,e[m>>1]=d?r<<16>>16<13106?13106:r:13106,r=i+106|0,e[r>>1]=X1(e[r>>1]|0,1,t)|0,s=i+108|0,l=X1(e[s>>1]|0,1,t)|0,e[s>>1]=l,f=e[m>>1]|0;e:do if(d){do if(f<<16>>16>19660)e[r>>1]=D[r>>1]|16384;else{if(f<<16>>16>16383)break;f=i+116|0,l=0;break e}while(!1);e[s>>1]=l&65535|16384,B=62}else B=62;while(!1);do if((B|0)==62){if(l=i+116|0,f<<16>>16<=22936){f=l,l=0;break}f=l,l=n1(e[l>>1]|0,1,t)|0}while(!1);e[f>>1]=l,(e[r>>1]&32640)!=32640?(c=(e[s>>1]&32767)==32767,e[i+122>>1]=c&1,c&&(B=67)):(e[i+122>>1]=1,B=67);do if((B|0)==67){if(f=i+98|0,(e[f>>1]|0)>=5)break;e[f>>1]=5}while(!1);c=i+102|0;do if((e[c>>1]&24576)==24576)B=71;else{if((e[i+104>>1]&31744)==31744){B=71;break}if(e[h>>1]&32640)s=k,f=0,l=0;else{e[i+98>>1]=20,s=32767;break}for(;;){o=e[i+18+(f<<1)>>1]|0,n=s<<16>>16>o<<16>>16,u=n?s:o,s=n?o:s,u=u<<16>>16<184?184:u,s=s<<16>>16<184?184:s,o=P2(s)|0,n=o<<16>>16;do if(o<<16>>16<0){if(r=0-n<<16,(r|0)>=983040){r=0;break}r=s<<16>>16>>(r>>16)&65535}else{if(r=s<<16>>16,s=r<<n,(s<<16>>16>>n|0)==(r|0)){r=s&65535;break}r=(r>>>15^32767)&65535}while(!1);if(u=r0(X1(u,1,t)|0,r)|0,l=n1(l,X1(u,y1(8,o,t)|0,t)|0,t)|0,f=f+1|0,(f|0)==9)break;s=e[F+(f<<1)>>1]|0}if(l<<16>>16>1e3){e[i+98>>1]=20,s=32767;break}s=e[h>>1]|0,f=i+98|0,l=e[f>>1]|0;do if(!(s&16384))B=86;else{if(!(l<<16>>16)){l=s;break}l=y1(l,1,t)|0,e[f>>1]=l,B=86}while(!1);if((B|0)==86){if(l<<16>>16==20){s=32767;break}l=e[h>>1]|0}s=l&16384?3276:16383}while(!1);for((B|0)==71&&(e[i+98>>1]=20,s=32767),f=k,l=0;u=i+18+(l<<1)|0,r=q5(s,y1(f,e[u>>1]|0,t)|0,t)|0,e[u>>1]=n1(e[u>>1]|0,r,t)|0,l=l+1|0,(l|0)!=9;)f=e[F+(l<<1)>>1]|0;do if(e[h>>1]&30720)B=95;else{if(e[c>>1]&30720){B=95;break}e[i+114>>1]|0?B=95:(n=2097,o=1638,r=2)}while(!1);do if((B|0)==95){if(!(e[i+98>>1]|0)&&!(e[i+114>>1]|0)){n=1867,o=491,r=2;break}n=1638,o=0,r=0}while(!1);s=0;do f=i+(s<<1)|0,l=y1(e[i+36+(s<<1)>>1]|0,e[f>>1]|0,t)|0,l<<16>>16<0?(l=q5(n,l,t)|0,l=n1(-2,n1(e[f>>1]|0,l,t)|0,t)|0,l=l<<16>>16<40?40:l):(l=q5(o,l,t)|0,l=n1(r,n1(e[f>>1]|0,l,t)|0,t)|0,l=l<<16>>16>16e3?16e3:l),e[f>>1]=l,s=s+1|0;while((s|0)!=9);if(e[i+36>>1]=k,e[i+38>>1]=I,e[i+40>>1]=A,e[i+42>>1]=N,e[i+44>>1]=O,e[i+46>>1]=S,e[i+48>>1]=R,e[i+50>>1]=E,e[i+52>>1]=b,f=w<<16>>16>100,s=f?7:4,f=f?4:5,!d)return e[i+94>>1]=0,e[i+96>>1]=0,e[i+114>>1]=0,e[i+116>>1]=0,t=0,i=i+120|0,e[i>>1]=t,C=M,t|0;n=i+114|0,o=e[n>>1]|0;do if((e[i+116>>1]|0)<=100){if(o<<16>>16)break;o=e[h>>1]|0;do if(!(o&16368)){if((e[m>>1]|0)>21298)o=1;else break;return i=i+120|0,e[i>>1]=o,C=M,o|0}while(!1);return n=i+94|0,o&16384?(t=n1(e[n>>1]|0,1,t)|0,e[n>>1]=t,(t<<16>>16|0)<(f|0)?(t=1,i=i+120|0,e[i>>1]=t,C=M,t|0):(e[i+96>>1]=s,t=1,i=i+120|0,e[i>>1]=t,C=M,t|0)):(e[n>>1]=0,o=i+96|0,n=e[o>>1]|0,n<<16>>16<=0?(t=0,i=i+120|0,e[i>>1]=t,C=M,t|0):(e[o>>1]=y1(n,1,t)|0,t=1,i=i+120|0,e[i>>1]=t,C=M,t|0))}else{if(o<<16>>16>=250)break;e[n>>1]=250,o=250}while(!1);return e[i+94>>1]=4,e[n>>1]=y1(o,1,t)|0,t=1,i=i+120|0,e[i>>1]=t,C=M,t|0}function r4(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;n=(e[t>>1]|0)*21955>>15,(n|0)>32767&&(a[o>>2]=1,n=32767),s=y1(e[i>>1]|0,n&65535,o)|0,n=(s<<16>>16)*21955>>15,(n|0)>32767&&(a[o>>2]=1,n=32767),f=n1(e[t>>1]|0,n&65535,o)|0,e[t>>1]=s,t=t+2|0,n=(e[t>>1]|0)*6390>>15,(n|0)>32767&&(a[o>>2]=1,n=32767),s=y1(e[r>>1]|0,n&65535,o)|0,n=(s<<16>>16)*6390>>15,(n|0)>32767&&(a[o>>2]=1,n=32767),n=n1(e[t>>1]|0,n&65535,o)|0,e[t>>1]=s,e[i>>1]=X1(n1(f,n,o)|0,1,o)|0,e[r>>1]=X1(y1(f,n,o)|0,1,o)|0}function j2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0;n=(e[t>>1]|0)*13363>>15,(n|0)>32767&&(a[o>>2]=1,n=32767),s=y1(e[r>>1]|0,n&65535,o)|0,n=(s<<16>>16)*13363>>15,(n|0)>32767&&(a[o>>2]=1,n=32767),n=n1(e[t>>1]|0,n&65535,o)|0,e[t>>1]=s,e[r>>1]=X1(y1(e[i>>1]|0,n,o)|0,1,o)|0,e[i>>1]=X1(n1(e[i>>1]|0,n,o)|0,1,o)|0}function J0(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0;if(t<<16>>16<o<<16>>16){m=n<<16>>16,u=s<<16>>16,w=t<<16>>16,c=0;do h=e[i+((g(w,m)|0)+u<<1)>>1]|0,h=(h&65535)-((h&65535)>>>15&65535)|0,h=(h<<16>>31^h)<<16,d=(h>>15)+c|0,(h>>16^c|0)>0&(d^c|0)<0?(a[l>>2]=1,c=(c>>>31)+2147483647|0):c=d,w=w+1|0;while((w&65535)<<16>>16!=o<<16>>16);w=c}else w=0;if(c=e[r>>1]|0,h=y1(16,f,l)|0,u=h<<16>>16,h<<16>>16>0?(o=c<<u,(o>>u|0)!=(c|0)&&(o=c>>31^2147483647)):(u=0-u<<16,(u|0)<2031616?o=c>>(u>>16):o=0),u=o+w|0,(o^w|0)>-1&(u^w|0)<0&&(a[l>>2]=1,u=(w>>>31)+2147483647|0),h=f<<16>>16,f=f<<16>>16>0,f?(o=w<<h,(o>>h|0)!=(w|0)&&(o=w>>31^2147483647)):(o=0-h<<16,(o|0)<2031616?o=w>>(o>>16):o=0),e[r>>1]=o>>>16,t<<16>>16>0){m=n<<16>>16,c=s<<16>>16,d=0;do s=e[i+((g(d,m)|0)+c<<1)>>1]|0,s=(s&65535)-((s&65535)>>>15&65535)|0,s=(s<<16>>31^s)<<16,o=(s>>15)+u|0,(s>>16^u|0)>0&(o^u|0)<0?(a[l>>2]=1,u=(u>>>31)+2147483647|0):u=o,d=d+1|0;while((d&65535)<<16>>16!=t<<16>>16)}return f?(o=u<<h,(o>>h|0)==(u|0)?(l=o,l=l>>>16,l=l&65535,l|0):(l=u>>31^2147483647,l=l>>>16,l=l&65535,l|0)):(o=0-h<<16,(o|0)>=2031616?(l=0,l=l>>>16,l=l&65535,l|0):(l=u>>(o>>16),l=l>>>16,l=l&65535,l|0))}function n1(i,r,t){return i=i|0,r=r|0,t=t|0,i=(r<<16>>16)+(i<<16>>16)|0,(i|0)<=32767?(i|0)<-32768&&(a[t>>2]=1,i=-32768):(a[t>>2]=1,i=32767),i&65535|0}function U5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0;I=C,C=C+32|0,N=I+12|0,A=I,e[N>>1]=1024,e[A>>1]=1024,u=e[i+2>>1]|0,f=e[i+20>>1]|0,o=((f+u|0)>>>2)+64512|0,e[N+2>>1]=o,f=((u-f|0)>>>2)+1024|0,e[A+2>>1]=f,u=e[i+4>>1]|0,n=e[i+18>>1]|0,o=((n+u|0)>>>2)-o|0,e[N+4>>1]=o,f=((u-n|0)>>>2)+f|0,e[A+4>>1]=f,n=e[i+6>>1]|0,u=e[i+16>>1]|0,o=((u+n|0)>>>2)-o|0,e[N+6>>1]=o,f=((n-u|0)>>>2)+f|0,e[A+6>>1]=f,u=e[i+8>>1]|0,n=e[i+14>>1]|0,o=((n+u|0)>>>2)-o|0,e[N+8>>1]=o,f=((u-n|0)>>>2)+f|0,e[A+8>>1]=f,n=e[i+10>>1]|0,u=e[i+12>>1]|0,o=((u+n|0)>>>2)-o|0,e[N+10>>1]=o,e[A+10>>1]=((n-u|0)>>>2)+f,f=e[3454]|0,u=f<<16>>16,i=e[N+2>>1]|0,n=(i<<16>>16<<14)+(u<<10)|0,E=n&-65536,n=(n>>>1)-(n>>16<<15)<<16,O=(((g(n>>16,u)|0)>>15)+(g(E>>16,u)|0)<<2)+-16777216|0,O=(e[N+4>>1]<<14)+O|0,l=O>>16,O=(O>>>1)-(l<<15)<<16,E=(((g(O>>16,u)|0)>>15)+(g(l,u)|0)<<2)-((n>>15)+E)|0,E=(e[N+6>>1]<<14)+E|0,n=E>>16,E=(E>>>1)-(n<<15)<<16,l=(((g(E>>16,u)|0)>>15)+(g(n,u)|0)<<2)-((O>>15)+(l<<16))|0,l=(e[N+8>>1]<<14)+l|0,O=l>>16,n=(o<<16>>3)+((((g((l>>>1)-(O<<15)<<16>>16,u)|0)>>15)+(g(O,u)|0)<<1)-((E>>15)+(n<<16)))|0,E=N+4|0,u=N,O=0,l=0,o=0,b=N+10|0,n=(n+33554432|0)>>>0<67108863?n>>>10&65535:(n|0)>33554431?32767:-32768;e:for(;;){for(R=i<<16>>16<<14,k=u+6|0,h=u+8|0,w=l<<16>>16;;){if((w|0)>=60)break e;if(u=(w&65535)+1<<16>>16,c=e[6908+(u<<16>>16<<1)>>1]|0,S=c<<16>>16,l=R+(S<<10)|0,s=l&-65536,l=(l>>>1)-(l>>16<<15)<<16,d=(((g(l>>16,S)|0)>>15)+(g(s>>16,S)|0)<<2)+-16777216|0,m=e[E>>1]|0,d=(m<<16>>16<<14)+d|0,M=d>>16,d=(d>>>1)-(M<<15)<<16,s=(((g(d>>16,S)|0)>>15)+(g(M,S)|0)<<2)-((l>>15)+s)|0,l=e[k>>1]|0,s=(l<<16>>16<<14)+s|0,i=s>>16,s=(s>>>1)-(i<<15)<<16,M=(((g(s>>16,S)|0)>>15)+(g(i,S)|0)<<2)-((d>>15)+(M<<16))|0,d=e[h>>1]|0,M=(d<<16>>16<<14)+M|0,B=M>>16,i=(((g((M>>>1)-(B<<15)<<16>>16,S)|0)>>15)+(g(B,S)|0)<<1)-((s>>15)+(i<<16))|0,s=e[b>>1]|0,i=(s<<16>>16<<13)+i|0,i=(i+33554432|0)>>>0<67108863?i>>>10&65535:(i|0)>33554431?32767:-32768,(g(i<<16>>16,n<<16>>16)|0)<1){S=u,u=m;break}else w=w+1|0,f=c,n=i}for(E=s<<16>>16<<13,b=u<<16>>16<<14,m=l<<16>>16<<14,h=d<<16>>16<<14,s=c<<16>>16,w=4;;)if(B=(f<<16>>16>>>1)+(s>>>1)|0,s=B<<16,k=s>>16,s=R+(s>>6)|0,M=s&-65536,s=(s>>>1)-(s>>16<<15)<<16,d=b+((((g(s>>16,k)|0)>>15)+(g(M>>16,k)|0)<<2)+-16777216)|0,u=d>>16,d=(d>>>1)-(u<<15)<<16,M=m+((((g(d>>16,k)|0)>>15)+(g(u,k)|0)<<2)-((s>>15)+M))|0,s=M>>16,M=(M>>>1)-(s<<15)<<16,u=h+((((g(M>>16,k)|0)>>15)+(g(s,k)|0)<<2)-((d>>15)+(u<<16)))|0,d=u>>16,B=B&65535,s=E+((((g((u>>>1)-(d<<15)<<16>>16,k)|0)>>15)+(g(d,k)|0)<<1)-((M>>15)+(s<<16)))|0,s=(s+33554432|0)>>>0<67108863?s>>>10&65535:(s|0)>33554431?32767:-32768,M=(g(s<<16>>16,i<<16>>16)|0)<1,k=M?c:B,i=M?i:s,f=M?B:f,n=M?s:n,w=w+-1<<16>>16,s=k<<16>>16,w<<16>>16)c=k;else{c=s,l=f,f=k;break}if(u=o<<16>>16,s=i<<16>>16,i=(n&65535)-s|0,n=i<<16,n&&(M=(i&65535)-(i>>>15&1)|0,M=M<<16>>31^M,i=(P2(M&65535)|0)<<16>>16,i=(g((r0(16383,M<<16>>16<<i&65535)|0)<<16>>16,(l&65535)-c<<16>>16)|0)>>19-i,(n|0)<0&&(i=0-(i<<16>>16)|0),f=c-((g(i<<16>>16,s)|0)>>>10)&65535),e[r+(u<<1)>>1]=f,n=O<<16>>16?N:A,B=f<<16>>16,i=e[n+2>>1]|0,s=(i<<16>>16<<14)+(B<<10)|0,M=s&-65536,s=(s>>>1)-(s>>16<<15)<<16,R=(((g(s>>16,B)|0)>>15)+(g(M>>16,B)|0)<<2)+-16777216|0,R=(e[n+4>>1]<<14)+R|0,E=R>>16,R=(R>>>1)-(E<<15)<<16,M=(((g(R>>16,B)|0)>>15)+(g(E,B)|0)<<2)-((s>>15)+M)|0,M=(e[n+6>>1]<<14)+M|0,s=M>>16,M=(M>>>1)-(s<<15)<<16,E=(((g(M>>16,B)|0)>>15)+(g(s,B)|0)<<2)-((R>>15)+(E<<16))|0,E=(e[n+8>>1]<<14)+E|0,R=E>>16,o=o+1<<16>>16,s=(((g((E>>>1)-(R<<15)<<16>>16,B)|0)>>15)+(g(R,B)|0)<<1)-((M>>15)+(s<<16))|0,s=(e[n+10>>1]<<13)+s|0,o<<16>>16<10)E=n+4|0,u=n,O=O^1,l=S,b=n+10|0,n=(s+33554432|0)>>>0<67108863?s>>>10&65535:(s|0)>33554431?32767:-32768;else{F=13;break}}if((F|0)==13){C=I;return}e[r>>1]=e[t>>1]|0,e[r+2>>1]=e[t+2>>1]|0,e[r+4>>1]=e[t+4>>1]|0,e[r+6>>1]=e[t+6>>1]|0,e[r+8>>1]=e[t+8>>1]|0,e[r+10>>1]=e[t+10>>1]|0,e[r+12>>1]=e[t+12>>1]|0,e[r+14>>1]=e[t+14>>1]|0,e[r+16>>1]=e[t+16>>1]|0,e[r+18>>1]=e[t+18>>1]|0,C=I}function r0(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,s=0,f=0,l=0;return n=r<<16>>16,i<<16>>16<1||i<<16>>16>r<<16>>16?(n=0,n|0):i<<16>>16==r<<16>>16?(n=32767,n|0):(o=n<<1,t=n<<2,s=i<<16>>16<<3,i=(s|0)<(t|0),s=s-(i?0:t)|0,i=i?0:4,f=(s|0)<(o|0),s=s-(f?0:o)|0,r=(s|0)<(n|0),i=(r&1|(f?i:i|2))<<3^8,r=s-(r?0:n)<<3,(r|0)>=(t|0)&&(r=r-t|0,i=i&65528|4),s=(r|0)<(o|0),f=r-(s?0:o)|0,r=(f|0)<(n|0),i=(r&1^1|(s?i:i|2))<<16>>13,r=f-(r?0:n)<<3,(r|0)>=(t|0)&&(r=r-t|0,i=i&65528|4),s=(r|0)<(o|0),f=r-(s?0:o)|0,r=(f|0)<(n|0),i=(r&1^1|(s?i:i|2))<<16>>13,r=f-(r?0:n)<<3,(r|0)>=(t|0)&&(r=r-t|0,i=i&65528|4),l=(r|0)<(o|0),s=r-(l?0:o)|0,f=(s|0)<(n|0),r=(f&1^1|(l?i:i|2))<<16>>13,i=s-(f?0:n)<<3,(i|0)>=(t|0)&&(i=i-t|0,r=r&65528|4),l=(i|0)<(o|0),l=((i-(l?0:o)|0)>=(n|0)|(l?r:r|2))&65535,l|0)}function n2(i){return i=i|0,i?(e[i>>1]=-14336,e[i+8>>1]=-2381,e[i+2>>1]=-14336,e[i+10>>1]=-2381,e[i+4>>1]=-14336,e[i+12>>1]=-2381,e[i+6>>1]=-14336,e[i+14>>1]=-2381,i=0,i|0):(i=-1,i|0)}function M2(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0;for(h=C,C=C+16|0,m=h+2|0,w=h,u=0,c=10;d=e[t>>1]|0,d=((g(d,d)|0)>>>3)+u|0,u=e[t+2>>1]|0,u=d+((g(u,u)|0)>>>3)|0,d=e[t+4>>1]|0,d=u+((g(d,d)|0)>>>3)|0,u=e[t+6>>1]|0,u=d+((g(u,u)|0)>>>3)|0,c=c+-1<<16>>16,c<<16>>16;)t=t+8|0;if(c=u<<4,c=(c|0)<0?2147483647:c,(r|0)==7){s2(((S1(c,l)|0)<<16>>16)*52428|0,m,w,l),d=D[m>>1]<<16,c=e[w>>1]<<1,r=e[i+8>>1]|0,u=(r<<16>>16)*88|0,r<<16>>16>-1&(u|0)<-783741?(a[l>>2]=1,t=2147483647):t=u+783741|0,r=(e[i+10>>1]|0)*74|0,u=r+t|0,(r^t|0)>-1&(u^t|0)<0?(a[l>>2]=1,t=(t>>>31)+2147483647|0):t=u,r=(e[i+12>>1]|0)*44|0,u=r+t|0,(r^t|0)>-1&(u^t|0)<0?(a[l>>2]=1,t=(t>>>31)+2147483647|0):t=u,i=(e[i+14>>1]|0)*24|0,u=i+t|0,(i^t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0),i=d+-1966080+c|0,t=u-i|0,((t^u)&(u^i)|0)<0&&(a[l>>2]=1,t=(u>>>31)+2147483647|0),l=t>>17,e[o>>1]=l,l=(t>>2)-(l<<15)|0,l=l&65535,e[n>>1]=l,C=h;return}switch(d=B1(c)|0,u=d<<16>>16,d<<16>>16>0?(t=c<<u,(t>>u|0)==(c|0)?c=t:c=c>>31^2147483647):(u=0-u<<16,(u|0)<2031616?c=c>>(u>>16):c=0),a4(c,d,m,w),m=g(e[m>>1]|0,-49320)|0,u=(g(e[w>>1]|0,-24660)|0)>>15,u=u&65536|0?u|-65536:u,w=u<<1,t=w+m|0,(w^m|0)>-1&(t^w|0)<0&&(a[l>>2]=1,t=(u>>>30&1)+2147483647|0),r|0){case 6:{u=t+2134784|0,(t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0);break}case 5:{e[f>>1]=c>>>16,e[s>>1]=-11-(d&65535),u=t+2183936|0,(t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0);break}case 4:{u=t+2085632|0,(t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0);break}case 3:{u=t+2065152|0,(t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0);break}default:u=t+2134784|0,(t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0)}do if((u|0)<=2097151)if((u|0)<-2097152){a[l>>2]=1,t=-2147483648;break}else{t=u<<10;break}else a[l>>2]=1,t=2147483647;while(!1);if(f=(e[i>>1]|0)*11142|0,u=f+t|0,(f^t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0),f=(e[i+2>>1]|0)*9502|0,t=f+u|0,(f^u|0)>-1&(t^u|0)<0&&(a[l>>2]=1,t=(u>>>31)+2147483647|0),f=(e[i+4>>1]|0)*5570|0,u=f+t|0,(f^t|0)>-1&(u^t|0)<0&&(a[l>>2]=1,u=(t>>>31)+2147483647|0),i=(e[i+6>>1]|0)*3112|0,t=i+u|0,(i^u|0)>-1&(t^u|0)<0&&(a[l>>2]=1,t=(u>>>31)+2147483647|0),t=g(t>>16,(r|0)==4?10878:10886)|0,(t|0)<0?t=~((t^-256)>>8):t=t>>8,e[o>>1]=t>>>16,(t|0)<0?u=~((t^-2)>>1):u=t>>1,o=t>>16<<15,t=u-o|0,((t^u)&(o^u)|0)>=0){l=t,l=l&65535,e[n>>1]=l,C=h;return}a[l>>2]=1,l=(u>>>31)+2147483647|0,l=l&65535,e[n>>1]=l,C=h}function W0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;n=i+4|0,e[i+6>>1]=e[n>>1]|0,s=i+12|0,e[i+14>>1]=e[s>>1]|0,o=i+2|0,e[n>>1]=e[o>>1]|0,n=i+10|0,e[s>>1]=e[n>>1]|0,e[o>>1]=e[i>>1]|0,o=i+8|0,e[n>>1]=e[o>>1]|0,e[o>>1]=r,e[i>>1]=t}function Ri(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0;s=n1(0,e[i+8>>1]|0,o)|0,s=n1(s,e[i+10>>1]|0,o)|0,s=n1(s,e[i+12>>1]|0,o)|0,s=n1(s,e[i+14>>1]|0,o)|0,n=s<<16>>16>>2,n=(s<<16>>16<0?n|49152:n)&65535,e[r>>1]=n<<16>>16<-2381?-2381:n,r=n1(0,e[i>>1]|0,o)|0,r=n1(r,e[i+2>>1]|0,o)|0,r=n1(r,e[i+4>>1]|0,o)|0,o=n1(r,e[i+6>>1]|0,o)|0,i=o<<16>>16>>2,i=(o<<16>>16<0?i|49152:i)&65535,e[t>>1]=i<<16>>16<-14336?-14336:i}function o4(i){i=i|0,a[i>>2]=6892,a[i+4>>2]=8180,a[i+8>>2]=21e3,a[i+12>>2]=9716,a[i+16>>2]=22024,a[i+20>>2]=12788,a[i+24>>2]=24072,a[i+28>>2]=26120,a[i+32>>2]=28168,a[i+36>>2]=6876,a[i+40>>2]=7452,a[i+44>>2]=8140,a[i+48>>2]=20980,a[i+52>>2]=16884,a[i+56>>2]=17908,a[i+60>>2]=7980,a[i+64>>2]=8160,a[i+68>>2]=6678,a[i+72>>2]=6646,a[i+76>>2]=6614,a[i+80>>2]=29704,a[i+84>>2]=28680,a[i+88>>2]=3720,a[i+92>>2]=8,a[i+96>>2]=4172,a[i+100>>2]=44,a[i+104>>2]=3436,a[i+108>>2]=30316,a[i+112>>2]=30796,a[i+116>>2]=31276,a[i+120>>2]=7472,a[i+124>>2]=7552,a[i+128>>2]=7632,a[i+132>>2]=7712}function k2(i,r){i=i|0,r=r|0;var t=0,o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0;if(m=C,C=C+48|0,c=m+18|0,d=m,u=r<<16>>16,R0(d|0,i|0,u<<1|0)|0,r<<16>>16>0)t=0,o=0;else return d=u>>1,d=c+(d<<1)|0,d=e[d>>1]|0,d=d<<16>>16,d=i+(d<<1)|0,d=e[d>>1]|0,C=m,d|0;do{for(l=0,f=-32767;n=e[d+(l<<1)>>1]|0,s=n<<16>>16<f<<16>>16,o=s?o:l&65535,l=l+1|0,(l&65535)<<16>>16!=r<<16>>16;)f=s?f:n;e[d+(o<<16>>16<<1)>>1]=-32768,e[c+(t<<1)>>1]=o,t=t+1|0}while((t&65535)<<16>>16!=r<<16>>16);return d=u>>1,d=c+(d<<1)|0,d=e[d>>1]|0,d=d<<16>>16,d=i+(d<<1)|0,d=e[d>>1]|0,C=m,d|0}function n4(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0;s=C,C=C+32|0,f=s,B=r+2|0,F=f+2|0,e[f>>1]=((e[r>>1]|0)>>>1)+((e[i>>1]|0)>>>1),I=r+4|0,A=f+4|0,e[F>>1]=((e[B>>1]|0)>>>1)+((e[i+2>>1]|0)>>>1),N=r+6|0,O=f+6|0,e[A>>1]=((e[I>>1]|0)>>>1)+((e[i+4>>1]|0)>>>1),S=r+8|0,R=f+8|0,e[O>>1]=((e[N>>1]|0)>>>1)+((e[i+6>>1]|0)>>>1),E=r+10|0,b=f+10|0,e[R>>1]=((e[S>>1]|0)>>>1)+((e[i+8>>1]|0)>>>1),k=r+12|0,h=f+12|0,e[b>>1]=((e[E>>1]|0)>>>1)+((e[i+10>>1]|0)>>>1),w=r+14|0,m=f+14|0,e[h>>1]=((e[k>>1]|0)>>>1)+((e[i+12>>1]|0)>>>1),d=r+16|0,c=f+16|0,e[m>>1]=((e[w>>1]|0)>>>1)+((e[i+14>>1]|0)>>>1),u=r+18|0,l=f+18|0,e[c>>1]=((e[d>>1]|0)>>>1)+((e[i+16>>1]|0)>>>1),e[l>>1]=((e[u>>1]|0)>>>1)+((e[i+18>>1]|0)>>>1),_0(f,o,n),_0(r,o+22|0,n),e[f>>1]=((e[t>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[F>>1]=((e[t+2>>1]|0)>>>1)+((e[B>>1]|0)>>>1),e[A>>1]=((e[t+4>>1]|0)>>>1)+((e[I>>1]|0)>>>1),e[O>>1]=((e[t+6>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[R>>1]=((e[t+8>>1]|0)>>>1)+((e[S>>1]|0)>>>1),e[b>>1]=((e[t+10>>1]|0)>>>1)+((e[E>>1]|0)>>>1),e[h>>1]=((e[t+12>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[m>>1]=((e[t+14>>1]|0)>>>1)+((e[w>>1]|0)>>>1),e[c>>1]=((e[t+16>>1]|0)>>>1)+((e[d>>1]|0)>>>1),e[l>>1]=((e[t+18>>1]|0)>>>1)+((e[u>>1]|0)>>>1),_0(f,o+44|0,n),_0(t,o+66|0,n),C=s}function Si(i,r,t,o,n){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0;var s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0;s=C,C=C+32|0,f=s,B=r+2|0,F=f+2|0,e[f>>1]=((e[r>>1]|0)>>>1)+((e[i>>1]|0)>>>1),I=r+4|0,A=f+4|0,e[F>>1]=((e[B>>1]|0)>>>1)+((e[i+2>>1]|0)>>>1),N=r+6|0,O=f+6|0,e[A>>1]=((e[I>>1]|0)>>>1)+((e[i+4>>1]|0)>>>1),S=r+8|0,R=f+8|0,e[O>>1]=((e[N>>1]|0)>>>1)+((e[i+6>>1]|0)>>>1),E=r+10|0,b=f+10|0,e[R>>1]=((e[S>>1]|0)>>>1)+((e[i+8>>1]|0)>>>1),k=r+12|0,h=f+12|0,e[b>>1]=((e[E>>1]|0)>>>1)+((e[i+10>>1]|0)>>>1),w=r+14|0,m=f+14|0,e[h>>1]=((e[k>>1]|0)>>>1)+((e[i+12>>1]|0)>>>1),d=r+16|0,c=f+16|0,e[m>>1]=((e[w>>1]|0)>>>1)+((e[i+14>>1]|0)>>>1),u=r+18|0,l=f+18|0,e[c>>1]=((e[d>>1]|0)>>>1)+((e[i+16>>1]|0)>>>1),e[l>>1]=((e[u>>1]|0)>>>1)+((e[i+18>>1]|0)>>>1),_0(f,o,n),e[f>>1]=((e[t>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[F>>1]=((e[t+2>>1]|0)>>>1)+((e[B>>1]|0)>>>1),e[A>>1]=((e[t+4>>1]|0)>>>1)+((e[I>>1]|0)>>>1),e[O>>1]=((e[t+6>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[R>>1]=((e[t+8>>1]|0)>>>1)+((e[S>>1]|0)>>>1),e[b>>1]=((e[t+10>>1]|0)>>>1)+((e[E>>1]|0)>>>1),e[h>>1]=((e[t+12>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[m>>1]=((e[t+14>>1]|0)>>>1)+((e[w>>1]|0)>>>1),e[c>>1]=((e[t+16>>1]|0)>>>1)+((e[d>>1]|0)>>>1),e[l>>1]=((e[t+18>>1]|0)>>>1)+((e[u>>1]|0)>>>1),_0(f,o+44|0,n),C=s}function s4(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0;n=C,C=C+32|0,s=n,H=e[i>>1]|0,e[s>>1]=H-(H>>>2)+((e[r>>1]|0)>>>2),H=i+2|0,j=e[H>>1]|0,U=r+2|0,K=s+2|0,e[K>>1]=j-(j>>>2)+((e[U>>1]|0)>>>2),j=i+4|0,M=e[j>>1]|0,Y=r+4|0,z=s+4|0,e[z>>1]=M-(M>>>2)+((e[Y>>1]|0)>>>2),M=i+6|0,I=e[M>>1]|0,T=r+6|0,B=s+6|0,e[B>>1]=I-(I>>>2)+((e[T>>1]|0)>>>2),I=i+8|0,O=e[I>>1]|0,F=r+8|0,A=s+8|0,e[A>>1]=O-(O>>>2)+((e[F>>1]|0)>>>2),O=i+10|0,E=e[O>>1]|0,N=r+10|0,S=s+10|0,e[S>>1]=E-(E>>>2)+((e[N>>1]|0)>>>2),E=i+12|0,h=e[E>>1]|0,R=r+12|0,b=s+12|0,e[b>>1]=h-(h>>>2)+((e[R>>1]|0)>>>2),h=i+14|0,d=e[h>>1]|0,k=r+14|0,w=s+14|0,e[w>>1]=d-(d>>>2)+((e[k>>1]|0)>>>2),d=i+16|0,l=e[d>>1]|0,m=r+16|0,c=s+16|0,e[c>>1]=l-(l>>>2)+((e[m>>1]|0)>>>2),l=i+18|0,q=e[l>>1]|0,u=r+18|0,f=s+18|0,e[f>>1]=q-(q>>>2)+((e[u>>1]|0)>>>2),_0(s,t,o),e[s>>1]=((e[i>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[K>>1]=((e[H>>1]|0)>>>1)+((e[U>>1]|0)>>>1),e[z>>1]=((e[j>>1]|0)>>>1)+((e[Y>>1]|0)>>>1),e[B>>1]=((e[M>>1]|0)>>>1)+((e[T>>1]|0)>>>1),e[A>>1]=((e[I>>1]|0)>>>1)+((e[F>>1]|0)>>>1),e[S>>1]=((e[O>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[b>>1]=((e[E>>1]|0)>>>1)+((e[R>>1]|0)>>>1),e[w>>1]=((e[h>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[c>>1]=((e[d>>1]|0)>>>1)+((e[m>>1]|0)>>>1),e[f>>1]=((e[l>>1]|0)>>>1)+((e[u>>1]|0)>>>1),_0(s,t+22|0,o),q=e[r>>1]|0,e[s>>1]=q-(q>>>2)+((e[i>>1]|0)>>>2),i=e[U>>1]|0,e[K>>1]=i-(i>>>2)+((e[H>>1]|0)>>>2),i=e[Y>>1]|0,e[z>>1]=i-(i>>>2)+((e[j>>1]|0)>>>2),i=e[T>>1]|0,e[B>>1]=i-(i>>>2)+((e[M>>1]|0)>>>2),i=e[F>>1]|0,e[A>>1]=i-(i>>>2)+((e[I>>1]|0)>>>2),i=e[N>>1]|0,e[S>>1]=i-(i>>>2)+((e[O>>1]|0)>>>2),i=e[R>>1]|0,e[b>>1]=i-(i>>>2)+((e[E>>1]|0)>>>2),i=e[k>>1]|0,e[w>>1]=i-(i>>>2)+((e[h>>1]|0)>>>2),i=e[m>>1]|0,e[c>>1]=i-(i>>>2)+((e[d>>1]|0)>>>2),i=e[u>>1]|0,e[f>>1]=i-(i>>>2)+((e[l>>1]|0)>>>2),_0(s,t+44|0,o),_0(r,t+66|0,o),C=n}function Di(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0;n=C,C=C+32|0,s=n,H=e[i>>1]|0,e[s>>1]=H-(H>>>2)+((e[r>>1]|0)>>>2),H=i+2|0,j=e[H>>1]|0,U=r+2|0,K=s+2|0,e[K>>1]=j-(j>>>2)+((e[U>>1]|0)>>>2),j=i+4|0,M=e[j>>1]|0,Y=r+4|0,z=s+4|0,e[z>>1]=M-(M>>>2)+((e[Y>>1]|0)>>>2),M=i+6|0,I=e[M>>1]|0,T=r+6|0,B=s+6|0,e[B>>1]=I-(I>>>2)+((e[T>>1]|0)>>>2),I=i+8|0,O=e[I>>1]|0,F=r+8|0,A=s+8|0,e[A>>1]=O-(O>>>2)+((e[F>>1]|0)>>>2),O=i+10|0,E=e[O>>1]|0,N=r+10|0,S=s+10|0,e[S>>1]=E-(E>>>2)+((e[N>>1]|0)>>>2),E=i+12|0,h=e[E>>1]|0,R=r+12|0,b=s+12|0,e[b>>1]=h-(h>>>2)+((e[R>>1]|0)>>>2),h=i+14|0,d=e[h>>1]|0,k=r+14|0,w=s+14|0,e[w>>1]=d-(d>>>2)+((e[k>>1]|0)>>>2),d=i+16|0,l=e[d>>1]|0,m=r+16|0,c=s+16|0,e[c>>1]=l-(l>>>2)+((e[m>>1]|0)>>>2),l=i+18|0,q=e[l>>1]|0,u=r+18|0,f=s+18|0,e[f>>1]=q-(q>>>2)+((e[u>>1]|0)>>>2),_0(s,t,o),e[s>>1]=((e[i>>1]|0)>>>1)+((e[r>>1]|0)>>>1),e[K>>1]=((e[H>>1]|0)>>>1)+((e[U>>1]|0)>>>1),e[z>>1]=((e[j>>1]|0)>>>1)+((e[Y>>1]|0)>>>1),e[B>>1]=((e[M>>1]|0)>>>1)+((e[T>>1]|0)>>>1),e[A>>1]=((e[I>>1]|0)>>>1)+((e[F>>1]|0)>>>1),e[S>>1]=((e[O>>1]|0)>>>1)+((e[N>>1]|0)>>>1),e[b>>1]=((e[E>>1]|0)>>>1)+((e[R>>1]|0)>>>1),e[w>>1]=((e[h>>1]|0)>>>1)+((e[k>>1]|0)>>>1),e[c>>1]=((e[d>>1]|0)>>>1)+((e[m>>1]|0)>>>1),e[f>>1]=((e[l>>1]|0)>>>1)+((e[u>>1]|0)>>>1),_0(s,t+22|0,o),r=e[r>>1]|0,e[s>>1]=r-(r>>>2)+((e[i>>1]|0)>>>2),i=e[U>>1]|0,e[K>>1]=i-(i>>>2)+((e[H>>1]|0)>>>2),i=e[Y>>1]|0,e[z>>1]=i-(i>>>2)+((e[j>>1]|0)>>>2),i=e[T>>1]|0,e[B>>1]=i-(i>>>2)+((e[M>>1]|0)>>>2),i=e[F>>1]|0,e[A>>1]=i-(i>>>2)+((e[I>>1]|0)>>>2),i=e[N>>1]|0,e[S>>1]=i-(i>>>2)+((e[O>>1]|0)>>>2),i=e[R>>1]|0,e[b>>1]=i-(i>>>2)+((e[E>>1]|0)>>>2),i=e[k>>1]|0,e[w>>1]=i-(i>>>2)+((e[h>>1]|0)>>>2),i=e[m>>1]|0,e[c>>1]=i-(i>>>2)+((e[d>>1]|0)>>>2),i=e[u>>1]|0,e[f>>1]=i-(i>>>2)+((e[l>>1]|0)>>>2),_0(s,t+44|0,o),C=n}function e2(i,r){i=i|0,r=r|0;var t=0,o=0;return(i|0)<1?(r=1073741823,r|0):(t=(B1(i)|0)<<16>>16,r=30-t|0,i=i<<t>>(r&1^1),t=(i>>25<<16)+-1048576>>16,o=e[7030+(t<<1)>>1]|0,r=(o<<16)-(g(o-(D[7030+(t+1<<1)>>1]|0)<<16>>15,i>>>10&32767)|0)>>(r<<16>>17)+1,r|0)}function s2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0,o=B1(i)|0,a4(i<<(o<<16>>16),o,r,t)}function a4(i,r,t,o){if(i=i|0,r=r|0,t=t|0,o=o|0,(i|0)<1){e[t>>1]=0,t=0,e[o>>1]=t;return}else{e[t>>1]=30-(r&65535),t=(i>>25<<16)+-2097152>>16,r=e[7128+(t<<1)>>1]|0,t=((r<<16)-(g(i>>>9&65534,r-(D[7128+(t+1<<1)>>1]|0)<<16>>16)|0)|0)>>>16&65535,e[o>>1]=t;return}}function z5(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0;for(o=i+2|0,t=e[o>>1]|0,e[r>>1]=t,n=i+4|0,e[r+2>>1]=(D[n>>1]|0)-(D[i>>1]|0),e[r+4>>1]=(D[i+6>>1]|0)-(D[o>>1]|0),o=i+8|0,e[r+6>>1]=(D[o>>1]|0)-(D[n>>1]|0),e[r+8>>1]=(D[i+10>>1]|0)-(D[i+6>>1]|0),n=i+12|0,e[r+10>>1]=(D[n>>1]|0)-(D[o>>1]|0),e[r+12>>1]=(D[i+14>>1]|0)-(D[i+10>>1]|0),e[r+14>>1]=(D[i+16>>1]|0)-(D[n>>1]|0),e[r+16>>1]=(D[i+18>>1]|0)-(D[i+14>>1]|0),e[r+18>>1]=16384-(D[i+16>>1]|0),i=10,n=r;t=t<<16>>16,r=(t<<16)+-120782848|0,(r|0)>0?r=1843-((r>>16)*12484>>16)|0:r=3427-((t*56320|0)>>>16)|0,o=n+2|0,e[n>>1]=r<<3,i=i+-1<<16>>16,!!(i<<16>>16);)t=e[o>>1]|0,n=o}function j5(i,r,t){return i=i|0,r=r|0,t=t|0,t=r<<16>>16,r<<16>>16>31?(r=0,r|0):r<<16>>16>0?((1<<t+-1&i|0)!=0&1)+(r<<16>>16<31?i>>t:0)|0:(t=0-t<<16>>16,r=i<<t,r=(r>>t|0)==(i|0)?r:i>>31^2147483647,r|0)}function _0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0;for(b=C,C=C+48|0,h=b+24|0,k=b,m=h+4|0,a[h>>2]=16777216,o=0-(e[i>>1]|0)|0,w=h+8|0,a[m>>2]=o<<10,n=e[i+4>>1]|0,u=o>>6,a[w>>2]=33554432-(((g((o<<9)-(u<<15)<<16>>16,n)|0)>>15)+(g(u,n)|0)<<2),u=h+4|0,n=(a[u>>2]|0)-(n<<10)|0,a[u>>2]=n,u=h+12|0,o=h+4|0,a[u>>2]=n,t=e[i+8>>1]|0,s=n,c=1;l=u+-4|0,f=a[l>>2]|0,d=f>>16,a[u>>2]=s+n-(((g((f>>>1)-(d<<15)<<16>>16,t)|0)>>15)+(g(d,t)|0)<<2),(c|0)!=2;)s=a[u+-12>>2]|0,u=l,n=f,c=c+1|0;for(a[o>>2]=(a[o>>2]|0)-(t<<10),t=h+16|0,o=a[h+8>>2]|0,a[t>>2]=o,l=e[i+12>>1]|0,n=o,u=1;f=t+-4|0,s=a[f>>2]|0,d=s>>16,a[t>>2]=n+o-(((g((s>>>1)-(d<<15)<<16>>16,l)|0)>>15)+(g(d,l)|0)<<2),(u|0)!=3;)n=a[t+-12>>2]|0,t=f,o=s,u=u+1|0;for(t=h+4|0,a[t>>2]=(a[t>>2]|0)-(l<<10),t=h+20|0,n=a[h+12>>2]|0,a[t>>2]=n,o=e[i+16>>1]|0,s=n,u=1;l=t+-4|0,f=a[l>>2]|0,d=f>>16,a[t>>2]=s+n-(((g((f>>>1)-(d<<15)<<16>>16,o)|0)>>15)+(g(d,o)|0)<<2),(u|0)!=4;)s=a[t+-12>>2]|0,t=l,n=f,u=u+1|0;for(u=h+4|0,a[u>>2]=(a[u>>2]|0)-(o<<10),a[k>>2]=16777216,u=0-(e[i+2>>1]|0)|0,d=k+8|0,a[k+4>>2]=u<<10,o=e[i+6>>1]|0,c=u>>6,a[d>>2]=33554432-(((g((u<<9)-(c<<15)<<16>>16,o)|0)>>15)+(g(c,o)|0)<<2),c=k+4|0,o=(a[c>>2]|0)-(o<<10)|0,a[c>>2]=o,c=k+12|0,u=k+4|0,a[c>>2]=o,l=e[i+10>>1]|0,n=o,t=1;f=c+-4|0,s=a[f>>2]|0,E=s>>16,a[c>>2]=n+o-(((g((s>>>1)-(E<<15)<<16>>16,l)|0)>>15)+(g(E,l)|0)<<2),(t|0)!=2;)n=a[c+-12>>2]|0,c=f,o=s,t=t+1|0;for(a[u>>2]=(a[u>>2]|0)-(l<<10),u=k+16|0,o=a[k+8>>2]|0,a[u>>2]=o,l=e[i+14>>1]|0,n=o,t=1;f=u+-4|0,s=a[f>>2]|0,E=s>>16,a[u>>2]=n+o-(((g((s>>>1)-(E<<15)<<16>>16,l)|0)>>15)+(g(E,l)|0)<<2),(t|0)!=3;)n=a[u+-12>>2]|0,u=f,o=s,t=t+1|0;for(t=k+4|0,a[t>>2]=(a[t>>2]|0)-(l<<10),t=k+20|0,l=a[k+12>>2]|0,a[t>>2]=l,o=e[i+18>>1]|0,f=l,u=1;n=t+-4|0,s=a[n>>2]|0,E=s>>16,a[t>>2]=f+l-(((g((s>>>1)-(E<<15)<<16>>16,o)|0)>>15)+(g(E,o)|0)<<2),(u|0)!=4;)f=a[t+-12>>2]|0,t=n,l=s,u=u+1|0;f=(a[k+4>>2]|0)-(o<<10)|0,c=h+20|0,l=k+20|0,u=a[h+16>>2]|0,i=(a[c>>2]|0)+u|0,a[c>>2]=i,c=a[k+16>>2]|0,E=(a[l>>2]|0)-c|0,a[l>>2]=E,l=a[h+12>>2]|0,u=u+l|0,a[h+16>>2]=u,s=a[k+12>>2]|0,c=c-s|0,a[k+16>>2]=c,o=a[w>>2]|0,l=l+o|0,a[h+12>>2]=l,n=a[d>>2]|0,w=s-n|0,a[k+12>>2]=w,s=a[m>>2]|0,d=o+s|0,a[h+8>>2]=d,m=n-f|0,a[k+8>>2]=m,h=s+(a[h>>2]|0)|0,k=f-(a[k>>2]|0)|0,e[r>>1]=4096,h=h+4096|0,e[r+2>>1]=(h+k|0)>>>13,e[r+20>>1]=(h-k|0)>>>13,k=d+4096|0,e[r+4>>1]=(k+m|0)>>>13,e[r+18>>1]=(k-m|0)>>>13,k=l+4096|0,e[r+6>>1]=(k+w|0)>>>13,e[r+16>>1]=(k-w|0)>>>13,k=u+4096|0,e[r+8>>1]=(k+c|0)>>>13,e[r+14>>1]=(k-c|0)>>>13,k=i+4096|0,e[r+10>>1]=(k+E|0)>>>13,e[r+12>>1]=(k-E|0)>>>13,C=b}function Ai(i){i=i|0;var r=0,t=0,o=0,n=0,s=0;if(!i||(a[i>>2]=0,r=o0(44)|0,!r)||(t=r+40|0,(Oi(t)|0)<<16>>16))return s=-1,s|0;o=r,n=7452,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));o=r+20|0,n=7452,s=o+20|0;do e[o>>1]=e[n>>1]|0,o=o+2|0,n=n+2|0;while((o|0)<(s|0));return u4(a[t>>2]|0)|0,a[i>>2]=r,s=0,s|0}function f4(i){i=i|0;var r=0,t=0,o=0;if(!i)return o=-1,o|0;r=i,t=7452,o=r+20|0;do e[r>>1]=e[t>>1]|0,r=r+2|0,t=t+2|0;while((r|0)<(o|0));r=i+20|0,t=7452,o=r+20|0;do e[r>>1]=e[t>>1]|0,r=r+2|0,t=t+2|0;while((r|0)<(o|0));return u4(a[i+40>>2]|0)|0,o=0,o|0}function Mi(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(Ci(r+40|0),G1(a[i>>2]|0),a[i>>2]=0))}function Pi(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0;if(w=C,C=C+64|0,m=w+44|0,u=w+24|0,c=w+4|0,d=w,(r|0)==7?(U5(o+22|0,u,i,l),U5(o+66|0,s,u,l),Si(i,u,s,o,l),(t|0)==8?o=6:(Ni(a[i+40>>2]|0,u,s,c,m,a[f>>2]|0,l),n4(i+20|0,c,m,n,l),n=(a[f>>2]|0)+10|0,o=7)):(U5(o+66|0,s,i,l),Di(i,s,o,l),(t|0)==8?o=6:(l4(a[i+40>>2]|0,r,s,m,a[f>>2]|0,d,l),s4(i+20|0,m,n,l),n=(a[f>>2]|0)+6|0,o=7)),(o|0)==6){o=i,n=o+20|0;do e[o>>1]=e[s>>1]|0,o=o+2|0,s=s+2|0;while((o|0)<(n|0));C=w;return}else if((o|0)==7){a[f>>2]=n,o=i,n=o+20|0;do e[o>>1]=e[s>>1]|0,o=o+2|0,s=s+2|0;while((o|0)<(n|0));o=i+20|0,s=m,n=o+20|0;do e[o>>1]=e[s>>1]|0,o=o+2|0,s=s+2|0;while((o|0)<(n|0));C=w;return}}function p0(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;if(t<<16>>16>0)o=0;else return;do s=e[i+(o<<1)>>1]|0,f=s>>8,n=e[7194+(f<<1)>>1]|0,e[r+(o<<1)>>1]=((g((e[7194+(f+1<<1)>>1]|0)-n|0,s&255)|0)>>>8)+n,o=o+1|0;while((o&65535)<<16>>16!=t<<16>>16)}function q2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;if(o=(t<<16>>16)+-1|0,t=o&65535,!(t<<16>>16<=-1))for(n=63,f=r+(o<<1)|0,s=i+(o<<1)|0;;){for(i=e[s>>1]|0,r=n;o=r<<16>>16,n=e[7194+(o<<1)>>1]|0,i<<16>>16>n<<16>>16;)r=r+-1<<16>>16;if(e[f>>1]=(((g(e[7324+(o<<1)>>1]|0,(i<<16>>16)-(n<<16>>16)|0)|0)+2048|0)>>>12)+(o<<8),t=t+-1<<16>>16,t<<16>>16>-1)n=r,f=f+-2|0,s=s+-2|0;else break}}function q5(i,r,t){return i=i|0,r=r|0,t=t|0,i=(g(r<<16>>16,i<<16>>16)|0)+16384>>15,i=i|0-(i&65536),(i|0)<=32767?(i|0)<-32768&&(a[t>>2]=1,i=-32768):(a[t>>2]=1,i=32767),i&65535|0}function B1(i){i=i|0;var r=0;e:do if(i|0&&(r=i-(i>>>31)|0,r=r>>31^r,(r&1073741824|0)==0)){for(i=r,r=0;;){if(i&536870912){i=7;break}if(i&268435456){i=8;break}if(i&134217728){i=9;break}if(r=r+4<<16>>16,i=i<<4,i&1073741824)break e}if((i|0)==7){r=r|1;break}else if((i|0)==8){r=r|2;break}else if((i|0)==9){r=r|3;break}}else r=0;while(!1);return r|0}function P2(i){i=i|0;var r=0,t=0;if(!(i<<16>>16))return t=0,t|0;if(r=(i&65535)-((i&65535)>>>15&65535)|0,r=(r<<16>>31^r)<<16,i=r>>16,!(i&16384))t=r,r=0;else return t=0,t|0;for(;;){if(i&8192){i=r,t=7;break}if(i&4096){i=r,t=8;break}if(i&2048){i=r,t=9;break}if(r=r+4<<16>>16,t=t<<4,i=t>>16,i&16384){i=r,t=10;break}}return(t|0)==7?(t=i|1,t|0):(t|0)==8?(t=i|2,t|0):(t|0)==9?(t=i|3,t|0):(t|0)==10?i|0:0}function x0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;return r=r<<16>>16,(r&134217727|0)==33554432?(a[t>>2]=1,r=2147483647):r=r<<6,o=r>>>16&31,s=e[7792+(o<<1)>>1]|0,n=s<<16,r=g(s-(D[7792+(o+1<<1)>>1]|0)<<16>>16,r>>>1&32767)|0,(r|0)==1073741824?(a[t>>2]=1,o=2147483647):o=r<<1,r=n-o|0,((r^n)&(o^n)|0)>=0?(s=r,i=i&65535,i=30-i|0,i=i&65535,t=j5(s,i,t)|0,t|0):(a[t>>2]=1,s=(s>>>15&1)+2147483647|0,i=i&65535,i=30-i|0,i=i&65535,t=j5(s,i,t)|0,t|0)}function H2(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0;if(w=C,C=C+48|0,m=w,d=0-(t&65535)|0,d=n<<16>>16?d<<1&131070:d,t=d&65535,d=(t<<16>>16<0?d+6|0:d)<<16>>16,s=6-d|0,e[m>>1]=e[7858+(d<<1)>>1]|0,e[m+2>>1]=e[7858+(s<<1)>>1]|0,e[m+4>>1]=e[7858+(d+6<<1)>>1]|0,e[m+6>>1]=e[7858+(s+6<<1)>>1]|0,e[m+8>>1]=e[7858+(d+12<<1)>>1]|0,e[m+10>>1]=e[7858+(s+12<<1)>>1]|0,e[m+12>>1]=e[7858+(d+18<<1)>>1]|0,e[m+14>>1]=e[7858+(s+18<<1)>>1]|0,e[m+16>>1]=e[7858+(d+24<<1)>>1]|0,e[m+18>>1]=e[7858+(s+24<<1)>>1]|0,e[m+20>>1]=e[7858+(d+30<<1)>>1]|0,e[m+22>>1]=e[7858+(s+30<<1)>>1]|0,e[m+24>>1]=e[7858+(d+36<<1)>>1]|0,e[m+26>>1]=e[7858+(s+36<<1)>>1]|0,e[m+28>>1]=e[7858+(d+42<<1)>>1]|0,e[m+30>>1]=e[7858+(s+42<<1)>>1]|0,e[m+32>>1]=e[7858+(d+48<<1)>>1]|0,e[m+34>>1]=e[7858+(s+48<<1)>>1]|0,e[m+36>>1]=e[7858+(d+54<<1)>>1]|0,e[m+38>>1]=e[7858+(s+54<<1)>>1]|0,s=o<<16>>16>>>1&65535,!(s<<16>>16)){C=w;return}for(d=i+((t<<16>>16>>15<<16>>16)-(r<<16>>16)<<1)|0;;){for(c=d+2|0,f=e[c>>1]|0,r=f,o=d,l=5,u=m,n=16384,t=16384;k=e[u>>1]|0,b=(g(k,r<<16>>16)|0)+t|0,h=e[c+-2>>1]|0,t=(g(h,k)|0)+n|0,k=o,o=o+4|0,E=e[u+2>>1]|0,t=t+(g(E,f<<16>>16)|0)|0,n=e[o>>1]|0,E=b+(g(n,E)|0)|0,c=c+-4|0,b=e[u+4>>1]|0,h=E+(g(b,h)|0)|0,r=e[c>>1]|0,b=t+(g(r<<16>>16,b)|0)|0,t=e[u+6>>1]|0,n=b+(g(t,n)|0)|0,f=e[k+6>>1]|0,t=h+(g(f<<16>>16,t)|0)|0,!(l<<16>>16<=1);)l=l+-1<<16>>16,u=u+8|0;if(e[i>>1]=n>>>15,e[i+2>>1]=t>>>15,s=s+-1<<16>>16,s<<16>>16)d=d+4|0,i=i+4|0;else break}C=w}function l4(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0;if(M=C,C=C+144|0,E=M+120|0,A=M+100|0,F=M+80|0,B=M+60|0,I=M+40|0,h=M+20|0,k=M,q2(t,E,10,f),z5(E,A,f),(r|0)==8)for(e[s>>1]=0,u=2147483647,b=0;;){d=b*10|0,t=0,c=0;do N=(D[7980+(c+d<<1)>>1]|0)+(D[8140+(c<<1)>>1]|0)|0,e[k+(c<<1)>>1]=N,N=(D[E+(c<<1)>>1]|0)-(N&65535)|0,e[h+(c<<1)>>1]=N,N=N<<16,t=(g(N>>15,N>>16)|0)+t|0,c=c+1|0;while((c|0)!=10);if((t|0)<(u|0)){R=B,w=h,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));R=F,w=k,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));R=i,w=7980+(d<<1)|0,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));e[s>>1]=b}else t=u;if(b=b+1|0,(b|0)==8)break;u=t}else{t=0;do N=g(e[8160+(t<<1)>>1]|0,e[i+(t<<1)>>1]|0)|0,N=(N>>>15)+(D[8140+(t<<1)>>1]|0)|0,e[F+(t<<1)>>1]=N,e[B+(t<<1)>>1]=(D[E+(t<<1)>>1]|0)-N,t=t+1|0;while((t|0)!=10)}do if(r>>>0>=2)if(N=B+2|0,O=B+4|0,S=D[B>>1]|0,R=e[A>>1]<<1,E=D[N>>1]|0,h=e[A+2>>1]<<1,w=D[O>>1]|0,m=e[A+4>>1]<<1,(r|0)==5){for(k=2147483647,s=0,t=0,b=17908;c=(g(S-(D[b>>1]|0)<<16>>16,R)|0)>>16,c=g(c,c)|0,d=(g(E-(D[b+2>>1]|0)<<16>>16,h)|0)>>16,c=(g(d,d)|0)+c|0,d=(g(w-(D[b+4>>1]|0)<<16>>16,m)|0)>>16,d=c+(g(d,d)|0)|0,c=(d|0)<(k|0),t=c?s:t,s=s+1<<16>>16,!(s<<16>>16>=512);)k=c?d:k,b=b+6|0;for(d=(t<<16>>16)*3|0,e[B>>1]=e[17908+(d<<1)>>1]|0,e[N>>1]=e[17908+(d+1<<1)>>1]|0,e[O>>1]=e[17908+(d+2<<1)>>1]|0,e[n>>1]=t,d=B+6|0,c=B+8|0,S=B+10|0,b=D[d>>1]|0,s=e[A+6>>1]<<1,k=D[c>>1]|0,h=e[A+8>>1]<<1,w=D[S>>1]|0,m=e[A+10>>1]<<1,l=2147483647,E=0,t=0,R=9716;u=(g(s,b-(D[R>>1]|0)<<16>>16)|0)>>16,u=g(u,u)|0,r=(g(h,k-(D[R+2>>1]|0)<<16>>16)|0)>>16,u=(g(r,r)|0)+u|0,r=(g(m,w-(D[R+4>>1]|0)<<16>>16)|0)>>16,r=u+(g(r,r)|0)|0,u=(r|0)<(l|0),t=u?E:t,E=E+1<<16>>16,!(E<<16>>16>=512);)l=u?r:l,R=R+6|0;l=(t<<16>>16)*3|0,e[d>>1]=e[9716+(l<<1)>>1]|0,e[c>>1]=e[9716+(l+1<<1)>>1]|0,e[S>>1]=e[9716+(l+2<<1)>>1]|0,e[n+2>>1]=t,l=B+12|0,e[n+4>>1]=H5(l,12788,A+12|0,512)|0,E=N,b=O,t=S,u=B;break}else{for(k=2147483647,s=0,t=0,b=8180;c=(g(S-(D[b>>1]|0)<<16>>16,R)|0)>>16,c=g(c,c)|0,d=(g(E-(D[b+2>>1]|0)<<16>>16,h)|0)>>16,c=(g(d,d)|0)+c|0,d=(g(w-(D[b+4>>1]|0)<<16>>16,m)|0)>>16,d=c+(g(d,d)|0)|0,c=(d|0)<(k|0),t=c?s:t,s=s+1<<16>>16,!(s<<16>>16>=256);)k=c?d:k,b=b+6|0;for(d=(t<<16>>16)*3|0,e[B>>1]=e[8180+(d<<1)>>1]|0,e[N>>1]=e[8180+(d+1<<1)>>1]|0,e[O>>1]=e[8180+(d+2<<1)>>1]|0,e[n>>1]=t,d=B+6|0,c=B+8|0,S=B+10|0,b=D[d>>1]|0,s=e[A+6>>1]<<1,k=D[c>>1]|0,h=e[A+8>>1]<<1,w=D[S>>1]|0,m=e[A+10>>1]<<1,l=2147483647,E=0,t=0,R=9716;u=(g(s,b-(D[R>>1]|0)<<16>>16)|0)>>16,u=g(u,u)|0,r=(g(h,k-(D[R+2>>1]|0)<<16>>16)|0)>>16,u=(g(r,r)|0)+u|0,r=(g(m,w-(D[R+4>>1]|0)<<16>>16)|0)>>16,r=u+(g(r,r)|0)|0,u=(r|0)<(l|0),t=u?E:t,E=E+1<<16>>16,!(E<<16>>16>=512);)l=u?r:l,R=R+6|0;l=(t<<16>>16)*3|0,e[d>>1]=e[9716+(l<<1)>>1]|0,e[c>>1]=e[9716+(l+1<<1)>>1]|0,e[S>>1]=e[9716+(l+2<<1)>>1]|0,e[n+2>>1]=t,l=B+12|0,e[n+4>>1]=H5(l,12788,A+12|0,512)|0,E=N,b=O,t=S,u=B;break}else{for(O=B+2|0,N=B+4|0,d=D[B>>1]|0,c=e[A>>1]<<1,u=D[O>>1]|0,l=e[A+2>>1]<<1,r=D[N>>1]|0,m=e[A+4>>1]<<1,k=2147483647,s=0,t=0,b=8180;h=(g(c,d-(D[b>>1]|0)<<16>>16)|0)>>16,h=g(h,h)|0,w=(g(l,u-(D[b+2>>1]|0)<<16>>16)|0)>>16,h=(g(w,w)|0)+h|0,w=(g(m,r-(D[b+4>>1]|0)<<16>>16)|0)>>16,w=h+(g(w,w)|0)|0,h=(w|0)<(k|0),t=h?s:t,s=s+1<<16>>16,!(s<<16>>16>=256);)k=h?w:k,b=b+6|0;for(d=(t<<16>>16)*3|0,e[B>>1]=e[8180+(d<<1)>>1]|0,e[O>>1]=e[8180+(d+1<<1)>>1]|0,e[N>>1]=e[8180+(d+2<<1)>>1]|0,e[n>>1]=t,d=B+6|0,c=B+8|0,S=B+10|0,b=D[d>>1]|0,s=e[A+6>>1]<<1,k=D[c>>1]|0,h=e[A+8>>1]<<1,w=D[S>>1]|0,m=e[A+10>>1]<<1,l=2147483647,E=0,t=0,R=9716;u=(g(s,b-(D[R>>1]|0)<<16>>16)|0)>>16,u=g(u,u)|0,r=(g(h,k-(D[R+2>>1]|0)<<16>>16)|0)>>16,u=(g(r,r)|0)+u|0,r=(g(m,w-(D[R+4>>1]|0)<<16>>16)|0)>>16,r=u+(g(r,r)|0)|0,u=(r|0)<(l|0),t=u?E:t,E=E+1<<16>>16,!(E<<16>>16>=256);)l=u?r:l,R=R+12|0;l=(t<<16>>16)*6|0,e[d>>1]=e[9716+(l<<1)>>1]|0,e[c>>1]=e[9716+((l|1)<<1)>>1]|0,e[S>>1]=e[9716+(l+2<<1)>>1]|0,e[n+2>>1]=t,l=B+12|0,e[n+4>>1]=H5(l,16884,A+12|0,128)|0,E=O,b=N,t=S,u=B}while(!1);R=i,w=B,m=R+20|0;do e[R>>1]=e[w>>1]|0,R=R+2|0,w=w+2|0;while((R|0)<(m|0));e[I>>1]=(D[F>>1]|0)+(D[u>>1]|0),e[I+2>>1]=(D[F+2>>1]|0)+(D[E>>1]|0),e[I+4>>1]=(D[F+4>>1]|0)+(D[b>>1]|0),e[I+6>>1]=(D[F+6>>1]|0)+(D[d>>1]|0),e[I+8>>1]=(D[F+8>>1]|0)+(D[c>>1]|0),e[I+10>>1]=(D[F+10>>1]|0)+(D[t>>1]|0),e[I+12>>1]=(D[F+12>>1]|0)+(D[l>>1]|0),e[I+14>>1]=(D[F+14>>1]|0)+(D[B+14>>1]|0),e[I+16>>1]=(D[F+16>>1]|0)+(D[B+16>>1]|0),e[I+18>>1]=(D[F+18>>1]|0)+(D[B+18>>1]|0),P0(I,205,10,f),p0(I,o,10,f),C=M}function H5(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0;if(R=i+2|0,S=i+4|0,O=i+6|0,o<<16>>16>0)for(d=D[i>>1]|0,m=e[t>>1]<<1,w=D[R>>1]|0,h=e[t+2>>1]<<1,k=D[S>>1]|0,b=e[t+4>>1]<<1,E=D[O>>1]|0,n=e[t+6>>1]<<1,l=2147483647,u=0,t=0,c=r;s=(g(m,d-(D[c>>1]|0)<<16>>16)|0)>>16,s=g(s,s)|0,f=(g(h,w-(D[c+2>>1]|0)<<16>>16)|0)>>16,s=(g(f,f)|0)+s|0,f=(g(b,k-(D[c+4>>1]|0)<<16>>16)|0)>>16,f=s+(g(f,f)|0)|0,s=(g(n,E-(D[c+6>>1]|0)<<16>>16)|0)>>16,s=f+(g(s,s)|0)|0,f=(s|0)<(l|0),t=f?u:t,u=u+1<<16>>16,!(u<<16>>16>=o<<16>>16);)l=f?s:l,c=c+8|0;else t=0;return o=t<<16>>16<<2,E=o|1,e[i>>1]=e[r+(o<<1)>>1]|0,e[R>>1]=e[r+(E<<1)>>1]|0,e[S>>1]=e[r+(E+1<<1)>>1]|0,e[O>>1]=e[r+((o|3)<<1)>>1]|0,t|0}function Ni(i,r,t,o,n,s,f){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0;var l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0;for(H=C,C=C+192|0,c=H+160|0,u=H+140|0,M=H+120|0,T=H+100|0,z=H+80|0,j=H+60|0,l=H+40|0,Y=H+20|0,K=H,q2(r,c,10,f),q2(t,u,10,f),z5(c,M,f),z5(u,T,f),d=0,t=z,r=j,m=l;B=(((e[i+(d<<1)>>1]|0)*21299|0)>>>15)+(D[20980+(d<<1)>>1]|0)|0,e[t>>1]=B,e[r>>1]=(D[c>>1]|0)-B,e[m>>1]=(D[u>>1]|0)-B,d=d+1|0,(d|0)!=10;)c=c+2|0,u=u+2|0,t=t+2|0,r=r+2|0,m=m+2|0;for(e[s>>1]=l5(j,l,21e3,e[M>>1]|0,e[M+2>>1]|0,e[T>>1]|0,e[T+2>>1]|0,128)|0,e[s+2>>1]=l5(j+4|0,l+4|0,22024,e[M+4>>1]|0,e[M+6>>1]|0,e[T+4>>1]|0,e[T+6>>1]|0,256)|0,A=j+8|0,I=l+8|0,F=j+10|0,B=l+10|0,t=e[A>>1]|0,w=e[M+8>>1]<<1,h=e[F>>1]|0,k=e[M+10>>1]<<1,b=e[I>>1]|0,E=e[T+8>>1]<<1,R=e[B>>1]|0,S=e[T+10>>1]<<1,u=2147483647,O=0,m=0,N=24072,r=0;c=e[N>>1]|0,d=(g(t-c<<16>>16,w)|0)>>16,d=g(d,d)|0,c=(g(c+t<<16>>16,w)|0)>>16,c=g(c,c)|0,U=e[N+2>>1]|0,q=(g(h-U<<16>>16,k)|0)>>16,d=(g(q,q)|0)+d|0,U=(g(U+h<<16>>16,k)|0)>>16,c=(g(U,U)|0)+c|0,(d|0)<(u|0)|(c|0)<(u|0)?(q=e[N+4>>1]|0,U=(g(b-q<<16>>16,E)|0)>>16,U=(g(U,U)|0)+d|0,q=(g(q+b<<16>>16,E)|0)>>16,q=(g(q,q)|0)+c|0,c=e[N+6>>1]|0,d=(g(R-c<<16>>16,S)|0)>>16,d=U+(g(d,d)|0)|0,c=(g(c+R<<16>>16,S)|0)>>16,c=q+(g(c,c)|0)|0,q=(d|0)<(u|0),d=q?d:u,U=(c|0)<(d|0),d=U?c:d,m=q|U?O:m,r=U?1:q?0:r):d=u,O=O+1<<16>>16,!(O<<16>>16>=256);)u=d,N=N+8|0;for(d=m<<16>>16,c=d<<2,m=c|1,u=24072+(m<<1)|0,t=e[24072+(c<<1)>>1]|0,r<<16>>16?(e[A>>1]=0-(t&65535),e[F>>1]=0-(D[u>>1]|0),e[I>>1]=0-(D[24072+(m+1<<1)>>1]|0),e[B>>1]=0-(D[24072+((c|3)<<1)>>1]|0),r=d<<1&65534|1):(e[A>>1]=t,e[F>>1]=e[u>>1]|0,e[I>>1]=e[24072+(m+1<<1)>>1]|0,e[B>>1]=e[24072+((c|3)<<1)>>1]|0,r=d<<1),e[s+4>>1]=r,e[s+6>>1]=l5(j+12|0,l+12|0,26120,e[M+12>>1]|0,e[M+14>>1]|0,e[T+12>>1]|0,e[T+14>>1]|0,256)|0,e[s+8>>1]=l5(j+16|0,l+16|0,28168,e[M+16>>1]|0,e[M+18>>1]|0,e[T+16>>1]|0,e[T+18>>1]|0,64)|0,u=0,c=Y,d=K,t=z,r=j;U=D[t>>1]|0,e[c>>1]=U+(D[r>>1]|0),q=e[l>>1]|0,e[d>>1]=U+(q&65535),e[i+(u<<1)>>1]=q,u=u+1|0,(u|0)!=10;)c=c+2|0,d=d+2|0,t=t+2|0,r=r+2|0,l=l+2|0;P0(Y,205,10,f),P0(K,205,10,f),p0(Y,o,10,f),p0(K,n,10,f),C=H}function l5(i,r,t,o,n,s,f,l){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0,f=f|0,l=l|0;var u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0;if(h=e[i>>1]|0,O=i+2|0,b=e[O>>1]|0,R=e[r>>1]|0,N=r+2|0,S=e[N>>1]|0,l<<16>>16>0)for(w=o<<16>>16<<1,m=n<<16>>16<<1,d=s<<16>>16<<1,n=f<<16>>16<<1,s=2147483647,u=0,o=0,c=t;f=(g(w,h-(e[c>>1]|0)|0)|0)>>16,f=g(f,f)|0,(f|0)<(s|0)&&(k=(g(m,b-(e[c+2>>1]|0)|0)|0)>>16,k=(g(k,k)|0)+f|0,(k|0)<(s|0))&&(E=(g(d,R-(e[c+4>>1]|0)|0)|0)>>16,E=(g(E,E)|0)+k|0,(E|0)<(s|0))?(f=(g(n,S-(e[c+6>>1]|0)|0)|0)>>16,f=(g(f,f)|0)+E|0,A=(f|0)<(s|0),f=A?f:s,o=A?u:o):f=s,u=u+1<<16>>16,!(u<<16>>16>=l<<16>>16);)s=f,c=c+8|0;else o=0;return A=o<<16>>16<<2,l=A|1,e[i>>1]=e[t+(A<<1)>>1]|0,e[O>>1]=e[t+(l<<1)>>1]|0,e[r>>1]=e[t+(l+1<<1)>>1]|0,e[N>>1]=e[t+((A|3)<<1)>>1]|0,o|0}function Oi(i){i=i|0;var r=0,t=0,o=0;if(!i||(a[i>>2]=0,r=o0(20)|0,!r))return o=-1,o|0;t=r,o=t+20|0;do e[t>>1]=0,t=t+2|0;while((t|0)<(o|0));return a[i>>2]=r,o=0,o|0}function u4(i){i=i|0;var r=0;if(!i)return r=-1,r|0;r=i+20|0;do e[i>>1]=0,i=i+2|0;while((i|0)<(r|0));return r=0,r|0}function Ci(i){i=i|0;var r=0;i&&(r=a[i>>2]|0,r&&(G1(r),a[i>>2]=0))}function P0(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0;if(!(t<<16>>16<=0))for(n=r<<16>>16,s=r&65535,f=0;o=e[i>>1]|0,o<<16>>16<r<<16>>16?(e[i>>1]=r,o=(r<<16>>16)+n|0):o=(o&65535)+s|0,f=f+1<<16>>16,!(f<<16>>16>=t<<16>>16);)r=o&65535,i=i+2|0}function N2(i,r,t,o){i=i|0,r=r|0,t=t|0,o=o|0;var n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0;if(n=o<<16>>16,o=n>>>2&65535,!!(o<<16>>16))for(w=n+-1|0,S=i+20|0,k=r+(n+-4<<1)|0,b=r+(n+-3<<1)|0,E=r+(n+-2<<1)|0,R=r+(w<<1)|0,h=r+(n+-11<<1)|0,w=t+(w<<1)|0;;){for(r=e[S>>1]|0,f=5,l=S,u=h,c=h+-2|0,d=h+-4|0,m=h+-6|0,s=2048,i=2048,n=2048,t=2048;s=(g(e[u>>1]|0,r)|0)+s|0,i=(g(e[c>>1]|0,r)|0)+i|0,n=(g(e[d>>1]|0,r)|0)+n|0,r=(g(e[m>>1]|0,r)|0)+t|0,t=e[l+-2>>1]|0,s=s+(g(e[u+2>>1]|0,t)|0)|0,i=i+(g(e[c+2>>1]|0,t)|0)|0,n=n+(g(e[d+2>>1]|0,t)|0)|0,l=l+-4|0,t=r+(g(e[m+2>>1]|0,t)|0)|0,f=f+-1<<16>>16,r=e[l>>1]|0,f<<16>>16;)u=u+4|0,c=c+4|0,d=d+4|0,m=m+4|0;if(u=(g(e[R>>1]|0,r)|0)+s|0,c=(g(e[E>>1]|0,r)|0)+i|0,d=(g(e[b>>1]|0,r)|0)+n|0,m=(g(e[k>>1]|0,r)|0)+t|0,e[w>>1]=u>>>12,e[w+-2>>1]=c>>>12,e[w+-4>>1]=d>>>12,e[w+-6>>1]=m>>>12,o=o+-1<<16>>16,o<<16>>16)k=k+-8|0,b=b+-8|0,E=E+-8|0,R=R+-8|0,h=h+-8|0,w=w+-8|0;else break}}function S1(i,r){i=i|0,r=r|0;var t=0;return t=i+32768|0,(i|0)>-1&(t^i|0)<0&&(a[r>>2]=1,t=(i>>>31)+2147483647|0),t>>>16&65535|0}function X1(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0;return o=r<<16>>16,r<<16>>16?r<<16>>16>0?(i=i<<16>>16>>(r<<16>>16>15?15:o)&65535,i|0):(n=0-o|0,r=i<<16>>16,n=(n&65535)<<16>>16>15?15:n<<16>>16,o=r<<n,(o<<16>>16>>n|0)==(r|0)?(n=o&65535,n|0):(a[t>>2]=1,n=i<<16>>16>0?32767:-32768,n|0)):i|0}function V2(i,r,t){return i=i|0,r=r|0,t=t|0,r<<16>>16>15?(r=0,r|0):(t=X1(i,r,t)|0,r<<16>>16>0?t+((1<<(r<<16>>16)+-1&i<<16>>16|0)!=0&1)<<16>>16|0:(r=t,r|0))}function V5(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0;return(i|0)<1?(e[r>>1]=0,t=0,t|0):(n=(B1(i)|0)&65534,s=n&65535,n=n<<16>>16,s<<16>>16>0?(o=i<<n,(o>>n|0)!=(i|0)&&(o=i>>31^2147483647)):(n=0-n<<16,(n|0)<2031616?o=i>>(n>>16):o=0),e[r>>1]=s,r=o>>>25&63,r=r>>>0>15?r+-16|0:r,s=e[30216+(r<<1)>>1]|0,i=s<<16,o=g(s-(D[30216+(r+1<<1)>>1]|0)<<16>>16,o>>>10&32767)|0,(o|0)==1073741824?(a[t>>2]=1,n=2147483647):n=o<<1,o=i-n|0,((o^i)&(n^i)|0)>=0?(t=o,t|0):(a[t>>2]=1,t=(s>>>15&1)+2147483647|0,t|0))}function y1(i,r,t){return i=i|0,r=r|0,t=t|0,i=(i<<16>>16)-(r<<16>>16)|0,(i+32768|0)>>>0<=65535?(t=i,t=t&65535,t|0):(a[t>>2]=1,t=(i|0)>32767?32767:-32768,t=t&65535,t|0)}function f0(i,r,t,o,n,s){i=i|0,r=r|0,t=t|0,o=o|0,n=n|0,s=s|0;var f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0;B=C,C=C+48|0,w=B,u=w,f=n,l=u+20|0;do e[u>>1]=e[f>>1]|0,u=u+2|0,f=f+2|0;while((u|0)<(l|0));for(m=w+18|0,E=i+2|0,R=i+4|0,h=r+20|0,S=i+6|0,O=i+8|0,N=i+10|0,A=i+12|0,I=i+14|0,F=i+16|0,k=i+18|0,b=i+20|0,l=e[m>>1]|0,f=5,c=r,d=t,u=w+20|0;z=e[i>>1]|0,T=(g(z,e[c>>1]|0)|0)+2048|0,z=(g(e[c+2>>1]|0,z)|0)+2048|0,w=l<<16>>16,T=T-(g(w,e[E>>1]|0)|0)|0,M=e[R>>1]|0,w=z-(g(w,M)|0)|0,z=e[m+-2>>1]|0,M=T-(g(z,M)|0)|0,T=e[S>>1]|0,z=w-(g(T,z)|0)|0,w=e[m+-4>>1]|0,T=M-(g(w,T)|0)|0,M=e[O>>1]|0,w=z-(g(M,w)|0)|0,z=e[m+-6>>1]|0,M=T-(g(z,M)|0)|0,T=e[N>>1]|0,z=w-(g(z,T)|0)|0,w=e[m+-8>>1]|0,T=M-(g(w,T)|0)|0,M=e[A>>1]|0,w=z-(g(M,w)|0)|0,z=e[m+-10>>1]|0,M=T-(g(z,M)|0)|0,T=e[I>>1]|0,z=w-(g(T,z)|0)|0,w=e[m+-12>>1]|0,T=M-(g(w,T)|0)|0,M=e[F>>1]|0,w=z-(g(w,M)|0)|0,z=e[m+-14>>1]|0,M=T-(g(z,M)|0)|0,T=e[k>>1]|0,z=w-(g(T,z)|0)|0,w=e[m+-16>>1]|0,T=M-(g(w,T)|0)|0,M=e[b>>1]|0,w=z-(g(M,w)|0)|0,M=T-(g(e[m+-18>>1]|0,M)|0)|0,M=(M+134217728|0)>>>0<268435455?M>>>12&65535:(M|0)>134217727?32767:-32768,w=w-(g(e[E>>1]|0,M<<16>>16)|0)|0,m=u+2|0,e[u>>1]=M,e[d>>1]=M,l=(w+134217728|0)>>>0<268435455?w>>>12&65535:(w|0)>134217727?32767:-32768,e[m>>1]=l,e[d+2>>1]=l,f=f+-1<<16>>16,f<<16>>16;)c=c+4|0,d=d+4|0,u=u+4|0;if(o=(o<<16>>16)+-10|0,u=o>>>1&65535,u<<16>>16)for(w=t+18|0,l=r+16|0,m=e[w>>1]|0,c=h,f=t+20|0;;){M=e[i>>1]|0,d=(g(M,e[c>>1]|0)|0)+2048|0,M=(g(e[l+6>>1]|0,M)|0)+2048|0,l=e[E>>1]|0,T=m<<16>>16,d=d-(g(T,l)|0)|0,z=e[R>>1]|0,T=M-(g(T,z)|0)|0,M=e[w+-2>>1]|0,z=d-(g(M,z)|0)|0,d=e[S>>1]|0,M=T-(g(d,M)|0)|0,T=e[w+-4>>1]|0,d=z-(g(T,d)|0)|0,z=e[O>>1]|0,T=M-(g(z,T)|0)|0,M=e[w+-6>>1]|0,z=d-(g(M,z)|0)|0,d=e[N>>1]|0,M=T-(g(M,d)|0)|0,T=e[w+-8>>1]|0,d=z-(g(T,d)|0)|0,z=e[A>>1]|0,T=M-(g(z,T)|0)|0,M=e[w+-10>>1]|0,z=d-(g(M,z)|0)|0,d=e[I>>1]|0,M=T-(g(d,M)|0)|0,T=e[w+-12>>1]|0,d=z-(g(T,d)|0)|0,z=e[F>>1]|0,T=M-(g(T,z)|0)|0,M=e[w+-14>>1]|0,z=d-(g(M,z)|0)|0,d=e[k>>1]|0,M=T-(g(d,M)|0)|0,T=e[w+-16>>1]|0,d=z-(g(T,d)|0)|0,z=e[b>>1]|0,T=M-(g(z,T)|0)|0,z=d-(g(e[w+-18>>1]|0,z)|0)|0,d=c+4|0,z=(z+134217728|0)>>>0<268435455?z>>>12&65535:(z|0)>134217727?32767:-32768,l=T-(g(l,z<<16>>16)|0)|0,w=f+2|0,e[f>>1]=z;do if((l+134217728|0)>>>0>=268435455)if(f=f+4|0,(l|0)>134217727){e[w>>1]=32767,l=32767;break}else{e[w>>1]=-32768,l=-32768;break}else l=l>>>12&65535,e[w>>1]=l,f=f+4|0;while(!1);if(u=u+-1<<16>>16,u<<16>>16)z=c,m=l,c=d,l=z;else break}if(!(s<<16>>16)){C=B;return}u=n,f=t+(o<<1)|0,l=u+20|0;do e[u>>1]=e[f>>1]|0,u=u+2|0,f=f+2|0;while((u|0)<(l|0));C=B}function X0(i,r,t){i=i|0,r=r|0,t=t|0,e[t>>1]=e[i>>1]|0,e[t+2>>1]=((g(e[r>>1]|0,e[i+2>>1]|0)|0)+16384|0)>>>15,e[t+4>>1]=((g(e[r+2>>1]|0,e[i+4>>1]|0)|0)+16384|0)>>>15,e[t+6>>1]=((g(e[r+4>>1]|0,e[i+6>>1]|0)|0)+16384|0)>>>15,e[t+8>>1]=((g(e[r+6>>1]|0,e[i+8>>1]|0)|0)+16384|0)>>>15,e[t+10>>1]=((g(e[r+8>>1]|0,e[i+10>>1]|0)|0)+16384|0)>>>15,e[t+12>>1]=((g(e[r+10>>1]|0,e[i+12>>1]|0)|0)+16384|0)>>>15,e[t+14>>1]=((g(e[r+12>>1]|0,e[i+14>>1]|0)|0)+16384|0)>>>15,e[t+16>>1]=((g(e[r+14>>1]|0,e[i+16>>1]|0)|0)+16384|0)>>>15,e[t+18>>1]=((g(e[r+16>>1]|0,e[i+18>>1]|0)|0)+16384|0)>>>15,e[t+20>>1]=((g(e[r+18>>1]|0,e[i+20>>1]|0)|0)+16384|0)>>>15}function o0(i){i=i|0;var r=0,t=0,o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0,z=0,j=0,Y=0,K=0,H=0,U=0,q=0,i1=0,W=0,o1=0,a1=0,u1=0,d1=0,c1=0,t1=0,l1=0,w1=0,m1=0,Z=0,V=0;do if(i>>>0<245){if(R=i>>>0<11?16:i+11&-8,i=R>>>3,m=a[26]|0,u=m>>>i,u&3){o=(u&1^1)+i|0,r=o<<1,t=144+(r<<2)|0,r=144+(r+2<<2)|0,n=a[r>>2]|0,s=n+8|0,f=a[s>>2]|0;do if((t|0)==(f|0))a[26]=m&~(1<<o);else{if(f>>>0>=(a[30]|0)>>>0&&(d=f+12|0,(a[d>>2]|0)==(n|0))){a[d>>2]=t,a[r>>2]=f;break}v1()}while(!1);Z=o<<3,a[n+4>>2]=Z|3,Z=n+(Z|4)|0,a[Z>>2]=a[Z>>2]|1;break}if(r=a[28]|0,R>>>0>r>>>0){if(u){n=2<<i,n=u<<i&(n|0-n),n=(n&0-n)+-1|0,s=n>>>12&16,n=n>>>s,o=n>>>5&8,n=n>>>o,t=n>>>2&4,n=n>>>t,f=n>>>1&2,n=n>>>f,l=n>>>1&1,l=(o|s|t|f|l)+(n>>>l)|0,n=l<<1,f=144+(n<<2)|0,n=144+(n+2<<2)|0,t=a[n>>2]|0,s=t+8|0,o=a[s>>2]|0;do if((f|0)==(o|0))a[26]=m&~(1<<l),w=r;else{if(o>>>0>=(a[30]|0)>>>0&&(c=o+12|0,(a[c>>2]|0)==(t|0))){a[c>>2]=f,a[n>>2]=o,w=a[28]|0;break}v1()}while(!1);Z=l<<3,r=Z-R|0,a[t+4>>2]=R|3,u=t+R|0,a[t+(R|4)>>2]=r|1,a[t+Z>>2]=r,w&&(t=a[31]|0,o=w>>>3,f=o<<1,l=144+(f<<2)|0,n=a[26]|0,o=1<<o,n&o?(n=144+(f+2<<2)|0,f=a[n>>2]|0,f>>>0<(a[30]|0)>>>0?v1():(k=n,b=f)):(a[26]=n|o,k=144+(f+2<<2)|0,b=l),a[k>>2]=t,a[b+12>>2]=t,a[t+8>>2]=b,a[t+12>>2]=l),a[28]=r,a[31]=u;break}if(i=a[27]|0,i){for(n=(i&0-i)+-1|0,m1=n>>>12&16,n=n>>>m1,w1=n>>>5&8,n=n>>>w1,Z=n>>>2&4,n=n>>>Z,f=n>>>1&2,n=n>>>f,u=n>>>1&1,u=a[408+((w1|m1|Z|f|u)+(n>>>u)<<2)>>2]|0,n=(a[u+4>>2]&-8)-R|0,f=u;;){if(l=a[f+16>>2]|0,!l&&(l=a[f+20>>2]|0,!l)){r=n;break}f=(a[l+4>>2]&-8)-R|0,Z=f>>>0<n>>>0,n=Z?f:n,f=l,u=Z?l:u}if(i=a[30]|0,u>>>0>=i>>>0&&(N=u+R|0,u>>>0<N>>>0)){o=a[u+24>>2]|0,l=a[u+12>>2]|0;do if((l|0)==(u|0)){if(f=u+20|0,l=a[f>>2]|0,!l&&(f=u+16|0,l=a[f>>2]|0,!l)){S=0;break}for(;;){if(s=l+20|0,n=a[s>>2]|0,n){l=n,f=s;continue}if(s=l+16|0,n=a[s>>2]|0,n)l=n,f=s;else break}if(f>>>0<i>>>0)v1();else{a[f>>2]=0,S=l;break}}else{if(f=a[u+8>>2]|0,f>>>0>=i>>>0&&(t=f+12|0,(a[t>>2]|0)==(u|0))&&(h=l+8|0,(a[h>>2]|0)==(u|0))){a[t>>2]=l,a[h>>2]=f,S=l;break}v1()}while(!1);do if(o){if(f=a[u+28>>2]|0,s=408+(f<<2)|0,(u|0)==(a[s>>2]|0)){if(a[s>>2]=S,!S){a[27]=a[27]&~(1<<f);break}}else if(o>>>0<(a[30]|0)>>>0&&v1(),f=o+16|0,(a[f>>2]|0)==(u|0)?a[f>>2]=S:a[o+20>>2]=S,!S)break;s=a[30]|0,S>>>0<s>>>0&&v1(),a[S+24>>2]=o,f=a[u+16>>2]|0;do if(f)if(f>>>0<s>>>0)v1();else{a[S+16>>2]=f,a[f+24>>2]=S;break}while(!1);if(f=a[u+20>>2]|0,f)if(f>>>0<(a[30]|0)>>>0)v1();else{a[S+20>>2]=f,a[f+24>>2]=S;break}}while(!1);r>>>0<16?(Z=r+R|0,a[u+4>>2]=Z|3,Z=u+(Z+4)|0,a[Z>>2]=a[Z>>2]|1):(a[u+4>>2]=R|3,a[u+(R|4)>>2]=r|1,a[u+(r+R)>>2]=r,o=a[28]|0,o&&(t=a[31]|0,n=o>>>3,f=n<<1,l=144+(f<<2)|0,s=a[26]|0,n=1<<n,s&n?(f=144+(f+2<<2)|0,s=a[f>>2]|0,s>>>0<(a[30]|0)>>>0?v1():(O=f,A=s)):(a[26]=s|n,O=144+(f+2<<2)|0,A=l),a[O>>2]=t,a[A+12>>2]=t,a[t+8>>2]=A,a[t+12>>2]=l),a[28]=r,a[31]=N),s=u+8|0;break}v1()}else V=154}else V=154}else if(i>>>0<=4294967231)if(i=i+11|0,A=i&-8,m=a[27]|0,m){u=0-A|0,i=i>>>8,i?A>>>0>16777215?d=31:(N=(i+1048320|0)>>>16&8,V=i<<N,O=(V+520192|0)>>>16&4,V=V<<O,d=(V+245760|0)>>>16&2,d=14-(O|N|d)+(V<<d>>>15)|0,d=A>>>(d+7|0)&1|d<<1):d=0,i=a[408+(d<<2)>>2]|0;e:do if(!i)l=0,i=0,V=86;else for(t=u,l=0,r=A<<((d|0)==31?0:25-(d>>>1)|0),c=i,i=0;;){if(o=a[c+4>>2]&-8,u=o-A|0,u>>>0<t>>>0)if((o|0)==(A|0)){o=c,i=c,V=90;break e}else i=c;else u=t;if(V=a[c+20>>2]|0,c=a[c+16+(r>>>31<<2)>>2]|0,l=(V|0)==0|(V|0)==(c|0)?l:V,c)t=u,r=r<<1;else{V=86;break}}while(!1);if((V|0)==86){if((l|0)==0&(i|0)==0){if(i=2<<d,i=m&(i|0-i),!i){R=A,V=154;break}i=(i&0-i)+-1|0,S=i>>>12&16,i=i>>>S,b=i>>>5&8,i=i>>>b,O=i>>>2&4,i=i>>>O,N=i>>>1&2,i=i>>>N,l=i>>>1&1,l=a[408+((b|S|O|N|l)+(i>>>l)<<2)>>2]|0,i=0}l?(o=l,V=90):(b=u,k=i)}if((V|0)==90)for(;;){if(V=0,N=(a[o+4>>2]&-8)-A|0,l=N>>>0<u>>>0,u=l?N:u,i=l?o:i,l=a[o+16>>2]|0,l){o=l,V=90;continue}if(o=a[o+20>>2]|0,o)V=90;else{b=u,k=i;break}}if(k|0&&b>>>0<((a[28]|0)-A|0)>>>0){if(i=a[30]|0,k>>>0>=i>>>0&&(U=k+A|0,k>>>0<U>>>0)){u=a[k+24>>2]|0,l=a[k+12>>2]|0;do if((l|0)==(k|0)){if(f=k+20|0,l=a[f>>2]|0,!l&&(f=k+16|0,l=a[f>>2]|0,!l)){F=0;break}for(;;){if(s=l+20|0,n=a[s>>2]|0,n){l=n,f=s;continue}if(s=l+16|0,n=a[s>>2]|0,n)l=n,f=s;else break}if(f>>>0<i>>>0)v1();else{a[f>>2]=0,F=l;break}}else{if(f=a[k+8>>2]|0,f>>>0>=i>>>0&&(E=f+12|0,(a[E>>2]|0)==(k|0))&&(R=l+8|0,(a[R>>2]|0)==(k|0))){a[E>>2]=l,a[R>>2]=f,F=l;break}v1()}while(!1);do if(u){if(l=a[k+28>>2]|0,f=408+(l<<2)|0,(k|0)==(a[f>>2]|0)){if(a[f>>2]=F,!F){a[27]=a[27]&~(1<<l);break}}else if(u>>>0<(a[30]|0)>>>0&&v1(),f=u+16|0,(a[f>>2]|0)==(k|0)?a[f>>2]=F:a[u+20>>2]=F,!F)break;l=a[30]|0,F>>>0<l>>>0&&v1(),a[F+24>>2]=u,f=a[k+16>>2]|0;do if(f)if(f>>>0<l>>>0)v1();else{a[F+16>>2]=f,a[f+24>>2]=F;break}while(!1);if(f=a[k+20>>2]|0,f)if(f>>>0<(a[30]|0)>>>0)v1();else{a[F+20>>2]=f,a[f+24>>2]=F;break}}while(!1);e:do if(b>>>0>=16){if(a[k+4>>2]=A|3,a[k+(A|4)>>2]=b|1,a[k+(b+A)>>2]=b,l=b>>>3,b>>>0<256){s=l<<1,o=144+(s<<2)|0,n=a[26]|0,f=1<<l,n&f?(f=144+(s+2<<2)|0,s=a[f>>2]|0,s>>>0<(a[30]|0)>>>0?v1():(B=f,M=s)):(a[26]=n|f,B=144+(s+2<<2)|0,M=o),a[B>>2]=U,a[M+12>>2]=U,a[k+(A+8)>>2]=M,a[k+(A+12)>>2]=o;break}if(t=b>>>8,t?b>>>0>16777215?l=31:(m1=(t+1048320|0)>>>16&8,Z=t<<m1,w1=(Z+520192|0)>>>16&4,Z=Z<<w1,l=(Z+245760|0)>>>16&2,l=14-(w1|m1|l)+(Z<<l>>>15)|0,l=b>>>(l+7|0)&1|l<<1):l=0,f=408+(l<<2)|0,a[k+(A+28)>>2]=l,a[k+(A+20)>>2]=0,a[k+(A+16)>>2]=0,s=a[27]|0,n=1<<l,!(s&n)){a[27]=s|n,a[f>>2]=U,a[k+(A+24)>>2]=f,a[k+(A+12)>>2]=U,a[k+(A+8)>>2]=U;break}t=a[f>>2]|0;i:do if((a[t+4>>2]&-8|0)!=(b|0)){for(l=b<<((l|0)==31?0:25-(l>>>1)|0);r=t+16+(l>>>31<<2)|0,f=a[r>>2]|0,!!f;)if((a[f+4>>2]&-8|0)==(b|0)){z=f;break i}else l=l<<1,t=f;if(r>>>0<(a[30]|0)>>>0)v1();else{a[r>>2]=U,a[k+(A+24)>>2]=t,a[k+(A+12)>>2]=U,a[k+(A+8)>>2]=U;break e}}else z=t;while(!1);if(t=z+8|0,r=a[t>>2]|0,Z=a[30]|0,r>>>0>=Z>>>0&z>>>0>=Z>>>0){a[r+12>>2]=U,a[t>>2]=U,a[k+(A+8)>>2]=r,a[k+(A+12)>>2]=z,a[k+(A+24)>>2]=0;break}else v1()}else Z=b+A|0,a[k+4>>2]=Z|3,Z=k+(Z+4)|0,a[Z>>2]=a[Z>>2]|1;while(!1);s=k+8|0;break}v1()}else R=A,V=154}else R=A,V=154;else R=-1,V=154;while(!1);e:do if((V|0)==154){if(i=a[28]|0,i>>>0>=R>>>0){r=i-R|0,t=a[31]|0,r>>>0>15?(a[31]=t+R,a[28]=r,a[t+(R+4)>>2]=r|1,a[t+i>>2]=r,a[t+4>>2]=R|3):(a[28]=0,a[31]=0,a[t+4>>2]=i|3,V=t+(i+4)|0,a[V>>2]=a[V>>2]|1),s=t+8|0;break}if(i=a[29]|0,i>>>0>R>>>0){V=i-R|0,a[29]=V,s=a[32]|0,a[32]=s+R,a[s+(R+4)>>2]=V|1,a[s+4>>2]=R|3,s=s+8|0;break}if(a[144]|0||Li(),m=R+48|0,t=a[146]|0,d=R+47|0,o=t+d|0,t=0-t|0,c=o&t,c>>>0>R>>>0){if(i=a[136]|0,i|0&&(z=a[134]|0,U=z+c|0,U>>>0<=z>>>0|U>>>0>i>>>0)){s=0;break}i:do if(a[137]&4)i=0,V=189;else{i=a[32]|0;t:do if(i){for(l=552;;){if(u=a[l>>2]|0,u>>>0<=i>>>0&&(I=l+4|0,(u+(a[I>>2]|0)|0)>>>0>i>>>0)){s=l,i=I;break}if(l=a[l+8>>2]|0,!l){V=172;break t}}if(u=o-(a[29]|0)&t,u>>>0<2147483647)if(l=m2(u|0)|0,U=(l|0)==((a[s>>2]|0)+(a[i>>2]|0)|0),i=U?u:0,U){if((l|0)!=-1){M=l,S=i,V=192;break i}}else V=182;else i=0}else V=172;while(!1);do if((V|0)==172)if(s=m2(0)|0,(s|0)!=-1)if(i=s,u=a[145]|0,l=u+-1|0,l&i?u=c-i+(l+i&0-u)|0:u=c,i=a[134]|0,l=i+u|0,u>>>0>R>>>0&u>>>0<2147483647){if(U=a[136]|0,U|0&&l>>>0<=i>>>0|l>>>0>U>>>0){i=0;break}if(l=m2(u|0)|0,V=(l|0)==(s|0),i=V?u:0,V){M=s,S=i,V=192;break i}else V=182}else i=0;else i=0;while(!1);t:do if((V|0)==182){s=0-u|0;do if(m>>>0>u>>>0&(u>>>0<2147483647&(l|0)!=-1)&&(T=a[146]|0,T=d-u+T&0-T,T>>>0<2147483647))if((m2(T|0)|0)==-1){m2(s|0)|0;break t}else{u=T+u|0;break}while(!1);if((l|0)!=-1){M=l,S=u,V=192;break i}}while(!1);a[137]=a[137]|4,V=189}while(!1);if((V|0)==189&&c>>>0<2147483647&&(j=m2(c|0)|0,Y=m2(0)|0,j>>>0<Y>>>0&((j|0)!=-1&(Y|0)!=-1))&&(K=Y-j|0,H=K>>>0>(R+40|0)>>>0,H)&&(M=j,S=H?K:i,V=192),(V|0)==192){u=(a[134]|0)+S|0,a[134]=u,u>>>0>(a[135]|0)>>>0&&(a[135]=u),b=a[32]|0;i:do if(b){s=552;do{if(i=a[s>>2]|0,u=s+4|0,l=a[u>>2]|0,(M|0)==(i+l|0)){q=i,i1=u,W=l,o1=s,V=202;break}s=a[s+8>>2]|0}while(s|0);if((V|0)==202&&!(a[o1+12>>2]&8|0)&&b>>>0<M>>>0&b>>>0>=q>>>0){a[i1>>2]=W+S,V=(a[29]|0)+S|0,Z=b+8|0,Z=Z&7|0?0-Z&7:0,m1=V-Z|0,a[32]=b+Z,a[29]=m1,a[b+(Z+4)>>2]=m1|1,a[b+(V+4)>>2]=40,a[33]=a[148];break}for(u=a[30]|0,M>>>0<u>>>0&&(a[30]=M,u=M),l=M+S|0,i=552;;){if((a[i>>2]|0)==(l|0)){s=i,l=i,V=210;break}if(i=a[i+8>>2]|0,!i){l=552;break}}if((V|0)==210)if(a[l+12>>2]&8)l=552;else{a[s>>2]=M,h=l+4|0,a[h>>2]=(a[h>>2]|0)+S,h=M+8|0,h=h&7|0?0-h&7:0,d=M+(S+8)|0,d=d&7|0?0-d&7:0,l=M+(d+S)|0,k=h+R|0,w=M+k|0,i=l-(M+h)-R|0,a[M+(h+4)>>2]=R|3;t:do if((l|0)!=(b|0)){if((l|0)==(a[31]|0)){V=(a[28]|0)+i|0,a[28]=V,a[31]=w,a[M+(k+4)>>2]=V|1,a[M+(V+k)>>2]=V;break}if(r=S+4|0,f=a[M+(r+d)>>2]|0,(f&3|0)==1){c=f&-8,o=f>>>3;r:do if(f>>>0>=256){t=a[M+((d|24)+S)>>2]|0,s=a[M+(S+12+d)>>2]|0;o:do if((s|0)==(l|0)){if(n=d|16,s=M+(r+n)|0,f=a[s>>2]|0,!f&&(s=M+(n+S)|0,f=a[s>>2]|0,!f)){l1=0;break}for(;;){if(n=f+20|0,o=a[n>>2]|0,o){f=o,s=n;continue}if(n=f+16|0,o=a[n>>2]|0,o)f=o,s=n;else break}if(s>>>0<u>>>0)v1();else{a[s>>2]=0,l1=f;break}}else{n=a[M+((d|8)+S)>>2]|0;do if(n>>>0>=u>>>0){if(u=n+12|0,(a[u>>2]|0)!=(l|0)||(f=s+8|0,(a[f>>2]|0)!=(l|0)))break;a[u>>2]=s,a[f>>2]=n,l1=s;break o}while(!1);v1()}while(!1);if(!t)break;u=a[M+(S+28+d)>>2]|0,f=408+(u<<2)|0;do if((l|0)!=(a[f>>2]|0)){if(t>>>0<(a[30]|0)>>>0&&v1(),f=t+16|0,(a[f>>2]|0)==(l|0)?a[f>>2]=l1:a[t+20>>2]=l1,!l1)break r}else{if(a[f>>2]=l1,l1)break;a[27]=a[27]&~(1<<u);break r}while(!1);u=a[30]|0,l1>>>0<u>>>0&&v1(),a[l1+24>>2]=t,l=d|16,f=a[M+(l+S)>>2]|0;do if(f)if(f>>>0<u>>>0)v1();else{a[l1+16>>2]=f,a[f+24>>2]=l1;break}while(!1);if(l=a[M+(r+l)>>2]|0,!l)break;if(l>>>0<(a[30]|0)>>>0)v1();else{a[l1+20>>2]=l,a[l+24>>2]=l1;break}}else{f=a[M+((d|8)+S)>>2]|0,s=a[M+(S+12+d)>>2]|0,n=144+(o<<1<<2)|0;do if((f|0)!=(n|0)){if(f>>>0>=u>>>0&&(a[f+12>>2]|0)==(l|0))break;v1()}while(!1);if((s|0)==(f|0)){a[26]=a[26]&~(1<<o);break}do if((s|0)==(n|0))a1=s+8|0;else{if(s>>>0>=u>>>0&&(u1=s+8|0,(a[u1>>2]|0)==(l|0))){a1=u1;break}v1()}while(!1);a[f+12>>2]=s,a[a1>>2]=f}while(!1);l=M+((c|d)+S)|0,i=c+i|0}if(l=l+4|0,a[l>>2]=a[l>>2]&-2,a[M+(k+4)>>2]=i|1,a[M+(i+k)>>2]=i,l=i>>>3,i>>>0<256){s=l<<1,o=144+(s<<2)|0,n=a[26]|0,f=1<<l;do if(!(n&f))a[26]=n|f,w1=144+(s+2<<2)|0,m1=o;else{if(f=144+(s+2<<2)|0,s=a[f>>2]|0,s>>>0>=(a[30]|0)>>>0){w1=f,m1=s;break}v1()}while(!1);a[w1>>2]=w,a[m1+12>>2]=w,a[M+(k+8)>>2]=m1,a[M+(k+12)>>2]=o;break}t=i>>>8;do if(!t)l=0;else{if(i>>>0>16777215){l=31;break}m1=(t+1048320|0)>>>16&8,V=t<<m1,w1=(V+520192|0)>>>16&4,V=V<<w1,l=(V+245760|0)>>>16&2,l=14-(w1|m1|l)+(V<<l>>>15)|0,l=i>>>(l+7|0)&1|l<<1}while(!1);if(f=408+(l<<2)|0,a[M+(k+28)>>2]=l,a[M+(k+20)>>2]=0,a[M+(k+16)>>2]=0,s=a[27]|0,n=1<<l,!(s&n)){a[27]=s|n,a[f>>2]=w,a[M+(k+24)>>2]=f,a[M+(k+12)>>2]=w,a[M+(k+8)>>2]=w;break}t=a[f>>2]|0;r:do if((a[t+4>>2]&-8|0)!=(i|0)){for(l=i<<((l|0)==31?0:25-(l>>>1)|0);r=t+16+(l>>>31<<2)|0,f=a[r>>2]|0,!!f;)if((a[f+4>>2]&-8|0)==(i|0)){Z=f;break r}else l=l<<1,t=f;if(r>>>0<(a[30]|0)>>>0)v1();else{a[r>>2]=w,a[M+(k+24)>>2]=t,a[M+(k+12)>>2]=w,a[M+(k+8)>>2]=w;break t}}else Z=t;while(!1);if(t=Z+8|0,r=a[t>>2]|0,V=a[30]|0,r>>>0>=V>>>0&Z>>>0>=V>>>0){a[r+12>>2]=w,a[t>>2]=w,a[M+(k+8)>>2]=r,a[M+(k+12)>>2]=Z,a[M+(k+24)>>2]=0;break}else v1()}else V=(a[29]|0)+i|0,a[29]=V,a[32]=w,a[M+(k+4)>>2]=V|1;while(!1);s=M+(h|8)|0;break e}for(;s=a[l>>2]|0,!(s>>>0<=b>>>0&&(f=a[l+4>>2]|0,n=s+f|0,n>>>0>b>>>0));)l=a[l+8>>2]|0;if(l=s+(f+-39)|0,l=s+(f+-47+(l&7|0?0-l&7:0))|0,u=b+16|0,l=l>>>0<u>>>0?b:l,f=l+8|0,s=M+8|0,s=s&7|0?0-s&7:0,V=S+-40-s|0,a[32]=M+s,a[29]=V,a[M+(s+4)>>2]=V|1,a[M+(S+-36)>>2]=40,a[33]=a[148],s=l+4|0,a[s>>2]=27,a[f>>2]=a[138],a[f+4>>2]=a[139],a[f+8>>2]=a[140],a[f+12>>2]=a[141],a[138]=M,a[139]=S,a[141]=0,a[140]=f,f=l+28|0,a[f>>2]=7,(l+32|0)>>>0<n>>>0)do V=f,f=f+4|0,a[f>>2]=7;while((V+8|0)>>>0<n>>>0);if((l|0)!=(b|0)){if(i=l-b|0,a[s>>2]=a[s>>2]&-2,a[b+4>>2]=i|1,a[l>>2]=i,n=i>>>3,i>>>0<256){f=n<<1,l=144+(f<<2)|0,s=a[26]|0,o=1<<n,s&o?(t=144+(f+2<<2)|0,r=a[t>>2]|0,r>>>0<(a[30]|0)>>>0?v1():(d1=t,c1=r)):(a[26]=s|o,d1=144+(f+2<<2)|0,c1=l),a[d1>>2]=b,a[c1+12>>2]=b,a[b+8>>2]=c1,a[b+12>>2]=l;break}if(t=i>>>8,t?i>>>0>16777215?f=31:(Z=(t+1048320|0)>>>16&8,V=t<<Z,m1=(V+520192|0)>>>16&4,V=V<<m1,f=(V+245760|0)>>>16&2,f=14-(m1|Z|f)+(V<<f>>>15)|0,f=i>>>(f+7|0)&1|f<<1):f=0,o=408+(f<<2)|0,a[b+28>>2]=f,a[b+20>>2]=0,a[u>>2]=0,t=a[27]|0,r=1<<f,!(t&r)){a[27]=t|r,a[o>>2]=b,a[b+24>>2]=o,a[b+12>>2]=b,a[b+8>>2]=b;break}t=a[o>>2]|0;t:do if((a[t+4>>2]&-8|0)!=(i|0)){for(f=i<<((f|0)==31?0:25-(f>>>1)|0);r=t+16+(f>>>31<<2)|0,o=a[r>>2]|0,!!o;)if((a[o+4>>2]&-8|0)==(i|0)){t1=o;break t}else f=f<<1,t=o;if(r>>>0<(a[30]|0)>>>0)v1();else{a[r>>2]=b,a[b+24>>2]=t,a[b+12>>2]=b,a[b+8>>2]=b;break i}}else t1=t;while(!1);if(t=t1+8|0,r=a[t>>2]|0,V=a[30]|0,r>>>0>=V>>>0&t1>>>0>=V>>>0){a[r+12>>2]=b,a[t>>2]=b,a[b+8>>2]=r,a[b+12>>2]=t1,a[b+24>>2]=0;break}else v1()}}else{V=a[30]|0,(V|0)==0|M>>>0<V>>>0&&(a[30]=M),a[138]=M,a[139]=S,a[141]=0,a[35]=a[144],a[34]=-1,t=0;do V=t<<1,Z=144+(V<<2)|0,a[144+(V+3<<2)>>2]=Z,a[144+(V+2<<2)>>2]=Z,t=t+1|0;while((t|0)!=32);V=M+8|0,V=V&7|0?0-V&7:0,Z=S+-40-V|0,a[32]=M+V,a[29]=Z,a[M+(V+4)>>2]=Z|1,a[M+(S+-36)>>2]=40,a[33]=a[148]}while(!1);if(r=a[29]|0,r>>>0>R>>>0){V=r-R|0,a[29]=V,s=a[32]|0,a[32]=s+R,a[s+(R+4)>>2]=V|1,a[s+4>>2]=R|3,s=s+8|0;break}}a[(c4()|0)>>2]=12,s=0}else s=0}while(!1);return s|0}function G1(i){i=i|0;var r=0,t=0,o=0,n=0,s=0,f=0,l=0,u=0,c=0,d=0,m=0,w=0,h=0,k=0,b=0,E=0,R=0,S=0,O=0,N=0,A=0,I=0,F=0,B=0,M=0,T=0;e:do if(i){n=i+-8|0,c=a[30]|0;i:do if(n>>>0>=c>>>0&&(o=a[i+-4>>2]|0,t=o&3,(t|0)!=1)){N=o&-8,A=i+(N+-8)|0;do if(o&1)T=n,s=N;else{if(n=a[n>>2]|0,!t)break e;if(d=-8-n|0,w=i+d|0,h=n+N|0,w>>>0<c>>>0)break i;if((w|0)==(a[31]|0)){if(s=i+(N+-4)|0,n=a[s>>2]|0,(n&3|0)!=3){T=w,s=h;break}a[28]=h,a[s>>2]=n&-2,a[i+(d+4)>>2]=h|1,a[A>>2]=h;break e}if(t=n>>>3,n>>>0<256){o=a[i+(d+8)>>2]|0,s=a[i+(d+12)>>2]|0,n=144+(t<<1<<2)|0;do if((o|0)!=(n|0)){if(o>>>0>=c>>>0&&(a[o+12>>2]|0)==(w|0))break;v1()}while(!1);if((s|0)==(o|0)){a[26]=a[26]&~(1<<t),T=w,s=h;break}do if((s|0)==(n|0))r=s+8|0;else{if(s>>>0>=c>>>0&&(f=s+8|0,(a[f>>2]|0)==(w|0))){r=f;break}v1()}while(!1);a[o+12>>2]=s,a[r>>2]=o,T=w,s=h;break}f=a[i+(d+24)>>2]|0,n=a[i+(d+12)>>2]|0;do if((n|0)==(w|0)){if(o=i+(d+20)|0,n=a[o>>2]|0,!n&&(o=i+(d+16)|0,n=a[o>>2]|0,!n)){m=0;break}for(;;){if(t=n+20|0,r=a[t>>2]|0,r){n=r,o=t;continue}if(t=n+16|0,r=a[t>>2]|0,r)n=r,o=t;else break}if(o>>>0<c>>>0)v1();else{a[o>>2]=0,m=n;break}}else{if(o=a[i+(d+8)>>2]|0,o>>>0>=c>>>0&&(l=o+12|0,(a[l>>2]|0)==(w|0))&&(u=n+8|0,(a[u>>2]|0)==(w|0))){a[l>>2]=n,a[u>>2]=o,m=n;break}v1()}while(!1);if(f){if(n=a[i+(d+28)>>2]|0,o=408+(n<<2)|0,(w|0)==(a[o>>2]|0)){if(a[o>>2]=m,!m){a[27]=a[27]&~(1<<n),T=w,s=h;break}}else if(f>>>0<(a[30]|0)>>>0&&v1(),n=f+16|0,(a[n>>2]|0)==(w|0)?a[n>>2]=m:a[f+20>>2]=m,!m){T=w,s=h;break}o=a[30]|0,m>>>0<o>>>0&&v1(),a[m+24>>2]=f,n=a[i+(d+16)>>2]|0;do if(n)if(n>>>0<o>>>0)v1();else{a[m+16>>2]=n,a[n+24>>2]=m;break}while(!1);if(n=a[i+(d+20)>>2]|0,n)if(n>>>0<(a[30]|0)>>>0)v1();else{a[m+20>>2]=n,a[n+24>>2]=m,T=w,s=h;break}else T=w,s=h}else T=w,s=h}while(!1);if(T>>>0<A>>>0&&(k=i+(N+-4)|0,b=a[k>>2]|0,(b&1|0)!=0)){if(b&2)a[k>>2]=b&-2,a[T+4>>2]=s|1,a[T+s>>2]=s;else{if((A|0)==(a[32]|0)){if(M=(a[29]|0)+s|0,a[29]=M,a[32]=T,a[T+4>>2]=M|1,(T|0)!=(a[31]|0))break e;a[31]=0,a[28]=0;break e}if((A|0)==(a[31]|0)){M=(a[28]|0)+s|0,a[28]=M,a[31]=T,a[T+4>>2]=M|1,a[T+M>>2]=M;break e}u=(b&-8)+s|0,t=b>>>3;do if(b>>>0>=256){r=a[i+(N+16)>>2]|0,s=a[i+(N|4)>>2]|0;do if((s|0)==(A|0)){if(n=i+(N+12)|0,s=a[n>>2]|0,!s&&(n=i+(N+8)|0,s=a[n>>2]|0,!s)){I=0;break}for(;;){if(o=s+20|0,t=a[o>>2]|0,t){s=t,n=o;continue}if(o=s+16|0,t=a[o>>2]|0,t)s=t,n=o;else break}if(n>>>0<(a[30]|0)>>>0)v1();else{a[n>>2]=0,I=s;break}}else{if(n=a[i+N>>2]|0,n>>>0>=(a[30]|0)>>>0&&(S=n+12|0,(a[S>>2]|0)==(A|0))&&(O=s+8|0,(a[O>>2]|0)==(A|0))){a[S>>2]=s,a[O>>2]=n,I=s;break}v1()}while(!1);if(r){if(s=a[i+(N+20)>>2]|0,n=408+(s<<2)|0,(A|0)==(a[n>>2]|0)){if(a[n>>2]=I,!I){a[27]=a[27]&~(1<<s);break}}else if(r>>>0<(a[30]|0)>>>0&&v1(),s=r+16|0,(a[s>>2]|0)==(A|0)?a[s>>2]=I:a[r+20>>2]=I,!I)break;s=a[30]|0,I>>>0<s>>>0&&v1(),a[I+24>>2]=r,n=a[i+(N+8)>>2]|0;do if(n)if(n>>>0<s>>>0)v1();else{a[I+16>>2]=n,a[n+24>>2]=I;break}while(!1);if(t=a[i+(N+12)>>2]|0,t)if(t>>>0<(a[30]|0)>>>0)v1();else{a[I+20>>2]=t,a[t+24>>2]=I;break}}}else{o=a[i+N>>2]|0,s=a[i+(N|4)>>2]|0,n=144+(t<<1<<2)|0;do if((o|0)!=(n|0)){if(o>>>0>=(a[30]|0)>>>0&&(a[o+12>>2]|0)==(A|0))break;v1()}while(!1);if((s|0)==(o|0)){a[26]=a[26]&~(1<<t);break}do if((s|0)==(n|0))E=s+8|0;else{if(s>>>0>=(a[30]|0)>>>0&&(R=s+8|0,(a[R>>2]|0)==(A|0))){E=R;break}v1()}while(!1);a[o+12>>2]=s,a[E>>2]=o}while(!1);if(a[T+4>>2]=u|1,a[T+u>>2]=u,(T|0)==(a[31]|0)){a[28]=u;break e}else s=u}if(n=s>>>3,s>>>0<256){o=n<<1,s=144+(o<<2)|0,r=a[26]|0,t=1<<n,r&t?(t=144+(o+2<<2)|0,r=a[t>>2]|0,r>>>0<(a[30]|0)>>>0?v1():(F=t,B=r)):(a[26]=r|t,F=144+(o+2<<2)|0,B=s),a[F>>2]=T,a[B+12>>2]=T,a[T+8>>2]=B,a[T+12>>2]=s;break e}r=s>>>8,r?s>>>0>16777215?n=31:(F=(r+1048320|0)>>>16&8,B=r<<F,i=(B+520192|0)>>>16&4,B=B<<i,n=(B+245760|0)>>>16&2,n=14-(i|F|n)+(B<<n>>>15)|0,n=s>>>(n+7|0)&1|n<<1):n=0,t=408+(n<<2)|0,a[T+28>>2]=n,a[T+20>>2]=0,a[T+16>>2]=0,r=a[27]|0,o=1<<n;t:do if(r&o){t=a[t>>2]|0;r:do if((a[t+4>>2]&-8|0)!=(s|0)){for(n=s<<((n|0)==31?0:25-(n>>>1)|0);r=t+16+(n>>>31<<2)|0,o=a[r>>2]|0,!!o;)if((a[o+4>>2]&-8|0)==(s|0)){M=o;break r}else n=n<<1,t=o;if(r>>>0<(a[30]|0)>>>0)v1();else{a[r>>2]=T,a[T+24>>2]=t,a[T+12>>2]=T,a[T+8>>2]=T;break t}}else M=t;while(!1);if(r=M+8|0,t=a[r>>2]|0,B=a[30]|0,t>>>0>=B>>>0&M>>>0>=B>>>0){a[t+12>>2]=T,a[r>>2]=T,a[T+8>>2]=t,a[T+12>>2]=M,a[T+24>>2]=0;break}else v1()}else a[27]=r|o,a[t>>2]=T,a[T+24>>2]=t,a[T+12>>2]=T,a[T+8>>2]=T;while(!1);if(T=(a[34]|0)+-1|0,a[34]=T,!T)r=560;else break e;for(;r=a[r>>2]|0,r;)r=r+8|0;a[34]=-1;break e}}while(!1);v1()}while(!1)}function c4(){var i=0;return i=600,i|0}function Li(){var i=0;do if(!(a[144]|0))if(i=a3(30)|0,i+-1&i)v1();else{a[146]=i,a[145]=i,a[147]=-1,a[148]=-1,a[149]=0,a[137]=0,a[144]=(f3(0)|0)&-16^1431655768;break}while(!1)}function Ti(){}function R0(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;if((t|0)>=4096)return l3(i|0,r|0,t|0)|0;if(o=i|0,(i&3)==(r&3)){for(;i&3;){if(!t)return o|0;_[i>>0]=_[r>>0]|0,i=i+1|0,r=r+1|0,t=t-1|0}for(;(t|0)>=4;)a[i>>2]=a[r>>2],i=i+4|0,r=r+4|0,t=t-4|0}for(;(t|0)>0;)_[i>>0]=_[r>>0]|0,i=i+1|0,r=r+1|0,t=t-1|0;return o|0}function i2(i,r,t){i=i|0,r=r|0,t=t|0;var o=0;if((r|0)<(i|0)&(i|0)<(r+t|0)){for(o=i,r=r+t|0,i=i+t|0;(t|0)>0;)i=i-1|0,r=r-1|0,t=t-1|0,_[i>>0]=_[r>>0]|0;i=o}else R0(i,r,t)|0;return i|0}function t2(i,r,t){i=i|0,r=r|0,t=t|0;var o=0,n=0,s=0,f=0;if(o=i+t|0,(t|0)>=20){if(r=r&255,s=i&3,f=r|r<<8|r<<16|r<<24,n=o&-4,s)for(s=i+4-s|0;(i|0)<(s|0);)_[i>>0]=r,i=i+1|0;for(;(i|0)<(n|0);)a[i>>2]=f,i=i+4|0}for(;(i|0)<(o|0);)_[i>>0]=r,i=i+1|0;return i-t|0}return{_free:G1,___errno_location:c4,_memmove:i2,_Decoder_Interface_Decode:v3,_Decoder_Interface_exit:b3,_Encoder_Interface_init:E3,_memset:t2,_malloc:o0,_memcpy:R0,_Encoder_Interface_exit:y3,_Decoder_Interface_init:k3,_Encoder_Interface_Encode:g3,runPostSets:Ti,stackAlloc:u3,stackSave:c3,stackRestore:d3,establishStackSpace:h3,setThrew:w3,setTempRet0:m3,getTempRet0:p3}}(L.asmGlobalArg,L.asmLibraryArg,z0);L._Encoder_Interface_Encode=t0._Encoder_Interface_Encode;var E5=L._free=t0._free;L.runPostSets=t0.runPostSets;var o3=L._memmove=t0._memmove;L._Decoder_Interface_exit=t0._Decoder_Interface_exit,L._Encoder_Interface_init=t0._Encoder_Interface_init;var y5,n3=L._memset=t0._memset,U2=L._malloc=t0._malloc,s3=L._memcpy=t0._memcpy;function D2(p){this.name="ExitStatus",this.message="Program terminated with exit("+p+")",this.status=p}function g5(p){function v(){L.calledRun||(L.calledRun=!0,W1||(f6(),j4(),L.onRuntimeInitialized&&L.onRuntimeInitialized(),L._main&&k6&&L.callMain(p),H4()))}p=p||L.arguments,r2>0||(z4(),r2>0||L.calledRun||(L.setStatus?(L.setStatus("Running..."),setTimeout(function(){setTimeout(function(){L.setStatus("")},1),v()},1)):v()))}function m6(p,v){if(!v||!L.noExitRuntime)throw L.noExitRuntime||(W1=!0,Z0=y5,q4(),L.onExit&&L.onExit(p)),A0&&typeof quit=="function"&&quit(p),new D2(p)}L._Decoder_Interface_Decode=t0._Decoder_Interface_Decode,L._Decoder_Interface_init=t0._Decoder_Interface_init,L._Encoder_Interface_exit=t0._Encoder_Interface_exit,L.___errno_location=t0.___errno_location,h1.stackAlloc=t0.stackAlloc,h1.stackSave=t0.stackSave,h1.stackRestore=t0.stackRestore,h1.establishStackSpace=t0.establishStackSpace,h1.setTempRet0=t0.setTempRet0,h1.getTempRet0=t0.getTempRet0,D2.prototype=new Error,D2.prototype.constructor=D2,B2=function p(){L.calledRun||g5(),L.calledRun||(B2=p)},L.callMain=L.callMain=function(p){k1(r2==0,"cannot call main when async dependencies remain! (listen on __ATMAIN__)"),k1(m5.length==0,"cannot call main when preRun functions remain to be called"),p=p||[],f6();var v=p.length+1;function y(){for(var a=0;a<3;a++)_.push(0)}var _=[I0(d2(L.thisProgram),"i8",g2)];y();for(var e=0;e<v-1;e+=1)_.push(I0(d2(p[e]),"i8",g2)),y();_.push(0),_=I0(_,"i32",g2),y5=h1.stackSave();try{m6(L._main(v,_,0),!0)}catch(a){if(a instanceof D2)return;if(a=="SimulateInfiniteLoop")return L.noExitRuntime=!0,void h1.stackRestore(y5);throw a&&typeof a=="object"&&a.stack&&L.printErr("exception thrown: "+[a,a.stack]),a}},L.run=L.run=g5,L.exit=L.exit=m6;var p6=[];function w2(p){p!==void 0?(L.print(p),L.printErr(p),p=JSON.stringify(p)):p="",W1=!0;var v=`
If this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.`,y="abort("+p+") at "+d5()+v;throw p6&&p6.forEach(function(_){y=_(y,p)}),y}if(L.abort=L.abort=w2,L.preInit)for(typeof L.preInit=="function"&&(L.preInit=[L.preInit]);L.preInit.length>0;)L.preInit.pop()();var k6=!0;return L.noInitialRun&&(k6=!1),L.noExitRuntime=!0,g5(),b1}();function r1(L,b1){b1=b1||8e3,self.postMessage({command:"encode",amr:H1.encode(L,b1,7)})}function f1(L){self.postMessage({command:"decode",amr:H1.decode(L)})}self.onmessage=function(L){switch(L.data.command){case"encode":r1(L.data.samples,L.data.sampleRate);break;case"decode":f1(L.data.buffer)}}},b4=k4.toString().replace(/^\s*function.*?\(\)\s*{/,"").replace(/}\s*$/,""),v4=(window.URL||window.webkitURL).createObjectURL(new Blob([b4],{type:"text/javascript"})),E4=function(){function H1(){var r1=this;Q5(this,H1),this._isInit=!1,this._isInitRecorder=!1,this._recorderControl=new v0,this._samples=new Float32Array(0),this._rawData=new Uint8Array(0),this._blob=null,this._onEnded=null,this._onAutoEnded=null,this._onPlay=null,this._onPause=null,this._onResume=null,this._onStop=null,this._onStartRecord=null,this._onCancelRecord=null,this._onFinishRecord=null,this._isPlaying=!1,this._isPaused=!1,this._startCtxTime=0,this._pauseTime=0,this._playEmpty=function(){r1._recorderControl.playPcm(new Float32Array(10),24e3)},this._onEndCallback=function(){r1._isPlaying&&(r1._isPlaying=!1,r1._onStop&&r1._onStop(),r1._onAutoEnded&&r1._onAutoEnded()),r1._isPaused||r1._onEnded&&r1._onEnded()},this._runAMRWorker=function(f1,L){var b1=new Worker(v4);b1.postMessage(f1),b1.onmessage=function(R1){L(R1.data.amr),b1.terminate()}}}return J5(H1,[{key:"isInit",value:function(){return this._isInit}},{key:"initWithArrayBuffer",value:function(r1){var f1=this;return(this._isInit||this._isInitRecorder)&&H1.throwAlreadyInitialized(),this._playEmpty(),new Promise(function(L,b1){var R1=new Uint8Array(r1);f1.decodeAMRAsync(R1).then(function(M1){f1._samples=M1,f1._isInit=!0,f1._samples?(f1._rawData=R1,L()):v0.decodeAudioArrayBufferByContext(r1).then(function(V1){return f1._isInit=!0,f1.encodeAMRAsync(V1,v0.getCtxSampleRate())}).then(function(V1){return f1._rawData=V1,f1._blob=H1.rawAMRData2Blob(V1),f1.decodeAMRAsync(V1)}).then(function(V1){f1._samples=V1,L()}).catch(function(){b1(new Error("Failed to decode."))})})})}},{key:"initWithBlob",value:function(r1){var f1=this;return(this._isInit||this._isInitRecorder)&&H1.throwAlreadyInitialized(),this._playEmpty(),this._blob=r1,new Promise(function(L){var b1=new FileReader;b1.onload=function(R1){L(R1.target.result)},b1.readAsArrayBuffer(r1)}).then(function(L){return f1.initWithArrayBuffer(L)})}},{key:"initWithUrl",value:function(r1){var f1=this;return(this._isInit||this._isInitRecorder)&&H1.throwAlreadyInitialized(),this._playEmpty(),new Promise(function(L,b1){var R1=new XMLHttpRequest;R1.open("GET",r1,!0),R1.responseType="arraybuffer",R1.onload=function(){L(this.response)},R1.onerror=function(){b1(new Error("Failed to fetch "+r1))},R1.send()}).then(function(L){return f1.initWithArrayBuffer(L)})}},{key:"initWithRecord",value:function(){var r1=this;return(this._isInit||this._isInitRecorder)&&H1.throwAlreadyInitialized(),this._playEmpty(),new Promise(function(f1,L){r1._recorderControl.initRecorder().then(function(){r1._isInitRecorder=!0,f1()}).catch(function(b1){L(b1)})})}},{key:"on",value:function(r1,f1){if(typeof f1=="function"||f1===null)switch(r1){case"play":this._onPlay=f1;break;case"stop":this._onStop=f1;break;case"pause":this._onPause=f1;break;case"resume":this._onResume=f1;break;case"ended":this._onEnded=f1;break;case"autoEnded":this._onAutoEnded=f1;break;case"startRecord":this._onStartRecord=f1;break;case"cancelRecord":this._onCancelRecord=f1;break;case"finishRecord":this._onFinishRecord=f1;break;case"*":case"all":this._onEnded=f1,this._onAutoEnded=f1,this._onPlay=f1,this._onPause=f1,this._onResume=f1,this._onStop=f1,this._onStartRecord=f1,this._onCancelRecord=f1,this._onFinishRecord=f1}}},{key:"off",value:function(r1){this.on(r1,null)}},{key:"onPlay",value:function(r1){this.on("play",r1)}},{key:"onStop",value:function(r1){this.on("stop",r1)}},{key:"onPause",value:function(r1){this.on("pause",r1)}},{key:"onResume",value:function(r1){this.on("resume",r1)}},{key:"onEnded",value:function(r1){this.on("ended",r1)}},{key:"onAutoEnded",value:function(r1){this.on("autoEnded",r1)}},{key:"onStartRecord",value:function(r1){this.on("startRecord",r1)}},{key:"onFinishRecord",value:function(r1){this.on("finishRecord",r1)}},{key:"onCancelRecord",value:function(r1){this.on("cancelRecord",r1)}},{key:"play",value:function(r1){var f1=r1&&r1<this.getDuration()?parseFloat(r1):0;if(!this._isInit)throw new Error("Please init AMR first.");this._onPlay&&this._onPlay(),this._isPlaying=!0,this._isPaused=!1,this._startCtxTime=v0.getCtxTime()-f1,this._recorderControl.playPcm(this._samples,this._isInitRecorder?v0.getCtxSampleRate():8e3,this._onEndCallback.bind(this),f1)}},{key:"stop",value:function(){this._recorderControl.stopPcm(),this._isPlaying=!1,this._isPaused=!1,this._onStop&&this._onStop()}},{key:"pause",value:function(){this._isPlaying&&(this._isPlaying=!1,this._isPaused=!0,this._pauseTime=v0.getCtxTime()-this._startCtxTime,this._recorderControl.stopPcm(),this._onPause&&this._onPause())}},{key:"resume",value:function(){this._isPaused&&(this._isPlaying=!0,this._isPaused=!1,this._startCtxTime=v0.getCtxTime()-this._pauseTime,this._recorderControl.playPcm(this._samples,this._isInitRecorder?v0.getCtxSampleRate():8e3,this._onEndCallback.bind(this),this._pauseTime),this._onResume&&this._onResume())}},{key:"playOrResume",value:function(){this._isPaused?this.resume():this.play()}},{key:"pauseOrResume",value:function(){this._isPaused?this.resume():this.pause()}},{key:"playOrPauseOrResume",value:function(){this._isPaused?this.resume():this._isPlaying?this.pause():this.play()}},{key:"setPosition",value:function(r1){var f1=parseFloat(r1);f1>this.getDuration()?this.stop():this._isPaused?this._pauseTime=f1:this._isPlaying?(this._recorderControl.stopPcmSilently(),this._startCtxTime=v0.getCtxTime()-f1,this._recorderControl.playPcm(this._samples,this._isInitRecorder?v0.getCtxSampleRate():8e3,this._onEndCallback.bind(this),f1)):this.play(f1)}},{key:"getCurrentPosition",value:function(){return this._isPaused?this._pauseTime:this._isPlaying?v0.getCtxTime()-this._startCtxTime:0}},{key:"isPlaying",value:function(){return this._isPlaying}},{key:"isPaused",value:function(){return this._isPaused}},{key:"startRecord",value:function(){this._recorderControl.startRecord(),this._onStartRecord&&this._onStartRecord()}},{key:"finishRecord",value:function(){var r1=this;return new Promise(function(f1){r1._recorderControl.stopRecord(),r1._recorderControl.generateRecordSamples().then(function(L){return r1._samples=L,r1.encodeAMRAsync(L,v0.getCtxSampleRate())}).then(function(L){r1._rawData=L,r1._blob=H1.rawAMRData2Blob(r1._rawData),r1._isInit=!0,r1._onFinishRecord&&r1._onFinishRecord(),r1._recorderControl.releaseRecord(),f1()})})}},{key:"cancelRecord",value:function(){this._recorderControl.stopRecord(),this._recorderControl.releaseRecord(),this._onCancelRecord&&this._onCancelRecord()}},{key:"isRecording",value:function(){return this._recorderControl.isRecording()}},{key:"getDuration",value:function(){var r1=this._isInitRecorder?v0.getCtxSampleRate():8e3;return this._samples.length/r1}},{key:"getBlob",value:function(){return this._blob}},{key:"destroy",value:function(){this._recorderControl.stopPcmSilently(),this._recorderControl.stopRecord(),this._recorderControl.releaseRecord(),this.off("*"),this._recorderControl=null,this._samples=null,this._rawData=null,this._blob=null}},{key:"encodeAMRAsync",value:function(r1,f1){var L=this;return new Promise(function(b1){L._runAMRWorker({command:"encode",samples:r1,sampleRate:f1},b1)})}},{key:"decodeAMRAsync",value:function(r1){var f1=this;return new Promise(function(L){f1._runAMRWorker({command:"decode",buffer:r1},L)})}}],[{key:"rawAMRData2Blob",value:function(r1){return new Blob([r1.buffer],{type:"audio/amr"})}},{key:"throwAlreadyInitialized",value:function(){throw new Error("AMR has been initialized. For a new AMR, please generate a new BenzAMRRecorder().")}},{key:"isPlaySupported",value:function(){return v0.isPlaySupported()}},{key:"isRecordSupported",value:function(){return v0.isRecordSupported()}}]),H1}();return E4}();const Bi=Ii(m4.exports);export{Bi as B};
