import{d as L,b as O,p as P,r as i,f as R,A as f,o as c,w as t,J as T,s as D,a as l,g as u,v as G,P as H,c4 as J,an as j,aB as I,c as X,F as z,y as K,R as Q,D as W,aC as Y,H as v,t as Z,M as $,G as ee,m as le}from"./index-CRsFgzy0.js";import{_ as ae}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{C as y}from"./constants-uird_4gU.js";import{a as se,c as te,u as ue}from"./brand-CKP-zont.js";const oe=L({name:"ProductBrandForm",__name:"BrandForm",emits:["success"],setup(re,{expose:w,emit:h}){const{t:p}=O(),V=P(),r=i(!1),_=i(""),d=i(!1),b=i(""),s=i({id:void 0,name:"",picUrl:"",status:y.ENABLE,description:""}),A=R({name:[{required:!0,message:"\u54C1\u724C\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],picUrl:[{required:!0,message:"\u54C1\u724C\u56FE\u7247\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u54C1\u724C\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=i();w({open:async(o,e)=>{if(r.value=!0,_.value=p("action."+o),b.value=o,E(),e){d.value=!0;try{s.value=await se(e)}finally{d.value=!1}}}});const B=h,C=async()=>{if(n&&await n.value.validate()){d.value=!0;try{const o=s.value;b.value==="create"?(await te(o),V.success(p("common.createSuccess"))):(await ue(o),V.success(p("common.updateSuccess"))),r.value=!1,B("success")}finally{d.value=!1}}},E=()=>{var o;s.value={id:void 0,name:"",picUrl:"",status:y.ENABLE,description:""},(o=n.value)==null||o.resetFields()};return(o,e)=>{const U=H,m=G,F=J,S=j,k=Y,q=I,x=D,g=ee,M=ae,N=$;return c(),f(M,{title:l(_),modelValue:l(r),"onUpdate:modelValue":e[6]||(e[6]=a=>le(r)?r.value=a:null)},{footer:t(()=>[u(g,{onClick:C,type:"primary",disabled:l(d)},{default:t(()=>e[7]||(e[7]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),u(g,{onClick:e[5]||(e[5]=a=>r.value=!1)},{default:t(()=>e[8]||(e[8]=[v("\u53D6 \u6D88")])),_:1})]),default:t(()=>[T((c(),f(x,{ref_key:"formRef",ref:n,model:l(s),rules:l(A),"label-width":"80px"},{default:t(()=>[u(m,{label:"\u54C1\u724C\u540D\u79F0",prop:"name"},{default:t(()=>[u(U,{modelValue:l(s).name,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).name=a),placeholder:"\u8BF7\u8F93\u5165\u54C1\u724C\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(m,{label:"\u54C1\u724C\u56FE\u7247",prop:"picUrl"},{default:t(()=>[u(F,{modelValue:l(s).picUrl,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).picUrl=a),limit:1,"is-show-tip":!1},null,8,["modelValue"])]),_:1}),u(m,{label:"\u54C1\u724C\u6392\u5E8F",prop:"sort"},{default:t(()=>[u(S,{modelValue:l(s).sort,"onUpdate:modelValue":e[2]||(e[2]=a=>l(s).sort=a),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),u(m,{label:"\u54C1\u724C\u72B6\u6001",prop:"status"},{default:t(()=>[u(q,{modelValue:l(s).status,"onUpdate:modelValue":e[3]||(e[3]=a=>l(s).status=a)},{default:t(()=>[(c(!0),X(z,null,K(l(Q)(l(W).COMMON_STATUS),a=>(c(),f(k,{key:a.value,value:a.value},{default:t(()=>[v(Z(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(m,{label:"\u54C1\u724C\u63CF\u8FF0"},{default:t(()=>[u(U,{modelValue:l(s).description,"onUpdate:modelValue":e[4]||(e[4]=a=>l(s).description=a),type:"textarea",placeholder:"\u8BF7\u8F93\u5165\u54C1\u724C\u63CF\u8FF0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[N,l(d)]])]),_:1},8,["title","modelValue"])}}});export{oe as _};
