import{d as N,r as p,f as Y,A as v,o as m,w as r,g as e,s as F,a,v as G,aB as H,cf as K,H as i,x as M,c as q,F as J,y as P,R as j,D as V,B as Q,C as W,G as X,I as Z,J as $,K as ee,L as le,M as ae,m as te}from"./index-CRsFgzy0.js";import{_ as re}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as se}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as oe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as ue}from"./el-avatar-Nl9DW69B.js";import{_ as pe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{d as ie}from"./formatTime-DhdtkSIS.js";import{g as ne}from"./index-CaIDt17n.js";import{m as de}from"./constants-uird_4gU.js";import{f as me}from"./formatter-D3GpDdeL.js";const ce=N({name:"BrokerageOrderListDialog",__name:"BrokerageOrderListDialog",setup(fe,{expose:D}){const c=p(!0),g=p(0),w=p([]),s=Y({pageNo:1,pageSize:10,userId:null,bizType:de.ORDER.type,sourceUserLevel:0,createTime:[],status:null}),U=p(),n=p(!1);D({open:async o=>{n.value=!0,s.userId=o,h()}});const b=async()=>{c.value=!0;try{const o=await ne({...s,sourceUserLevel:s.sourceUserLevel===0?void 0:s.sourceUserLevel});w.value=o.list,g.value=o.total}finally{c.value=!1}},f=()=>{s.pageNo=1,b()},h=()=>{var o;(o=U.value)==null||o.resetFields(),f()};return(o,l)=>{const _=K,L=H,d=G,T=Q,E=M,S=W,x=Z,y=X,k=F,R=pe,u=le,A=ue,C=oe,O=ee,B=se,I=re,z=ae;return m(),v(I,{modelValue:a(n),"onUpdate:modelValue":l[5]||(l[5]=t=>te(n)?n.value=t:null),title:"\u63A8\u5E7F\u8BA2\u5355\u5217\u8868",width:"75%"},{default:r(()=>[e(R,null,{default:r(()=>[e(k,{ref_key:"queryFormRef",ref:U,inline:!0,model:a(s),class:"-mb-15px","label-width":"85px"},{default:r(()=>[e(d,{label:"\u7528\u6237\u7C7B\u578B",prop:"sourceUserLevel"},{default:r(()=>[e(L,{modelValue:a(s).sourceUserLevel,"onUpdate:modelValue":l[0]||(l[0]=t=>a(s).sourceUserLevel=t),onChange:f},{default:r(()=>[e(_,{value:0},{default:r(()=>l[6]||(l[6]=[i("\u5168\u90E8")])),_:1}),e(_,{value:1},{default:r(()=>l[7]||(l[7]=[i("\u4E00\u7EA7\u63A8\u5E7F\u4EBA")])),_:1}),e(_,{value:2},{default:r(()=>l[8]||(l[8]=[i("\u4E8C\u7EA7\u63A8\u5E7F\u4EBA")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[e(E,{modelValue:a(s).status,"onUpdate:modelValue":l[1]||(l[1]=t=>a(s).status=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:r(()=>[(m(!0),q(J,null,P(a(j)(a(V).BROKERAGE_RECORD_STATUS),t=>(m(),v(T,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u7ED1\u5B9A\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(S,{modelValue:a(s).createTime,"onUpdate:modelValue":l[2]||(l[2]=t=>a(s).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(d,null,{default:r(()=>[e(y,{onClick:f},{default:r(()=>[e(x,{class:"mr-5px",icon:"ep:search"}),l[9]||(l[9]=i(" \u641C\u7D22 "))]),_:1}),e(y,{onClick:h},{default:r(()=>[e(x,{class:"mr-5px",icon:"ep:refresh"}),l[10]||(l[10]=i(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(R,null,{default:r(()=>[$((m(),v(O,{data:a(w),"show-overflow-tooltip":!0,stripe:!0},{default:r(()=>[e(u,{align:"center",label:"\u8BA2\u5355\u7F16\u53F7","min-width":"80px",prop:"bizId"}),e(u,{align:"center",label:"\u7528\u6237\u7F16\u53F7","min-width":"80px",prop:"sourceUserId"}),e(u,{align:"center",label:"\u5934\u50CF",prop:"sourceUserAvatar",width:"70px"},{default:r(t=>[e(A,{src:t.row.sourceUserAvatar},null,8,["src"])]),_:1}),e(u,{align:"center",label:"\u6635\u79F0","min-width":"80px",prop:"sourceUserNickname"}),e(u,{formatter:a(me),align:"center",label:"\u4F63\u91D1","min-width":"100px",prop:"price"},null,8,["formatter"]),e(u,{align:"center",label:"\u72B6\u6001","min-width":"85",prop:"status"},{default:r(t=>[e(C,{type:a(V).BROKERAGE_RECORD_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),e(u,{formatter:a(ie),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[z,a(c)]]),e(B,{limit:a(s).pageSize,"onUpdate:limit":l[3]||(l[3]=t=>a(s).pageSize=t),page:a(s).pageNo,"onUpdate:page":l[4]||(l[4]=t=>a(s).pageNo=t),total:a(g),onPagination:b},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue"])}}});export{ce as _};
