import{d as P,b as z,p as E,r as n,f as O,A as o,o as r,w as l,J as Q,s as T,a as t,g as s,v as W,P as X,cr as Y,K as Z,L as ee,dv as ae,H as i,t as g,an as le,a3 as F,G as te,M as se,m as de}from"./index-CRsFgzy0.js";import{_ as ue}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as oe}from"./Tooltip.vue_vue_type_script_setup_true_lang-CQxPX9Oy.js";import{D as re,b as ne,c as ie,u as ce}from"./index-Bt_b8XZy.js";import{d as pe,h as me}from"./tree-COGD3qag.js";import{g as fe}from"./index-C0yL_L5C.js";const ve=P({__name:"BusinessStatusForm",emits:["success"],setup(ye,{expose:R,emit:$}){const{t:_}=z(),V=E(),c=n(!1),C=n(""),p=n(!1),x=n(""),d=n({id:void 0,name:"",deptIds:[],statuses:[]}),A=O({name:[{required:!0,message:"\u72B6\u6001\u7EC4\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),h=n(),S=n([]),f=n(),I=n(!0);R({open:async(u,e)=>{if(c.value=!0,C.value=_("action."+u),x.value=u,j(),e){p.value=!0;try{d.value=await ne(e),f.value.setCheckedKeys(d.value.deptIds),d.value.statuses.length==0&&w()}finally{p.value=!1}}else w();S.value=me(await fe())}});const B=$,D=async()=>{await h.value.validate(),p.value=!0;try{const u=d.value;u.deptIds=f.value.getCheckedKeys(!1),x.value==="create"?(await ie(u),V.success(_("common.createSuccess"))):(await ce(u),V.success(_("common.updateSuccess"))),c.value=!1,B("success")}finally{p.value=!1}},j=()=>{var u,e;I.value=!0,d.value={id:void 0,name:"",deptIds:[],statuses:[]},(u=f.value)==null||u.setCheckedNodes([]),(e=h.value)==null||e.resetFields()},w=()=>{d.value.statuses.push({name:"",percent:void 0})};return(u,e)=>{const U=X,b=W,q=oe,G=Y,v=ae,y=ee,H=le,k=te,J=Z,L=T,M=ue,N=se;return r(),o(M,{title:t(C),modelValue:t(c),"onUpdate:modelValue":e[2]||(e[2]=a=>de(c)?c.value=a:null)},{footer:l(()=>[s(k,{onClick:D,type:"primary",disabled:t(p)},{default:l(()=>e[6]||(e[6]=[i("\u786E \u5B9A")])),_:1},8,["disabled"]),s(k,{onClick:e[1]||(e[1]=a=>c.value=!1)},{default:l(()=>e[7]||(e[7]=[i("\u53D6 \u6D88")])),_:1})]),default:l(()=>[Q((r(),o(L,{ref_key:"formRef",ref:h,model:t(d),rules:t(A),"label-width":"100px"},{default:l(()=>[s(b,{label:"\u72B6\u6001\u7EC4\u540D",prop:"name"},{default:l(()=>[s(U,{modelValue:t(d).name,"onUpdate:modelValue":e[0]||(e[0]=a=>t(d).name=a),placeholder:"\u8BF7\u8F93\u5165\u72B6\u6001\u7EC4\u540D"},null,8,["modelValue"])]),_:1}),s(b,{label:"\u5E94\u7528\u90E8\u95E8",prop:"deptIds"},{label:l(()=>[s(q,{message:"\u4E0D\u9009\u62E9\u90E8\u95E8\u65F6\uFF0C\u9ED8\u8BA4\u5168\u516C\u53F8\u751F\u6548",title:"\u5E94\u7528\u90E8\u95E8"})]),default:l(()=>[s(G,{ref_key:"treeRef",ref:f,data:t(S),props:t(pe),"check-strictly":!t(I),"node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8","show-checkbox":""},null,8,["data","props","check-strictly"])]),_:1}),s(b,{label:"\u9636\u6BB5\u8BBE\u7F6E",prop:"statuses"},{default:l(()=>[s(J,{border:"",style:{width:"100%"},data:t(d).statuses.concat(re)},{default:l(()=>[s(y,{align:"center",label:"\u9636\u6BB5",width:"70"},{default:l(a=>[a.row.defaultStatus?(r(),o(v,{key:1},{default:l(()=>e[3]||(e[3]=[i("\u7ED3\u675F")])),_:1})):(r(),o(v,{key:0},{default:l(()=>[i("\u9636\u6BB5 "+g(a.$index+1),1)]),_:2},1024))]),_:1}),s(y,{align:"center",label:"\u9636\u6BB5\u540D\u79F0",width:"160",prop:"name"},{default:l(({row:a})=>[a.endStatus?(r(),o(v,{key:1},{default:l(()=>[i(g(a.name),1)]),_:2},1024)):(r(),o(U,{key:0,modelValue:a.name,"onUpdate:modelValue":m=>a.name=m,placeholder:"\u8BF7\u8F93\u5165\u72B6\u6001\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),s(y,{width:"140",align:"center",label:"\u8D62\u5355\u7387\uFF08%\uFF09",prop:"percent"},{default:l(({row:a})=>[a.endStatus?(r(),o(v,{key:1},{default:l(()=>[i(g(a.percent),1)]),_:2},1024)):(r(),o(H,{key:0,modelValue:a.percent,"onUpdate:modelValue":m=>a.percent=m,placeholder:"\u8BF7\u8F93\u5165\u8D62\u5355\u7387","controls-position":"right",min:0,max:100,precision:2,class:"!w-1/1"},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),s(y,{label:"\u64CD\u4F5C",width:"110",align:"center"},{default:l(a=>[a.row.endStatus?F("",!0):(r(),o(k,{key:0,link:"",type:"primary",onClick:m=>w(a.$index)},{default:l(()=>e[4]||(e[4]=[i(" \u6DFB\u52A0 ")])),_:2},1032,["onClick"])),a.row.endStatus?F("",!0):(r(),o(k,{key:1,link:"",type:"danger",onClick:m=>{return K=a.$index,void d.value.statuses.splice(K,1);var K},disabled:t(d).statuses.length<=1},{default:l(()=>e[5]||(e[5]=[i(" \u5220\u9664 ")])),_:2},1032,["onClick","disabled"]))]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["model","rules"])),[[N,t(p)]])]),_:1},8,["title","modelValue"])}}});export{ve as _};
