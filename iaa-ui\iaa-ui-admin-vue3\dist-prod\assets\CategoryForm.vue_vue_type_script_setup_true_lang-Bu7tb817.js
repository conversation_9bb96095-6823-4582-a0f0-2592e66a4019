import{d as z,b as D,p as G,r as o,f as H,A as p,o as d,w as t,J,s as j,a as l,g as u,v as K,x as Q,c as w,B as W,F as x,y as C,P as X,i as Y,c4 as Z,an as $,aB as ee,R as ae,D as le,aC as se,H as f,t as te,M as ue,G as re,m as oe}from"./index-CRsFgzy0.js";import{_ as de}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{C as k}from"./constants-uird_4gU.js";import{a as ie,g as me,c as ne,u as pe}from"./category--cl9fhwU.js";const ce=z({name:"ProductCategory",__name:"CategoryForm",emits:["success"],setup(ve,{expose:h,emit:q}){const{t:v}=D(),g=G(),i=o(!1),b=o(""),m=o(!1),V=o(""),s=o({id:void 0,name:"",picUrl:"",status:k.ENABLE}),E=H({parentId:[{required:!0,message:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u5206\u7C7B",trigger:"blur"}],name:[{required:!0,message:"\u5206\u7C7B\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],picUrl:[{required:!0,message:"\u5206\u7C7B\u56FE\u7247\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u5206\u7C7B\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),c=o(),_=o([]);h({open:async(r,e)=>{if(i.value=!0,b.value=v("action."+r),V.value=r,A(),e){m.value=!0;try{s.value=await ie(e)}finally{m.value=!1}}_.value=await me({parentId:0})}});const I=q,S=async()=>{if(c&&await c.value.validate()){m.value=!0;try{const r=s.value;V.value==="create"?(await ne(r),g.success(v("common.createSuccess"))):(await pe(r),g.success(v("common.updateSuccess"))),i.value=!1,I("success")}finally{m.value=!1}}},A=()=>{var r;s.value={id:void 0,name:"",picUrl:"",status:k.ENABLE},(r=c.value)==null||r.resetFields()};return(r,e)=>{const y=W,B=Q,n=K,F=X,M=Z,N=$,L=se,O=ee,P=j,U=re,R=de,T=ue;return d(),p(R,{title:l(b),modelValue:l(i),"onUpdate:modelValue":e[6]||(e[6]=a=>oe(i)?i.value=a:null)},{footer:t(()=>[u(U,{onClick:S,type:"primary",disabled:l(m)},{default:t(()=>e[8]||(e[8]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),u(U,{onClick:e[5]||(e[5]=a=>i.value=!1)},{default:t(()=>e[9]||(e[9]=[f("\u53D6 \u6D88")])),_:1})]),default:t(()=>[J((d(),p(P,{ref_key:"formRef",ref:c,model:l(s),rules:l(E),"label-width":"120px"},{default:t(()=>[u(n,{label:"\u4E0A\u7EA7\u5206\u7C7B",prop:"parentId"},{default:t(()=>[u(B,{modelValue:l(s).parentId,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).parentId=a),placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u5206\u7C7B"},{default:t(()=>[(d(),p(y,{key:0,label:"\u9876\u7EA7\u5206\u7C7B",value:0})),(d(!0),w(x,null,C(l(_),a=>(d(),p(y,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(n,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name"},{default:t(()=>[u(F,{modelValue:l(s).name,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).name=a),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(n,{label:"\u79FB\u52A8\u7AEF\u5206\u7C7B\u56FE",prop:"picUrl"},{default:t(()=>[u(M,{modelValue:l(s).picUrl,"onUpdate:modelValue":e[2]||(e[2]=a=>l(s).picUrl=a),limit:1,"is-show-tip":!1},null,8,["modelValue"]),e[7]||(e[7]=Y("div",{style:{"font-size":"10px"},class:"pl-10px"},"\u63A8\u8350 180x180 \u56FE\u7247\u5206\u8FA8\u7387",-1))]),_:1}),u(n,{label:"\u5206\u7C7B\u6392\u5E8F",prop:"sort"},{default:t(()=>[u(N,{modelValue:l(s).sort,"onUpdate:modelValue":e[3]||(e[3]=a=>l(s).sort=a),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),u(n,{label:"\u5F00\u542F\u72B6\u6001",prop:"status"},{default:t(()=>[u(O,{modelValue:l(s).status,"onUpdate:modelValue":e[4]||(e[4]=a=>l(s).status=a)},{default:t(()=>[(d(!0),w(x,null,C(l(ae)(l(le).COMMON_STATUS),a=>(d(),p(L,{key:a.value,value:a.value},{default:t(()=>[f(te(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[T,l(m)]])]),_:1},8,["title","modelValue"])}}});export{ce as _};
