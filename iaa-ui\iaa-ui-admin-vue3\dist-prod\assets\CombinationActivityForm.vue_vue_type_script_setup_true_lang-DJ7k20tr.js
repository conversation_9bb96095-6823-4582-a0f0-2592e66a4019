import{f as T,D as U,d as z,b as j,p as B,r,c as H,o as M,F as J,g as n,a as i,m as Q,w as u,J as q,A as K,G as W,H as k,L as X,an as Z,M as $,aG as ee,aP as V,aQ as ae,aO as oe}from"./index-CRsFgzy0.js";import{_ as te}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as se}from"./Form-BF4H89jq.js";import{g as ie,c as le,u as re}from"./combinationActivity-DX3uo1WR.js";import{b as L}from"./formatTime-DhdtkSIS.js";import{r as m}from"./formRules-V2Qetfkc.js";import{u as ne}from"./useCrudSchemas-CNYomGr4.js";import{_ as ue}from"./SpuSelect.vue_vue_type_script_setup_true_lang-tSVtv9n7.js";import{_ as me}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-DGosLJYC.js";import{g as ce}from"./index-CQgbu5O7.js";import{b as pe}from"./spu-BHhhuUrI.js";const de=T({name:[m],totalLimitCount:[m],singleLimitCount:[m],startTime:[m],endTime:[m],userSize:[m],limitDuration:[m],virtualGroup:[m]}),fe=T([{label:"\u62FC\u56E2\u540D\u79F0",field:"name",isSearch:!0,isTable:!1,form:{colProps:{span:24}}},{label:"\u6D3B\u52A8\u5F00\u59CB\u65F6\u95F4",field:"startTime",formatter:L,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u6D3B\u52A8\u7ED3\u675F\u65F6\u95F4",field:"endTime",formatter:L,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u53C2\u4E0E\u4EBA\u6570",field:"userSize",isSearch:!1,form:{component:"InputNumber",labelMessage:"\u53C2\u4E0E\u4EBA\u6570\u4E0D\u80FD\u5C11\u4E8E\u4E24\u4EBA",value:2}},{label:"\u9650\u5236\u65F6\u957F",field:"limitDuration",isSearch:!1,isTable:!1,form:{component:"InputNumber",labelMessage:"\u9650\u5236\u65F6\u957F(\u5C0F\u65F6)",componentProps:{placeholder:"\u8BF7\u8F93\u5165\u9650\u5236\u65F6\u957F(\u5C0F\u65F6)"}}},{label:"\u603B\u9650\u8D2D\u6570\u91CF",field:"totalLimitCount",isSearch:!1,isTable:!1,form:{component:"InputNumber",value:0}},{label:"\u5355\u6B21\u9650\u8D2D\u6570\u91CF",field:"singleLimitCount",isSearch:!1,isTable:!1,form:{component:"InputNumber",value:0}},{label:"\u865A\u62DF\u6210\u56E2",field:"virtualGroup",dictType:U.INFRA_BOOLEAN_STRING,dictClass:"boolean",isSearch:!0,form:{component:"Radio",value:!1}},{label:"\u62FC\u56E2\u5546\u54C1",field:"spuId",isSearch:!1,form:{colProps:{span:24}}}]),{allSchemas:be}=ne(fe),ve=z({name:"PromotionCombinationActivityForm",__name:"CombinationActivityForm",emits:["success"],setup(he,{expose:R,emit:Y}){const{t:S}=j(),_=B(),p=r(!1),C=r(""),d=r(!1),w=r(""),c=r(),I=r(),D=r(),h=r([]),P=r([]),A=[{name:"productConfig.combinationPrice",rule:o=>o>=.01,message:"\u5546\u54C1\u62FC\u56E2\u4EF7\u683C\u4E0D\u80FD\u5C0F\u4E8E0.01 \uFF01\uFF01\uFF01"}],N=(o,e)=>{c.value.setValues({spuId:o}),F(o,e)},F=async(o,e,t)=>{var g;const l=[],f=await pe([o]);if(f.length==0)return;h.value=[];const a=f[0],b=e===void 0?a==null?void 0:a.skus:(g=a==null?void 0:a.skus)==null?void 0:g.filter(s=>e.includes(s.id));b==null||b.forEach(s=>{let v={spuId:a.id,skuId:s.id,combinationPrice:0};if(t!==void 0){const y=t.find(O=>O.skuId===s.id);y&&(y.combinationPrice=oe(y.combinationPrice)),v=y||v}s.productConfig=v}),a.skus=b,l.push({spuId:a.id,spuDetail:a,propertyList:ce(a)}),h.value.push(a),P.value=l};R({open:async(o,e)=>{var t;if(p.value=!0,C.value=S("action."+o),w.value=o,await E(),e){d.value=!0;try{const l=await ie(e);await F(l.spuId,(t=l.products)==null?void 0:t.map(f=>f.skuId),l.products),c.value.setValues(l)}finally{d.value=!1}}}});const E=async()=>{h.value=[],P.value=[],await ee(),c.value.getElFormRef().resetFields()},x=Y,G=async()=>{if(c&&await c.value.getElFormRef().validate()){d.value=!0;try{const o=V(D.value.getSkuConfigs("productConfig"));o.forEach(t=>{t.combinationPrice=ae(t.combinationPrice)});const e=V(c.value.formModel);e.products=o,w.value==="create"?(await le(e),_.success(S("common.createSuccess"))):(await re(e),_.success(S("common.updateSuccess"))),p.value=!1,x("success")}finally{d.value=!1}}};return(o,e)=>{const t=W,l=Z,f=X,a=se,b=te,g=$;return M(),H(J,null,[n(b,{modelValue:i(p),"onUpdate:modelValue":e[2]||(e[2]=s=>Q(p)?p.value=s:null),title:i(C),width:"65%"},{footer:u(()=>[n(t,{disabled:i(d),type:"primary",onClick:G},{default:u(()=>e[4]||(e[4]=[k("\u786E \u5B9A")])),_:1},8,["disabled"]),n(t,{onClick:e[1]||(e[1]=s=>p.value=!1)},{default:u(()=>e[5]||(e[5]=[k("\u53D6 \u6D88")])),_:1})]),default:u(()=>[q((M(),K(a,{ref_key:"formRef",ref:c,"is-col":!0,rules:i(de),schema:i(be).formSchema,class:"mt-10px"},{spuId:u(()=>[n(t,{onClick:e[0]||(e[0]=s=>i(I).open())},{default:u(()=>e[3]||(e[3]=[k("\u9009\u62E9\u5546\u54C1")])),_:1}),n(i(me),{ref_key:"spuAndSkuListRef",ref:D,"rule-config":A,"spu-list":i(h),"spu-property-list-p":i(P)},{default:u(()=>[n(f,{align:"center",label:"\u62FC\u56E2\u4EF7\u683C(\u5143)","min-width":"168"},{default:u(({row:s})=>[n(l,{modelValue:s.productConfig.combinationPrice,"onUpdate:modelValue":v=>s.productConfig.combinationPrice=v,min:0,precision:2,step:.1,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["spu-list","spu-property-list-p"])]),_:1},8,["rules","schema"])),[[g,i(d)]])]),_:1},8,["modelValue","title"]),n(i(ue),{ref_key:"spuSelectRef",ref:I,isSelectSku:!0,onConfirm:N},null,512)],64)}}});export{ve as _};
