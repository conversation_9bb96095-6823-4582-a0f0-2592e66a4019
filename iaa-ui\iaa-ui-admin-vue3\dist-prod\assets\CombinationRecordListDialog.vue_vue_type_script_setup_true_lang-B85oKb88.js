import{as as f,d as D,r as m,f as U,A as _,o as w,w as o,g as a,J as z,K as A,a as e,L as E,aE as M,H as x,t as L,D as V,M as j,m as P}from"./index-CRsFgzy0.js";import{_ as k}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as B}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as H}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as J}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as K}from"./el-avatar-Nl9DW69B.js";import{d as h}from"./formatTime-DhdtkSIS.js";const b=async d=>await f.get({url:"/promotion/combination-record/page",params:d}),q=async()=>await f.get({url:"/promotion/combination-record/get-summary"}),F=D({name:"CombinationRecordListDialog",__name:"CombinationRecordListDialog",setup(d,{expose:v}){const p=m(!0),u=m(0),c=m([]),i=U({pageNo:1,pageSize:10,headId:void 0}),s=m(!1);v({open:async n=>{s.value=!0,i.headId=n,await g()}});const g=async()=>{p.value=!0;try{const n=await b(i);c.value=n.list,u.value=n.total}finally{p.value=!1}};return(n,r)=>{const l=E,y=K,I=M,N=J,O=A,T=H,R=B,S=k,C=j;return w(),_(S,{modelValue:e(s),"onUpdate:modelValue":r[2]||(r[2]=t=>P(s)?s.value=t:null),title:"\u62FC\u56E2\u5217\u8868",width:"950"},{default:o(()=>[a(R,null,{default:o(()=>[z((w(),_(O,{data:e(c)},{default:o(()=>[a(l,{align:"center",label:"\u7F16\u53F7",prop:"id","min-width":"50"}),a(l,{align:"center",label:"\u5934\u50CF",prop:"avatar","min-width":"80"},{default:o(t=>[a(y,{src:t.row.avatar},null,8,["src"])]),_:1}),a(l,{align:"center",label:"\u6635\u79F0",prop:"nickname","min-width":"100"}),a(l,{align:"center",label:"\u5F00\u56E2\u56E2\u957F",prop:"headId","min-width":"100"},{default:o(({row:t})=>[a(I,null,{default:o(()=>[x(L(t.headId===0?"\u56E2\u957F":"\u56E2\u5458"),1)]),_:2},1024)]),_:1}),a(l,{formatter:e(h),align:"center",label:"\u53C2\u56E2\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(l,{formatter:e(h),align:"center",label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),a(l,{align:"center",label:"\u62FC\u56E2\u72B6\u6001",prop:"status","min-width":"150"},{default:o(t=>[a(N,{type:e(V).PROMOTION_COMBINATION_RECORD_STATUS,value:t.row.status},null,8,["type","value"])]),_:1})]),_:1},8,["data"])),[[C,e(p)]]),a(T,{limit:e(i).pageSize,"onUpdate:limit":r[0]||(r[0]=t=>e(i).pageSize=t),page:e(i).pageNo,"onUpdate:page":r[1]||(r[1]=t=>e(i).pageNo=t),total:e(u),onPagination:g},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue"])}}});export{F as _,q as a,b as g};
