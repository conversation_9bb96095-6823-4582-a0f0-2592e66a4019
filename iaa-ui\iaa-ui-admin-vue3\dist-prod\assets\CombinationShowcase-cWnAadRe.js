import{d as U,ah as v,e6 as I,a2 as N,r as b,aK as j,c as u,o as t,i as n,g as i,A as E,a3 as R,F as h,y as T,w as g,J as q,I as F,a6 as J,bi as K,a as V,aU as _,_ as L}from"./index-CRsFgzy0.js";import{E as M}from"./el-image-BQpHFDaE.js";import{e as X}from"./combinationActivity-DX3uo1WR.js";import{_ as z}from"./CombinationTableSelect.vue_vue_type_script_setup_true_lang-djoDpnVn.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import"./color-CIFUYK2M.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";import"./formatter-D3GpDdeL.js";import"./formatTime-DhdtkSIS.js";const B={class:"flex flex-wrap items-center gap-8px"},D={class:"relative h-full w-full"},G=L(U({name:"CombinationShowcase",__name:"CombinationShowcase",props:{modelValue:I([Number,Array]).isRequired,limit:v.number.def(Number.MAX_VALUE),disabled:v.bool.def(!1)},emits:["update:modelValue","change"],setup(o,{emit:w}){const a=o,y=N(()=>!a.disabled&&(!a.limit||l.value.length<a.limit)),l=b([]);j(()=>a.modelValue,async()=>{const e=_(a.modelValue)?a.modelValue:a.modelValue?[a.modelValue]:[];e.length!==0?(l.value.length===0||l.value.some(d=>!e.includes(d.id)))&&(l.value=await X(e)):l.value=[]},{immediate:!0});const c=b(),k=()=>{c.value.open(l.value)},x=e=>{l.value=_(e)?e:[e],r()},s=w,r=()=>{if(a.limit===1){const e=l.value.length>0?l.value[0]:null;s("update:modelValue",(e==null?void 0:e.id)||0),s("change",e)}else s("update:modelValue",l.value.map(e=>e.id)),s("change",l.value)};return(e,d)=>{const A=M,p=F,f=K;return t(),u(h,null,[n("div",B,[(t(!0),u(h,null,T(V(l),(m,C)=>(t(),u("div",{key:m.id,class:"select-box spu-pic"},[i(f,{content:m.name},{default:g(()=>[n("div",D,[i(A,{src:m.picUrl,class:"h-full w-full"},null,8,["src"]),q(i(p,{class:"del-icon",icon:"ep:circle-close-filled",onClick:H=>(S=>{l.value.splice(S,1),r()})(C)},null,8,["onClick"]),[[J,!o.disabled]])])]),_:2},1032,["content"])]))),128)),V(y)?(t(),E(f,{key:0,content:"\u9009\u62E9\u6D3B\u52A8"},{default:g(()=>[n("div",{class:"select-box",onClick:k},[i(p,{icon:"ep:plus"})])]),_:1})):R("",!0)]),i(z,{ref_key:"combinationActivityTableSelectRef",ref:c,multiple:o.limit!=1,onChange:x},null,8,["multiple"])],64)}}}),[["__scopeId","data-v-ef65b035"]]);export{G as default};
