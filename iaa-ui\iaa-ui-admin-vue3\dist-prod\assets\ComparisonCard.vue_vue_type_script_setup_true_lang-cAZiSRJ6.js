import{d as x,ah as a,a2 as b,eZ as g,c as v,o as w,i as s,g as l,t as r,aE as y,w as _,H as d,X as j,a as t,dW as n,I as C,j as q}from"./index-CRsFgzy0.js";import{_ as R}from"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";const h={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},E={class:"flex items-center justify-between text-gray-500"},H={class:"flex flex-row items-baseline justify-between"},I={class:"flex flex-row items-center justify-between text-sm"},M=x({name:"ComparisonCard",__name:"ComparisonCard",props:{title:a.string.def("").isRequired,tag:a.string.def(""),prefix:a.string.def(""),value:a.number.def(0).isRequired,reference:a.number.def(0).isRequired,decimals:a.number.def(0)},setup(e){const c=e,i=b(()=>g(c.value,c.reference));return(W,f)=>{const o=y,m=R,p=C,u=q;return w(),v("div",h,[s("div",E,[s("span",null,r(e.title),1),l(o,null,{default:_(()=>[d(r(e.tag),1)]),_:1})]),s("div",H,[l(m,{prefix:e.prefix,"end-val":e.value,decimals:e.decimals,class:"text-3xl"},null,8,["prefix","end-val","decimals"]),s("span",{class:j(t(n)(t(i))>0?"text-red-500":"text-green-500")},[d(r(Math.abs(t(n)(t(i))))+"% ",1),l(p,{icon:t(n)(t(i))>0?"ep:caret-top":"ep:caret-bottom",class:"!text-sm"},null,8,["icon"])],2)]),l(u,{class:"mb-1! mt-2!"}),s("div",I,[f[0]||(f[0]=s("span",{class:"text-gray-500"},"\u6628\u65E5\u6570\u636E",-1)),s("span",null,r(e.prefix||"")+r(e.reference),1)])])}}});export{M as _};
