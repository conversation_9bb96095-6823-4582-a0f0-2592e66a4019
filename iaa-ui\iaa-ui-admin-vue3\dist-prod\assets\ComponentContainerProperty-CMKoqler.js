import{d as L,f5 as k,A as u,o as i,l as I,w as l,a3 as U,g as o,n as $,b3 as v,k as z,s as j,a as d,v as A,aB as H,aC as P,H as s,c4 as q,cr as D,ca as E,aw as F,_ as G}from"./index-CRsFgzy0.js";import{_ as J}from"./index-CGOSLF-t.js";const K=G(L({name:"ComponentContainer",__name:"ComponentContainerProperty",props:{modelValue:{}},emits:["update:modelValue"],setup(f,{emit:c}){const a=k(f,"modelValue",c),R=[{label:"\u5916\u90E8\u8FB9\u8DDD",prop:"margin",children:[{label:"\u4E0A",prop:"marginTop"},{label:"\u53F3",prop:"marginRight"},{label:"\u4E0B",prop:"marginBottom"},{label:"\u5DE6",prop:"marginLeft"}]},{label:"\u5185\u90E8\u8FB9\u8DDD",prop:"padding",children:[{label:"\u4E0A",prop:"paddingTop"},{label:"\u53F3",prop:"paddingRight"},{label:"\u4E0B",prop:"paddingBottom"},{label:"\u5DE6",prop:"paddingLeft"}]},{label:"\u8FB9\u6846\u5706\u89D2",prop:"borderRadius",children:[{label:"\u4E0A\u5DE6",prop:"borderTopLeftRadius"},{label:"\u4E0A\u53F3",prop:"borderTopRightRadius"},{label:"\u4E0B\u53F3",prop:"borderBottomRightRadius"},{label:"\u4E0B\u5DE6",prop:"borderBottomLeftRadius"}]}];return(n,e)=>{const m=$,b=P,_=H,t=A,h=J,V=q,T=E,y=D,w=j,B=z,x=I;return i(),u(x,{stretch:""},{default:l(()=>[n.$slots.default?(i(),u(m,{key:0,label:"\u5185\u5BB9"},{default:l(()=>[v(n.$slots,"default",{},void 0,!0)]),_:3})):U("",!0),o(m,{label:"\u6837\u5F0F",lazy:""},{default:l(()=>[o(B,{header:"\u7EC4\u4EF6\u6837\u5F0F",class:"property-group"},{default:l(()=>[o(w,{model:d(a),"label-width":"80px"},{default:l(()=>[o(t,{label:"\u7EC4\u4EF6\u80CC\u666F",prop:"bgType"},{default:l(()=>[o(_,{modelValue:d(a).bgType,"onUpdate:modelValue":e[0]||(e[0]=p=>d(a).bgType=p)},{default:l(()=>[o(b,{value:"color"},{default:l(()=>e[3]||(e[3]=[s("\u7EAF\u8272")])),_:1}),o(b,{value:"img"},{default:l(()=>e[4]||(e[4]=[s("\u56FE\u7247")])),_:1})]),_:1},8,["modelValue"])]),_:1}),d(a).bgType==="color"?(i(),u(t,{key:0,label:"\u9009\u62E9\u989C\u8272",prop:"bgColor"},{default:l(()=>[o(h,{modelValue:d(a).bgColor,"onUpdate:modelValue":e[1]||(e[1]=p=>d(a).bgColor=p)},null,8,["modelValue"])]),_:1})):(i(),u(t,{key:1,label:"\u4E0A\u4F20\u56FE\u7247",prop:"bgImg"},{default:l(()=>[o(V,{modelValue:d(a).bgImg,"onUpdate:modelValue":e[2]||(e[2]=p=>d(a).bgImg=p),limit:1},{tip:l(()=>e[5]||(e[5]=[s("\u5EFA\u8BAE\u5BBD\u5EA6 750px")])),_:1},8,["modelValue"])]),_:1})),o(y,{data:R,"expand-on-click-node":!1,"default-expand-all":""},{default:l(({node:p,data:r})=>[o(t,{label:r.label,prop:r.prop,"label-width":p.level===1?"80px":"62px",class:"w-full m-b-0!"},{default:l(()=>[o(T,{modelValue:d(a)[r.prop],"onUpdate:modelValue":g=>d(a)[r.prop]=g,max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1,onInput:g=>(C=>{switch(C){case"margin":a.value.marginTop=a.value.margin,a.value.marginRight=a.value.margin,a.value.marginBottom=a.value.margin,a.value.marginLeft=a.value.margin;break;case"padding":a.value.paddingTop=a.value.padding,a.value.paddingRight=a.value.padding,a.value.paddingBottom=a.value.padding,a.value.paddingLeft=a.value.padding;break;case"borderRadius":a.value.borderTopLeftRadius=a.value.borderRadius,a.value.borderTopRightRadius=a.value.borderRadius,a.value.borderBottomRightRadius=a.value.borderRadius,a.value.borderBottomLeftRadius=a.value.borderRadius}})(r.prop)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1032,["label","prop","label-width"])]),_:1}),v(n.$slots,"style",{style:F(d(a))},void 0,!0)]),_:3},8,["model"])]),_:3})]),_:3})]),_:3})}}}),[["__scopeId","data-v-85e8be98"]]);export{K as _};
