import{d as w,r as m,f as b,q as v,c as _,o as p,F as k,g as a,k as C,w as n,a as o,J as A,M as q,A as P,L as R,K as I,eO as L}from"./index-CRsFgzy0.js";import{E as D}from"./el-skeleton-item-CZ5buDOR.js";import{_ as E}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as F}from"./rank-DcFXIK-C.js";const J=w({name:"ContactCountRank",__name:"ContactCountRank",props:{queryParams:{}},setup(u,{expose:c}){const g=u,e=m(!1),i=m([]),t=b({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u65B0\u589E\u8054\u7CFB\u4EBA\u6570\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u65B0\u589E\u8054\u7CFB\u4EBA\u6570\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u65B0\u589E\u8054\u7CFB\u4EBA\u6570\uFF08\u4E2A\uFF09"},yAxis:{type:"category",name:"\u521B\u5EFA\u4EBA"}}),l=async()=>{e.value=!0;const r=await F.getContactsCountRank(g.queryParams);t.dataset&&t.dataset.source&&(t.dataset.source=L(r).reverse()),i.value=r,e.value=!1};return c({loadData:l}),v(()=>{l()}),(r,K)=>{const h=E,f=D,d=C,s=R,x=I,y=q;return p(),_(k,null,[a(d,{shadow:"never"},{default:n(()=>[a(f,{loading:o(e),animated:""},{default:n(()=>[a(h,{height:500,options:o(t)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(d,{shadow:"never",class:"mt-16px"},{default:n(()=>[A((p(),P(x,{data:o(i)},{default:n(()=>[a(s,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(s,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"nickname","min-width":"200"}),a(s,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(s,{label:"\u65B0\u589E\u8054\u7CFB\u4EBA\u6570\uFF08\u4E2A\uFF09",align:"center",prop:"count","min-width":"200"})]),_:1},8,["data"])),[[y,o(e)]])]),_:1})],64)}}});export{J as _};
