import{d as Y,r as C,f as m,q as P,c as y,o as p,F as f,g as u,k as S,w as n,a as r,J as F,M as k,A as L,y as I,L as N,H as U,t as q,K as E}from"./index-CRsFgzy0.js";import{E as T}from"./el-skeleton-item-CZ5buDOR.js";import{_ as H}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as J}from"./performance-C6IgBWCd.js";const K=Y({name:"ContractCountPerformance",__name:"ContractCountPerformance",props:{queryParams:{}},setup(M,{expose:g}){const b=M,l=C(!1),c=C([]),e=m({grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{},series:[{name:"\u5F53\u6708\u5408\u540C\u6570\u91CF\uFF08\u4E2A\uFF09",type:"line",data:[]},{name:"\u4E0A\u6708\u5408\u540C\u6570\u91CF\uFF08\u4E2A\uFF09",type:"line",data:[]},{name:"\u53BB\u5E74\u540C\u6708\u5408\u540C\u6570\u91CF\uFF08\u4E2A\uFF09",type:"line",data:[]},{name:"\u73AF\u6BD4\u589E\u957F\u7387\uFF08%\uFF09",type:"line",yAxisIndex:1,data:[]},{name:"\u540C\u6BD4\u589E\u957F\u7387\uFF08%\uFF09",type:"line",yAxisIndex:1,data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5BA2\u6237\u603B\u91CF\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6570\u91CF\uFF08\u4E2A\uFF09",axisTick:{show:!1},axisLabel:{color:"#BDBDBD",formatter:"{value}"},axisLine:{lineStyle:{color:"#BDBDBD"}},splitLine:{show:!0,lineStyle:{color:"#e6e6e6"}}},{type:"value",name:"",axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:"#BDBDBD",formatter:"{value}%"},axisLine:{lineStyle:{color:"#BDBDBD"}},splitLine:{show:!0,lineStyle:{color:"#e6e6e6"}}}],xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),x=async()=>{l.value=!0;const t=await J.getContractCountPerformance(b.queryParams);e.xAxis&&e.xAxis.data&&(e.xAxis.data=t.map(a=>a.time)),e.series&&e.series[0]&&e.series[0].data&&(e.series[0].data=t.map(a=>a.currentMonthCount)),e.series&&e.series[1]&&e.series[1].data&&(e.series[1].data=t.map(a=>a.lastMonthCount),e.series[3].data=t.map(a=>a.lastMonthCount!==0?((a.currentMonthCount-a.lastMonthCount)/a.lastMonthCount*100).toFixed(2):"NULL")),e.series&&e.series[2]&&e.series[2].data&&(e.series[2].data=t.map(a=>a.lastYearCount),e.series[4].data=t.map(a=>a.lastYearCount!==0?((a.currentMonthCount-a.lastYearCount)/a.lastYearCount*100).toFixed(2):"NULL")),c.value=t,D(),l.value=!1},s=m([]),o=m([{title:"\u5F53\u6708\u5408\u540C\u6570\u91CF\u7EDF\u8BA1\uFF08\u4E2A\uFF09"},{title:"\u4E0A\u6708\u5408\u540C\u6570\u91CF\u7EDF\u8BA1\uFF08\u4E2A\uFF09"},{title:"\u53BB\u5E74\u5F53\u6708\u5408\u540C\u6570\u91CF\u7EDF\u8BA1\uFF08\u4E2A\uFF09"},{title:"\u73AF\u6BD4\u589E\u957F\u7387\uFF08%\uFF09"},{title:"\u540C\u6BD4\u589E\u957F\u7387\uFF08%\uFF09"}]),D=()=>{s.splice(0,s.length),s.push({label:"\u65E5\u671F",prop:"title"}),c.value.forEach((t,a)=>{const d={label:t.time,prop:"prop"+a};s.push(d),o[0]["prop"+a]=t.currentMonthCount,o[1]["prop"+a]=t.lastMonthCount,o[2]["prop"+a]=t.lastYearCount,o[3]["prop"+a]=t.lastMonthCount!==0?((t.currentMonthCount-t.lastMonthCount)/t.lastMonthCount*100).toFixed(2):"NULL",o[4]["prop"+a]=t.lastYearCount!==0?((t.currentMonthCount-t.lastYearCount)/t.lastYearCount*100).toFixed(2):"NULL"})};return g({loadData:x}),P(async()=>{await x()}),(t,a)=>{const d=H,v=T,h=S,w=N,B=E,A=k;return p(),y(f,null,[u(h,{shadow:"never"},{default:n(()=>[u(v,{loading:r(l),animated:""},{default:n(()=>[u(d,{height:500,options:r(e)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),u(h,{shadow:"never",class:"mt-16px"},{default:n(()=>[F((p(),L(B,{data:r(o)},{default:n(()=>[(p(!0),y(f,null,I(r(s),i=>(p(),L(w,{key:i.prop,label:i.label,prop:i.prop,align:"center"},{default:n(_=>[U(q(_.row[i.prop]),1)]),_:2},1032,["label","prop"]))),128))]),_:1},8,["data"])),[[A,r(l)]])]),_:1})],64)}}});export{K as _};
