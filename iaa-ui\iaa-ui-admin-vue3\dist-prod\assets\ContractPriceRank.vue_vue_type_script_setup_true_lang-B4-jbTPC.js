import{d as w,r as d,f as b,q as v,c as _,o as c,F as k,g as a,k as P,w as o,a as e,J as A,M as q,A as C,L as R,e9 as I,K as L,eO as j}from"./index-CRsFgzy0.js";import{E as D}from"./el-skeleton-item-CZ5buDOR.js";import{_ as E}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as F}from"./rank-DcFXIK-C.js";const J=w({name:"ContractPriceRank",__name:"ContractPriceRank",props:{queryParams:{}},setup(p,{expose:u}){const g=p,t=d(!1),i=d([]),s=b({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u5408\u540C\u91D1\u989D\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5408\u540C\u91D1\u989D\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09"},yAxis:{type:"category",name:"\u7B7E\u8BA2\u4EBA"}}),l=async()=>{t.value=!0;const r=await F.getContractPriceRank(g.queryParams);s.dataset&&s.dataset.source&&(s.dataset.source=j(r).reverse()),i.value=r,t.value=!1};return u({loadData:l}),v(()=>{l()}),(r,K)=>{const f=E,h=D,m=P,n=R,x=L,y=q;return c(),_(k,null,[a(m,{shadow:"never"},{default:o(()=>[a(h,{loading:e(t),animated:""},{default:o(()=>[a(f,{height:500,options:e(s)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(m,{shadow:"never",class:"mt-16px"},{default:o(()=>[A((c(),C(x,{data:e(i)},{default:o(()=>[a(n,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(n,{label:"\u7B7E\u8BA2\u4EBA",align:"center",prop:"nickname","min-width":"200"}),a(n,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(n,{label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09",align:"center",prop:"count","min-width":"200",formatter:e(I)},null,8,["formatter"])]),_:1},8,["data"])),[[y,e(t)]])]),_:1})],64)}}});export{J as _};
