import{d as E,p as A,r as i,f as B,A as c,o as p,a as l,m as L,w as t,J as O,M as P,s as j,g as o,P as G,v as H,x as J,c as N,y as R,B as S,F as z,an as D,H as y,G as K}from"./index-CRsFgzy0.js";import{_ as Q}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{C as W}from"./constants-uird_4gU.js";import{C as X}from"./index-BhYBVsvq.js";import{C}from"./index-expHhv7K.js";const Y=E({name:"ChatConversationUpdateForm",__name:"ConversationUpdateForm",emits:["success"],setup(Z,{expose:V,emit:b}){const _=A(),m=i(!1),u=i(!1),s=i({id:void 0,systemMessage:void 0,modelId:void 0,temperature:void 0,maxTokens:void 0,maxContexts:void 0}),k=B({modelId:[{required:!0,message:"\u6A21\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],temperature:[{required:!0,message:"\u6E29\u5EA6\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],maxTokens:[{required:!0,message:"\u56DE\u590D\u6570 Token \u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],maxContexts:[{required:!0,message:"\u4E0A\u4E0B\u6587\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=i(),f=i([]);V({open:async r=>{if(m.value=!0,T(),r){u.value=!0;try{const e=await C.getChatConversationMy(r);s.value=Object.keys(s.value).reduce((n,d)=>(e.hasOwnProperty(d)&&(n[d]=e[d]),n),{})}finally{u.value=!1}}f.value=await X.getChatModelSimpleList(W.ENABLE)}});const h=b,M=async()=>{await v.value.validate(),u.value=!0;try{const r=s.value;await C.updateChatConversationMy(r),_.success("\u5BF9\u8BDD\u914D\u7F6E\u5DF2\u66F4\u65B0"),m.value=!1,h("success")}finally{u.value=!1}},T=()=>{var r;s.value={id:void 0,systemMessage:void 0,modelId:void 0,temperature:void 0,maxTokens:void 0,maxContexts:void 0},(r=v.value)==null||r.resetFields()};return(r,e)=>{const n=G,d=H,w=S,U=J,x=D,I=j,g=K,q=Q,F=P;return p(),c(q,{title:"\u8BBE\u5B9A",modelValue:l(m),"onUpdate:modelValue":e[6]||(e[6]=a=>L(m)?m.value=a:null)},{footer:t(()=>[o(g,{onClick:M,type:"primary",disabled:l(u)},{default:t(()=>e[7]||(e[7]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),o(g,{onClick:e[5]||(e[5]=a=>m.value=!1)},{default:t(()=>e[8]||(e[8]=[y("\u53D6 \u6D88")])),_:1})]),default:t(()=>[O((p(),c(I,{ref_key:"formRef",ref:v,model:l(s),rules:l(k),"label-width":"130px"},{default:t(()=>[o(d,{label:"\u89D2\u8272\u8BBE\u5B9A",prop:"systemMessage"},{default:t(()=>[o(n,{type:"textarea",modelValue:l(s).systemMessage,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).systemMessage=a),rows:"4",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u8BBE\u5B9A"},null,8,["modelValue"])]),_:1}),o(d,{label:"\u6A21\u578B",prop:"modelId"},{default:t(()=>[o(U,{modelValue:l(s).modelId,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).modelId=a),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B"},{default:t(()=>[(p(!0),N(z,null,R(l(f),a=>(p(),c(w,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(d,{label:"\u6E29\u5EA6\u53C2\u6570",prop:"temperature"},{default:t(()=>[o(x,{modelValue:l(s).temperature,"onUpdate:modelValue":e[2]||(e[2]=a=>l(s).temperature=a),placeholder:"\u8BF7\u8F93\u5165\u6E29\u5EA6\u53C2\u6570",min:0,max:2,precision:2},null,8,["modelValue"])]),_:1}),o(d,{label:"\u56DE\u590D\u6570 Token \u6570",prop:"maxTokens"},{default:t(()=>[o(x,{modelValue:l(s).maxTokens,"onUpdate:modelValue":e[3]||(e[3]=a=>l(s).maxTokens=a),placeholder:"\u8BF7\u8F93\u5165\u56DE\u590D\u6570 Token \u6570",min:0,max:4096},null,8,["modelValue"])]),_:1}),o(d,{label:"\u4E0A\u4E0B\u6587\u6570\u91CF",prop:"maxContexts"},{default:t(()=>[o(x,{modelValue:l(s).maxContexts,"onUpdate:modelValue":e[4]||(e[4]=a=>l(s).maxContexts=a),placeholder:"\u8BF7\u8F93\u5165\u4E0A\u4E0B\u6587\u6570\u91CF",min:0,max:20},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[F,l(u)]])]),_:1},8,["modelValue"])}}});export{Y as _};
