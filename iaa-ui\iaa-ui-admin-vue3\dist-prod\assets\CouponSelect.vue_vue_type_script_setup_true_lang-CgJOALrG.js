import{d as B,r as s,f as G,A as g,o as _,a as l,m as H,w as o,g as e,s as W,v as j,P as Q,x as X,c as Z,F as $,y as J,R as ee,D as i,B as le,G as ae,H as d,I as te,J as oe,M as pe,K as ue,L as ne,t as se}from"./index-CRsFgzy0.js";import{_ as re}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as ie}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as de}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{d as ce,v as fe,r as _e,t as ye}from"./formatter-Ctw2YEr4.js";import{g as Te}from"./couponTemplate-Be-ijyut.js";import{h as ve}from"./constants-uird_4gU.js";const ge=B({name:"CouponSelect",__name:"CouponSelect",props:{multipleSelection:{},takeType:{}},emits:["update:multipleSelection","change"],setup(U,{expose:x,emit:R}){const b=U,O=R,r=s(!1),V=s("\u9009\u62E9\u4F18\u60E0\u52B5"),E=s(!1),y=s(!0),S=s(0),w=s([]),p=G({pageNo:1,pageSize:10,name:null,discountType:null,canTakeTypes:[ve.USER.type]}),m=s(),h=s([]),C=async()=>{y.value=!0;try{p.canTakeTypes=[b.takeType];const n=await Te(p);w.value=n.list,S.value=n.total}finally{y.value=!1}},T=()=>{p.pageNo=1,C()},k=()=>{var n;(n=m==null?void 0:m.value)==null||n.resetFields(),T()};x({open:async()=>{r.value=!0,k()}});const I=n=>{b.multipleSelection?O("update:multipleSelection",n):h.value=n},M=()=>{r.value=!1,O("change",h.value)};return(n,t)=>{const D=Q,v=j,A=le,F=X,N=te,c=ae,z=W,P=me,u=ne,f=de,K=ue,Y=ie,L=re,q=pe;return _(),g(L,{modelValue:l(r),"onUpdate:modelValue":t[5]||(t[5]=a=>H(r)?r.value=a:null),title:l(V),width:"65%"},{footer:o(()=>[e(c,{disabled:l(E),type:"primary",onClick:M},{default:o(()=>t[8]||(t[8]=[d("\u786E \u5B9A")])),_:1},8,["disabled"]),e(c,{onClick:t[4]||(t[4]=a=>r.value=!1)},{default:o(()=>t[9]||(t[9]=[d("\u53D6 \u6D88")])),_:1})]),default:o(()=>[e(P,null,{default:o(()=>[e(z,{ref_key:"queryFormRef",ref:m,inline:!0,model:l(p),class:"-mb-15px","label-width":"82px"},{default:o(()=>[e(v,{label:"\u4F18\u60E0\u5238\u540D\u79F0",prop:"name"},{default:o(()=>[e(D,{modelValue:l(p).name,"onUpdate:modelValue":t[0]||(t[0]=a=>l(p).name=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u52B5\u540D",onKeyup:T},null,8,["modelValue"])]),_:1}),e(v,{label:"\u4F18\u60E0\u7C7B\u578B",prop:"discountType"},{default:o(()=>[e(F,{modelValue:l(p).discountType,"onUpdate:modelValue":t[1]||(t[1]=a=>l(p).discountType=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u4F18\u60E0\u5238\u7C7B\u578B"},{default:o(()=>[(_(!0),Z($,null,J(l(ee)(l(i).PROMOTION_DISCOUNT_TYPE),a=>(_(),g(A,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:o(()=>[e(c,{onClick:T},{default:o(()=>[e(N,{class:"mr-5px",icon:"ep:search"}),t[6]||(t[6]=d(" \u641C\u7D22 "))]),_:1}),e(c,{onClick:k},{default:o(()=>[e(N,{class:"mr-5px",icon:"ep:refresh"}),t[7]||(t[7]=d(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:o(()=>[oe((_(),g(K,{data:l(w),onSelectionChange:I},{default:o(()=>[e(u,{type:"selection",width:"55"}),e(u,{label:"\u4F18\u60E0\u5238\u540D\u79F0","min-width":"140",prop:"name"}),e(u,{label:"\u7C7B\u578B","min-width":"80",prop:"productScope"},{default:o(a=>[e(f,{type:l(i).PROMOTION_PRODUCT_SCOPE,value:a.row.productScope},null,8,["type","value"])]),_:1}),e(u,{label:"\u4F18\u60E0","min-width":"100",prop:"discount"},{default:o(a=>[e(f,{type:l(i).PROMOTION_DISCOUNT_TYPE,value:a.row.discountType},null,8,["type","value"]),d(" "+se(l(ce)(a.row)),1)]),_:1}),e(u,{label:"\u9886\u53D6\u65B9\u5F0F","min-width":"100",prop:"takeType"},{default:o(a=>[e(f,{type:l(i).PROMOTION_COUPON_TAKE_TYPE,value:a.row.takeType},null,8,["type","value"])]),_:1}),e(u,{formatter:l(fe),align:"center",label:"\u4F7F\u7528\u65F6\u95F4",prop:"validityType",width:"185"},null,8,["formatter"]),e(u,{align:"center",label:"\u53D1\u653E\u6570\u91CF",prop:"totalCount"}),e(u,{formatter:l(_e),align:"center",label:"\u5269\u4F59\u6570\u91CF",prop:"totalCount"},null,8,["formatter"]),e(u,{formatter:l(ye),align:"center",label:"\u9886\u53D6\u4E0A\u9650",prop:"takeLimitCount"},null,8,["formatter"]),e(u,{align:"center",label:"\u72B6\u6001",prop:"status"},{default:o(a=>[e(f,{type:l(i).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1})]),_:1},8,["data"])),[[q,l(y)]]),e(Y,{limit:l(p).pageSize,"onUpdate:limit":t[2]||(t[2]=a=>l(p).pageSize=a),page:l(p).pageNo,"onUpdate:page":t[3]||(t[3]=a=>l(p).pageNo=a),total:l(S),onPagination:C},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue","title"])}}});export{ge as _};
