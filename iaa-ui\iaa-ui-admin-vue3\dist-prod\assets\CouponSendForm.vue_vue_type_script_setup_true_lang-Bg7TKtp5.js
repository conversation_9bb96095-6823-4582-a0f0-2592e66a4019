import{d as j,p as q,r as i,O as B,A as g,o as v,w as n,g as l,J as P,i as D,s as G,a as e,v as H,P as J,G as L,H as h,I as O,K as R,L as E,M as K,m as Q}from"./index-CRsFgzy0.js";import{_ as $}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as W}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{g as X}from"./couponTemplate-Be-ijyut.js";import{s as Y}from"./coupon-DpNOGVQz.js";import{d as Z,u as ee,v as ae,r as le}from"./formatter-Ctw2YEr4.js";import{h as oe}from"./constants-uird_4gU.js";const te=j({name:"PromotionCouponSendForm",__name:"CouponSendForm",setup(ne,{expose:S}){const V=q(),y=i(0),w=i([]),u=i(!1),m=i(!1),p=i(!1),o=i({pageNo:1,pageSize:10,name:null,canTakeTypes:[oe.ADMIN.type]}),d=i();let _=[];S({open:r=>{_=r,x(),p.value=!0}});const b=async()=>{u.value=!0;try{const r=await X(o.value);w.value=r.list,y.value=r.total}finally{u.value=!1}},c=()=>{o.value.pageNo=1,b()},x=()=>{var r;(r=d==null?void 0:d.value)==null||r.resetFields(),c()};return(r,a)=>{const N=J,k=H,C=O,f=L,F=G,s=E,I=R,T=W,U=$,z=B("hasPermi"),A=K;return v(),g(U,{modelValue:e(p),"onUpdate:modelValue":a[3]||(a[3]=t=>Q(p)?p.value=t:null),appendToBody:!0,title:"\u53D1\u9001\u4F18\u60E0\u5238",width:"70%"},{default:n(()=>[l(F,{ref_key:"queryFormRef",ref:d,inline:!0,model:e(o),class:"-mb-15px","label-width":"82px"},{default:n(()=>[l(k,{label:"\u4F18\u60E0\u5238\u540D\u79F0",prop:"name"},{default:n(()=>[l(N,{modelValue:e(o).name,"onUpdate:modelValue":a[0]||(a[0]=t=>e(o).name=t),class:"!w-240px",placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u52B5\u540D",clearable:"",onKeyup:c},null,8,["modelValue"])]),_:1}),l(k,null,{default:n(()=>[l(f,{onClick:c},{default:n(()=>[l(C,{class:"mr-5px",icon:"ep:search"}),a[4]||(a[4]=h(" \u641C\u7D22 "))]),_:1}),l(f,{onClick:x},{default:n(()=>[l(C,{class:"mr-5px",icon:"ep:refresh"}),a[5]||(a[5]=h(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),P((v(),g(I,{data:e(w),"show-overflow-tooltip":""},{default:n(()=>[l(s,{align:"center",label:"\u4F18\u60E0\u5238\u540D\u79F0",prop:"name","min-width":"60"}),l(s,{label:"\u4F18\u60E0\u91D1\u989D / \u6298\u6263",align:"center",prop:"discount",formatter:e(Z),"min-width":"60"},null,8,["formatter"]),l(s,{align:"center",label:"\u6700\u4F4E\u6D88\u8D39",prop:"usePrice","min-width":"60",formatter:e(ee)},null,8,["formatter"]),l(s,{align:"center",label:"\u6709\u6548\u671F\u9650",prop:"validityType","min-width":"140",formatter:e(ae)},null,8,["formatter"]),l(s,{align:"center",label:"\u5269\u4F59\u6570\u91CF","min-width":"60",formatter:e(le)},null,8,["formatter"]),l(s,{label:"\u64CD\u4F5C",align:"center","min-width":"60px",fixed:"right"},{default:n(t=>[P((v(),g(f,{link:"",type:"primary",disabled:e(m),loading:e(m),onClick:re=>(async M=>{try{m.value=!0,await Y({templateId:M,userIds:_}),V.success("\u53D1\u9001\u6210\u529F"),p.value=!1}finally{m.value=!1}})(t.row.id)},{default:n(()=>a[6]||(a[6]=[h(" \u53D1\u9001 ")])),_:2},1032,["disabled","loading","onClick"])),[[z,["promotion:coupon:send"]]])]),_:1})]),_:1},8,["data"])),[[A,e(u)]]),l(T,{limit:e(o).pageSize,"onUpdate:limit":a[1]||(a[1]=t=>e(o).pageSize=t),page:e(o).pageNo,"onUpdate:page":a[2]||(a[2]=t=>e(o).pageNo=t),total:e(y),onPagination:b},null,8,["limit","page","total"]),a[7]||(a[7]=D("div",{class:"clear-both"},null,-1))]),_:1},8,["modelValue"])}}});export{te as _};
