import{d as Q,b as j,p as K,r as f,f as W,A as i,o as u,w as d,J as X,s as Z,a,g as o,a3 as m,v as $,P as ee,aB as le,c as k,F as b,y as E,R as x,D as I,aC as ae,H as r,t as L,an as te,C as de,M as oe,G as ue,m as ie,aO as w,aQ as U,aG as re}from"./index-CRsFgzy0.js";import{_ as se}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as ce,c as pe,u as me}from"./couponTemplate-Be-ijyut.js";import{k as v,i as V,j as _}from"./constants-uird_4gU.js";import ne from"./SpuShowcase-DtIWpNCA.js";import{_ as ve}from"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";const ye=Q({name:"CouponTemplateForm",__name:"CouponTemplateForm",emits:["success"],setup(Te,{expose:A,emit:D}){const{t:S}=j(),h=K(),y=f(!1),O=f(""),T=f(!1),R=f(""),e=f({id:void 0,name:void 0,discountType:V.PRICE.type,discountPrice:void 0,discountPercent:void 0,discountLimitPrice:void 0,usePrice:void 0,takeType:1,totalCount:void 0,takeLimitCount:void 0,validityType:_.DATE.type,validTimes:[],validStartTime:void 0,validEndTime:void 0,fixedStartTerm:void 0,fixedEndTerm:void 0,productScope:v.ALL.scope,description:void 0,productScopeValues:[],productCategoryIds:[],productSpuIds:[]}),N=W({name:[{required:!0,message:"\u4F18\u60E0\u5238\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],discountType:[{required:!0,message:"\u4F18\u60E0\u5238\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],discountPrice:[{required:!0,message:"\u4F18\u60E0\u5238\u9762\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],discountPercent:[{required:!0,message:"\u4F18\u60E0\u5238\u6298\u6263\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],discountLimitPrice:[{required:!0,message:"\u6700\u591A\u4F18\u60E0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],usePrice:[{required:!0,message:"\u6EE1\u591A\u5C11\u5143\u53EF\u4EE5\u4F7F\u7528\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],takeType:[{required:!0,message:"\u9886\u53D6\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],totalCount:[{required:!0,message:"\u53D1\u653E\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],takeLimitCount:[{required:!0,message:"\u6BCF\u4EBA\u9650\u9886\u4E2A\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],validityType:[{required:!0,message:"\u6709\u6548\u671F\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],validTimes:[{required:!0,message:"\u56FA\u5B9A\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],fixedStartTerm:[{required:!0,message:"\u5F00\u59CB\u9886\u53D6\u5929\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],fixedEndTerm:[{required:!0,message:"\u5F00\u59CB\u9886\u53D6\u5929\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productScope:[{required:!0,message:"\u5546\u54C1\u8303\u56F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productSpuIds:[{required:!0,message:"\u5546\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productCategoryIds:[{required:!0,message:"\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),P=f();A({open:async(s,l)=>{if(y.value=!0,O.value=S("action."+s),R.value=s,F(),l){T.value=!0;try{const p=await ce(l);e.value={...p,discountPrice:w(p.discountPrice),discountPercent:p.discountPercent!==void 0?p.discountPercent/10:void 0,discountLimitPrice:w(p.discountLimitPrice),usePrice:w(p.usePrice),validTimes:[p.validStartTime,p.validEndTime]},await G()}finally{T.value=!1}}}});const Y=D,M=async()=>{if(P&&await P.value.validate()){T.value=!0;try{const s={...e.value,discountPrice:U(e.value.discountPrice),discountPercent:e.value.discountPercent!==void 0?10*e.value.discountPercent:void 0,discountLimitPrice:U(e.value.discountLimitPrice),usePrice:U(e.value.usePrice),validStartTime:e.value.validTimes&&e.value.validTimes.length===2?e.value.validTimes[0]:void 0,validEndTime:e.value.validTimes&&e.value.validTimes.length===2?e.value.validTimes[1]:void 0,totalCount:e.value.takeType===1?e.value.totalCount:-1,takeLimitCount:e.value.takeType===1?e.value.takeLimitCount:-1};(function(l){switch(e.value.productScope){case v.SPU.scope:l.productScopeValues=e.value.productSpuIds;break;case v.CATEGORY.scope:l.productScopeValues=Array.isArray(e.value.productCategoryIds)?e.value.productCategoryIds:[e.value.productCategoryIds]}})(s),R.value==="create"?(await pe(s),h.success(S("common.createSuccess"))):(await me(s),h.success(S("common.updateSuccess"))),y.value=!1,Y("success")}finally{T.value=!1}}},F=()=>{var s;e.value={id:void 0,name:void 0,description:void 0,discountType:V.PRICE.type,discountPrice:void 0,discountPercent:void 0,discountLimitPrice:void 0,usePrice:void 0,takeType:1,totalCount:void 0,takeLimitCount:void 0,validityType:_.DATE.type,validTimes:[],validStartTime:void 0,validEndTime:void 0,fixedStartTerm:void 0,fixedEndTerm:void 0,productScope:v.ALL.scope,productScopeValues:[],productSpuIds:[],productCategoryIds:[]},(s=P.value)==null||s.resetFields()},G=async()=>{switch(e.value.productScope){case v.SPU.scope:e.value.productSpuIds=e.value.productScopeValues;break;case v.CATEGORY.scope:await re(()=>{let s=e.value.productScopeValues;Array.isArray(s)&&s.length>0&&(s=s[0]),e.value.productCategoryIds=s})}};return(s,l)=>{const p=ee,c=$,g=ae,C=le,n=te,z=de,B=Z,q=ue,H=se,J=oe;return u(),i(H,{modelValue:a(y),"onUpdate:modelValue":l[18]||(l[18]=t=>ie(y)?y.value=t:null),title:a(O)},{footer:d(()=>[o(q,{disabled:a(T),type:"primary",onClick:M},{default:d(()=>l[31]||(l[31]=[r("\u786E \u5B9A")])),_:1},8,["disabled"]),o(q,{onClick:l[17]||(l[17]=t=>y.value=!1)},{default:d(()=>l[32]||(l[32]=[r("\u53D6 \u6D88")])),_:1})]),default:d(()=>[X((u(),i(B,{ref_key:"formRef",ref:P,model:a(e),rules:a(N),"label-width":"140px"},{default:d(()=>[o(c,{label:"\u4F18\u60E0\u5238\u540D\u79F0",prop:"name"},{default:d(()=>[o(p,{modelValue:a(e).name,"onUpdate:modelValue":l[0]||(l[0]=t=>a(e).name=t),placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u5238\u540D\u79F0"},null,8,["modelValue"])]),_:1}),o(c,{label:"\u4F18\u60E0\u5238\u63CF\u8FF0",prop:"description"},{default:d(()=>[o(p,{modelValue:a(e).description,"onUpdate:modelValue":l[1]||(l[1]=t=>a(e).description=t),autosize:{minRows:2,maxRows:2},clearable:!0,"show-word-limit":!0,class:"w-1/1!",maxlength:"512",placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u5238\u63CF\u8FF0",type:"textarea"},null,8,["modelValue"])]),_:1}),o(c,{label:"\u4F18\u60E0\u52B5\u7C7B\u578B",prop:"productScope"},{default:d(()=>[o(C,{modelValue:a(e).productScope,"onUpdate:modelValue":l[2]||(l[2]=t=>a(e).productScope=t)},{default:d(()=>[(u(!0),k(b,null,E(a(x)(a(I).PROMOTION_PRODUCT_SCOPE),t=>(u(),i(g,{key:t.value,value:t.value},{default:d(()=>[r(L(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(e).productScope===a(v).SPU.scope?(u(),i(c,{key:0,label:"\u5546\u54C1",prop:"productSpuIds"},{default:d(()=>[o(ne,{modelValue:a(e).productSpuIds,"onUpdate:modelValue":l[3]||(l[3]=t=>a(e).productSpuIds=t)},null,8,["modelValue"])]),_:1})):m("",!0),a(e).productScope===a(v).CATEGORY.scope?(u(),i(c,{key:1,label:"\u5206\u7C7B",prop:"productCategoryIds"},{default:d(()=>[o(ve,{modelValue:a(e).productCategoryIds,"onUpdate:modelValue":l[4]||(l[4]=t=>a(e).productCategoryIds=t)},null,8,["modelValue"])]),_:1})):m("",!0),o(c,{label:"\u4F18\u60E0\u7C7B\u578B",prop:"discountType"},{default:d(()=>[o(C,{modelValue:a(e).discountType,"onUpdate:modelValue":l[5]||(l[5]=t=>a(e).discountType=t)},{default:d(()=>[(u(!0),k(b,null,E(a(x)(a(I).PROMOTION_DISCOUNT_TYPE),t=>(u(),i(g,{key:t.value,value:t.value},{default:d(()=>[r(L(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(e).discountType===a(V).PRICE.type?(u(),i(c,{key:2,label:"\u4F18\u60E0\u5238\u9762\u989D",prop:"discountPrice"},{default:d(()=>[o(n,{modelValue:a(e).discountPrice,"onUpdate:modelValue":l[6]||(l[6]=t=>a(e).discountPrice=t),min:0,precision:2,class:"mr-2 !w-400px",placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u91D1\u989D\uFF0C\u5355\u4F4D\uFF1A\u5143"},null,8,["modelValue"]),l[19]||(l[19]=r(" \u5143 "))]),_:1})):m("",!0),a(e).discountType===a(V).PERCENT.type?(u(),i(c,{key:3,label:"\u4F18\u60E0\u5238\u6298\u6263",prop:"discountPercent"},{default:d(()=>[o(n,{modelValue:a(e).discountPercent,"onUpdate:modelValue":l[7]||(l[7]=t=>a(e).discountPercent=t),max:9.9,min:1,precision:1,class:"mr-2 !w-400px",placeholder:"\u4F18\u60E0\u5238\u6298\u6263\u4E0D\u80FD\u5C0F\u4E8E 1 \u6298\uFF0C\u4E14\u4E0D\u53EF\u5927\u4E8E 9.9 \u6298"},null,8,["modelValue"]),l[20]||(l[20]=r(" \u6298 "))]),_:1})):m("",!0),a(e).discountType===a(V).PERCENT.type?(u(),i(c,{key:4,label:"\u6700\u591A\u4F18\u60E0",prop:"discountLimitPrice"},{default:d(()=>[o(n,{modelValue:a(e).discountLimitPrice,"onUpdate:modelValue":l[8]||(l[8]=t=>a(e).discountLimitPrice=t),min:0,precision:2,class:"mr-2 !w-400px",placeholder:"\u8BF7\u8F93\u5165\u6700\u591A\u4F18\u60E0"},null,8,["modelValue"]),l[21]||(l[21]=r(" \u5143 "))]),_:1})):m("",!0),o(c,{label:"\u6EE1\u591A\u5C11\u5143\u53EF\u4EE5\u4F7F\u7528",prop:"usePrice"},{default:d(()=>[o(n,{modelValue:a(e).usePrice,"onUpdate:modelValue":l[9]||(l[9]=t=>a(e).usePrice=t),min:0,precision:2,class:"mr-2 !w-400px",placeholder:"\u65E0\u95E8\u69DB\u8BF7\u8BBE\u4E3A 0"},null,8,["modelValue"]),l[22]||(l[22]=r(" \u5143 "))]),_:1}),o(c,{label:"\u9886\u53D6\u65B9\u5F0F",prop:"takeType"},{default:d(()=>[o(C,{modelValue:a(e).takeType,"onUpdate:modelValue":l[10]||(l[10]=t=>a(e).takeType=t)},{default:d(()=>[(u(),i(g,{key:1,value:1},{default:d(()=>l[23]||(l[23]=[r("\u76F4\u63A5\u9886\u53D6")])),_:1})),(u(),i(g,{key:2,value:2},{default:d(()=>l[24]||(l[24]=[r("\u6307\u5B9A\u53D1\u653E")])),_:1})),(u(),i(g,{key:2,value:3},{default:d(()=>l[25]||(l[25]=[r("\u65B0\u4EBA\u52B5")])),_:1}))]),_:1},8,["modelValue"])]),_:1}),a(e).takeType===1?(u(),i(c,{key:5,label:"\u53D1\u653E\u6570\u91CF",prop:"totalCount"},{default:d(()=>[o(n,{modelValue:a(e).totalCount,"onUpdate:modelValue":l[11]||(l[11]=t=>a(e).totalCount=t),min:-1,precision:0,class:"mr-2 !w-400px",placeholder:"\u53D1\u653E\u6570\u91CF\uFF0C\u6CA1\u6709\u4E4B\u540E\u4E0D\u80FD\u9886\u53D6\u6216\u53D1\u653E\uFF0C-1 \u4E3A\u4E0D\u9650\u5236"},null,8,["modelValue"]),l[26]||(l[26]=r(" \u5F20 "))]),_:1})):m("",!0),a(e).takeType===1?(u(),i(c,{key:6,label:"\u6BCF\u4EBA\u9650\u9886\u4E2A\u6570",prop:"takeLimitCount"},{default:d(()=>[o(n,{modelValue:a(e).takeLimitCount,"onUpdate:modelValue":l[12]||(l[12]=t=>a(e).takeLimitCount=t),min:-1,precision:0,class:"mr-2 !w-400px",placeholder:"\u8BBE\u7F6E\u4E3A -1 \u65F6\uFF0C\u53EF\u65E0\u9650\u9886\u53D6"},null,8,["modelValue"]),l[27]||(l[27]=r(" \u5F20 "))]),_:1})):m("",!0),o(c,{label:"\u6709\u6548\u671F\u7C7B\u578B",prop:"validityType"},{default:d(()=>[o(C,{modelValue:a(e).validityType,"onUpdate:modelValue":l[13]||(l[13]=t=>a(e).validityType=t)},{default:d(()=>[(u(!0),k(b,null,E(a(x)(a(I).PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE),t=>(u(),i(g,{key:t.value,value:t.value},{default:d(()=>[r(L(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(e).validityType===a(_).DATE.type?(u(),i(c,{key:7,label:"\u56FA\u5B9A\u65E5\u671F",prop:"validTimes"},{default:d(()=>[o(z,{modelValue:a(e).validTimes,"onUpdate:modelValue":l[14]||(l[14]=t=>a(e).validTimes=t),"default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)],type:"datetimerange","value-format":"x"},null,8,["modelValue","default-time"])]),_:1})):m("",!0),a(e).validityType===a(_).TERM.type?(u(),i(c,{key:8,label:"\u9886\u53D6\u65E5\u671F",prop:"fixedStartTerm"},{default:d(()=>[l[28]||(l[28]=r(" \u7B2C ")),o(n,{modelValue:a(e).fixedStartTerm,"onUpdate:modelValue":l[15]||(l[15]=t=>a(e).fixedStartTerm=t),min:0,precision:0,class:"mx-2",placeholder:"0 \u4E3A\u4ECA\u5929\u751F\u6548"},null,8,["modelValue"]),l[29]||(l[29]=r(" \u81F3 ")),o(n,{modelValue:a(e).fixedEndTerm,"onUpdate:modelValue":l[16]||(l[16]=t=>a(e).fixedEndTerm=t),min:0,precision:0,class:"mx-2",placeholder:"\u8BF7\u8F93\u5165\u7ED3\u675F\u5929\u6570"},null,8,["modelValue"]),l[30]||(l[30]=r(" \u5929\u6709\u6548 "))]),_:1})):m("",!0)]),_:1},8,["model","rules"])),[[J,a(T)]])]),_:1},8,["modelValue","title"])}}});export{ye as _};
