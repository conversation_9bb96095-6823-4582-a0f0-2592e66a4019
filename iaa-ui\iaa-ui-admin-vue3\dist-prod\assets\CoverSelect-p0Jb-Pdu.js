import{d as C,p as F,ed as S,a2 as E,bv as G,r as v,f as H,c as q,o as u,i as o,A as h,g as r,a as e,I as L,X as R,bh as W,w as s,G as X,H as f,cz as D,m as J,_ as K}from"./index-CRsFgzy0.js";import{E as N}from"./el-image-BQpHFDaE.js";import{W as O}from"./main-87AQXDxa.js";import{U as b,u as P}from"./useUpload-D8UAyHOj.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./main-3TzxBWwr.js";import"./main-DDCad-8G.js";import"./BenzAMRRecorder-WUNTwMf2.js";import"./main.vue_vue_type_script_setup_true_lang-DoLPAFxa.js";import"./index-1FyZgYZc.js";import"./index-C64AUasM.js";import"./formatTime-DhdtkSIS.js";const Q={class:"thumb-div"},T={class:"thumb-but"},Y=K(C({__name:"CoverSelect",props:{modelValue:{},isFirst:{type:Boolean}},emits:["update:modelValue"],setup(g,{emit:y}){const m=F(),_={Authorization:"Bearer "+S()},I=g,V=y,l=E({get:()=>I.modelValue,set(a){V("update:modelValue",a)}}),d=G("accountId"),i=v(!1),p=v([]),U=H({type:b.Image,accountId:d}),x=a=>{i.value=!1,l.value.thumbMediaId=a.mediaId,l.value.thumbUrl=a.url},w=a=>P(b.Image,2)(a),z=a=>{if(a.code!==0)return m.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;p.value=[],l.value.thumbMediaId=a.data.mediaId,l.value.thumbUrl=a.data.url},M=a=>{m.error("\u4E0A\u4F20\u5931\u8D25: "+a.message)};return(a,t)=>{const k=N,j=L,n=X,A=W,B=D;return u(),q("div",null,[t[5]||(t[5]=o("p",null,"\u5C01\u9762:",-1)),o("div",Q,[e(l).thumbUrl?(u(),h(k,{key:0,style:{width:"300px","max-height":"300px"},src:e(l).thumbUrl,fit:"contain"},null,8,["src"])):(u(),h(j,{key:1,icon:"ep:plus",class:R(["avatar-uploader-icon",a.isFirst?"avatar":"avatar1"])},null,8,["class"])),o("div",T,[r(A,{action:"http://shouhou.iaa360.com/admin-api/mp/material/upload-permanent",headers:_,multiple:"",limit:1,"file-list":e(p),data:e(U),"before-upload":w,"on-error":M,"on-success":z},{trigger:s(()=>[r(n,{size:"small",type:"primary"},{default:s(()=>t[2]||(t[2]=[f("\u672C\u5730\u4E0A\u4F20")])),_:1})]),tip:s(()=>t[4]||(t[4]=[o("div",{class:"el-upload__tip"},"\u652F\u6301 bmp/png/jpeg/jpg/gif \u683C\u5F0F\uFF0C\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M",-1)])),default:s(()=>[r(n,{size:"small",type:"primary",onClick:t[0]||(t[0]=c=>i.value=!0),style:{"margin-left":"5px"}},{default:s(()=>t[3]||(t[3]=[f(" \u7D20\u6750\u5E93\u9009\u62E9 ")])),_:1})]),_:1},8,["file-list","data"])]),r(B,{title:"\u9009\u62E9\u56FE\u7247",modelValue:e(i),"onUpdate:modelValue":t[1]||(t[1]=c=>J(i)?i.value=c:null),width:"80%","append-to-body":"","destroy-on-close":""},{default:s(()=>[r(e(O),{type:"image","account-id":e(d),onSelectMaterial:x},null,8,["account-id"])]),_:1},8,["modelValue"])])])}}}),[["__scopeId","data-v-9d295cb2"]]);export{Y as default};
