import{d as D,r as g,f as N,q as P,c as R,o as y,F as U,g as e,k as I,w as i,a as t,J as M,M as q,A as E,L as T,e9 as x,D as w,K as O}from"./index-CRsFgzy0.js";import{_ as F}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as L}from"./el-skeleton-item-CZ5buDOR.js";import{_ as j}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as h}from"./customer-BAP7SwSn.js";import{d as C}from"./formatTime-DhdtkSIS.js";const k=D({name:"CustomerConversionStat",__name:"CustomerConversionStat",props:{queryParams:{}},setup(b,{expose:v}){const m=b,l=g(!1),d=g([]),r=N({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u5BA2\u6237\u8F6C\u5316\u7387",type:"line",data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5BA2\u6237\u8F6C\u5316\u7387\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:{type:"value",name:"\u8F6C\u5316\u7387(%)"},xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),p=async()=>{l.value=!0;try{await(async()=>{const n=await h.getCustomerSummaryByDate(m.queryParams),u=await h.getContractSummary(m.queryParams);r.xAxis&&r.xAxis.data&&(r.xAxis.data=n.map(o=>o.time)),r.series&&r.series[0]&&r.series[0].data&&(r.series[0].data=n.map(o=>({name:o.time,value:o.customerCreateCount?(o.customerDealCount/o.customerCreateCount*100).toFixed(2):0}))),d.value=u})()}finally{l.value=!1}};return v({loadData:p}),P(()=>{p()}),(n,u)=>{const o=j,_=L,c=I,a=T,f=F,S=O,A=q;return y(),R(U,null,[e(c,{shadow:"never"},{default:i(()=>[e(_,{loading:t(l),animated:""},{default:i(()=>[e(o,{height:500,options:t(r)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(c,{shadow:"never",class:"mt-16px"},{default:i(()=>[M((y(),E(S,{data:t(d)},{default:i(()=>[e(a,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80",fixed:"left"}),e(a,{label:"\u5BA2\u6237\u540D\u79F0",align:"center",prop:"customerName","min-width":"200",fixed:"left"}),e(a,{label:"\u5408\u540C\u540D\u79F0",align:"center",prop:"contractName","min-width":"200"}),e(a,{label:"\u5408\u540C\u603B\u91D1\u989D",align:"center",prop:"totalPrice","min-width":"200",formatter:t(x)},null,8,["formatter"]),e(a,{label:"\u56DE\u6B3E\u91D1\u989D",align:"center",prop:"receivablePrice","min-width":"200",formatter:t(x)},null,8,["formatter"]),e(a,{align:"center",label:"\u5BA2\u6237\u6765\u6E90",prop:"source",width:"100"},{default:i(s=>[e(f,{type:t(w).CRM_CUSTOMER_SOURCE,value:s.row.source},null,8,["type","value"])]),_:1}),e(a,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:i(s=>[e(f,{type:t(w).CRM_CUSTOMER_INDUSTRY,value:s.row.industryId},null,8,["type","value"])]),_:1}),e(a,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"ownerUserName","min-width":"200"}),e(a,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorUserName","min-width":"200"}),e(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(C),"min-width":"200"},null,8,["formatter"]),e(a,{label:"\u4E0B\u5355\u65E5\u671F",align:"center",prop:"orderDate",formatter:t(C),"min-width":"200",fixed:"right"},null,8,["formatter"])]),_:1},8,["data"])),[[A,t(l)]])]),_:1})],64)}}});export{k as _};
