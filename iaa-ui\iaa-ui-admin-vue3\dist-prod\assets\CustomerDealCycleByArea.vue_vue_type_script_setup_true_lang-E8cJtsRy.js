import{d as A,r as p,f as w,q as D,c as v,o as u,F as b,g as t,k as _,w as i,a as o,J as I,M as N,A as q,L as B,K as L}from"./index-CRsFgzy0.js";import{E as P}from"./el-skeleton-item-CZ5buDOR.js";import{_ as S}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as k}from"./customer-BAP7SwSn.js";const E=A({name:"CustomerDealCycleByArea",__name:"CustomerDealCycleByArea",props:{queryParams:{}},setup(c,{expose:y}){const x=c,r=p(!1),n=p([]),a=w({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u6210\u4EA4\u5468\u671F(\u5929)",type:"bar",data:[],yAxisIndex:0},{name:"\u6210\u4EA4\u5BA2\u6237\u6570",type:"bar",data:[],yAxisIndex:1}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u6210\u4EA4\u5468\u671F\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6210\u4EA4\u5468\u671F(\u5929)",min:0,minInterval:1},{type:"value",name:"\u6210\u4EA4\u5BA2\u6237\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u533A\u57DF",data:[]}}),m=async()=>{r.value=!0;try{await(async()=>{const s=(await k.getCustomerDealCycleByArea(x.queryParams)).map(e=>({areaName:e.areaName,customerDealCycle:e.customerDealCycle,customerDealCount:e.customerDealCount}));a.xAxis&&a.xAxis.data&&(a.xAxis.data=s.map(e=>e.areaName)),a.series&&a.series[0]&&a.series[0].data&&(a.series[0].data=s.map(e=>e.customerDealCycle)),a.series&&a.series[1]&&a.series[1].data&&(a.series[1].data=s.map(e=>e.customerDealCount)),n.value=s})()}finally{r.value=!1}};return y({loadData:m}),D(()=>{m()}),(s,e)=>{const g=S,C=P,d=_,l=B,f=L,h=N;return u(),v(b,null,[t(d,{shadow:"never"},{default:i(()=>[t(C,{loading:o(r),animated:""},{default:i(()=>[t(g,{height:500,options:o(a)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),t(d,{shadow:"never",class:"mt-16px"},{default:i(()=>[I((u(),q(f,{data:o(n)},{default:i(()=>[t(l,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),t(l,{label:"\u533A\u57DF",align:"center",prop:"areaName","min-width":"200"}),t(l,{label:"\u6210\u4EA4\u5468\u671F(\u5929)",align:"center",prop:"customerDealCycle","min-width":"200"}),t(l,{label:"\u6210\u4EA4\u5BA2\u6237\u6570",align:"center",prop:"customerDealCount","min-width":"200"})]),_:1},8,["data"])),[[h,o(r)]])]),_:1})],64)}}});export{E as _};
