import{d as D,r as u,f as b,q as w,c as v,o as p,F as A,g as t,k as _,w as l,a as i,J as I,M as P,A as N,L as q,K as B}from"./index-CRsFgzy0.js";import{E as L}from"./el-skeleton-item-CZ5buDOR.js";import{_ as S}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as k}from"./customer-BAP7SwSn.js";const E=D({name:"CustomerDealCycleByProduct",__name:"CustomerDealCycleByProduct",props:{queryParams:{}},setup(c,{expose:y}){const x=c,r=u(!1),n=u([]),e=b({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u6210\u4EA4\u5468\u671F(\u5929)",type:"bar",data:[],yAxisIndex:0},{name:"\u6210\u4EA4\u5BA2\u6237\u6570",type:"bar",data:[],yAxisIndex:1}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u6210\u4EA4\u5468\u671F\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6210\u4EA4\u5468\u671F(\u5929)",min:0,minInterval:1},{type:"value",name:"\u6210\u4EA4\u5BA2\u6237\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u4EA7\u54C1\u540D\u79F0",data:[]}}),m=async()=>{r.value=!0;try{await(async()=>{const s=(await k.getCustomerDealCycleByProduct(x.queryParams)).map(a=>({productName:a.productName??"\u672A\u77E5",customerDealCycle:a.customerDealCount,customerDealCount:a.customerDealCount}));e.xAxis&&e.xAxis.data&&(e.xAxis.data=s.map(a=>a.productName)),e.series&&e.series[0]&&e.series[0].data&&(e.series[0].data=s.map(a=>a.customerDealCycle)),e.series&&e.series[1]&&e.series[1].data&&(e.series[1].data=s.map(a=>a.customerDealCount)),n.value=s})()}finally{r.value=!1}};return y({loadData:m}),w(()=>{m()}),(s,a)=>{const g=S,C=L,d=_,o=q,f=B,h=P;return p(),v(A,null,[t(d,{shadow:"never"},{default:l(()=>[t(C,{loading:i(r),animated:""},{default:l(()=>[t(g,{height:500,options:i(e)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),t(d,{shadow:"never",class:"mt-16px"},{default:l(()=>[I((p(),N(f,{data:i(n)},{default:l(()=>[t(o,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),t(o,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName","min-width":"200"}),t(o,{label:"\u6210\u4EA4\u5468\u671F(\u5929)",align:"center",prop:"customerDealCycle","min-width":"200"}),t(o,{label:"\u6210\u4EA4\u5BA2\u6237\u6570",align:"center",prop:"customerDealCount","min-width":"200"})]),_:1},8,["data"])),[[h,i(r)]])]),_:1})],64)}}});export{E as _};
