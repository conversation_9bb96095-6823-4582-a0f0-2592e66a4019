import{d as b,r as x,f as D,q as v,c as A,o as g,F as _,g as a,k as I,w as i,a as l,J as q,M as B,A as P,L as U,K as L}from"./index-CRsFgzy0.js";import{E as S}from"./el-skeleton-item-CZ5buDOR.js";import{_ as k}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as d}from"./customer-BAP7SwSn.js";const E=b({name:"CustomerDealCycleByUser",__name:"CustomerDealCycleByUser",props:{queryParams:{}},setup(w,{expose:f}){const n=w,s=x(!1),y=x([]),e=D({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u6210\u4EA4\u5468\u671F(\u5929)",type:"bar",data:[],yAxisIndex:0},{name:"\u6210\u4EA4\u5BA2\u6237\u6570",type:"bar",data:[],yAxisIndex:1}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u6210\u4EA4\u5468\u671F\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6210\u4EA4\u5468\u671F(\u5929)",min:0,minInterval:1},{type:"value",name:"\u6210\u4EA4\u5BA2\u6237\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),p=async()=>{s.value=!0;try{await(async()=>{const o=await d.getCustomerDealCycleByDate(n.queryParams),u=await d.getCustomerSummaryByDate(n.queryParams),m=await d.getCustomerDealCycleByUser(n.queryParams);e.xAxis&&e.xAxis.data&&(e.xAxis.data=o.map(t=>t.time)),e.series&&e.series[0]&&e.series[0].data&&(e.series[0].data=o.map(t=>t.customerDealCycle)),e.series&&e.series[1]&&e.series[1].data&&(e.series[1].data=u.map(t=>t.customerDealCount)),y.value=m})()}finally{s.value=!1}};return f({loadData:p}),v(()=>{p()}),(o,u)=>{const m=k,t=S,c=I,r=U,h=L,C=B;return g(),A(_,null,[a(c,{shadow:"never"},{default:i(()=>[a(t,{loading:l(s),animated:""},{default:i(()=>[a(m,{height:500,options:l(e)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(c,{shadow:"never",class:"mt-16px"},{default:i(()=>[q((g(),P(h,{data:l(y)},{default:i(()=>[a(r,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),a(r,{label:"\u65E5\u671F",align:"center",prop:"ownerUserName","min-width":"200"}),a(r,{label:"\u6210\u4EA4\u5468\u671F(\u5929)",align:"center",prop:"customerDealCycle","min-width":"200"}),a(r,{label:"\u6210\u4EA4\u5BA2\u6237\u6570",align:"center",prop:"customerDealCount","min-width":"200"})]),_:1},8,["data"])),[[C,l(s)]])]),_:1})],64)}}});export{E as _};
