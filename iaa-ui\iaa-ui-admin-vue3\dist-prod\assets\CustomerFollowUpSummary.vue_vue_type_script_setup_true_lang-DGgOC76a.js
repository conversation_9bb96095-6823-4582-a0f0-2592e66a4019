import{d as b,r as y,f as v,q as A,c as U,o as x,F as _,g as e,k as C,w as r,a as l,J as I,M as S,A as F,L as q,K as L}from"./index-CRsFgzy0.js";import{E as P}from"./el-skeleton-item-CZ5buDOR.js";import{S as w}from"./customer-BAP7SwSn.js";import{_ as k}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";const B=b({name:"CustomerFollowupSummary",__name:"CustomerFollowUpSummary",props:{queryParams:{}},setup(g,{expose:c}){const n=g,s=y(!1),m=y([]),a=v({grid:{left:20,right:30,bottom:20,containLabel:!0},legend:{},series:[{name:"\u8DDF\u8FDB\u5BA2\u6237\u6570",type:"bar",yAxisIndex:0,data:[]},{name:"\u8DDF\u8FDB\u6B21\u6570",type:"bar",yAxisIndex:1,data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5BA2\u6237\u8DDF\u8FDB\u6B21\u6570\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u8DDF\u8FDB\u5BA2\u6237\u6570",min:0,minInterval:1},{type:"value",name:"\u8DDF\u8FDB\u6B21\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u65E5\u671F",axisTick:{alignWithLabel:!0},data:[]}}),p=async()=>{s.value=!0;try{await(async()=>{s.value=!0;const o=await w.getFollowUpSummaryByDate(n.queryParams),d=await w.getFollowUpSummaryByUser(n.queryParams);a.xAxis&&a.xAxis.data&&(a.xAxis.data=o.map(t=>t.time)),a.series&&a.series[0]&&a.series[0].data&&(a.series[0].data=o.map(t=>t.followUpCustomerCount)),a.series&&a.series[1]&&a.series[1].data&&(a.series[1].data=o.map(t=>t.followUpRecordCount)),m.value=d})()}finally{s.value=!1}};return c({loadData:p}),A(()=>{p()}),(o,d)=>{const t=P,u=C,i=q,f=L,h=S;return x(),U(_,null,[e(u,{shadow:"never"},{default:r(()=>[e(t,{loading:l(s),animated:""},{default:r(()=>[e(k,{height:500,options:l(a)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(u,{shadow:"never",class:"mt-16px"},{default:r(()=>[I((x(),F(f,{data:l(m)},{default:r(()=>[e(i,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),e(i,{label:"\u5458\u5DE5\u59D3\u540D",align:"center",prop:"ownerUserName","min-width":"200"}),e(i,{label:"\u8DDF\u8FDB\u6B21\u6570",align:"right",prop:"followUpRecordCount","min-width":"200"}),e(i,{label:"\u8DDF\u8FDB\u5BA2\u6237\u6570",align:"right",prop:"followUpCustomerCount","min-width":"200"})]),_:1},8,["data"])),[[h,l(s)]])]),_:1})],64)}}});export{B as _};
