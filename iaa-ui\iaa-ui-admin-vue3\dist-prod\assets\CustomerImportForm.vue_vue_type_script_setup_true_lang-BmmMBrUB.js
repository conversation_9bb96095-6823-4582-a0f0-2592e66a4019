import{_ as L}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as O,p as R,r,A as b,o as _,w as o,i as n,g as i,x as q,m as y,a as d,c as D,F as E,y as J,B as K,bh as M,I as P,H as x,a4 as Q,a5 as T,G as W,e as X,aG as Y}from"./index-CRsFgzy0.js";import{k as Z,m as $}from"./index-BI-6YuuK.js";import{d as ee}from"./download-oWiM5xVU.js";import{g as ae}from"./index-D4y5Z4cM.js";const le={class:"flex items-center my-10px"},se={class:"el-upload__tip text-center"},te={class:"el-upload__tip"},oe=O({name:"SystemUserImportForm",__name:"CustomerImportForm",emits:["success"],setup(ue,{expose:V,emit:h}){const f=R(),m=r(!1),u=r(!1),g=r(),p=r([]),v=r(!1),c=r(),C=r([]);V({open:async()=>{m.value=!0,await S(),C.value=await ae(),c.value=X().getUser.id}});const k=async()=>{if(p.value.length!=0){u.value=!0;try{const a=new FormData;a.append("updateSupport",String(v.value)),a.append("file",p.value[0].raw),a.append("ownerUserId",String(c.value));const e=await Z(a);U(e)}catch{F()}finally{u.value=!1}}else f.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6")},N=h,U=a=>{if(a.code!==0)return f.error(a.msg),void(u.value=!1);const e=a.data;let s="\u4E0A\u4F20\u6210\u529F\u6570\u91CF\uFF1A"+e.createCustomerNames.length+";";for(let t of e.createCustomerNames)s+="< "+t+" >";s+="\u66F4\u65B0\u6210\u529F\u6570\u91CF\uFF1A"+e.updateCustomerNames.length+";";for(const t of e.updateCustomerNames)s+="< "+t+" >";s+="\u66F4\u65B0\u5931\u8D25\u6570\u91CF\uFF1A"+Object.keys(e.failureCustomerNames).length+";";for(const t in e.failureCustomerNames)s+="< "+t+": "+e.failureCustomerNames[t]+" >";f.alert(s),u.value=!1,m.value=!1,N("success")},F=()=>{f.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),u.value=!1},S=async()=>{var a;p.value=[],v.value=!1,c.value=void 0,await Y(),(a=g.value)==null||a.clearFiles()},I=()=>{f.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},G=async()=>{const a=await $();ee.excel(a,"\u5BA2\u6237\u5BFC\u5165\u6A21\u7248.xls")};return(a,e)=>{const s=K,t=q,j=P,z=Q,A=T,B=M,w=W,H=L;return _(),b(H,{modelValue:d(m),"onUpdate:modelValue":e[4]||(e[4]=l=>y(m)?m.value=l:null),title:"\u5BA2\u6237\u5BFC\u5165",width:"400"},{footer:o(()=>[i(w,{disabled:d(u),type:"primary",onClick:k},{default:o(()=>e[10]||(e[10]=[x("\u786E \u5B9A")])),_:1},8,["disabled"]),i(w,{onClick:e[3]||(e[3]=l=>m.value=!1)},{default:o(()=>e[11]||(e[11]=[x("\u53D6 \u6D88")])),_:1})]),default:o(()=>[n("div",le,[e[5]||(e[5]=n("span",{class:"mr-10px"},"\u8D1F\u8D23\u4EBA",-1)),i(t,{modelValue:d(c),"onUpdate:modelValue":e[0]||(e[0]=l=>y(c)?c.value=l:null),class:"!w-240px",clearable:""},{default:o(()=>[(_(!0),D(E,null,J(d(C),l=>(_(),b(s,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),i(B,{ref_key:"uploadRef",ref:g,"file-list":d(p),"onUpdate:fileList":e[2]||(e[2]=l=>y(p)?p.value=l:null),"auto-upload":!1,disabled:d(u),limit:1,"on-exceed":I,accept:".xlsx, .xls",action:"none",drag:""},{tip:o(()=>[n("div",se,[n("div",te,[i(z,{modelValue:d(v),"onUpdate:modelValue":e[1]||(e[1]=l=>y(v)?v.value=l:null)},null,8,["modelValue"]),e[6]||(e[6]=x(" \u662F\u5426\u66F4\u65B0\u5DF2\u7ECF\u5B58\u5728\u7684\u5BA2\u6237\u6570\u636E\uFF08\u201C\u5BA2\u6237\u540D\u79F0\u201D\u91CD\u590D\uFF09 "))]),e[8]||(e[8]=n("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),i(A,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:G},{default:o(()=>e[7]||(e[7]=[x(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:o(()=>[i(j,{icon:"ep:upload"}),e[9]||(e[9]=n("div",{class:"el-upload__text"},[x("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),n("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","disabled"])]),_:1},8,["modelValue"])}}});export{oe as _};
