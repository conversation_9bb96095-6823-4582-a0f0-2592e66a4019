import{as as c,d as Y,b as F,p as j,r as i,f as q,A as _,o as p,w as u,J as B,M as G,a,s as H,g as o,a3 as J,v as z,x as D,c as P,F as W,y as X,B as Z,an as $,a0 as ee,G as ae,H as V,m as le}from"./index-CRsFgzy0.js";import{_ as te}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{E as se}from"./el-tree-select-BijZG_HG.js";import{g as ue}from"./index-C0yL_L5C.js";import{d as de,h as oe}from"./tree-COGD3qag.js";import{g as ie}from"./index-D4y5Z4cM.js";var n=(s=>(s[s.CUSTOMER_QUANTITY_LIMIT=1]="CUSTOMER_QUANTITY_LIMIT",s[s.CUSTOMER_LOCK_LIMIT=2]="CUSTOMER_LOCK_LIMIT",s))(n||{});const re=async s=>await c.get({url:"/crm/customer-limit-config/page",params:s}),me=async s=>await c.delete({url:"/crm/customer-limit-config/delete?id="+s}),ne=Y({__name:"CustomerLimitConfigForm",emits:["success"],setup(s,{expose:w,emit:E}){const{t:C}=F(),T=j(),r=i(!1),g=i(""),m=i(!1),b=i(""),l=i({id:void 0,type:n.CUSTOMER_LOCK_LIMIT,userIds:void 0,deptIds:void 0,maxCount:void 0,dealCountEnabled:!1}),O=q({type:[{required:!0,message:"\u89C4\u5219\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],maxCount:[{required:!0,message:"\u6570\u91CF\u4E0A\u9650\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=i(),M=i(),U=i([]);w({open:async(d,e,v)=>{if(r.value=!0,g.value=C("action."+d),b.value=d,k(),v){m.value=!0;try{l.value=await(async y=>await c.get({url:"/crm/customer-limit-config/get?id="+y}))(v)}finally{m.value=!1}}else l.value.type=e;M.value=oe(await ue()),U.value=await ie()}});const S=E,x=async()=>{if(f&&await f.value.validate()){m.value=!0;try{const d=l.value;b.value==="create"?(await(async e=>await c.post({url:"/crm/customer-limit-config/create",data:e}))(d),T.success(C("common.createSuccess"))):(await(async e=>await c.put({url:"/crm/customer-limit-config/update",data:e}))(d),T.success(C("common.updateSuccess"))),r.value=!1,S("success")}finally{m.value=!1}}},k=()=>{var d;l.value={id:void 0,type:n.CUSTOMER_LOCK_LIMIT,userIds:void 0,deptIds:void 0,maxCount:void 0,dealCountEnabled:!1},(d=f.value)==null||d.resetFields()};return(d,e)=>{const v=Z,y=D,I=z,R=se,h=$,A=ee,K=H,L=ae,N=te,Q=G;return p(),_(N,{title:a(g),modelValue:a(r),"onUpdate:modelValue":e[5]||(e[5]=t=>le(r)?r.value=t:null)},{footer:u(()=>[o(L,{onClick:x,type:"primary",disabled:a(m)},{default:u(()=>e[6]||(e[6]=[V("\u786E \u5B9A")])),_:1},8,["disabled"]),o(L,{onClick:e[4]||(e[4]=t=>r.value=!1)},{default:u(()=>e[7]||(e[7]=[V("\u53D6 \u6D88")])),_:1})]),default:u(()=>[B((p(),_(K,{ref_key:"formRef",ref:f,model:a(l),rules:a(O),"label-width":"200px"},{default:u(()=>[o(I,{label:"\u89C4\u5219\u9002\u7528\u4EBA\u7FA4",prop:"userIds"},{default:u(()=>[o(y,{multiple:"",filterable:"",modelValue:a(l).userIds,"onUpdate:modelValue":e[0]||(e[0]=t=>a(l).userIds=t)},{default:u(()=>[(p(!0),P(W,null,X(a(U),t=>(p(),_(v,{key:t.id,label:t.nickname,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(I,{label:"\u89C4\u5219\u9002\u7528\u90E8\u95E8",prop:"deptIds"},{default:u(()=>[o(R,{modelValue:a(l).deptIds,"onUpdate:modelValue":e[1]||(e[1]=t=>a(l).deptIds=t),data:a(M),props:a(de),multiple:"",filterable:"","check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u89C4\u5219\u9002\u7528\u90E8\u95E8"},null,8,["modelValue","data","props"])]),_:1}),o(I,{label:a(l).type===a(n).CUSTOMER_QUANTITY_LIMIT?"\u62E5\u6709\u5BA2\u6237\u6570\u4E0A\u9650":"\u9501\u5B9A\u5BA2\u6237\u6570\u4E0A\u9650",prop:"maxCount"},{default:u(()=>[o(h,{modelValue:a(l).maxCount,"onUpdate:modelValue":e[2]||(e[2]=t=>a(l).maxCount=t),placeholder:"\u8BF7\u8F93\u5165\u6570\u91CF\u4E0A\u9650"},null,8,["modelValue"])]),_:1},8,["label"]),a(l).type===a(n).CUSTOMER_QUANTITY_LIMIT?(p(),_(I,{key:0,label:"\u6210\u4EA4\u5BA2\u6237\u662F\u5426\u5360\u7528\u62E5\u6709\u5BA2\u6237\u6570",prop:"dealCountEnabled"},{default:u(()=>[o(A,{modelValue:a(l).dealCountEnabled,"onUpdate:modelValue":e[3]||(e[3]=t=>a(l).dealCountEnabled=t)},null,8,["modelValue"])]),_:1})):J("",!0)]),_:1},8,["model","rules"])),[[Q,a(m)]])]),_:1},8,["title","modelValue"])}}});export{n as L,ne as _,me as d,re as g};
