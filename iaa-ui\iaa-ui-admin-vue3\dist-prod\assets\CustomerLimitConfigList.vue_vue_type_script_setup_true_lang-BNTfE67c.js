import{d as M,p as O,b as R,r as d,f as j,q as z,O as F,c as G,o as p,F as P,g as t,J as g,G as D,w as r,H as y,I as H,A as u,M as Q,a as l,a3 as Y,L as q,D as B,K as J}from"./index-CRsFgzy0.js";import{_ as K}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as V}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{d as W}from"./formatTime-DhdtkSIS.js";import{L as h,_ as X,g as Z,d as $}from"./CustomerLimitConfigForm.vue_vue_type_script_setup_true_lang-GsK5-e0a.js";const ee=M({name:"CustomerLimitConfigList",__name:"CustomerLimitConfigList",props:{confType:{}},setup(T){const w=O(),{t:v}=R(),_=d(!0),b=d(0),k=d([]),s=j({pageNo:1,pageSize:10,type:T.confType}),c=async()=>{_.value=!0;try{const o=await Z(s);k.value=o.list,b.value=o.total}finally{_.value=!1}},I=d(),L=(o,e)=>{I.value.open(o,T.confType,e)},S=()=>{s.pageNo=1,c()};return z(()=>{c()}),(o,e)=>{const N=H,f=D,i=q,x=V,U=J,A=K,C=F("hasPermi"),E=Q;return p(),G(P,null,[t(f,{plain:"",onClick:S},{default:r(()=>[t(N,{icon:"ep:refresh",class:"mr-5px"}),e[3]||(e[3]=y(" \u5237\u65B0 "))]),_:1}),g((p(),u(f,{type:"primary",plain:"",onClick:e[0]||(e[0]=a=>L("create"))},{default:r(()=>[t(N,{icon:"ep:plus",class:"mr-5px"}),e[4]||(e[4]=y(" \u65B0\u589E "))]),_:1})),[[C,["crm:customer-limit-config:create"]]]),g((p(),u(U,{data:l(k),stripe:!0,"show-overflow-tooltip":!0,class:"mt-4"},{default:r(()=>[t(i,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(i,{label:"\u89C4\u5219\u9002\u7528\u4EBA\u7FA4",align:"center",formatter:a=>{var n;return(n=a.users)==null?void 0:n.map(m=>m.nickname).join("\uFF0C")}},null,8,["formatter"]),t(i,{label:"\u89C4\u5219\u9002\u7528\u90E8\u95E8",align:"center",formatter:a=>{var n;return(n=a.depts)==null?void 0:n.map(m=>m.name).join("\uFF0C")}},null,8,["formatter"]),t(i,{label:o.confType===l(h).CUSTOMER_QUANTITY_LIMIT?"\u62E5\u6709\u5BA2\u6237\u6570\u4E0A\u9650":"\u9501\u5B9A\u5BA2\u6237\u6570\u4E0A\u9650",align:"center",prop:"maxCount"},null,8,["label"]),o.confType===l(h).CUSTOMER_QUANTITY_LIMIT?(p(),u(i,{key:0,label:"\u6210\u4EA4\u5BA2\u6237\u662F\u5426\u5360\u7528\u62E5\u6709\u5BA2\u6237\u6570",align:"center",prop:"dealCountEnabled","min-width":"100"},{default:r(a=>[t(x,{type:l(B).INFRA_BOOLEAN_STRING,value:a.row.dealCountEnabled},null,8,["type","value"])]),_:1})):Y("",!0),t(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(W),width:"180px"},null,8,["formatter"]),t(i,{label:"\u64CD\u4F5C",align:"center","min-width":"110",fixed:"right"},{default:r(a=>[g((p(),u(f,{link:"",type:"primary",onClick:n=>L("update",a.row.id)},{default:r(()=>e[5]||(e[5]=[y(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[C,["crm:customer-limit-config:update"]]]),g((p(),u(f,{link:"",type:"danger",onClick:n=>(async m=>{try{await w.delConfirm(),await $(m),w.success(v("common.delSuccess")),await c()}catch{}})(a.row.id)},{default:r(()=>e[6]||(e[6]=[y(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[C,["crm:customer-limit-config:delete"]]])]),_:1})]),_:1},8,["data"])),[[E,l(_)]]),t(A,{total:l(b),page:l(s).pageNo,"onUpdate:page":e[1]||(e[1]=a=>l(s).pageNo=a),limit:l(s).pageSize,"onUpdate:limit":e[2]||(e[2]=a=>l(s).pageSize=a),onPagination:c},null,8,["total","page","limit"]),t(X,{ref_key:"formRef",ref:I,onSuccess:c},null,512)],64)}}});export{ee as _};
