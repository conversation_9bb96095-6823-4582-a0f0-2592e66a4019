import{d as D,b as A,p as G,r as m,f as H,A as b,o as w,w as o,J as I,s as j,a,g as u,v as J,P as M,M as P,G as R,H as _,m as z}from"./index-CRsFgzy0.js";import{_ as B}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as E,c as K,u as L}from"./index-BYKUBRQa.js";const N=D({name:"InfraDataSourceConfigForm",__name:"DataSourceConfigForm",emits:["success"],setup(O,{expose:y,emit:h}){const{t:c}=A(),f=G(),d=m(!1),v=m(""),t=m(!1),g=m(""),l=m({id:void 0,name:"",url:"",username:"",password:""}),U=H({name:[{required:!0,message:"\u6570\u636E\u6E90\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],url:[{required:!0,message:"\u6570\u636E\u6E90\u8FDE\u63A5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],username:[{required:!0,message:"\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],password:[{required:!0,message:"\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=m();y({open:async(r,e)=>{if(d.value=!0,v.value=c("action."+r),g.value=r,S(),e){t.value=!0;try{l.value=await E(e)}finally{t.value=!1}}}});const q=h,C=async()=>{if(n&&await n.value.validate()){t.value=!0;try{const r=l.value;g.value==="create"?(await K(r),f.success(c("common.createSuccess"))):(await L(r),f.success(c("common.updateSuccess"))),d.value=!1,q("success")}finally{t.value=!1}}},S=()=>{var r;l.value={id:void 0,name:"",url:"",username:"",password:""},(r=n.value)==null||r.resetFields()};return(r,e)=>{const i=M,p=J,k=j,V=R,F=B,x=P;return w(),b(F,{modelValue:a(d),"onUpdate:modelValue":e[5]||(e[5]=s=>z(d)?d.value=s:null),title:a(v)},{footer:o(()=>[u(V,{disabled:a(t),type:"primary",onClick:C},{default:o(()=>e[6]||(e[6]=[_("\u786E \u5B9A")])),_:1},8,["disabled"]),u(V,{onClick:e[4]||(e[4]=s=>d.value=!1)},{default:o(()=>e[7]||(e[7]=[_("\u53D6 \u6D88")])),_:1})]),default:o(()=>[I((w(),b(k,{ref_key:"formRef",ref:n,model:a(l),rules:a(U),"label-width":"100px"},{default:o(()=>[u(p,{label:"\u6570\u636E\u6E90\u540D\u79F0",prop:"name"},{default:o(()=>[u(i,{modelValue:a(l).name,"onUpdate:modelValue":e[0]||(e[0]=s=>a(l).name=s),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(p,{label:"\u6570\u636E\u6E90\u8FDE\u63A5",prop:"url"},{default:o(()=>[u(i,{modelValue:a(l).url,"onUpdate:modelValue":e[1]||(e[1]=s=>a(l).url=s),placeholder:"\u8BF7\u8F93\u5165\u6570\u636E\u6E90\u8FDE\u63A5"},null,8,["modelValue"])]),_:1}),u(p,{label:"\u7528\u6237\u540D",prop:"username"},{default:o(()=>[u(i,{modelValue:a(l).username,"onUpdate:modelValue":e[2]||(e[2]=s=>a(l).username=s),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D"},null,8,["modelValue"])]),_:1}),u(p,{label:"\u5BC6\u7801",prop:"password"},{default:o(()=>[u(i,{modelValue:a(l).password,"onUpdate:modelValue":e[3]||(e[3]=s=>a(l).password=s),placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[x,a(t)]])]),_:1},8,["modelValue","title"])}}});export{N as _};
