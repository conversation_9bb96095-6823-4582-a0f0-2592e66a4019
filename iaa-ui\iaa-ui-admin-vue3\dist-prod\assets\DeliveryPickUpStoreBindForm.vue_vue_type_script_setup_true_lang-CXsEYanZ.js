import{d as G,p as H,r as u,f as J,O as L,c as N,o as v,F as R,g as a,a as r,m as j,w as l,J as w,A as y,s as q,E as z,h as Q,v as W,P as X,a3 as Y,G as Z,H as m,K as $,L as ee,D as ae,M as le}from"./index-CRsFgzy0.js";import{_ as se}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as re}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as te}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{g as ie,b as oe}from"./index-CPqNYl_u.js";import{_ as de}from"./StoreStaffTableSelect.vue_vue_type_script_setup_true_lang-CRG1D8kL.js";const ue=G({__name:"DeliveryPickUpStoreBindForm",setup(ne,{expose:g}){const h=H(),o=u(!1),c=u(""),d=u(!1),s=u({id:void 0,name:"",verifyUserIds:[],verifyUsers:[]}),C=J({}),n=u(),_=u();g({open:async t=>{o.value=!0,c.value="\u7ED1\u5B9A\u81EA\u63D0\u95E8\u5E97\u5458\u5DE5",S(),d.value=!0;try{s.value=await ie(t)}finally{d.value=!1}}});const V=async()=>{if(n&&await n.value.validate()){d.value=!0;try{const t={id:s.value.id,verifyUserIds:s.value.verifyUsers.map(e=>e.id)};await oe(t),h.success("\u7ED1\u5B9A\u6210\u529F"),o.value=!1}finally{d.value=!1}}},I=t=>{s.value.verifyUsers=t},S=()=>{var t;s.value={id:void 0,name:"",verifyUserIds:[],verifyUsers:[]},(t=n.value)==null||t.resetFields()};return(t,e)=>{const x=X,U=W,k=Q,b=z,f=Z,p=ee,F=te,O=$,T=re,M=q,P=se,A=L("hasPermi"),D=le;return v(),N(R,null,[a(P,{title:r(c),modelValue:r(o),"onUpdate:modelValue":e[3]||(e[3]=i=>j(o)?o.value=i:null),width:"20%"},{footer:l(()=>[a(f,{onClick:V,type:"primary",disabled:r(d)},{default:l(()=>e[6]||(e[6]=[m("\u786E \u5B9A")])),_:1},8,["disabled"]),a(f,{onClick:e[2]||(e[2]=i=>o.value=!1)},{default:l(()=>e[7]||(e[7]=[m("\u53D6 \u6D88")])),_:1})]),default:l(()=>[w((v(),y(M,{ref_key:"formRef",ref:n,model:r(s),rules:r(C),"label-width":"120px"},{default:l(()=>[a(b,null,{default:l(()=>[a(k,{span:24},{default:l(()=>[a(U,{label:"\u95E8\u5E97\u540D\u79F0",prop:"name"},{default:l(()=>[a(x,{modelValue:r(s).name,"onUpdate:modelValue":e[0]||(e[0]=i=>r(s).name=i),placeholder:"\u8BF7\u8F93\u5165\u95E8\u5E97\u540D\u79F0",readonly:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(b,null,{default:l(()=>[a(k,{span:24},{default:l(()=>[a(U,{label:"\u95E8\u5E97\u5E97\u5458",prop:"verifyUserIds"},{default:l(()=>[a(f,{type:"primary",onClick:e[1]||(e[1]=i=>r(_).open())},{default:l(()=>e[4]||(e[4]=[m("\u9009\u62E9\u5E97\u5458")])),_:1})]),_:1}),r(s).verifyUsers.length>0?(v(),y(T,{key:0},{default:l(()=>[a(O,{data:r(s).verifyUsers},{default:l(()=>[a(p,{label:"\u7F16\u53F7",align:"center",prop:"id"}),a(p,{label:"\u7528\u6237\u6635\u79F0",align:"center",prop:"nickname","show-overflow-tooltip":!0}),a(p,{label:"\u72B6\u6001",align:"center",key:"status"},{default:l(i=>[a(F,{type:r(ae).COMMON_STATUS,value:i.row.status},null,8,["type","value"])]),_:1}),a(p,{align:"center",label:"\u64CD\u4F5C"},{default:l(i=>[w((v(),y(f,{link:"",type:"danger",onClick:fe=>(async K=>{const B=s.value.verifyUsers.findIndex(E=>{if(E.id==K)return!0});s.value.verifyUsers.splice(B,1)})(i.row.id)},{default:l(()=>e[5]||(e[5]=[m(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[A,["trade:delivery:pick-up-store:delete"]]])]),_:1})]),_:1},8,["data"])]),_:1})):Y("",!0)]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[D,r(d)]])]),_:1},8,["title","modelValue"]),a(de,{ref_key:"storeStaffTableSelect",ref:_,onChange:I},null,512)],64)}}});export{ue as _};
