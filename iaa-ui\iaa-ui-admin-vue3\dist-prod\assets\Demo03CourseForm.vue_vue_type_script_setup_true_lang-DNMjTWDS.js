import{d as C,r as p,f as U,aK as q,c as D,o as x,F as j,J as F,g as e,M as K,a as r,A,s as E,w as a,K as G,L as H,v as J,P as L,G as M,H as b,E as P}from"./index-CRsFgzy0.js";import{a as R}from"./index-CAxVHOSV.js";const z=C({__name:"Demo03CourseForm",props:{studentId:{}},setup(w,{expose:h}){const c=w,i=p(!1),l=p([]),m=U({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],score:[{required:!0,message:"\u5206\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=p();q(()=>c.studentId,async d=>{if(l.value=[],d)try{i.value=!0,l.value=await R(d)}finally{i.value=!1}},{immediate:!0});const V=()=>{const d={id:void 0,studentId:void 0,name:void 0,score:void 0};d.studentId=c.studentId,l.value.push(d)};return h({validate:()=>f.value.validate(),getData:()=>l.value}),(d,u)=>{const o=H,v=L,g=J,_=M,y=G,I=E,k=P,$=K;return x(),D(j,null,[F((x(),A(I,{ref_key:"formRef",ref:f,model:r(l),rules:r(m),"label-width":"0px","inline-message":!0},{default:a(()=>[e(y,{data:r(l),class:"-mt-10px"},{default:a(()=>[e(o,{label:"\u5E8F\u53F7",type:"index",width:"100"}),e(o,{label:"\u540D\u5B57","min-width":"150"},{default:a(({row:s,$index:n})=>[e(g,{prop:`${n}.name`,rules:r(m).name,class:"mb-0px!"},{default:a(()=>[e(v,{modelValue:s.name,"onUpdate:modelValue":t=>s.name=t,placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(o,{label:"\u5206\u6570","min-width":"150"},{default:a(({row:s,$index:n})=>[e(g,{prop:`${n}.score`,rules:r(m).score,class:"mb-0px!"},{default:a(()=>[e(v,{modelValue:s.score,"onUpdate:modelValue":t=>s.score=t,placeholder:"\u8BF7\u8F93\u5165\u5206\u6570"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(o,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:a(({$index:s})=>[e(_,{onClick:n=>{return t=s,void l.value.splice(t,1);var t},link:""},{default:a(()=>u[0]||(u[0]=[b("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules"])),[[$,r(i)]]),e(k,{justify:"center",class:"mt-3"},{default:a(()=>[e(_,{onClick:V,round:""},{default:a(()=>u[1]||(u[1]=[b("+ \u6DFB\u52A0\u5B66\u751F\u8BFE\u7A0B")])),_:1})]),_:1})],64)}}});export{z as _};
