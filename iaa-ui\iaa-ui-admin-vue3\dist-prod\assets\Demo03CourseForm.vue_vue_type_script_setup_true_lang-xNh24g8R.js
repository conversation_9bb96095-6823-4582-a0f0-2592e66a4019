import{d as S,b as A,p as D,r,f as G,A as g,o as V,w as o,J as H,M as j,a,s as J,g as i,v as M,P,G as R,H as y,m as z}from"./index-CRsFgzy0.js";import{_ as B}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as E,b as K,d as L}from"./index-BHxG5Zar.js";const N=S({__name:"Demo03CourseForm",emits:["success"],setup(O,{expose:w,emit:h}){const{t:c}=A(),v=D(),u=r(!1),p=r(""),t=r(!1),f=r(""),l=r({id:void 0,studentId:void 0,name:void 0,score:void 0}),k=G({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],score:[{required:!0,message:"\u5206\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=r();w({open:async(s,e,m)=>{if(u.value=!0,p.value=c("action."+s),f.value=s,C(),l.value.studentId=m,e){t.value=!0;try{l.value=await E(e)}finally{t.value=!1}}}});const q=h,x=async()=>{await n.value.validate(),t.value=!0;try{const s=l.value;f.value==="create"?(await K(s),v.success(c("common.createSuccess"))):(await L(s),v.success(c("common.updateSuccess"))),u.value=!1,q("success")}finally{t.value=!1}},C=()=>{var s;l.value={id:void 0,studentId:void 0,name:void 0,score:void 0},(s=n.value)==null||s.resetFields()};return(s,e)=>{const m=P,b=M,I=J,_=R,U=B,F=j;return V(),g(U,{title:a(p),modelValue:a(u),"onUpdate:modelValue":e[3]||(e[3]=d=>z(u)?u.value=d:null)},{footer:o(()=>[i(_,{onClick:x,type:"primary",disabled:a(t)},{default:o(()=>e[4]||(e[4]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),i(_,{onClick:e[2]||(e[2]=d=>u.value=!1)},{default:o(()=>e[5]||(e[5]=[y("\u53D6 \u6D88")])),_:1})]),default:o(()=>[H((V(),g(I,{ref_key:"formRef",ref:n,model:a(l),rules:a(k),"label-width":"100px"},{default:o(()=>[i(b,{label:"\u540D\u5B57",prop:"name"},{default:o(()=>[i(m,{modelValue:a(l).name,"onUpdate:modelValue":e[0]||(e[0]=d=>a(l).name=d),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),i(b,{label:"\u5206\u6570",prop:"score"},{default:o(()=>[i(m,{modelValue:a(l).score,"onUpdate:modelValue":e[1]||(e[1]=d=>a(l).score=d),placeholder:"\u8BF7\u8F93\u5165\u5206\u6570"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[F,a(t)]])]),_:1},8,["title","modelValue"])}}});export{N as _};
