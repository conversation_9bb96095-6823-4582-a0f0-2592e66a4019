import{d as f,b as g,p as _,r as o,q as b,A as l,o as n,w as p,J as v,M as w,a as r,K as y,g as t,L as h}from"./index-CRsFgzy0.js";import{_ as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{d as I}from"./formatTime-DhdtkSIS.js";import{a as L}from"./index-CN3eSHy6.js";const j=f({__name:"Demo03CourseList",props:{studentId:{}},setup(i){const{t:q}=g();_();const m=i,e=o(!1),s=o([]);return b(()=>{(async()=>{e.value=!0;try{s.value=await L(m.studentId)}finally{e.value=!1}})()}),(A,C)=>{const a=h,u=y,d=x,c=w;return n(),l(d,null,{default:p(()=>[v((n(),l(u,{data:r(s),stripe:!0,"show-overflow-tooltip":!0},{default:p(()=>[t(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),t(a,{label:"\u5206\u6570",align:"center",prop:"score"}),t(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:r(I),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[c,r(e)]])]),_:1})}}});export{j as _};
