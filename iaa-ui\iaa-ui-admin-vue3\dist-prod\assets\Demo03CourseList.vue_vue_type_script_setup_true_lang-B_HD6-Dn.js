import{d as D,b as F,p as H,r as d,f as O,aK as T,O as U,c as j,o as i,F as A,g as a,w as l,J as u,A as m,H as w,I as G,G as J,M as R,a as o,L as q,K as B}from"./index-CRsFgzy0.js";import{_ as E}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Q}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{d as V}from"./formatTime-DhdtkSIS.js";import{e as W,f as X}from"./index-BHxG5Zar.js";import{_ as Y}from"./Demo03CourseForm.vue_vue_type_script_setup_true_lang-xNh24g8R.js";const Z=D({__name:"Demo03CourseList",props:{studentId:{}},setup(b){const{t:S}=F(),c=H(),f=b,g=d(!1),k=d([]),v=d(0),s=O({pageNo:1,pageSize:10,studentId:void 0});T(()=>f.studentId,t=>{t&&(s.studentId=t,h())},{immediate:!0,deep:!0});const p=async()=>{g.value=!0;try{const t=await W(s);k.value=t.list,v.value=t.total}finally{g.value=!1}},h=()=>{s.pageNo=1,p()},C=d(),I=(t,e)=>{f.studentId?C.value.open(t,e,f.studentId):c.error("\u8BF7\u9009\u62E9\u4E00\u4E2A\u5B66\u751F")};return(t,e)=>{const z=G,_=J,n=q,N=B,x=Q,K=E,y=U("hasPermi"),L=R;return i(),j(A,null,[a(K,null,{default:l(()=>[u((i(),m(_,{plain:"",type:"primary",onClick:e[0]||(e[0]=r=>I("create"))},{default:l(()=>[a(z,{class:"mr-5px",icon:"ep:plus"}),e[3]||(e[3]=w(" \u65B0\u589E "))]),_:1})),[[y,["infra:demo03-student:create"]]]),u((i(),m(N,{data:o(k),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[a(n,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(n,{align:"center",label:"\u540D\u5B57",prop:"name"}),a(n,{align:"center",label:"\u5206\u6570",prop:"score"}),a(n,{formatter:o(V),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(n,{align:"center",label:"\u64CD\u4F5C"},{default:l(r=>[u((i(),m(_,{link:"",type:"primary",onClick:M=>I("update",r.row.id)},{default:l(()=>e[4]||(e[4]=[w(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:update"]]]),u((i(),m(_,{link:"",type:"danger",onClick:M=>(async P=>{try{await c.delConfirm(),await X(P),c.success(S("common.delSuccess")),await p()}catch{}})(r.row.id)},{default:l(()=>e[5]||(e[5]=[w(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[L,o(g)]]),a(x,{limit:o(s).pageSize,"onUpdate:limit":e[1]||(e[1]=r=>o(s).pageSize=r),page:o(s).pageNo,"onUpdate:page":e[2]||(e[2]=r=>o(s).pageNo=r),total:o(v),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(Y,{ref_key:"formRef",ref:C,onSuccess:p},null,512)],64)}}});export{Z as _};
