import{d as G,b as S,p as j,r,f as A,A as g,o as h,w as t,J as D,M as H,a,s as J,g as i,v as M,P,G as R,H as y,m as z}from"./index-CRsFgzy0.js";import{_ as B}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{h as E,i as K,j as L}from"./index-BHxG5Zar.js";const N=G({__name:"Demo03GradeForm",emits:["success"],setup(O,{expose:V,emit:w}){const{t:c}=S(),v=j(),u=r(!1),p=r(""),d=r(!1),f=r(""),l=r({id:void 0,studentId:void 0,name:void 0,teacher:void 0}),I=A({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],teacher:[{required:!0,message:"\u73ED\u4E3B\u4EFB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=r();V({open:async(s,e,m)=>{if(u.value=!0,p.value=c("action."+s),f.value=s,U(),l.value.studentId=m,e){d.value=!0;try{l.value=await E(e)}finally{d.value=!1}}}});const k=w,q=async()=>{await n.value.validate(),d.value=!0;try{const s=l.value;f.value==="create"?(await K(s),v.success(c("common.createSuccess"))):(await L(s),v.success(c("common.updateSuccess"))),u.value=!1,k("success")}finally{d.value=!1}},U=()=>{var s;l.value={id:void 0,studentId:void 0,name:void 0,teacher:void 0},(s=n.value)==null||s.resetFields()};return(s,e)=>{const m=P,_=M,x=J,b=R,C=B,F=H;return h(),g(C,{title:a(p),modelValue:a(u),"onUpdate:modelValue":e[3]||(e[3]=o=>z(u)?u.value=o:null)},{footer:t(()=>[i(b,{onClick:q,type:"primary",disabled:a(d)},{default:t(()=>e[4]||(e[4]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),i(b,{onClick:e[2]||(e[2]=o=>u.value=!1)},{default:t(()=>e[5]||(e[5]=[y("\u53D6 \u6D88")])),_:1})]),default:t(()=>[D((h(),g(x,{ref_key:"formRef",ref:n,model:a(l),rules:a(I),"label-width":"100px"},{default:t(()=>[i(_,{label:"\u540D\u5B57",prop:"name"},{default:t(()=>[i(m,{modelValue:a(l).name,"onUpdate:modelValue":e[0]||(e[0]=o=>a(l).name=o),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),i(_,{label:"\u73ED\u4E3B\u4EFB",prop:"teacher"},{default:t(()=>[i(m,{modelValue:a(l).teacher,"onUpdate:modelValue":e[1]||(e[1]=o=>a(l).teacher=o),placeholder:"\u8BF7\u8F93\u5165\u73ED\u4E3B\u4EFB"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[F,a(d)]])]),_:1},8,["title","modelValue"])}}});export{N as _};
