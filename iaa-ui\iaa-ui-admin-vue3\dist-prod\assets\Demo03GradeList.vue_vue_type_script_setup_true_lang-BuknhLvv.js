import{d as D,b as F,p as H,r as d,f as M,aK as O,O as U,c as j,o as i,F as A,g as a,w as r,J as m,A as u,H as w,I as J,G as R,M as T,a as o,L as q,K as B}from"./index-CRsFgzy0.js";import{_ as E}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Q}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{d as V}from"./formatTime-DhdtkSIS.js";import{k as W,l as X}from"./index-BHxG5Zar.js";import{_ as Y}from"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-DP_QF5UP.js";const Z=D({__name:"Demo03GradeList",props:{studentId:{}},setup(C){const{t:S}=F(),c=H(),f=C,g=d(!1),k=d([]),b=d(0),s=M({pageNo:1,pageSize:10,studentId:void 0});O(()=>f.studentId,t=>{t&&(s.studentId=t,h())},{immediate:!0,deep:!0});const p=async()=>{g.value=!0;try{const t=await W(s);k.value=t.list,b.value=t.total}finally{g.value=!1}},h=()=>{s.pageNo=1,p()},v=d(),I=(t,e)=>{f.studentId?v.value.open(t,e,f.studentId):c.error("\u8BF7\u9009\u62E9\u4E00\u4E2A\u5B66\u751F")};return(t,e)=>{const z=J,_=R,n=q,N=B,x=Q,L=E,y=U("hasPermi"),G=T;return i(),j(A,null,[a(L,null,{default:r(()=>[m((i(),u(_,{plain:"",type:"primary",onClick:e[0]||(e[0]=l=>I("create"))},{default:r(()=>[a(z,{class:"mr-5px",icon:"ep:plus"}),e[3]||(e[3]=w(" \u65B0\u589E "))]),_:1})),[[y,["infra:demo03-student:create"]]]),m((i(),u(N,{data:o(k),"show-overflow-tooltip":!0,stripe:!0},{default:r(()=>[a(n,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(n,{align:"center",label:"\u540D\u5B57",prop:"name"}),a(n,{align:"center",label:"\u73ED\u4E3B\u4EFB",prop:"teacher"}),a(n,{formatter:o(V),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(n,{align:"center",label:"\u64CD\u4F5C"},{default:r(l=>[m((i(),u(_,{link:"",type:"primary",onClick:K=>I("update",l.row.id)},{default:r(()=>e[4]||(e[4]=[w(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:update"]]]),m((i(),u(_,{link:"",type:"danger",onClick:K=>(async P=>{try{await c.delConfirm(),await X(P),c.success(S("common.delSuccess")),await p()}catch{}})(l.row.id)},{default:r(()=>e[5]||(e[5]=[w(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[G,o(g)]]),a(x,{limit:o(s).pageSize,"onUpdate:limit":e[1]||(e[1]=l=>o(s).pageSize=l),page:o(s).pageNo,"onUpdate:page":e[2]||(e[2]=l=>o(s).pageNo=l),total:o(b),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(Y,{ref_key:"formRef",ref:v,onSuccess:p},null,512)],64)}}});export{Z as _};
