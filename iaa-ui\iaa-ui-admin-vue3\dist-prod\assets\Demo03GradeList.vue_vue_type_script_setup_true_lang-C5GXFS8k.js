import{d as b,b as g,p as _,r as l,q as w,A as n,o as p,w as i,J as h,M as v,a as s,K as y,g as t,L as x}from"./index-CRsFgzy0.js";import{_ as I}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{d as L}from"./formatTime-DhdtkSIS.js";import{b as q}from"./index-CN3eSHy6.js";const A=b({__name:"Demo03GradeList",props:{studentId:{}},setup(m){const{t:D}=g();_();const u=m,e=l(!1),o=l([]);return w(()=>{(async()=>{e.value=!0;try{const r=await q(u.studentId);if(!r)return;o.value.push(r)}finally{e.value=!1}})()}),(r,G)=>{const a=x,d=y,c=I,f=v;return p(),n(c,null,{default:i(()=>[h((p(),n(d,{data:s(o),stripe:!0,"show-overflow-tooltip":!0},{default:i(()=>[t(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),t(a,{label:"\u73ED\u4E3B\u4EFB",align:"center",prop:"teacher"}),t(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:s(L),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[f,s(e)]])]),_:1})}}});export{A as _};
