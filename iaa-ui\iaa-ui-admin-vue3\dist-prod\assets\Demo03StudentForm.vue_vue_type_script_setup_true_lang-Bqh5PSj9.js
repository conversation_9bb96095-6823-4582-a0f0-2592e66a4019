import{d as A,b as j,p as B,r as i,f as G,A as v,o as n,w as d,J as H,s as J,a,g as u,v as P,P as T,aB as X,c as Y,F as z,y as I,R as K,D as L,aC as N,H as f,t as O,C as Q,c5 as W,M as Z,G as $,m as ee}from"./index-CRsFgzy0.js";import{_ as ae}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{g as le,c as se,u as de}from"./index-BHxG5Zar.js";const ue=A({__name:"Demo03StudentForm",emits:["success"],setup(oe,{expose:V,emit:h}){const{t:c}=j(),b=B(),t=i(!1),y=i(""),r=i(!1),_=i(""),s=i({id:void 0,name:void 0,sex:void 0,birthday:void 0,description:void 0}),x=G({name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sex:[{required:!0,message:"\u6027\u522B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],birthday:[{required:!0,message:"\u51FA\u751F\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{required:!0,message:"\u7B80\u4ECB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=i();V({open:async(o,e)=>{if(t.value=!0,y.value=c("action."+o),_.value=o,U(),e){r.value=!0;try{s.value=await le(e)}finally{r.value=!1}}}});const S=h,w=async()=>{await p.value.validate(),r.value=!0;try{const o=s.value;_.value==="create"?(await se(o),b.success(c("common.createSuccess"))):(await de(o),b.success(c("common.updateSuccess"))),t.value=!1,S("success")}finally{r.value=!1}},U=()=>{var o;s.value={id:void 0,name:void 0,sex:void 0,birthday:void 0,description:void 0},(o=p.value)==null||o.resetFields()};return(o,e)=>{const k=T,m=P,q=N,C=X,E=Q,F=W,D=J,g=$,M=ae,R=Z;return n(),v(M,{title:a(y),modelValue:a(t),"onUpdate:modelValue":e[5]||(e[5]=l=>ee(t)?t.value=l:null)},{footer:d(()=>[u(g,{onClick:w,type:"primary",disabled:a(r)},{default:d(()=>e[6]||(e[6]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),u(g,{onClick:e[4]||(e[4]=l=>t.value=!1)},{default:d(()=>e[7]||(e[7]=[f("\u53D6 \u6D88")])),_:1})]),default:d(()=>[H((n(),v(D,{ref_key:"formRef",ref:p,model:a(s),rules:a(x),"label-width":"100px"},{default:d(()=>[u(m,{label:"\u540D\u5B57",prop:"name"},{default:d(()=>[u(k,{modelValue:a(s).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(s).name=l),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),u(m,{label:"\u6027\u522B",prop:"sex"},{default:d(()=>[u(C,{modelValue:a(s).sex,"onUpdate:modelValue":e[1]||(e[1]=l=>a(s).sex=l)},{default:d(()=>[(n(!0),Y(z,null,I(a(K)(a(L).SYSTEM_USER_SEX),l=>(n(),v(q,{key:l.value,value:l.value},{default:d(()=>[f(O(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(m,{label:"\u51FA\u751F\u65E5\u671F",prop:"birthday"},{default:d(()=>[u(E,{modelValue:a(s).birthday,"onUpdate:modelValue":e[2]||(e[2]=l=>a(s).birthday=l),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u51FA\u751F\u65E5\u671F"},null,8,["modelValue"])]),_:1}),u(m,{label:"\u7B80\u4ECB",prop:"description"},{default:d(()=>[u(F,{modelValue:a(s).description,"onUpdate:modelValue":e[3]||(e[3]=l=>a(s).description=l),height:"150px"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[R,a(r)]])]),_:1},8,["title","modelValue"])}}});export{ue as _};
