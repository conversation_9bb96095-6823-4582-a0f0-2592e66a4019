import{as as N,d as E,b as S,p as Y,r as t,f as B,A as v,o as m,w as d,J as f,s as G,a,g as s,v as H,aB as J,c as j,F as M,y as $,R as z,D as K,aC as O,H as y,t as Q,an as W,P as X,a6 as U,M as Z,G as ee,m as ae,e$ as le}from"./index-CRsFgzy0.js";import{_ as oe}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";function de(V){return N.get({url:"/pay/demo-transfer/page",params:V})}const se=E({__name:"DemoTransferForm",emits:["success"],setup(V,{expose:x,emit:F}){const{t:_}=S(),I=Y(),r=t(!1),g=t(""),p=t(!1),b=t(""),o=t({id:void 0,price:void 0,type:void 0,userName:void 0,alipayLogonId:void 0,openid:void 0}),L=B({price:[{required:!0,message:"\u8F6C\u8D26\u91D1\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],type:[{required:!0,message:"\u8F6C\u8D26\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),n=t();x({open:async u=>{r.value=!0,g.value=_("action."+u),b.value=u,h()},close:async()=>{r.value=!1,h()}});const k=F,A=async()=>{if(n&&await n.value.validate()){p.value=!0;try{const u={...o.value};u.price=le(u.price),b.value==="create"&&(await function(e){return N.post({url:"/pay/demo-transfer/create",data:e})}(u),I.success(_("common.createSuccess"))),r.value=!1,k("success")}finally{p.value=!1}}},h=()=>{var u;o.value={id:void 0,price:void 0,userName:void 0,alipayLogonId:void 0,openid:void 0},(u=n.value)==null||u.resetFields()};return(u,e)=>{const C=O,P=J,i=H,R=W,c=X,D=G,w=ee,T=oe,q=Z;return m(),v(T,{title:a(g),modelValue:a(r),"onUpdate:modelValue":e[6]||(e[6]=l=>ae(r)?r.value=l:null),width:"800px"},{footer:d(()=>[s(w,{onClick:A,type:"primary",disabled:a(p)},{default:d(()=>e[7]||(e[7]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),s(w,{onClick:e[5]||(e[5]=l=>r.value=!1)},{default:d(()=>e[8]||(e[8]=[y("\u53D6 \u6D88")])),_:1})]),default:d(()=>[f((m(),v(D,{ref_key:"formRef",ref:n,model:a(o),rules:a(L),"label-width":"120px"},{default:d(()=>[s(i,{label:"\u8F6C\u8D26\u7C7B\u578B",prop:"type"},{default:d(()=>[s(P,{modelValue:a(o).type,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).type=l)},{default:d(()=>[(m(!0),j(M,null,$(a(z)(a(K).PAY_TRANSFER_TYPE),l=>(m(),v(C,{key:l.value,value:l.value,disabled:l.value===2||l.value===3||l.value===4},{default:d(()=>[y(Q(l.label),1)]),_:2},1032,["value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(i,{label:"\u8F6C\u8D26\u91D1\u989D(\u5143)",prop:"price"},{default:d(()=>[s(R,{modelValue:a(o).price,"onUpdate:modelValue":e[1]||(e[1]=l=>a(o).price=l),min:0,precision:2,step:.01,placeholder:"\u8BF7\u8F93\u5165\u8F6C\u8D26\u91D1\u989D",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),s(i,{label:"\u6536\u6B3E\u4EBA\u59D3\u540D",prop:"userName"},{default:d(()=>[s(c,{modelValue:a(o).userName,"onUpdate:modelValue":e[2]||(e[2]=l=>a(o).userName=l),placeholder:"\u8BF7\u8F93\u5165\u6536\u6B3E\u4EBA\u59D3\u540D"},null,8,["modelValue"])]),_:1}),f(s(i,{label:"\u652F\u4ED8\u5B9D\u767B\u5F55\u8D26\u53F7",prop:"alipayLogonId"},{default:d(()=>[s(c,{modelValue:a(o).alipayLogonId,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).alipayLogonId=l),placeholder:"\u8BF7\u8F93\u5165\u652F\u4ED8\u5B9D\u767B\u5F55\u8D26\u53F7"},null,8,["modelValue"])]),_:1},512),[[U,a(o).type===1]]),f(s(i,{label:"\u5FAE\u4FE1 openid",prop:"openid"},{default:d(()=>[s(c,{modelValue:a(o).openid,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).openid=l),placeholder:"\u8BF7\u8F93\u5165\u5FAE\u4FE1 openid"},null,8,["modelValue"])]),_:1},512),[[U,a(o).type===2]])]),_:1},8,["model","rules"])),[[q,a(p)]])]),_:1},8,["title","modelValue"])}}});export{se as _,de as g};
