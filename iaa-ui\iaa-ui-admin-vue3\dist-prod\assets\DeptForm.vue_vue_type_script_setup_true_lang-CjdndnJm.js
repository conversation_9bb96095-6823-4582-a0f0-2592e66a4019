import{d as G,b as H,p as J,r as d,f as P,A as c,o as i,w as s,J as $,s as j,a,g as o,v as z,P as K,an as Q,x as W,c as w,F as k,y as x,B as X,R as Y,D as Z,M as ee,G as le,H as E,m as ae}from"./index-CRsFgzy0.js";import{_ as te}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{E as oe}from"./el-tree-select-BijZG_HG.js";import{d as se,h as re}from"./tree-COGD3qag.js";import{a as de,c as ue,u as me,g as ie}from"./index-C0yL_L5C.js";import{g as ne}from"./index-D4y5Z4cM.js";import{C as S}from"./constants-uird_4gU.js";const pe=G({name:"SystemDeptForm",__name:"DeptForm",emits:["success"],setup(ce,{expose:A,emit:C}){const{t:v}=H(),V=J(),m=d(!1),b=d(""),n=d(!1),h=d(""),t=d({id:void 0,title:"",parentId:void 0,name:void 0,sort:void 0,leaderUserId:void 0,phone:void 0,email:void 0,status:S.ENABLE}),F=P({parentId:[{required:!0,message:"\u4E0A\u7EA7\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u90E8\u95E8\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u663E\u793A\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],email:[{type:"email",message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=d(),f=d(),_=d([]);A({open:async(r,e)=>{if(m.value=!0,b.value=v("action."+r),h.value=r,D(),e){n.value=!0;try{t.value=await de(e)}finally{n.value=!1}}_.value=await ne(),await N()}});const q=C,B=async()=>{if(p&&await p.value.validate()){n.value=!0;try{const r=t.value;h.value==="create"?(await ue(r),V.success(v("common.createSuccess"))):(await me(r),V.success(v("common.updateSuccess"))),m.value=!1,q("success")}finally{n.value=!1}}},D=()=>{var r;t.value={id:void 0,title:"",parentId:void 0,name:void 0,sort:void 0,leaderUserId:void 0,phone:void 0,email:void 0,status:S.ENABLE},(r=p.value)==null||r.resetFields()},N=async()=>{f.value=[];const r=await ie();let e={id:0,name:"\u9876\u7EA7\u90E8\u95E8",children:[]};e.children=re(r),f.value.push(e)};return(r,e)=>{const L=oe,u=z,g=K,M=Q,y=X,U=W,O=j,I=le,R=te,T=ee;return i(),c(R,{modelValue:a(m),"onUpdate:modelValue":e[8]||(e[8]=l=>ae(m)?m.value=l:null),title:a(b)},{footer:s(()=>[o(I,{type:"primary",onClick:B},{default:s(()=>e[9]||(e[9]=[E("\u786E \u5B9A")])),_:1}),o(I,{onClick:e[7]||(e[7]=l=>m.value=!1)},{default:s(()=>e[10]||(e[10]=[E("\u53D6 \u6D88")])),_:1})]),default:s(()=>[$((i(),c(O,{ref_key:"formRef",ref:p,model:a(t),rules:a(F),"label-width":"80px"},{default:s(()=>[o(u,{label:"\u4E0A\u7EA7\u90E8\u95E8",prop:"parentId"},{default:s(()=>[o(L,{modelValue:a(t).parentId,"onUpdate:modelValue":e[0]||(e[0]=l=>a(t).parentId=l),data:a(f),props:a(se),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u90E8\u95E8","value-key":"deptId"},null,8,["modelValue","data","props"])]),_:1}),o(u,{label:"\u90E8\u95E8\u540D\u79F0",prop:"name"},{default:s(()=>[o(g,{modelValue:a(t).name,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).name=l),placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u663E\u793A\u6392\u5E8F",prop:"sort"},{default:s(()=>[o(M,{modelValue:a(t).sort,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).sort=l),min:0,"controls-position":"right"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u8D1F\u8D23\u4EBA",prop:"leaderUserId"},{default:s(()=>[o(U,{modelValue:a(t).leaderUserId,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).leaderUserId=l),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8D1F\u8D23\u4EBA"},{default:s(()=>[(i(!0),w(k,null,x(a(_),l=>(i(),c(y,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"\u8054\u7CFB\u7535\u8BDD",prop:"phone"},{default:s(()=>[o(g,{modelValue:a(t).phone,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).phone=l),maxlength:"11",placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u90AE\u7BB1",prop:"email"},{default:s(()=>[o(g,{modelValue:a(t).email,"onUpdate:modelValue":e[5]||(e[5]=l=>a(t).email=l),maxlength:"50",placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1"},null,8,["modelValue"])]),_:1}),o(u,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[o(U,{modelValue:a(t).status,"onUpdate:modelValue":e[6]||(e[6]=l=>a(t).status=l),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:s(()=>[(i(!0),w(k,null,x(a(Y)(a(Z).COMMON_STATUS),l=>(i(),c(y,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[T,a(n)]])]),_:1},8,["modelValue","title"])}}});export{pe as _};
