import{d as k,r as o,aK as v,q as y,c as x,o as g,F as _,i as c,g as r,P as w,a as l,m as V,w as b,I as D,cr as T}from"./index-CRsFgzy0.js";import{g as U}from"./index-C0yL_L5C.js";import{d as j,h as q}from"./tree-COGD3qag.js";const C={class:"head-container"},F={class:"head-container"},I=k({name:"SystemUserDeptTree",__name:"DeptTree",emits:["node-click"],setup(K,{emit:d}){const a=o(""),n=o([]),t=o(),i=(e,s)=>!e||s.name.includes(e),p=async e=>{m("node-click",e)},m=d;return v(a,e=>{t.value.filter(e)}),y(async()=>{await(async()=>{const e=await U();n.value=[],n.value.push(...q(e))})()}),(e,s)=>{const u=D,f=w;return g(),x(_,null,[c("div",C,[r(f,{modelValue:l(a),"onUpdate:modelValue":s[0]||(s[0]=h=>V(a)?a.value=h:null),class:"mb-20px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0"},{prefix:b(()=>[r(u,{icon:"ep:search"})]),_:1},8,["modelValue"])]),c("div",F,[r(l(T),{ref_key:"treeRef",ref:t,data:l(n),"expand-on-click-node":!1,"filter-node-method":i,props:l(j),"default-expand-all":"","highlight-current":"","node-key":"id",onNodeClick:p},null,8,["data","props"])])],64)}}});export{I as _};
