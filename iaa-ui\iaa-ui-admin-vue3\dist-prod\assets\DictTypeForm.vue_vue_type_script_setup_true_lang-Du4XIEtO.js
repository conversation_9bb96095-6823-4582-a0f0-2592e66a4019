import{d as B,b as F,p as M,r as m,f as N,A as v,o as n,w as t,J as L,s as O,a,g as u,v as R,P as j,aB as G,c as H,F as J,y as I,R as P,D as z,aC as K,H as y,t as Q,M as W,G as X,m as Y}from"./index-CRsFgzy0.js";import{_ as Z}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as $,c as ee,u as ae}from"./dict.type-DsokmC1-.js";import{C as k}from"./constants-uird_4gU.js";const le=B({name:"SystemDictTypeForm",__name:"DictTypeForm",emits:["success"],setup(se,{expose:w,emit:S}){const{t:c}=F(),_=M(),o=m(!1),V=m(""),d=m(!1),b=m(""),s=m({id:void 0,name:"",type:"",status:k.<PERSON>,remark:""}),U=N({name:[{required:!0,message:"\u5B57\u5178\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],type:[{required:!0,message:"\u5B57\u5178\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),i=m();w({open:async(r,e)=>{if(o.value=!0,V.value=c("action."+r),b.value=r,C(),e){d.value=!0;try{s.value=await $(e)}finally{d.value=!1}}}});const h=S,A=async()=>{if(i&&await i.value.validate()){d.value=!0;try{const r=s.value;b.value==="create"?(await ee(r),_.success(c("common.createSuccess"))):(await ae(r),_.success(c("common.updateSuccess"))),o.value=!1,h("success")}finally{d.value=!1}}},C=()=>{var r;s.value={id:void 0,type:"",name:"",status:k.ENABLE,remark:""},(r=i.value)==null||r.resetFields()};return(r,e)=>{const f=j,p=R,E=K,x=G,D=O,g=X,T=Z,q=W;return n(),v(T,{modelValue:a(o),"onUpdate:modelValue":e[5]||(e[5]=l=>Y(o)?o.value=l:null),title:a(V)},{footer:t(()=>[u(g,{disabled:a(d),type:"primary",onClick:A},{default:t(()=>e[6]||(e[6]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),u(g,{onClick:e[4]||(e[4]=l=>o.value=!1)},{default:t(()=>e[7]||(e[7]=[y("\u53D6 \u6D88")])),_:1})]),default:t(()=>[L((n(),v(D,{ref_key:"formRef",ref:i,model:a(s),rules:a(U),"label-width":"80px"},{default:t(()=>[u(p,{label:"\u5B57\u5178\u540D\u79F0",prop:"name"},{default:t(()=>[u(f,{modelValue:a(s).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(s).name=l),placeholder:"\u8BF7\u8F93\u5165\u5B57\u5178\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(p,{label:"\u5B57\u5178\u7C7B\u578B",prop:"type"},{default:t(()=>[u(f,{modelValue:a(s).type,"onUpdate:modelValue":e[1]||(e[1]=l=>a(s).type=l),disabled:a(s).id!==void 0,placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u540D\u79F0"},null,8,["modelValue","disabled"])]),_:1}),u(p,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[u(x,{modelValue:a(s).status,"onUpdate:modelValue":e[2]||(e[2]=l=>a(s).status=l)},{default:t(()=>[(n(!0),H(J,null,I(a(P)(a(z).COMMON_STATUS),l=>(n(),v(E,{key:l.value,value:l.value},{default:t(()=>[y(Q(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(p,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[u(f,{modelValue:a(s).remark,"onUpdate:modelValue":e[3]||(e[3]=l=>a(s).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[q,a(d)]])]),_:1},8,["modelValue","title"])}}});export{le as _};
