import{f as Y,as as P,d as W,b as X,p as Z,r as n,bS as E,c as ee,o as M,F as te,g as c,a as i,m as ae,w as d,J as oe,A as se,G as ie,H as D,L as re,an as ne,aR as b,M as ce,aP as U,aQ as A,eL as ue,e$ as le,aG as de}from"./index-CRsFgzy0.js";import{_ as pe}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as me}from"./Form-BF4H89jq.js";import{_ as fe}from"./SpuSelect.vue_vue_type_script_setup_true_lang-tSVtv9n7.js";import{_ as ve}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-DGosLJYC.js";import{b as L}from"./formatTime-DhdtkSIS.js";import{r as k}from"./formRules-V2Qetfkc.js";import{u as ye}from"./useCrudSchemas-CNYomGr4.js";import{b as ge}from"./spu-BHhhuUrI.js";import{g as Pe}from"./index-CQgbu5O7.js";import{i as G}from"./constants-uird_4gU.js";const he=Y({name:[k],startTime:[k],endTime:[k],discountType:[k]}),we=Y([{label:"\u6D3B\u52A8\u540D\u79F0",field:"name",isSearch:!0,form:{colProps:{span:24}},table:{width:120}},{label:"\u6D3B\u52A8\u5F00\u59CB\u65F6\u95F4",field:"startTime",formatter:L,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u6D3B\u52A8\u7ED3\u675F\u65F6\u95F4",field:"endTime",formatter:L,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u6D3B\u52A8\u5546\u54C1",field:"spuId",isTable:!0,isSearch:!1,form:{colProps:{span:24}},table:{width:300}},{label:"\u5907\u6CE8",field:"remark",isSearch:!1,form:{component:"Input",componentProps:{type:"textarea",rows:4},colProps:{span:24}},table:{width:300}}]),{allSchemas:Ce}=ye(we),be=async m=>await P.get({url:"/promotion/discount-activity/page",params:m}),ke=async m=>await P.put({url:"/promotion/discount-activity/close?id="+m}),_e=async m=>await P.delete({url:"/promotion/discount-activity/delete?id="+m}),Se=W({name:"PromotionDiscountActivityForm",__name:"DiscountActivityForm",emits:["success"],setup(m,{expose:H,emit:J}){const{t:_}=X(),S=Z(),f=n(!1),V=n(""),v=n(!1),F=n(""),y=n(),x=n(),T=n(),Q=[{name:"productConfig.discountPrice",rule:e=>e>0,message:"\u5546\u54C1\u4F18\u60E0\u91D1\u989D\u4E0D\u80FD\u4E3A 0 \uFF01\uFF01\uFF01"}],I=n([]),h=n([]),w=n([]),j=(e,t)=>{R(e,t)},R=async(e,t,a,r)=>{var C;if(w.value.includes(e))return void(r!=="load"&&S.error("\u6570\u636E\u91CD\u590D\u9009\u62E9\uFF01"));w.value.push(e);const u=await ge([e]);if(u.length==0)return;const s=u[0],p=t===void 0?s==null?void 0:s.skus:(C=s==null?void 0:s.skus)==null?void 0:C.filter(o=>t.includes(o.id));p==null||p.forEach(o=>{let l={skuId:o.id,spuId:s.id,discountType:1,discountPercent:0,discountPrice:0};if(a!==void 0){const g=a.find(O=>O.skuId===o.id);g&&(g.discountPercent=b(g.discountPercent),g.discountPrice=b(g.discountPrice)),l=g||l}o.productConfig=l}),s.skus=p,h.value.push({spuId:s.id,spuDetail:s,propertyList:Pe(s)}),I.value.push(s)};H({open:async(e,t)=>{var a;if(f.value=!0,V.value=_("action."+e),F.value=e,await B(),t){v.value=!0;try{const r=await(async u=>await P.get({url:"/promotion/discount-activity/get?id="+u}))(t);for(let u in r.products){const s=r.products[u].spuId;await R(s,(a=r.products)==null?void 0:a.map(p=>p.skuId),r.products,"load")}y.value.setValues(r)}finally{v.value=!1}}}});const N=J,$=async()=>{if(y&&await y.value.getElFormRef().validate()){v.value=!0;try{const e=U(T.value.getSkuConfigs("productConfig"));e.forEach(a=>{a.discountPercent=A(a.discountPercent),a.discountPrice=A(a.discountPrice)});const t=U(y.value.formModel);t.products=e,F.value==="create"?(await(async a=>await P.post({url:"/promotion/discount-activity/create",data:a}))(t),S.success(_("common.createSuccess"))):(await(async a=>await P.put({url:"/promotion/discount-activity/update",data:a}))(t),S.success(_("common.updateSuccess"))),f.value=!1,N("success")}finally{v.value=!1}}},q=E(e=>{e.productConfig.discountPrice<=0||(e.productConfig.discountType=G.PRICE.type,e.productConfig.discountPercent=ue(e.price-le(e.productConfig.discountPrice),e.price))},200),z=E(e=>{e.productConfig.discountPercent<=0||e.productConfig.discountPercent>=100||(e.productConfig.discountType=G.PERCENT.type,e.productConfig.discountPrice=b(e.price-e.price*(e.productConfig.discountPercent/100||0)))},200),B=async()=>{I.value=[],h.value=[],w.value=[],await de(),y.value.getElFormRef().resetFields()},K=e=>{w.value.splice(w.value.findIndex(t=>t==e),1),h.value.splice(h.value.findIndex(t=>t.spuId==e),1)};return(e,t)=>{const a=ie,r=ne,u=re,s=me,p=pe,C=ce;return M(),ee(te,null,[c(p,{modelValue:i(f),"onUpdate:modelValue":t[2]||(t[2]=o=>ae(f)?f.value=o:null),title:i(V),width:"65%"},{footer:d(()=>[c(a,{disabled:i(v),type:"primary",onClick:$},{default:d(()=>t[4]||(t[4]=[D("\u786E \u5B9A")])),_:1},8,["disabled"]),c(a,{onClick:t[1]||(t[1]=o=>f.value=!1)},{default:d(()=>t[5]||(t[5]=[D("\u53D6 \u6D88")])),_:1})]),default:d(()=>[oe((M(),se(s,{ref_key:"formRef",ref:y,isCol:!0,rules:i(he),schema:i(Ce).formSchema},{spuId:d(()=>[c(a,{onClick:t[0]||(t[0]=o=>i(x).open())},{default:d(()=>t[3]||(t[3]=[D("\u9009\u62E9\u5546\u54C1")])),_:1}),c(i(ve),{ref_key:"spuAndSkuListRef",ref:T,deletable:!0,"rule-config":Q,"spu-list":i(I),"spu-property-list-p":i(h),onDelete:K},{default:d(()=>[c(u,{align:"center",label:"\u4F18\u60E0\u91D1\u989D","min-width":"168"},{default:d(({row:o})=>[c(r,{modelValue:o.productConfig.discountPrice,"onUpdate:modelValue":l=>o.productConfig.discountPrice=l,max:parseFloat(i(b)(o.price)),min:0,precision:2,step:.1,class:"w-100%",onChange:l=>i(q)(o)},null,8,["modelValue","onUpdate:modelValue","max","onChange"])]),_:1}),c(u,{align:"center",label:"\u6298\u6263\u767E\u5206\u6BD4(%)","min-width":"168"},{default:d(({row:o})=>[c(r,{modelValue:o.productConfig.discountPercent,"onUpdate:modelValue":l=>o.productConfig.discountPercent=l,max:100,min:0,precision:2,step:.1,class:"w-100%",onChange:l=>i(z)(o)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})]),_:1},8,["spu-list","spu-property-list-p"])]),_:1},8,["rules","schema"])),[[C,i(v)]])]),_:1},8,["modelValue","title"]),c(i(fe),{ref_key:"spuSelectRef",ref:x,isSelectSku:!0,onConfirm:j},null,512)],64)}}});export{Se as _,ke as c,_e as d,be as g};
