import{d as C,O as b,J as l,M as w,c as s,o as e,F as m,y as I,a3 as g,g as t,a as h,E as v,w as i,A as d,I as x,G as j,_ as A}from"./index-CRsFgzy0.js";import B from"./main-3TzxBWwr.js";import"./el-image-BQpHFDaE.js";const D={class:"waterfall"},E={key:0,class:"waterfall-item"},F=A(C({__name:"DraftTable",props:{list:{},loading:{type:Boolean}},emits:["publish","update","delete"],setup(f,{emit:k}){const p=f,c=k;return(G,J)=>{const n=x,o=j,_=v,r=b("hasPermi"),y=w;return l((e(),s("div",D,[(e(!0),s(m,null,I(p.list,a=>(e(),s(m,{key:a.articleId},[a.content&&a.content.newsItem?(e(),s("div",E,[t(h(B),{articles:a.content.newsItem},null,8,["articles"]),t(_,null,{default:i(()=>[l((e(),d(o,{type:"success",circle:"",onClick:u=>c("publish",a)},{default:i(()=>[t(n,{icon:"fa:upload"})]),_:2},1032,["onClick"])),[[r,["mp:free-publish:submit"]]]),l((e(),d(o,{type:"primary",circle:"",onClick:u=>c("update",a)},{default:i(()=>[t(n,{icon:"ep:edit"})]),_:2},1032,["onClick"])),[[r,["mp:draft:update"]]]),l((e(),d(o,{type:"danger",circle:"",onClick:u=>c("delete",a)},{default:i(()=>[t(n,{icon:"ep:delete"})]),_:2},1032,["onClick"])),[[r,["mp:draft:delete"]]])]),_:2},1024)])):g("",!0)],64))),128))])),[[y,p.loading]])}}}),[["__scopeId","data-v-30dff376"]]);export{F as default};
