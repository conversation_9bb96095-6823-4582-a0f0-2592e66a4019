import{d as x,a2 as g,A as j,o as r,dB as w,w as s,g as o,aF as y,i as t,c as l,y as _,a as h,F as k,I as v}from"./index-CRsFgzy0.js";import{u as b}from"./emoji-MM8zhL9J.js";const C={class:"ml-2 flex flex-wrap px-2"},E=["title","onClick"],F=["src"],P=x({name:"EmojiSelectPopover",__name:"EmojiSelectPopover",emits:["select-emoji"],setup(S,{emit:i}){const{getEmojiList:a}=b(),c=g(()=>a()),m=i;return(z,A)=>{const n=v,p=y,u=w;return r(),j(u,{width:500,placement:"top",trigger:"click"},{reference:s(()=>[o(n,{size:30,class:"ml-10px cursor-pointer",icon:"twemoji:grinning-face"})]),default:s(()=>[o(p,{height:"300px"},{default:s(()=>[t("ul",C,[(r(!0),l(k,null,_(h(c),(e,f)=>(r(),l("li",{key:f,style:{borderColor:"var(--el-color-primary)",color:"var(--el-color-primary)"},title:e.name,class:"icon-item mr-2 mt-1 w-1/10 flex cursor-pointer items-center justify-center border border-solid p-2",onClick:B=>(d=>{m("select-emoji",d)})(e)},[t("img",{src:e.url,class:"w-24px h-24px"},null,8,F)],8,E))),128))])]),_:1})]),_:1})}}});export{P as _};
