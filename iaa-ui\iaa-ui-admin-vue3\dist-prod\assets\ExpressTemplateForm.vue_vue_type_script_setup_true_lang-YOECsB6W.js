import{d as X,b as z,p as B,r as i,f as N,q as Q,A as w,o as b,w as t,J as W,s as Y,a as r,g as a,v as Z,P as ee,aB as ae,c as le,F as te,y as re,R as oe,D as ue,aC as se,H as m,t as de,K as ne,L as ie,cb as ce,an as pe,G as me,I as fe,M as ge,m as Ve,aR as y,aP as Ce,e$ as U}from"./index-CRsFgzy0.js";import{_ as ve}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as he,c as _e,u as be}from"./index-wRqO_lBC.js";import{g as Pe}from"./index-DLC3Afbg.js";import{d as xe}from"./tree-COGD3qag.js";const we=X({__name:"ExpressTemplateForm",emits:["success"],setup(ye,{expose:F,emit:S}){const{t:P}=z(),T=B(),k={...xe,multiple:!0},f=i(!1),M=i(""),g=i(!1),E=i(""),o=i({id:void 0,name:"",chargeMode:1,sort:0,charges:[],frees:[]}),V=new Map,C=i({startCountTitle:"\u9996\u4EF6",extraCountTitle:"\u7EED\u4EF6",freeCountTitle:"\u5305\u90AE\u4EF6\u6570"}),$=N({name:[{required:!0,message:"\u6A21\u677F\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],chargeMode:[{required:!0,message:"\u914D\u9001\u8BA1\u8D39\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u5206\u7C7B\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),_=i();F({open:async(s,l)=>{f.value=!0,M.value=P("action."+s),E.value=s,G();try{l&&(g.value=!0,o.value=await he(l),C.value=V.get(o.value.chargeMode),o.value.charges.forEach(n=>{n.startPrice=y(n.startPrice),n.extraPrice=y(n.extraPrice)}),o.value.frees.forEach(n=>{n.freePrice=y(n.freePrice)}))}finally{g.value=!1}}});const A=S,D=async()=>{if(_&&await _.value.validate()){g.value=!0;try{const s=Ce(o.value);s.charges.forEach(l=>{l.startPrice=U(l.startPrice),l.extraPrice=U(l.extraPrice)}),s.frees.forEach(l=>{l.freePrice=U(l.freePrice)}),E.value==="create"?(await _e(s),T.success(P("common.createSuccess"))):(await be(s),T.success(P("common.updateSuccess"))),f.value=!1,A("success")}finally{g.value=!1}}},G=()=>{var s;o.value={id:void 0,name:"",chargeMode:1,charges:[{areaIds:[1],startCount:2,startPrice:5,extraCount:5,extraPrice:10}],frees:[],sort:0},C.value=V.get(1),(s=_.value)==null||s.resetFields()},H=s=>{C.value=V.get(s)},x=i([]);return Q(()=>{(async()=>(V.set(1,{startCountTitle:"\u9996\u4EF6",extraCountTitle:"\u7EED\u4EF6",freeCountTitle:"\u5305\u90AE\u4EF6\u6570"}),V.set(2,{startCountTitle:"\u9996\u4EF6\u91CD\u91CF(kg)",extraCountTitle:"\u7EED\u4EF6\u91CD\u91CF(kg)",freeCountTitle:"\u5305\u90AE\u91CD\u91CF(kg)"}),V.set(3,{startCountTitle:"\u9996\u4EF6\u4F53\u79EF(m\xB3)",extraCountTitle:"\u7EED\u4EF6\u4F53\u79EF(m\xB3)",freeCountTitle:"\u5305\u90AE\u4F53\u79EF(m\xB3)"}),x.value=await Pe()))()}),(s,l)=>{const n=ee,c=Z,j=se,J=ae,I=ce,d=ie,p=pe,v=me,R=ne,q=fe,K=Y,L=ve,O=ge;return b(),w(L,{title:r(M),modelValue:r(f),"onUpdate:modelValue":l[6]||(l[6]=e=>Ve(f)?f.value=e:null),width:"1300px"},{footer:t(()=>[a(v,{onClick:D,type:"primary",disabled:r(g)},{default:t(()=>l[11]||(l[11]=[m("\u786E \u5B9A")])),_:1},8,["disabled"]),a(v,{onClick:l[5]||(l[5]=e=>f.value=!1)},{default:t(()=>l[12]||(l[12]=[m("\u53D6 \u6D88")])),_:1})]),default:t(()=>[W((b(),w(K,{ref_key:"formRef",ref:_,model:r(o),rules:r($),"label-width":"80px"},{default:t(()=>[a(c,{label:"\u6A21\u677F\u540D\u79F0",prop:"name"},{default:t(()=>[a(n,{modelValue:r(o).name,"onUpdate:modelValue":l[0]||(l[0]=e=>r(o).name=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u540D\u79F0"},null,8,["modelValue"])]),_:1}),a(c,{label:"\u8BA1\u8D39\u65B9\u5F0F",prop:"chargeMode"},{default:t(()=>[a(J,{modelValue:r(o).chargeMode,"onUpdate:modelValue":l[1]||(l[1]=e=>r(o).chargeMode=e),onChange:H},{default:t(()=>[(b(!0),le(te,null,re(r(oe)(r(ue).EXPRESS_CHARGE_MODE),e=>(b(),w(j,{key:e.value,value:e.value},{default:t(()=>[m(de(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"\u8FD0\u8D39",prop:"charges"},{default:t(()=>[a(R,{border:"",style:{width:"100%"},data:r(o).charges},{default:t(()=>[a(d,{align:"center",label:"\u533A\u57DF",width:"360"},{default:t(({row:e})=>[a(I,{modelValue:e.areaIds,"onUpdate:modelValue":u=>e.areaIds=u,options:r(x),props:k,class:"w-1/1",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u5730\u533A",filterable:"","collapse-tags":""},null,8,["modelValue","onUpdate:modelValue","options"])]),_:1}),a(d,{align:"center",label:r(C).startCountTitle,width:"180",prop:"startCount"},{default:t(({row:e})=>[a(p,{modelValue:e.startCount,"onUpdate:modelValue":u=>e.startCount=u,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"]),a(d,{width:"180",align:"center",label:"\u8FD0\u8D39(\u5143)",prop:"startPrice"},{default:t(({row:e})=>[a(p,{modelValue:e.startPrice,"onUpdate:modelValue":u=>e.startPrice=u,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(d,{width:"180",align:"center",label:r(C).extraCountTitle,prop:"extraCount"},{default:t(({row:e})=>[a(p,{modelValue:e.extraCount,"onUpdate:modelValue":u=>e.extraCount=u,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"]),a(d,{width:"180",align:"center",label:"\u7EED\u8D39(\u5143)",prop:"extraPrice"},{default:t(({row:e})=>[a(p,{modelValue:e.extraPrice,"onUpdate:modelValue":u=>e.extraPrice=u,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(d,{label:"\u64CD\u4F5C",align:"center"},{default:t(e=>[a(v,{link:"",type:"danger",onClick:u=>{return h=e.$index,void o.value.charges.splice(h,1);var h}},{default:t(()=>l[7]||(l[7]=[m(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),a(c,null,{default:t(()=>[a(v,{type:"primary",plain:"",onClick:l[2]||(l[2]=e=>{o.value.charges.push({areaIds:[],startCount:1,startPrice:1,extraCount:1,extraPrice:1})})},{default:t(()=>[a(q,{icon:"ep:plus",class:"mr-5px"}),l[8]||(l[8]=m(" \u6DFB\u52A0\u533A\u57DF "))]),_:1})]),_:1}),a(c,{label:"\u5305\u90AE\u533A\u57DF",prop:"frees"},{default:t(()=>[a(R,{border:"",style:{width:"100%"},data:r(o).frees},{default:t(()=>[a(d,{align:"center",label:"\u533A\u57DF",width:"360"},{default:t(({row:e})=>[a(I,{modelValue:e.areaIds,"onUpdate:modelValue":u=>e.areaIds=u,options:r(x),props:k,class:"w-1/1",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B",filterable:"","collapse-tags":""},null,8,["modelValue","onUpdate:modelValue","options"])]),_:1}),a(d,{align:"center",label:r(C).freeCountTitle,prop:"freeCount"},{default:t(({row:e})=>[a(p,{modelValue:e.freeCount,"onUpdate:modelValue":u=>e.freeCount=u,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"]),a(d,{align:"center",label:"\u5305\u90AE\u91D1\u989D\uFF08\u5143\uFF09",prop:"freePrice"},{default:t(({row:e})=>[a(p,{modelValue:e.freePrice,"onUpdate:modelValue":u=>e.freePrice=u,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(d,{label:"\u64CD\u4F5C",align:"center"},{default:t(e=>[a(v,{link:"",type:"danger",onClick:u=>{return h=e.$index,void o.value.frees.splice(h,1);var h}},{default:t(()=>l[9]||(l[9]=[m(" \u5220\u9664 ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),a(c,null,{default:t(()=>[a(v,{type:"primary",plain:"",onClick:l[3]||(l[3]=e=>{o.value.frees.push({areaIds:[],freeCount:1,freePrice:1})})},{default:t(()=>[a(q,{icon:"ep:plus",class:"mr-5px"}),l[10]||(l[10]=m(" \u6DFB\u52A0\u533A\u57DF "))]),_:1})]),_:1}),a(c,{label:"\u6392\u5E8F",prop:"sort"},{default:t(()=>[a(p,{modelValue:r(o).sort,"onUpdate:modelValue":l[4]||(l[4]=e=>r(o).sort=e),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[O,r(g)]])]),_:1},8,["title","modelValue"])}}});export{we as _};
