import{as as c,d as ee,b as ae,p as le,r as o,f as te,a2 as de,aK as ue,A as v,o as p,w as d,J as ie,s as ne,a as e,g as a,E as se,h as oe,v as re,P as me,C as pe,x as ce,c as h,F as x,y as S,B as fe,c2 as ye,l as ve,m as J,n as _e,ea as K,an as be,M as Ve,a3 as Pe,G as we,H as L}from"./index-CRsFgzy0.js";import{_ as Ue}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as ge}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Fe}from"./FinancePaymentItemForm.vue_vue_type_script_setup_true_lang-CRydRYWq.js";import{S as Ie}from"./index-BXOKnbFS.js";import{g as ke}from"./index-D4y5Z4cM.js";import{A as he}from"./index-DkE0YaRG.js";const _={getFinancePaymentPage:async i=>await c.get({url:"/erp/finance-payment/page",params:i}),getFinancePayment:async i=>await c.get({url:"/erp/finance-payment/get?id="+i}),createFinancePayment:async i=>await c.post({url:"/erp/finance-payment/create",data:i}),updateFinancePayment:async i=>await c.put({url:"/erp/finance-payment/update",data:i}),updateFinancePaymentStatus:async(i,b)=>await c.put({url:"/erp/finance-payment/update-status",params:{id:i,status:b}}),deleteFinancePayment:async i=>await c.delete({url:"/erp/finance-payment/delete",params:{ids:i.join(",")}}),exportFinancePayment:async i=>await c.download({url:"/erp/finance-payment/export-excel",params:i})},xe=ee({name:"FinancePaymentForm",__name:"FinancePaymentForm",emits:["success"],setup(i,{expose:b,emit:M}){const{t:V}=ae(),T=le(),f=o(!1),A=o(""),y=o(!1),P=o(""),u=o({id:void 0,supplierId:void 0,accountId:void 0,financeUserId:void 0,paymentTime:void 0,remark:void 0,fileUrl:"",totalPrice:0,discountPrice:0,paymentPrice:0,items:[],no:void 0}),R=te({supplierId:[{required:!0,message:"\u4F9B\u5E94\u5546\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],paymentTime:[{required:!0,message:"\u8BA2\u5355\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),w=de(()=>P.value==="detail"),U=o(),C=o([]),g=o([]),q=o([]),F=o("item"),E=o();ue(()=>u.value,s=>{if(!s)return;const l=s.items.reduce((m,n)=>m+n.paymentPrice,0);u.value.totalPrice=l,u.value.paymentPrice=l-s.discountPrice},{deep:!0}),b({open:async(s,l)=>{if(f.value=!0,A.value=V("action."+s),P.value=s,D(),l){y.value=!0;try{u.value=await _.getFinancePayment(l)}finally{y.value=!1}}C.value=await Ie.getSupplierSimpleList(),q.value=await ke(),g.value=await he.getAccountSimpleList();const m=g.value.find(n=>n.defaultStatus);m&&(u.value.accountId=m.id)}});const j=M,B=async()=>{await U.value.validate(),await E.value.validate(),y.value=!0;try{const s=u.value;P.value==="create"?(await _.createFinancePayment(s),T.success(V("common.createSuccess"))):(await _.updateFinancePayment(s),T.success(V("common.updateSuccess"))),f.value=!1,j("success")}finally{y.value=!1}},D=()=>{var s;u.value={id:void 0,supplierId:void 0,accountId:void 0,financeUserId:void 0,paymentTime:void 0,remark:void 0,fileUrl:void 0,totalPrice:0,discountPrice:0,paymentPrice:0,items:[],no:void 0},(s=U.value)==null||s.resetFields()};return(s,l)=>{const m=me,n=re,r=oe,N=pe,I=fe,k=ce,z=ye,G=se,O=_e,Q=ve,W=ge,X=be,Y=ne,H=we,Z=Ue,$=Ve;return p(),v(Z,{title:e(A),modelValue:e(f),"onUpdate:modelValue":l[12]||(l[12]=t=>J(f)?f.value=t:null),width:"1080"},{footer:d(()=>[e(w)?Pe("",!0):(p(),v(H,{key:0,onClick:B,type:"primary",disabled:e(y)},{default:d(()=>l[13]||(l[13]=[L(" \u786E \u5B9A ")])),_:1},8,["disabled"])),a(H,{onClick:l[11]||(l[11]=t=>f.value=!1)},{default:d(()=>l[14]||(l[14]=[L("\u53D6 \u6D88")])),_:1})]),default:d(()=>[ie((p(),v(Y,{ref_key:"formRef",ref:U,model:e(u),rules:e(R),"label-width":"100px",disabled:e(w)},{default:d(()=>[a(G,{gutter:20},{default:d(()=>[a(r,{span:8},{default:d(()=>[a(n,{label:"\u4ED8\u6B3E\u5355\u53F7",prop:"no"},{default:d(()=>[a(m,{disabled:"",modelValue:e(u).no,"onUpdate:modelValue":l[0]||(l[0]=t=>e(u).no=t),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:d(()=>[a(n,{label:"\u4ED8\u6B3E\u65F6\u95F4",prop:"paymentTime"},{default:d(()=>[a(N,{modelValue:e(u).paymentTime,"onUpdate:modelValue":l[1]||(l[1]=t=>e(u).paymentTime=t),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u4ED8\u6B3E\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:d(()=>[a(n,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:d(()=>[a(k,{modelValue:e(u).supplierId,"onUpdate:modelValue":l[2]||(l[2]=t=>e(u).supplierId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:d(()=>[(p(!0),h(x,null,S(e(C),t=>(p(),v(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:d(()=>[a(n,{label:"\u8D22\u52A1\u4EBA\u5458",prop:"financeUserId"},{default:d(()=>[a(k,{modelValue:e(u).financeUserId,"onUpdate:modelValue":l[3]||(l[3]=t=>e(u).financeUserId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8D22\u52A1\u4EBA\u5458",class:"!w-1/1"},{default:d(()=>[(p(!0),h(x,null,S(e(q),t=>(p(),v(I,{key:t.id,label:t.nickname,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:16},{default:d(()=>[a(n,{label:"\u5907\u6CE8",prop:"remark"},{default:d(()=>[a(m,{type:"textarea",modelValue:e(u).remark,"onUpdate:modelValue":l[4]||(l[4]=t=>e(u).remark=t),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:d(()=>[a(n,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:d(()=>[a(z,{"is-show-tip":!1,modelValue:e(u).fileUrl,"onUpdate:modelValue":l[5]||(l[5]=t=>e(u).fileUrl=t),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(W,null,{default:d(()=>[a(Q,{modelValue:e(F),"onUpdate:modelValue":l[6]||(l[6]=t=>J(F)?F.value=t:null),class:"-mt-15px -mb-10px"},{default:d(()=>[a(O,{label:"\u91C7\u8D2D\u5165\u5E93\u3001\u9000\u8D27\u5355",name:"item"},{default:d(()=>[a(Fe,{ref_key:"itemFormRef",ref:E,"supplier-id":e(u).supplierId,items:e(u).items,disabled:e(w)},null,8,["supplier-id","items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(G,{gutter:20},{default:d(()=>[a(r,{span:8},{default:d(()=>[a(n,{label:"\u4ED8\u6B3E\u8D26\u6237",prop:"accountId"},{default:d(()=>[a(k,{modelValue:e(u).accountId,"onUpdate:modelValue":l[7]||(l[7]=t=>e(u).accountId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:d(()=>[(p(!0),h(x,null,S(e(g),t=>(p(),v(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:d(()=>[a(n,{label:"\u5408\u8BA1\u4ED8\u6B3E",prop:"totalPrice"},{default:d(()=>[a(m,{disabled:"",modelValue:e(u).totalPrice,"onUpdate:modelValue":l[8]||(l[8]=t=>e(u).totalPrice=t),formatter:e(K)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(r,{span:8},{default:d(()=>[a(n,{label:"\u4F18\u60E0\u91D1\u989D",prop:"discountPrice"},{default:d(()=>[a(X,{modelValue:e(u).discountPrice,"onUpdate:modelValue":l[9]||(l[9]=t=>e(u).discountPrice=t),"controls-position":"right",precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u91D1\u989D",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(r,{span:8},{default:d(()=>[a(n,{label:"\u5B9E\u9645\u4ED8\u6B3E"},{default:d(()=>[a(m,{disabled:"",modelValue:e(u).paymentPrice,"onUpdate:modelValue":l[10]||(l[10]=t=>e(u).paymentPrice=t),formatter:e(K)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[$,e(y)]])]),_:1},8,["title","modelValue"])}}});export{_ as F,xe as _};
