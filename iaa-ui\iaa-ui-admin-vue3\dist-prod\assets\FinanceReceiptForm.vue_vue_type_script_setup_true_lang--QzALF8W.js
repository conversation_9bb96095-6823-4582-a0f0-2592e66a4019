import{as as m,d as ee,b as ae,p as le,r as s,f as te,a2 as ie,aK as de,A as _,o as p,w as i,J as ue,s as re,a as e,g as a,E as oe,h as se,v as ce,P as ne,C as pe,x as me,c as R,F as h,y as x,B as fe,c2 as ve,l as _e,m as H,n as be,ea as J,an as Ve,M as we,a3 as ye,G as Ue,H as K}from"./index-CRsFgzy0.js";import{_ as ge}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Pe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Fe}from"./FinanceReceiptItemForm.vue_vue_type_script_setup_true_lang-BfDU00tT.js";import{g as Ie}from"./index-D4y5Z4cM.js";import{A as ke}from"./index-DkE0YaRG.js";import{C as Re}from"./index-COzCXl3z.js";const b={getFinanceReceiptPage:async u=>await m.get({url:"/erp/finance-receipt/page",params:u}),getFinanceReceipt:async u=>await m.get({url:"/erp/finance-receipt/get?id="+u}),createFinanceReceipt:async u=>await m.post({url:"/erp/finance-receipt/create",data:u}),updateFinanceReceipt:async u=>await m.put({url:"/erp/finance-receipt/update",data:u}),updateFinanceReceiptStatus:async(u,V)=>await m.put({url:"/erp/finance-receipt/update-status",params:{id:u,status:V}}),deleteFinanceReceipt:async u=>await m.delete({url:"/erp/finance-receipt/delete",params:{ids:u.join(",")}}),exportFinanceReceipt:async u=>await m.download({url:"/erp/finance-receipt/export-excel",params:u})},he=ee({name:"FinanceReceiptForm",__name:"FinanceReceiptForm",emits:["success"],setup(u,{expose:V,emit:L}){const{t:w}=ae(),S=le(),f=s(!1),T=s(""),v=s(!1),y=s(""),d=s({id:void 0,customerId:void 0,accountId:void 0,financeUserId:void 0,receiptTime:void 0,remark:void 0,fileUrl:"",totalPrice:0,discountPrice:0,receiptPrice:0,items:[],no:void 0}),M=te({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],receiptTime:[{required:!0,message:"\u8BA2\u5355\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),U=ie(()=>y.value==="detail"),g=s(),C=s([]),P=s([]),A=s([]),F=s("item"),q=s();de(()=>d.value,o=>{if(!o)return;const l=o.items.reduce((n,r)=>n+r.receiptPrice,0);d.value.totalPrice=l,d.value.receiptPrice=l-o.discountPrice},{deep:!0}),V({open:async(o,l)=>{if(f.value=!0,T.value=w("action."+o),y.value=o,D(),l){v.value=!0;try{d.value=await b.getFinanceReceipt(l)}finally{v.value=!1}}C.value=await Re.getCustomerSimpleList(),A.value=await Ie(),P.value=await ke.getAccountSimpleList();const n=P.value.find(r=>r.defaultStatus);n&&(d.value.accountId=n.id)}});const j=L,B=async()=>{await g.value.validate(),await q.value.validate(),v.value=!0;try{const o=d.value;y.value==="create"?(await b.createFinanceReceipt(o),S.success(w("common.createSuccess"))):(await b.updateFinanceReceipt(o),S.success(w("common.updateSuccess"))),f.value=!1,j("success")}finally{v.value=!1}},D=()=>{var o;d.value={id:void 0,customerId:void 0,accountId:void 0,financeUserId:void 0,receiptTime:void 0,remark:void 0,fileUrl:void 0,totalPrice:0,discountPrice:0,receiptPrice:0,items:[],no:void 0},(o=g.value)==null||o.resetFields()};return(o,l)=>{const n=ne,r=ce,c=se,N=pe,I=fe,k=me,z=ve,E=oe,O=be,Q=_e,W=Pe,X=Ve,Y=re,G=Ue,Z=ge,$=we;return p(),_(Z,{title:e(T),modelValue:e(f),"onUpdate:modelValue":l[12]||(l[12]=t=>H(f)?f.value=t:null),width:"1080"},{footer:i(()=>[e(U)?ye("",!0):(p(),_(G,{key:0,onClick:B,type:"primary",disabled:e(v)},{default:i(()=>l[13]||(l[13]=[K(" \u786E \u5B9A ")])),_:1},8,["disabled"])),a(G,{onClick:l[11]||(l[11]=t=>f.value=!1)},{default:i(()=>l[14]||(l[14]=[K("\u53D6 \u6D88")])),_:1})]),default:i(()=>[ue((p(),_(Y,{ref_key:"formRef",ref:g,model:e(d),rules:e(M),"label-width":"100px",disabled:e(U)},{default:i(()=>[a(E,{gutter:20},{default:i(()=>[a(c,{span:8},{default:i(()=>[a(r,{label:"\u6536\u6B3E\u5355\u53F7",prop:"no"},{default:i(()=>[a(n,{disabled:"",modelValue:e(d).no,"onUpdate:modelValue":l[0]||(l[0]=t=>e(d).no=t),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(c,{span:8},{default:i(()=>[a(r,{label:"\u6536\u6B3E\u65F6\u95F4",prop:"receiptTime"},{default:i(()=>[a(N,{modelValue:e(d).receiptTime,"onUpdate:modelValue":l[1]||(l[1]=t=>e(d).receiptTime=t),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u6536\u6B3E\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(c,{span:8},{default:i(()=>[a(r,{label:"\u5BA2\u6237",prop:"customerId"},{default:i(()=>[a(k,{modelValue:e(d).customerId,"onUpdate:modelValue":l[2]||(l[2]=t=>e(d).customerId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:i(()=>[(p(!0),R(h,null,x(e(C),t=>(p(),_(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(c,{span:8},{default:i(()=>[a(r,{label:"\u8D22\u52A1\u4EBA\u5458",prop:"financeUserId"},{default:i(()=>[a(k,{modelValue:e(d).financeUserId,"onUpdate:modelValue":l[3]||(l[3]=t=>e(d).financeUserId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8D22\u52A1\u4EBA\u5458",class:"!w-1/1"},{default:i(()=>[(p(!0),R(h,null,x(e(A),t=>(p(),_(I,{key:t.id,label:t.nickname,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(c,{span:16},{default:i(()=>[a(r,{label:"\u5907\u6CE8",prop:"remark"},{default:i(()=>[a(n,{type:"textarea",modelValue:e(d).remark,"onUpdate:modelValue":l[4]||(l[4]=t=>e(d).remark=t),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(c,{span:8},{default:i(()=>[a(r,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:i(()=>[a(z,{"is-show-tip":!1,modelValue:e(d).fileUrl,"onUpdate:modelValue":l[5]||(l[5]=t=>e(d).fileUrl=t),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(W,null,{default:i(()=>[a(Q,{modelValue:e(F),"onUpdate:modelValue":l[6]||(l[6]=t=>H(F)?F.value=t:null),class:"-mt-15px -mb-10px"},{default:i(()=>[a(O,{label:"\u91C7\u8D2D\u5165\u5E93\u3001\u9000\u8D27\u5355",name:"item"},{default:i(()=>[a(Fe,{ref_key:"itemFormRef",ref:q,"customer-id":e(d).customerId,items:e(d).items,disabled:e(U)},null,8,["customer-id","items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(E,{gutter:20},{default:i(()=>[a(c,{span:8},{default:i(()=>[a(r,{label:"\u6536\u6B3E\u8D26\u6237",prop:"accountId"},{default:i(()=>[a(k,{modelValue:e(d).accountId,"onUpdate:modelValue":l[7]||(l[7]=t=>e(d).accountId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:i(()=>[(p(!0),R(h,null,x(e(P),t=>(p(),_(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(c,{span:8},{default:i(()=>[a(r,{label:"\u5408\u8BA1\u6536\u6B3E",prop:"totalPrice"},{default:i(()=>[a(n,{disabled:"",modelValue:e(d).totalPrice,"onUpdate:modelValue":l[8]||(l[8]=t=>e(d).totalPrice=t),formatter:e(J)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(c,{span:8},{default:i(()=>[a(r,{label:"\u4F18\u60E0\u91D1\u989D",prop:"discountPrice"},{default:i(()=>[a(X,{modelValue:e(d).discountPrice,"onUpdate:modelValue":l[9]||(l[9]=t=>e(d).discountPrice=t),"controls-position":"right",precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u91D1\u989D",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(c,{span:8},{default:i(()=>[a(r,{label:"\u5B9E\u9645\u6536\u6B3E"},{default:i(()=>[a(n,{disabled:"",modelValue:e(d).receiptPrice,"onUpdate:modelValue":l[10]||(l[10]=t=>e(d).receiptPrice=t),formatter:e(J)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[$,e(v)]])]),_:1},8,["title","modelValue"])}}});export{b as F,he as _};
