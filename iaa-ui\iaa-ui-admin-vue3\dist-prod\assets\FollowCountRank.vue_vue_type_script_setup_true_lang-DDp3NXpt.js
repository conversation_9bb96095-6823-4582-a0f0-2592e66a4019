import{d as x,r as m,f as b,q as v,c as _,o as p,F as k,g as a,k as A,w as o,a as n,J as F,M as q,A as C,L as P,K as R,eO as I}from"./index-CRsFgzy0.js";import{E as L}from"./el-skeleton-item-CZ5buDOR.js";import{_ as j}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as D}from"./rank-DcFXIK-C.js";const E=x({name:"FollowCountRank",__name:"FollowCountRank",props:{queryParams:{}},setup(u,{expose:c}){const g=u,e=m(!1),l=m([]),t=b({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u8DDF\u8FDB\u6B21\u6570\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u8DDF\u8FDB\u6B21\u6570\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u8DDF\u8FDB\u6B21\u6570\uFF08\u6B21\uFF09"},yAxis:{type:"category",name:"\u5458\u5DE5"}}),i=async()=>{e.value=!0;const r=await D.getFollowCountRank(g.queryParams);t.dataset&&t.dataset.source&&(t.dataset.source=I(r).reverse()),l.value=r,e.value=!1};return c({loadData:i}),v(()=>{i()}),(r,J)=>{const h=j,w=L,d=A,s=P,y=R,f=q;return p(),_(k,null,[a(d,{shadow:"never"},{default:o(()=>[a(w,{loading:n(e),animated:""},{default:o(()=>[a(h,{height:500,options:n(t)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(d,{shadow:"never",class:"mt-16px"},{default:o(()=>[F((p(),C(y,{data:n(l)},{default:o(()=>[a(s,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(s,{label:"\u5458\u5DE5",align:"center",prop:"nickname","min-width":"200"}),a(s,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(s,{label:"\u8DDF\u8FDB\u6B21\u6570\uFF08\u6B21\uFF09",align:"center",prop:"count","min-width":"200"})]),_:1},8,["data"])),[[f,n(e)]])]),_:1})],64)}}});export{E as _};
