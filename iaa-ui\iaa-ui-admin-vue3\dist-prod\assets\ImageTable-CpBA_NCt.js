import{d as _,O as y,J as i,M as k,c as s,o as a,F as v,y as w,i as l,g as r,t as h,E as I,w as n,A as b,I as C,G as j,_ as x}from"./index-CRsFgzy0.js";const A={class:"waterfall"},B=["href"],E=["src"],F={class:"item-name"},G=x(_({__name:"ImageTable",props:{list:{},loading:{type:Boolean}},emits:["delete"],setup(c,{emit:d}){const t=c,m=d;return(J,M)=>{const o=C,u=j,p=I,f=y("hasPermi"),g=k;return i((a(),s("div",A,[(a(!0),s(v,null,w(t.list,e=>(a(),s("div",{class:"waterfall-item",key:e.id},[l("a",{target:"_blank",href:e.url},[l("img",{class:"material-img",src:e.url},null,8,E),l("div",F,h(e.name),1)],8,B),r(p,{justify:"center"},{default:n(()=>[i((a(),b(u,{type:"danger",circle:"",onClick:O=>m("delete",e.id)},{default:n(()=>[r(o,{icon:"ep:delete"})]),_:2},1032,["onClick"])),[[f,["mp:material:delete"]]])]),_:2},1024)]))),128))])),[[g,t.loading]])}}}),[["__scopeId","data-v-415c75a3"]]);export{G as default};
