import{d as J,p as L,r as d,f as M,A as b,o as f,w as l,g as a,s as N,a as o,v as O,x as P,c as Q,F as z,y as D,B as W,P as X,Q as V,G as Y,H as v,I as Z,E as $,J as ee,K as ae,L as le,M as oe,m as te}from"./index-CRsFgzy0.js";import{_ as se}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{b as de,c as ne}from"./index-DjgTygXx.js";import{g as ue}from"./index-BYKUBRQa.js";const me=J({name:"InfraCodegenImportTable",__name:"ImportTable",emits:["success"],setup(re,{expose:x,emit:S}){const k=L(),n=d(!1),_=d(!0),g=d([]),t=M({name:void 0,comment:void 0,dataSourceConfigId:0}),R=d(),m=d([]),u=async()=>{_.value=!0;try{g.value=await de(t)}finally{_.value=!1}},U=async()=>{t.name=void 0,t.comment=void 0,t.dataSourceConfigId=m.value[0].id,await u()};x({open:async()=>{m.value=await ue(),t.dataSourceConfigId=m.value[0].id,n.value=!0,await u()}});const y=()=>{n.value=!1,r.value=[]},C=d(),r=d([]),K=c=>{var e;(e=o(C))==null||e.toggleRowSelection(c)},T=c=>{r.value=c.map(e=>e.name)},F=async()=>{await ne({dataSourceConfigId:t.dataSourceConfigId,tableNames:r.value}),k.success("\u5BFC\u5165\u6210\u529F"),j("success"),y()},j=S;return(c,e)=>{const q=W,A=P,i=O,h=X,I=Z,p=Y,B=N,w=le,E=$,G=se,H=oe;return f(),b(G,{modelValue:o(n),"onUpdate:modelValue":e[3]||(e[3]=s=>te(n)?n.value=s:null),title:"\u5BFC\u5165\u8868",width:"800px"},{footer:l(()=>[a(p,{disabled:o(r).length===0,type:"primary",onClick:F},{default:l(()=>e[6]||(e[6]=[v(" \u5BFC\u5165 ")])),_:1},8,["disabled"]),a(p,{onClick:y},{default:l(()=>e[7]||(e[7]=[v("\u5173\u95ED")])),_:1})]),default:l(()=>[a(B,{ref_key:"queryFormRef",ref:R,inline:!0,model:o(t),"label-width":"68px"},{default:l(()=>[a(i,{label:"\u6570\u636E\u6E90",prop:"dataSourceConfigId"},{default:l(()=>[a(A,{modelValue:o(t).dataSourceConfigId,"onUpdate:modelValue":e[0]||(e[0]=s=>o(t).dataSourceConfigId=s),class:"!w-240px",placeholder:"\u8BF7\u9009\u62E9\u6570\u636E\u6E90"},{default:l(()=>[(f(!0),Q(z,null,D(o(m),s=>(f(),b(q,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u8868\u540D\u79F0",prop:"name"},{default:l(()=>[a(h,{modelValue:o(t).name,"onUpdate:modelValue":e[1]||(e[1]=s=>o(t).name=s),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u540D\u79F0",onKeyup:V(u,["enter"])},null,8,["modelValue"])]),_:1}),a(i,{label:"\u8868\u63CF\u8FF0",prop:"comment"},{default:l(()=>[a(h,{modelValue:o(t).comment,"onUpdate:modelValue":e[2]||(e[2]=s=>o(t).comment=s),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u63CF\u8FF0",onKeyup:V(u,["enter"])},null,8,["modelValue"])]),_:1}),a(i,null,{default:l(()=>[a(p,{onClick:u},{default:l(()=>[a(I,{class:"mr-5px",icon:"ep:search"}),e[4]||(e[4]=v(" \u641C\u7D22 "))]),_:1}),a(p,{onClick:U},{default:l(()=>[a(I,{class:"mr-5px",icon:"ep:refresh"}),e[5]||(e[5]=v(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),a(E,null,{default:l(()=>[ee((f(),b(o(ae),{ref_key:"tableRef",ref:C,data:o(g),height:"260px",onRowClick:K,onSelectionChange:T},{default:l(()=>[a(w,{type:"selection",width:"55"}),a(w,{"show-overflow-tooltip":!0,label:"\u8868\u540D\u79F0",prop:"name"}),a(w,{"show-overflow-tooltip":!0,label:"\u8868\u63CF\u8FF0",prop:"comment"})]),_:1},8,["data"])),[[H,o(_)]])]),_:1})]),_:1},8,["modelValue"])}}});export{me as _};
