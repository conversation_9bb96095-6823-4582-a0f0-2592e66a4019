import{_ as D}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as A,r as n,A as v,o as f,w as a,g as l,H as r,t as u,a as e,D as E,c as I,F as J,y as g,m as V}from"./index-CRsFgzy0.js";import{E as C,a as F}from"./el-descriptions-item-lelixL8M.js";import{E as N,a as S}from"./el-timeline-item-D5y24tPs.js";import{_ as U}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{f as j}from"./formatTime-DhdtkSIS.js";import{g as k,a as B}from"./index-CeqLCnkA.js";const H=A({name:"InfraJobDetail",__name:"JobDetail",setup(O,{expose:y}){const s=n(!1),i=n(!1),t=n({}),_=n([]);return y({open:async m=>{if(s.value=!0,m){i.value=!0;try{t.value=await k(m),_.value=await B(m)}finally{i.value=!1}}}}),(m,p)=>{const o=F,c=U,h=S,w=N,T=C,x=D;return f(),v(x,{modelValue:e(s),"onUpdate:modelValue":p[0]||(p[0]=d=>V(s)?s.value=d:null),title:"\u4EFB\u52A1\u8BE6\u7EC6",width:"700px"},{default:a(()=>[l(T,{column:1,border:""},{default:a(()=>[l(o,{label:"\u4EFB\u52A1\u7F16\u53F7","min-width":"60"},{default:a(()=>[r(u(e(t).id),1)]),_:1}),l(o,{label:"\u4EFB\u52A1\u540D\u79F0"},{default:a(()=>[r(u(e(t).name),1)]),_:1}),l(o,{label:"\u4EFB\u52A1\u72B6\u6001"},{default:a(()=>[l(c,{type:e(E).INFRA_JOB_STATUS,value:e(t).status},null,8,["type","value"])]),_:1}),l(o,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57"},{default:a(()=>[r(u(e(t).handlerName),1)]),_:1}),l(o,{label:"\u5904\u7406\u5668\u7684\u53C2\u6570"},{default:a(()=>[r(u(e(t).handlerParam),1)]),_:1}),l(o,{label:"Cron \u8868\u8FBE\u5F0F"},{default:a(()=>[r(u(e(t).cronExpression),1)]),_:1}),l(o,{label:"\u91CD\u8BD5\u6B21\u6570"},{default:a(()=>[r(u(e(t).retryCount),1)]),_:1}),l(o,{label:"\u91CD\u8BD5\u95F4\u9694"},{default:a(()=>[r(u(e(t).retryInterval+" \u6BEB\u79D2"),1)]),_:1}),l(o,{label:"\u76D1\u63A7\u8D85\u65F6\u65F6\u95F4"},{default:a(()=>[r(u(e(t).monitorTimeout>0?e(t).monitorTimeout+" \u6BEB\u79D2":"\u672A\u5F00\u542F"),1)]),_:1}),l(o,{label:"\u540E\u7EED\u6267\u884C\u65F6\u95F4"},{default:a(()=>[l(w,null,{default:a(()=>[(f(!0),I(J,null,g(e(_),(d,b)=>(f(),v(h,{key:b,timestamp:e(j)(d)},{default:a(()=>[r(" \u7B2C "+u(b+1)+" \u6B21 ",1)]),_:2},1032,["timestamp"]))),128))]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{H as _};
