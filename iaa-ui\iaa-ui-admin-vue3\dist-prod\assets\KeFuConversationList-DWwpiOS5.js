import{d as G,p as H,V as J,r as c,a2 as T,f0 as N,aK as B,q as Q,aL as W,O as Z,A as $,o as g,e4 as ee,w as _,i as s,c as P,J as f,t as w,a as n,y as ae,bu as m,X as se,g as r,eg as te,F as ne,a6 as y,H as x,I as ie,aw as le,_ as oe}from"./index-CRsFgzy0.js";import{E as ce}from"./el-avatar-Nl9DW69B.js";import{u as re,K as o,a as Y}from"./kefu-CwcoonwQ.js";import{u as ue}from"./emoji-MM8zhL9J.js";import{c as de}from"./formatTime-DhdtkSIS.js";const me={class:"color-[#999] font-bold my-10px"},pe=["onClick","onContextmenu"],ve={class:"flex justify-center items-center w-100%"},fe={class:"flex justify-center items-center w-50px h-50px"},xe={class:"ml-10px w-100%"},Ce={class:"flex justify-between items-center w-100%"},ge={class:"username"},we={class:"color-[#999]",style:{"font-size":"13px"}},ye={class:"last-message flex items-center color-[#999]"},he=oe(G({name:"KeFuConversationList",__name:"KeFuConversationList",emits:["change"],setup(ke,{expose:D,emit:O}){const h=H(),j=J(),u=re(),{replaceEmoji:K}=ue(),C=c(-1),X=T(()=>j.getCollapse),k=c(new Map),E=()=>{var t;(t=u.getConversationList)==null||t.forEach(e=>{k.value.set(e.id,de(e.lastMessageTime,"YYYY-MM-DD"))})};D({calculationLastMessageTime:E});const A=O,F=T(()=>(t,e)=>{switch(t){case o.SYSTEM:return"[\u7CFB\u7EDF\u6D88\u606F]";case o.VIDEO:return"[\u89C6\u9891\u6D88\u606F]";case o.IMAGE:return"[\u56FE\u7247\u6D88\u606F]";case o.PRODUCT:return"[\u5546\u54C1\u6D88\u606F]";case o.ORDER:return"[\u8BA2\u5355\u6D88\u606F]";case o.VOICE:return"[\u8BED\u97F3\u6D88\u606F]";case o.TEXT:return K(N(e).text||e);default:return""}}),p=c(!1),M=c({}),i=c({}),d=()=>{p.value=!1},L=async t=>{await Y.updateConversationPinned({id:i.value.id,adminPinned:t}),h.notifySuccess(t?"\u7F6E\u9876\u6210\u529F":"\u53D6\u6D88\u7F6E\u9876\u6210\u529F"),d(),await u.updateConversation(i.value.id)},R=async()=>{await h.confirm("\u60A8\u786E\u5B9A\u8981\u5220\u9664\u8BE5\u4F1A\u8BDD\u5417\uFF1F"),await Y.deleteConversation(i.value.id),d(),u.deleteConversation(i.value.id)};B(p,t=>{t?document.body.addEventListener("click",d):document.body.removeEventListener("click",d)});const b=c();return Q(()=>{b.value=setInterval(E,1e4)}),W(()=>{clearInterval(b.value)}),(t,e)=>{const S=ce,U=te,v=ie,V=ee,q=Z("dompurify-html");return g(),$(V,{class:"kefu pt-5px h-100%",width:"260px"},{default:_(()=>[s("div",me," \u4F1A\u8BDD\u8BB0\u5F55("+w(n(u).getConversationList.length)+") ",1),(g(!0),P(ne,null,ae(n(u).getConversationList,a=>(g(),P("div",{key:a.id,class:se([{active:a.id===n(C),pinned:a.adminPinned},"kefu-conversation px-10px flex items-center"]),onClick:I=>(l=>{C.value!==l.id&&(C.value=l.id,A("change",l))})(a),onContextmenu:m(I=>((l,z)=>{i.value=z,p.value=!0,M.value={top:l.clientY-110+"px",left:X.value?l.clientX-80+"px":l.clientX-210+"px"}})(I,a),["prevent"])},[s("div",ve,[s("div",fe,[r(U,{hidden:a.adminUnreadMessageCount===0,max:99,value:a.adminUnreadMessageCount},{default:_(()=>[r(S,{src:a.userAvatar,alt:"avatar"},null,8,["src"])]),_:2},1032,["hidden","value"])]),s("div",xe,[s("div",Ce,[s("span",ge,w(a.userNickname),1),s("span",we,w(n(k).get(a.id)??"\u8BA1\u7B97\u4E2D"),1)]),f(s("div",ye,null,512),[[q,n(F)(a.lastMessageContentType,a.lastMessageContent)]])])])],42,pe))),128)),f(s("ul",{style:le(n(M)),class:"right-menu-ul"},[f(s("li",{class:"flex items-center",onClick:e[0]||(e[0]=m(a=>L(!0),["stop"]))},[r(v,{class:"mr-5px",icon:"ep:top"}),e[2]||(e[2]=x(" \u7F6E\u9876\u4F1A\u8BDD "))],512),[[y,!n(i).adminPinned]]),f(s("li",{class:"flex items-center",onClick:e[1]||(e[1]=m(a=>L(!1),["stop"]))},[r(v,{class:"mr-5px",icon:"ep:bottom"}),e[3]||(e[3]=x(" \u53D6\u6D88\u7F6E\u9876 "))],512),[[y,n(i).adminPinned]]),s("li",{class:"flex items-center",onClick:m(R,["stop"])},[r(v,{class:"mr-5px",color:"red",icon:"ep:delete"}),e[4]||(e[4]=x(" \u5220\u9664\u4F1A\u8BDD "))]),s("li",{class:"flex items-center",onClick:m(d,["stop"])},[r(v,{class:"mr-5px",color:"red",icon:"ep:close"}),e[5]||(e[5]=x(" \u53D6\u6D88 "))])],4),[[y,n(p)]])]),_:1})}}}),[["__scopeId","data-v-87bdc0f6"]]);export{he as default};
