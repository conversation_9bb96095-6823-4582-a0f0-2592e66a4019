import{as as R,d as me,ck as A,r as c,p as pe,f as ve,a2 as E,f0 as de,dD as D,bS as fe,a as s,aD as ye,O as ge,A as f,o as r,aq as we,w as o,g as l,i as y,t as L,am as Te,J as X,aF as xe,c as x,a3 as m,F as ke,y as he,X as G,a6 as Me,I as _e,al as Ee,P as Ie,Q as Se,bu as be,m as Ne,ap as Ue,aG as Re,_ as Ae}from"./index-CRsFgzy0.js";import{E as De}from"./el-empty-CqTDiVWi.js";import{E as Le}from"./el-image-BQpHFDaE.js";import{E as Ke}from"./el-avatar-Nl9DW69B.js";import{_ as je}from"./EmojiSelectPopover.vue_vue_type_script_setup_true_lang-CGvb_qyx.js";import{_ as Ce}from"./PictureSelectUpload.vue_vue_type_script_setup_true_lang-DXhFfzoT.js";import He from"./ProductItem-CSRkJo9p.js";import Oe from"./OrderItem-DgbO8iIn.js";import{u as Fe}from"./emoji-MM8zhL9J.js";import{u as Je,K as p}from"./kefu-CwcoonwQ.js";import{U as I}from"./constants-uird_4gU.js";import{f as P}from"./formatTime-DhdtkSIS.js";import{r as Xe}from"./relativeTime-Bcjvi3Pu.js";import"./picture-CTjip5lJ.js";const Ge=async g=>await R.post({url:"/promotion/kefu-message/send",data:g}),Pe=async g=>await R.put({url:"/promotion/kefu-message/update-read-status?conversationId="+g}),Ve=async g=>await R.get({url:"/promotion/kefu-message/list",params:g}),Ye={class:"kefu-title"},Be={class:"flex justify-center items-center mb-20px"},Qe={key:0,class:"date-message"},qe={key:1,class:"system-message"},We={key:0,class:"line-height-normal text-justify h-1/1 w-full"},ze={class:"chat-tools flex items-center"},Ze=Ae(me({name:"KeFuMessageList",__name:"KeFuMessageList",setup(g,{expose:V}){A.extend(Xe);const v=c(""),{replaceEmoji:Y}=Fe(),B=pe(),n=c([]),i=c({}),S=c(!1),w=ve({conversationId:0,createTime:void 0}),Q=c(0),b=c(!1),N=Je(),u=E(()=>e=>de(e.content)),K=async()=>{const e=await Ve(w);if(D(e))U.value=!0;else{if(w.createTime=P(e.at(-1).createTime),w.createTime)for(const t of e)j(t);else n.value=e;b.value=!0}},j=e=>{n.value.some(t=>t.id===e.id)||n.value.push(e)},q=E(()=>(n.value.sort((e,t)=>e.createTime-t.createTime),n.value)),k=async e=>{if(i.value){if(e!==void 0){if(e.conversationId!==i.value.id)return;j(e)}else w.createTime=void 0,await K();T.value?S.value=!0:await H()}};V({getNewMessageList:async e=>{N.saveMessageList(i.value.id,n.value),n.value=N.getConversationMessageList(e.id)||[],Q.value=n.value.length||0,T.value=!1,b.value=!1,U.value=!1,i.value=e,w.conversationId=e.id,w.createTime=void 0,await k()},refreshMessageList:k});const W=E(()=>!D(i.value)),U=c(!1),z=e=>{v.value+=e.name},Z=async e=>{const t={conversationId:i.value.id,contentType:p.IMAGE,content:JSON.stringify({picUrl:e})};await C(t)},$=async e=>{var d;if(e.shiftKey)return;if(D((d=s(v.value))==null?void 0:d.trim()))return B.notifyWarning("\u8BF7\u8F93\u5165\u6D88\u606F\u540E\u518D\u53D1\u9001\u54E6\uFF01"),void(v.value="");const t={conversationId:i.value.id,contentType:p.TEXT,content:JSON.stringify({text:v.value})};await C(t)},C=async e=>{await Ge(e),v.value="",await k(),await N.updateConversation(i.value.id)},h=c(),M=c(),H=async()=>{T.value=!1,await(async()=>{T.value||(await Re(),M.value.setScrollTop(h.value.clientHeight),S.value=!1,await Pe(i.value.id))})()},T=c(!1),ee=fe(({scrollTop:e})=>{var d;if(U.value)return;Math.floor(e)===0&&ae();const t=(d=M.value)==null?void 0:d.wrapRef;Math.abs(t.scrollHeight-t.clientHeight-t.scrollTop)<1&&(T.value=!1,k())},200),ae=async()=>{var t;const e=(t=h.value)==null?void 0:t.clientHeight;e&&(T.value=!0,await K(),M.value.setScrollTop(h.value.clientHeight-e))},se=E(()=>(e,t)=>s(n.value)[t+1]?A(s(n.value)[t+1].createTime).fromNow()!==A(s(e).createTime).fromNow():!1);return(e,t)=>{const d=Te,O=Ke,_=ye("MessageItem"),te=Le,le=xe,re=_e,F=Ee,ie=Ie,oe=Ue,J=we,ne=De,ce=ge("dompurify-html");return s(W)?(r(),f(J,{key:0,class:"kefu"},{default:o(()=>[l(d,{class:"kefu-header"},{default:o(()=>[y("div",Ye,L(s(i).userNickname),1)]),_:1}),l(F,{class:"kefu-content overflow-visible"},{default:o(()=>[l(le,{ref_key:"scrollbarRef",ref:M,always:"",onScroll:s(ee)},{default:o(()=>[s(b)?(r(),x("div",{key:0,ref_key:"innerRef",ref:h,class:"w-[100%] px-10px"},[(r(!0),x(ke,null,he(s(q),(a,ue)=>(r(),x("div",{key:a.id,class:"w-[100%]"},[y("div",Be,[a.contentType!==s(p).SYSTEM&&s(se)(a,ue)?(r(),x("div",Qe,L(s(P)(a.createTime)),1)):m("",!0),a.contentType===s(p).SYSTEM?(r(),x("div",qe,L(a.content),1)):m("",!0)]),y("div",{class:G([[a.senderType===s(I).MEMBER?"ss-row-left":a.senderType===s(I).ADMIN?"ss-row-right":""],"flex mb-20px w-[100%]"])},[a.senderType===s(I).MEMBER?(r(),f(O,{key:0,src:s(i).userAvatar,alt:"avatar",class:"w-60px h-60px"},null,8,["src"])):m("",!0),y("div",{class:G({"kefu-message":s(p).TEXT===a.contentType})},[l(_,{message:a},{default:o(()=>[s(p).TEXT===a.contentType?X((r(),x("div",We,null,512)),[[ce,s(Y)(s(u)(a).text||a.content)]]):m("",!0)]),_:2},1032,["message"]),l(_,{message:a},{default:o(()=>[s(p).IMAGE===a.contentType?(r(),f(te,{key:0,"initial-index":0,"preview-src-list":[s(u)(a).picUrl||a.content],src:s(u)(a).picUrl||a.content,class:"w-200px mx-10px",fit:"contain","preview-teleported":""},null,8,["preview-src-list","src"])):m("",!0)]),_:2},1032,["message"]),l(_,{message:a},{default:o(()=>[s(p).PRODUCT===a.contentType?(r(),f(He,{key:0,picUrl:s(u)(a).picUrl,price:s(u)(a).price,"sales-count":s(u)(a).salesCount,spuId:s(u)(a).spuId,stock:s(u)(a).stock,title:s(u)(a).spuName,class:"max-w-300px mx-10px"},null,8,["picUrl","price","sales-count","spuId","stock","title"])):m("",!0)]),_:2},1032,["message"]),l(_,{message:a},{default:o(()=>[s(p).ORDER===a.contentType?(r(),f(Oe,{key:0,message:a,class:"max-w-100% mx-10px"},null,8,["message"])):m("",!0)]),_:2},1032,["message"])],2),a.senderType===s(I).ADMIN?(r(),f(O,{key:1,src:a.senderAvatar,alt:"avatar"},null,8,["src"])):m("",!0)],2)]))),128))],512)):m("",!0)]),_:1},8,["onScroll"]),X(y("div",{class:"newMessageTip flex items-center cursor-pointer",onClick:H},[t[1]||(t[1]=y("span",null,"\u6709\u65B0\u6D88\u606F",-1)),l(re,{class:"ml-5px",icon:"ep:bottom"})],512),[[Me,s(S)]])]),_:1}),l(oe,{class:"kefu-footer"},{default:o(()=>[y("div",ze,[l(je,{onSelectEmoji:z}),l(Ce,{class:"ml-15px mt-3px cursor-pointer",onSendPicture:Z})]),l(ie,{modelValue:s(v),"onUpdate:modelValue":t[0]||(t[0]=a=>Ne(v)?v.value=a:null),rows:6,placeholder:"\u8F93\u5165\u6D88\u606F\uFF0CEnter\u53D1\u9001\uFF0CShift+Enter\u6362\u884C",style:{"border-style":"none"},type:"textarea",onKeyup:Se(be($,["prevent"]),["enter"])},null,8,["modelValue","onKeyup"])]),_:1})]),_:1})):(r(),f(J,{key:1,class:"kefu"},{default:o(()=>[l(F,null,{default:o(()=>[l(ne,{description:"\u8BF7\u9009\u62E9\u5DE6\u4FA7\u7684\u4E00\u4E2A\u4F1A\u8BDD\u540E\u5F00\u59CB"})]),_:1})]),_:1}))}}}),[["__scopeId","data-v-39d9583a"]]);export{Ze as default};
