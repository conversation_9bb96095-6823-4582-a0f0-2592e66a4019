import{dq as $t,r as S,bB as Kt,dM as Qt,q as ye,fc as el,aY as Ye,d as R,aZ as Ke,a2 as d,A as F,o as _,w,c as B,a3 as se,a as e,bu as Pt,X as P,aw as tl,b3 as Me,g as t,ay as It,fd as ll,$ as Qe,b6 as Tt,aW as Mt,b9 as al,bI as ol,aM as St,aX as rl,bv as nl,bA as sl,i as g,bd as Lt,t as I,b7 as il,W as q,b as Q,I as Z,F as Y,fe as Se,ff as ie,fg as cl,fh as ul,V as G,u as fe,a1 as Le,aF as ce,fi as dl,cj as Vt,_ as ee,f as et,aP as Ve,aK as ge,cZ as tt,y as Re,c_ as lt,H as ue,cY as at,fj as Rt,fk as ml,aD as ot,aG as zt,fl as vl,aJ as hl,ep as fl,dB as pl,l as gl,m as ze,n as bl,eg as wl,ah as rt,cV as xl,e as nt,s as _l,v as yl,P as jt,G as At,ck as kl,fm as Cl,fn as $l,c1 as Pl,J as de,a6 as je,fo as Il,dE as Tl,aI as Ml,fp as Sl,Z as Ll,fq as Vl,O as Ae}from"./index-CRsFgzy0.js";import{c as Rl,g as zl,f as jl}from"./tree-COGD3qag.js";import{u as Oe}from"./tagsView-BnrVTrUo.js";import{u as Al}from"./useTagsView-CtpfzI81.js";import{_ as Ol}from"./logo-DQEDlIK-.js";import{_ as Bl}from"./XButton-BAqgoI8I.js";import{a as Be}from"./avatar-BG6NdH5s.js";import{f as Fl}from"./formatTime-DhdtkSIS.js";import{c as ql,d as El}from"./index-BXWQayLD.js";import{E as Dl}from"./el-avatar-Nl9DW69B.js";import{_ as Hl}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Ul}from"./LocaleDropdown.vue_vue_type_script_setup_true_lang-BQ73QW5r.js";const Wl={visibilityHeight:{type:Number,default:200},target:{type:String,default:""},right:{type:Number,default:40},bottom:{type:Number,default:40}},Nl={click:c=>c instanceof MouseEvent},Ot="ElBacktop",Zl=R({name:Ot}),Gl=Tt(Ye(R({...Zl,props:Wl,emits:Nl,setup(c,{emit:s}){const o=c,l=Ke("backtop"),{handleClick:a,visible:n}=((u,h,b)=>{const f=$t(),m=$t(),v=S(!1),k=()=>{f.value&&(v.value=f.value.scrollTop>=u.visibilityHeight)},x=Qt(k,300,!0);return Kt(m,"scroll",x),ye(()=>{var i;m.value=document,f.value=document.documentElement,u.target&&(f.value=(i=document.querySelector(u.target))!=null?i:void 0,f.value||el(b,`target does not exist: ${u.target}`),m.value=f.value),k()}),{visible:v,handleClick:i=>{var C;(C=f.value)==null||C.scrollTo({top:0,behavior:"smooth"}),h("click",i)}}})(o,s,Ot),r=d(()=>({right:`${o.right}px`,bottom:`${o.bottom}px`}));return(u,h)=>(_(),F(Qe,{name:`${e(l).namespace.value}-fade-in`},{default:w(()=>[e(n)?(_(),B("div",{key:0,style:tl(e(r)),class:P(e(l).b()),onClick:Pt(e(a),["stop"])},[Me(u.$slots,"default",{},()=>[t(e(It),{class:P(e(l).e("icon"))},{default:w(()=>[t(e(ll))]),_:1},8,["class"])])],14,["onClick"])):se("v-if",!0)]),_:3},8,["name"]))}}),[["__file","backtop.vue"]])),Bt=Symbol("breadcrumbKey"),Jl=Mt({separator:{type:String,default:"/"},separatorIcon:{type:al}}),Xl=R({name:"ElBreadcrumb"}),Yl=R({...Xl,props:Jl,setup(c){const s=c,{t:o}=ol(),l=Ke("breadcrumb"),a=S();return St(Bt,s),ye(()=>{const n=a.value.querySelectorAll(`.${l.e("item")}`);n.length&&n[n.length-1].setAttribute("aria-current","page")}),(n,r)=>(_(),B("div",{ref_key:"breadcrumb",ref:a,class:P(e(l).b()),"aria-label":e(o)("el.breadcrumb.label"),role:"navigation"},[Me(n.$slots,"default")],10,["aria-label"]))}});var Kl=Ye(Yl,[["__file","breadcrumb.vue"]]);const Ql=Mt({to:{type:rl([String,Object]),default:""},replace:Boolean}),ea=R({name:"ElBreadcrumbItem"});var Ft=Ye(R({...ea,props:Ql,setup(c){const s=c,o=sl(),l=nl(Bt,void 0),a=Ke("breadcrumb"),n=o.appContext.config.globalProperties.$router,r=S(),u=()=>{s.to&&n&&(s.replace?n.replace(s.to):n.push(s.to))};return(h,b)=>{var f,m;return _(),B("span",{class:P(e(a).e("item"))},[g("span",{ref_key:"link",ref:r,class:P([e(a).e("inner"),e(a).is("link",!!h.to)]),role:"link",onClick:u},[Me(h.$slots,"default")],2),(f=e(l))!=null&&f.separatorIcon?(_(),F(e(It),{key:0,class:P(e(a).e("separator"))},{default:w(()=>[(_(),F(Lt(e(l).separatorIcon)))]),_:1},8,["class"])):(_(),B("span",{key:1,class:P(e(a).e("separator")),role:"presentation"},I((m=e(l))==null?void 0:m.separator),3))],2)}}}),[["__file","breadcrumb-item.vue"]]);const ta=Tt(Kl,{BreadcrumbItem:Ft}),la=il(Ft),aa=R({name:"BackTop",__name:"Backtop",setup(c){const{getPrefixCls:s,variables:o}=q(),l=s("backtop");return(a,n)=>(_(),F(e(Gl),{class:P(`${e(l)}-backtop`),target:`.${e(o).namespace}-layout-content-scrollbar .${e(o).elNamespace}-scrollbar__wrap`},null,8,["class","target"]))}}),oa=(c,s)=>(Rl(c,o=>o.path===s)||[]).map(o=>o.path),{renderMenuTitle:qt}={renderMenuTitle:c=>{const{t:s}=Q(),{title:o="Please set title",icon:l}=c;return l?t(Y,null,[t(Z,{icon:c.icon},null),t("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[s(o)])]):t("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[s(o)])}},ra=()=>{const c=(s,o="/")=>s.filter(l=>{var a;return!((a=l.meta)!=null&&a.hidden)}).map(l=>{const a=l.meta??{},{oneShowingChild:n,onlyOneChild:r}=((h=[],b)=>{const f=S(),m=h.filter(v=>!(v.meta??{}).hidden&&(f.value=v,!0));return m.length===1?{oneShowingChild:!0,onlyOneChild:e(f)}:m.length?{oneShowingChild:!1,onlyOneChild:e(f)}:(f.value={...b,path:"",noShowingChildren:!0},{oneShowingChild:!0,onlyOneChild:e(f)})})(l.children,l),u=Se(l.path)?l.path:ie(o,l.path);return!n||r!=null&&r.children&&!(r!=null&&r.noShowingChildren)||a!=null&&a.alwaysShow?t(ul,{index:u},{title:()=>qt(a),default:()=>c(l.children,u)}):t(cl,{index:r?ie(u,r.path):u},{default:()=>qt(r?r==null?void 0:r.meta:a)})});return{renderMenuItem:c}},{getPrefixCls:na}=q(),ke=na("menu"),sa=R({name:"Menu",props:{menuSelect:{type:Function,default:void 0}},setup(c){const s=G(),o=d(()=>s.getLayout),{push:l,currentRoute:a}=fe(),n=Le(),r=d(()=>["classic","topLeft","cutMenu"].includes(e(o))?"vertical":"horizontal"),u=d(()=>e(o)==="cutMenu"?n.getMenuTabRouters:n.getRouters),h=d(()=>s.getCollapse),b=d(()=>s.getUniqueOpened),f=d(()=>{const{meta:x,path:i}=e(a);return x.activeMenu?x.activeMenu:i}),m=x=>{c.menuSelect&&c.menuSelect(x),Se(x)?window.open(x):l(x)},v=()=>{if(e(o)==="top")return k();{let i;return t(ce,null,typeof(x=i=k())=="function"||Object.prototype.toString.call(x)==="[object Object]"&&!Vt(x)?i:{default:()=>[i]})}var x},k=()=>t(dl,{defaultActive:e(f),mode:e(r),collapse:e(o)!=="top"&&e(o)!=="cutMenu"&&e(h),uniqueOpened:e(o)!=="top"&&e(b),backgroundColor:"var(--left-menu-bg-color)",textColor:"var(--left-menu-text-color)",activeTextColor:"var(--left-menu-text-active-color)",popperClass:e(r)==="vertical"?`${ke}-popper--vertical`:`${ke}-popper--horizontal`,onSelect:m},{default:()=>{const{renderMenuItem:x}=ra(e(r));return x(e(u))}});return()=>t("div",{id:ke,class:[`${ke} ${ke}__${e(r)}`,"h-[100%] overflow-hidden flex-col bg-[var(--left-menu-bg-color)]",{"w-[var(--left-menu-min-width)]":e(h)&&e(o)!=="cutMenu","w-[var(--left-menu-max-width)]":!e(h)&&e(o)!=="cutMenu"}]},[v()])}}),Fe=ee(sa,[["__scopeId","data-v-d886b223"]]),qe=et({}),Et=(c,s)=>{const o=[];for(const l of c){let a=null;const n=l.meta??{};if(!n.hidden||n.canTo){const r=oa(s,l.path),u=Se(l.path)?l.path:r.join("/");a=Ve(l),a.path=u,l.children&&a&&(a.children=Et(l.children,s)),a&&o.push(a),r.length&&Reflect.has(qe,r[0])&&qe[r[0]].push(u)}}return o},{getPrefixCls:ia,variables:ca}=q(),st=ia("tab-menu"),ua=R({name:"TabMenu",setup(){const{push:c,currentRoute:s}=fe(),{t:o}=Q(),l=G(),a=d(()=>l.getCollapse),n=d(()=>l.getFixedMenu),r=Le(),u=d(()=>r.getRouters),h=d(()=>e(u).filter(i=>{var C;return!((C=i==null?void 0:i.meta)!=null&&C.hidden)})),b=()=>{l.setCollapse(!e(a))};ye(()=>{var i;if(e(n)){const C=`/${e(s).path.split("/")[1]}`,y=(i=e(h).find($=>{var j,T,L;return(((j=$.meta)==null?void 0:j.alwaysShow)||((T=$==null?void 0:$.children)==null?void 0:T.length)&&((L=$==null?void 0:$.children)==null?void 0:L.length)>1)&&$.path===C}))==null?void 0:i.children;v.value=C,y&&r.setMenuTabRouters(Ve(y).map($=>($.path=ie(e(v),$.path),$)))}}),ge(()=>u.value,i=>{(C=>{for(const y of C){const $=y.meta??{};$!=null&&$.hidden||(qe[y.path]=[])}})(i),Et(i,i)},{immediate:!0,deep:!0});const f=S(!0);ge(()=>a.value,i=>{i?f.value=!i:setTimeout(()=>{f.value=!i},200)});const m=S(!!e(n)),v=S(""),k=i=>{const{path:C}=e(s);return!!qe[i].includes(C)},x=()=>{e(m)&&!e(n)&&(m.value=!1)};return()=>t("div",{id:`${ca.namespace}-menu`,class:[st,"relative bg-[var(--left-menu-bg-color)] layout-border__right",{"w-[var(--tab-menu-max-width)]":!e(a),"w-[var(--tab-menu-min-width)]":e(a)}],onMouseleave:x},[t(ce,{class:"!h-[calc(100%-var(--tab-menu-collapse-height))]"},{default:()=>[t("div",null,{default:()=>e(h).map(i=>{var y,$,j,T,L,U;const C=(y=i.meta)!=null&&y.alwaysShow||($=i==null?void 0:i.children)!=null&&$.length&&((j=i==null?void 0:i.children)==null?void 0:j.length)>1?i:{...(i==null?void 0:i.children)&&(i==null?void 0:i.children[0]),path:ie(i.path,(T=(i==null?void 0:i.children)&&(i==null?void 0:i.children[0]))==null?void 0:T.path)};return t("div",{class:[`${st}__item`,"text-center text-12px relative py-12px cursor-pointer",{"is-active":k(i.path)}],onClick:()=>{(V=>{if(Se(V.path))return void window.open(V.path);const z=V.children?V.path:V.path.split("/")[0],A=e(v);v.value=V.children?V.path:V.path.split("/")[0],V.children?(z!==A&&e(m)||(m.value=!!e(n)||!e(m)),e(m)&&r.setMenuTabRouters(Ve(V.children).map(J=>(J.path=ie(e(v),J.path),J)))):(c(V.path),r.setMenuTabRouters([]),m.value=!1)})(C)}},[t("div",null,[t(Z,{icon:(L=C==null?void 0:C.meta)==null?void 0:L.icon},null)]),e(f)?t("p",{class:"mt-5px break-words px-2px"},[o((U=C.meta)==null?void 0:U.title)]):void 0])})})]}),t("div",{class:[`${st}--collapse`,"text-center h-[var(--tab-menu-collapse-height)] leading-[var(--tab-menu-collapse-height)] cursor-pointer"],onClick:b},[t(Z,{icon:e(a)?"ep:d-arrow-right":"ep:d-arrow-left"},null)]),t(Fe,{class:["!absolute top-0 z-11",{"!left-[var(--tab-menu-min-width)]":e(a),"!left-[var(--tab-menu-max-width)]":!e(a),"!w-[var(--left-menu-max-width)]":e(m)||e(n),"!w-0":!e(m)&&!e(n)}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null)])}}),da=ee(ua,[["__scopeId","data-v-73123a16"]]),Dt=(c,s="")=>{let o=[];return c.forEach(l=>{const a=l.meta,n=ie(s,l.path);if(a!=null&&a.affix&&o.push({...l,path:n,fullPath:n}),l.children){const r=Dt(l.children,n);r.length>=1&&(o=[...o,...r])}}),o},Ht=R({name:"ContextMenu",__name:"ContextMenu",props:{schema:{type:Array,default:()=>[]},trigger:{type:String,default:"contextmenu"},tagItem:{type:Object,default:()=>({})}},emits:["visibleChange"],setup(c,{expose:s,emit:o}){const{getPrefixCls:l}=q(),a=l("context-menu"),{t:n}=Q(),r=o,u=c,h=m=>{m.command&&m.command(m)},b=m=>{r("visibleChange",m,u.tagItem)},f=S();return s({elDropdownMenuRef:f,tagItem:u.tagItem}),(m,v)=>{const k=Z,x=lt,i=tt,C=at;return _(),F(C,{ref_key:"elDropdownMenuRef",ref:f,class:P(e(a)),trigger:c.trigger,placement:"bottom-start","popper-class":"v-context-menu-popper",onCommand:h,onVisibleChange:b},{dropdown:w(()=>[t(i,null,{default:w(()=>[(_(!0),B(Y,null,Re(c.schema,(y,$)=>(_(),F(x,{key:`dropdown${$}`,command:y,disabled:y.disabled,divided:y.divided},{default:w(()=>[t(k,{icon:y.icon},null,8,["icon"]),ue(" "+I(e(n)(y.label)),1)]),_:2},1032,["command","disabled","divided"]))),128))]),_:1})]),default:w(()=>[Me(m.$slots,"default")]),_:3},8,["class","trigger"])}}});function Ce({el:c,position:s="scrollLeft",to:o,duration:l=500,callback:a}){const n=S(!1),r=c[s],u=o-r;let h=0;function b(){if(!e(n))return;h+=20;const f=((m,v,k,x)=>(m/=x/2)<1?k/2*m*m+v:-k/2*(--m*(m-2)-1)+v)(h,r,u,l);((m,v,k)=>{m[v]=k})(c,s,f),h<l&&e(n)?requestAnimationFrame(b):a&&a()}return{start:function(){n.value=!0,b()},stop:function(){n.value=!1}}}const ma=["id"],va={class:"flex-1 overflow-hidden"},ha={class:"h-[var(--tags-view-height)] flex"},fa=["onClick"],pa=R({name:"TagsView",__name:"TagsView",setup(c){const{getPrefixCls:s}=q(),o=s("tags-view"),{t:l}=Q(),{currentRoute:a,push:n}=fe(),{closeAll:r,closeLeft:u,closeRight:h,closeOther:b,closeCurrent:f,refreshPage:m}=Al(),v=Le(),k=d(()=>v.getRouters),x=Oe(),i=d(()=>x.getVisitedViews),C=S([]),y=d(()=>x.getSelectedTag),$=x.setSelectedTag,j=G(),T=d(()=>j.getTagsViewImmerse),L=d(()=>j.getTagsViewIcon),U=d(()=>j.getIsDark),V=()=>{const{name:M}=e(a);M&&($(e(a)),x.addView(e(a)))},z=M=>{f(M,()=>{ct(M)&&A()})},A=()=>{const M=x.getVisitedViews.slice(-1)[0];if(M)n(M);else{if(e(a).path===v.getAddRouters[0].path||e(a).path===v.getAddRouters[0].redirect)return void V();n(v.getAddRouters[0].path)}},J=()=>{r(()=>{A()})},le=()=>{b()},X=async M=>{m(M)},W=()=>{u()},pe=()=>{h()},Pe=Rt(),Je=M=>{var be;const O=(be=e(Xe))==null?void 0:be.wrapRef;let D=null,N=null;const ne=e(Pe);if(ne.length>0&&(D=ne[0],N=ne[ne.length-1]),(D==null?void 0:D.to).fullPath===M.fullPath){const{start:ae}=Ce({el:O,position:"scrollLeft",to:0,duration:500});ae()}else if((N==null?void 0:N.to).fullPath===M.fullPath){const{start:ae}=Ce({el:O,position:"scrollLeft",to:O.scrollWidth-O.offsetWidth,duration:500});ae()}else{const ae=ne.findIndex(K=>(K==null?void 0:K.to).fullPath===M.fullPath),we=document.getElementsByClassName(`${o}__item`),Te=we[ae-1],xe=we[ae+1],p=xe.offsetLeft+xe.offsetWidth+4,_e=Te.offsetLeft-4;if(p>e(Ie)+O.offsetWidth){const{start:K}=Ce({el:O,position:"scrollLeft",to:p-O.offsetWidth,duration:500});K()}else if(_e<e(Ie)){const{start:K}=Ce({el:O,position:"scrollLeft",to:_e,duration:500});K()}}},ct=M=>M.fullPath===e(a).fullPath,ut=Rt(),Jt=(M,O)=>{if(M)for(const D of e(ut)){const N=D.elDropdownMenuRef;O.fullPath!==D.tagItem.fullPath&&(N==null||N.handleClose(),$(O))}},Xe=S(),Ie=S(0),Xt=({scrollLeft:M})=>{Ie.value=M},dt=M=>{var N;const O=(N=e(Xe))==null?void 0:N.wrapRef,{start:D}=Ce({el:O,position:"scrollLeft",to:e(Ie)+M,duration:500});D()};return ml(()=>{(()=>{C.value=Dt(e(k));for(const M of e(C))M.name&&x.addVisitedView(Ve(M))})(),V()}),ge(()=>a.value,()=>{V(),(async()=>{await zt();for(const M of e(i))if(M.fullPath===e(a).fullPath){Je(M);break}})()}),(M,O)=>{var ne,be,ae,we,Te,xe;const D=Z,N=ot("router-link");return _(),B("div",{id:e(o),class:P([e(o),"relative w-full flex bg-[#fff] dark:bg-[var(--el-bg-color)]"])},[g("span",{class:P([T.value?"":`${e(o)}__tool ${e(o)}__tool--first`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[0]||(O[0]=p=>dt(-200))},[t(D,{"hover-color":U.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:d-arrow-left"},null,8,["hover-color"])],2),g("div",va,[t(e(ce),{ref_key:"scrollbarRef",ref:Xe,class:"h-full",onScroll:Xt},{default:w(()=>[g("div",ha,[(_(!0),B(Y,null,Re(i.value,p=>{var _e,K,mt,vt,ht,ft,pt,gt,bt;return _(),F(e(Ht),{key:p.fullPath,ref_for:!0,ref:e(ut).set,class:P([`${e(o)}__item`,T.value?`${e(o)}__item--immerse`:"",L.value?`${e(o)}__item--icon`:"",T.value&&L.value?`${e(o)}__item--immerse--icon`:"",(_e=p==null?void 0:p.meta)!=null&&_e.affix?`${e(o)}__item--affix`:"",{"is-active":ct(p)}]),schema:[{icon:"ep:refresh",label:e(l)("common.reload"),disabled:((K=y.value)==null?void 0:K.fullPath)!==p.fullPath,command:()=>{X(p)}},{icon:"ep:close",label:e(l)("common.closeTab"),disabled:!!((mt=i.value)!=null&&mt.length)&&((vt=y.value)==null?void 0:vt.meta.affix),command:()=>{z(p)}},{divided:!0,icon:"ep:d-arrow-left",label:e(l)("common.closeTheLeftTab"),disabled:!!((ht=i.value)!=null&&ht.length)&&(p.fullPath===i.value[0].fullPath||((ft=y.value)==null?void 0:ft.fullPath)!==p.fullPath),command:()=>{W()}},{icon:"ep:d-arrow-right",label:e(l)("common.closeTheRightTab"),disabled:!!((pt=i.value)!=null&&pt.length)&&(p.fullPath===i.value[i.value.length-1].fullPath||((gt=y.value)==null?void 0:gt.fullPath)!==p.fullPath),command:()=>{pe()}},{divided:!0,icon:"ep:discount",label:e(l)("common.closeOther"),disabled:((bt=y.value)==null?void 0:bt.fullPath)!==p.fullPath,command:()=>{le()}},{icon:"ep:minus",label:e(l)("common.closeAll"),command:()=>{J()}}],"tag-item":p,onVisibleChange:Jt},{default:w(()=>[g("div",null,[t(N,{ref_for:!0,ref:e(Pe).set,to:{...p},custom:""},{default:w(({navigate:Yt})=>{var wt,xt,_t,yt,kt,Ct;return[g("div",{class:P(`h-full flex items-center justify-center whitespace-nowrap pl-15px ${e(o)}__item--label`),onClick:Yt},[L.value&&((wt=p==null?void 0:p.meta)!=null&&wt.icon||p!=null&&p.matched&&p.matched[0]&&((xt=p.matched[p.matched.length-1].meta)!=null&&xt.icon))?(_(),F(D,{key:0,icon:((_t=p==null?void 0:p.meta)==null?void 0:_t.icon)||p.matched[p.matched.length-1].meta.icon,size:12,class:"mr-5px"},null,8,["icon"])):se("",!0),ue(" "+I(e(l)((yt=p==null?void 0:p.meta)==null?void 0:yt.title)+((kt=p==null?void 0:p.meta)!=null&&kt.titleSuffix?` (${(Ct=p==null?void 0:p.meta)==null?void 0:Ct.titleSuffix})`:""))+" ",1),t(D,{class:P(`${e(o)}__item--close`),size:12,color:"#333",icon:"ep:close",onClick:Pt(xo=>z(p),["prevent","stop"])},null,8,["class","onClick"])],10,fa)]}),_:2},1032,["to"])])]),_:2},1032,["class","schema","tag-item"])}),128))])]),_:1},512)]),g("span",{class:P([T.value?"":`${e(o)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[1]||(O[1]=p=>dt(200))},[t(D,{"hover-color":U.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:d-arrow-right"},null,8,["hover-color"])],2),g("span",{class:P([T.value?"":`${e(o)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[2]||(O[2]=p=>X(y.value))},[t(D,{"hover-color":U.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:refresh-right"},null,8,["hover-color"])],2),t(e(Ht),{schema:[{icon:"ep:refresh",label:e(l)("common.reload"),command:()=>{X(y.value)}},{icon:"ep:close",label:e(l)("common.closeTab"),disabled:!!((ne=i.value)!=null&&ne.length)&&((be=y.value)==null?void 0:be.meta.affix),command:()=>{z(y.value)}},{divided:!0,icon:"ep:d-arrow-left",label:e(l)("common.closeTheLeftTab"),disabled:!!((ae=i.value)!=null&&ae.length)&&((we=y.value)==null?void 0:we.fullPath)===i.value[0].fullPath,command:()=>{W()}},{icon:"ep:d-arrow-right",label:e(l)("common.closeTheRightTab"),disabled:!!((Te=i.value)!=null&&Te.length)&&((xe=y.value)==null?void 0:xe.fullPath)===i.value[i.value.length-1].fullPath,command:()=>{pe()}},{divided:!0,icon:"ep:discount",label:e(l)("common.closeOther"),command:()=>{le()}},{icon:"ep:minus",label:e(l)("common.closeAll"),command:()=>{J()}}],trigger:"click"},{default:w(()=>[g("span",{class:P([T.value?"":`${e(o)}__tool`,"block h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"])},[t(D,{"hover-color":U.value?"#fff":"var(--el-color-black)",color:"var(--el-text-color-placeholder)",icon:"ep:menu"},null,8,["hover-color"])],2)]),_:1},8,["schema"])],10,ma)}}}),Ee=ee(pa,[["__scopeId","data-v-3858c8e4"]]),De=R({name:"Logo",__name:"Logo",setup(c){const{getPrefixCls:s}=q(),o=s("logo"),l=G(),a=S(!0),n=d(()=>l.getTitle),r=d(()=>l.getLayout),u=d(()=>l.getCollapse);return ye(()=>{e(u)&&(a.value=!1)}),ge(()=>u.value,h=>{e(r)!=="topLeft"&&e(r)!=="cutMenu"?h?a.value=!h:setTimeout(()=>{a.value=!h},400):a.value=!0}),ge(()=>r.value,h=>{h==="top"||h==="cutMenu"?a.value=!0:e(u)?a.value=!1:a.value=!0}),(h,b)=>{const f=ot("router-link");return _(),B("div",null,[t(f,{class:P([e(o),r.value!=="classic"?`${e(o)}__Top`:"","flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative decoration-none overflow-hidden"]),to:"/"},{default:w(()=>[b[0]||(b[0]=g("img",{class:"h-[calc(var(--logo-height)-10px)] w-[calc(var(--logo-height)-10px)]",src:Ol},null,-1)),a.value?(_(),B("div",{key:0,class:P(["ml-10px text-16px font-700",{"text-[var(--logo-title-text-color)]":r.value==="classic","text-[var(--top-header-text-color)]":r.value==="topLeft"||r.value==="top"||r.value==="cutMenu"}])},I(n.value),3)):se("",!0)]),_:1},8,["class"])])}}}),ga={class:"text-14px"},ba=R({name:"Footer",__name:"Footer",setup(c){const{getPrefixCls:s}=q(),o=s("footer"),l=G(),a=d(()=>l.getTitle),n=d(()=>new Date().getFullYear());return(r,u)=>(_(),B("div",{class:P([e(o),"h-[var(--app-footer-height)] bg-[var(--app-content-bg-color)] text-center leading-[var(--app-footer-height)] text-[var(--el-text-color-placeholder)] dark:bg-[var(--el-bg-color)] overflow-hidden"])},[g("span",ga,"Copyright \xA9"+I(e(n))+" "+I(e(a)),1)],2))}}),He=R({name:"AppView",__name:"AppView",setup(c){const s=G();d(()=>s.getLayout),d(()=>s.getFixedHeader);const o=d(()=>s.getFooter),l=Oe(),a=d(()=>l.getCachedViews);d(()=>s.getTagsView);const n=S(!0);return St("reload",()=>{n.value=!1,zt(()=>n.value=!0)}),(r,u)=>{const h=ot("router-view");return _(),B(Y,null,[g("section",{class:P(["p-[var(--app-content-padding)] w-full bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]",{"!min-h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-footer-height))] pb-0":e(o)}])},[e(n)?(_(),F(h,{key:0},{default:w(({Component:b,route:f})=>[(_(),F(vl,{include:e(a)},[(_(),F(Lt(b),{key:f.fullPath}))],1032,["include"]))]),_:1})):se("",!0)],2),e(o)?(_(),F(e(ba),{key:0})):se("",!0)],64)}}}),wa={class:"message"},xa={class:"message-content"},_a={class:"message-title"},ya={class:"message-date"},ka={style:{"margin-top":"10px","text-align":"right"}},Ut="/favicon.ico",Ca=R({name:"Message",__name:"Message",setup(c){const{push:s}=fe(),o=hl(),l=S("notice"),a=S(0),n=S([]),r=document.title;let u=null,h=null;const b=async()=>{n.value=await ql(),a.value=0},f=async()=>{El().then(v=>{a.value=v})},m=()=>{s({name:"MyNotifyMessage"})};return ye(()=>{f(),setInterval(()=>{o.getIsSetUser?(f(),a.value>0&&(fl({title:"\u65B0\u4F1A\u8BDD\u63D0\u9192",message:'\u60A8\u6709\u65B0\u8F6C\u63A5\u7684\u4F1A\u8BDD\u672A\u5904\u7406\uFF0C\u8BF7\u53CA\u65F6<a href="/agent/im" style="color: #409EFF; text-decoration: underline; cursor: pointer;">\u524D\u5F80\u5BA2\u670D\u4E2D\u5FC3-\u6211\u7684\u4F1A\u8BDD</a>',dangerouslyUseHTMLString:!0,type:"warning",duration:0,onClose:()=>{b()}}),document.visibilityState==="hidden"&&((v=>{u||(u=setInterval(()=>{document.title=document.title===v?r:v},1e3))})("\u3010\u6709\u65B0\u4F1A\u8BDD\u3011"),(()=>{if(h)return;const v=document.querySelector("link[rel~='icon']");v&&(h=setInterval(()=>{const k=new Date().getTime();v.href=v.href.includes(Ut)?`/message.ico?t=${k}`:`${Ut}?t=${k}`},1e3))})()))):a.value=0},6e4)}),(v,k)=>{const x=Z,i=wl,C=ce,y=bl,$=gl,j=Bl,T=pl;return _(),B("div",wa,[t(T,{width:400,placement:"bottom",trigger:"click"},{reference:w(()=>[t(i,{"is-dot":e(a)>0,class:"item"},{default:w(()=>[t(x,{size:18,class:"cursor-pointer",icon:"ep:bell",onClick:b})]),_:1},8,["is-dot"])]),default:w(()=>[t($,{modelValue:e(l),"onUpdate:modelValue":k[0]||(k[0]=L=>ze(l)?l.value=L:null)},{default:w(()=>[t(y,{label:"\u6211\u7684\u7AD9\u5185\u4FE1",name:"notice"},{default:w(()=>[t(C,{class:"message-list"},{default:w(()=>[(_(!0),B(Y,null,Re(e(n),L=>(_(),B("div",{key:L.id,class:"message-item"},[k[1]||(k[1]=g("img",{alt:"",class:"message-icon",src:Be},null,-1)),g("div",xa,[g("span",_a,I(L.templateNickname)+"\uFF1A"+I(L.templateContent),1),g("span",ya,I(e(Fl)(L.createTime)),1)])]))),128))]),_:1})]),_:1})]),_:1},8,["modelValue"]),g("div",ka,[t(j,{preIcon:"ep:view",title:"\u67E5\u770B\u5168\u90E8",type:"primary",onClick:m})])]),_:1})])}}}),$a=ee(Ca,[["__scopeId","data-v-19107c7f"]]),Pa=R({name:"Collapse",__name:"Collapse",props:{color:rt.string.def("")},setup(c){const{getPrefixCls:s}=q(),o=s("collapse"),l=G(),a=d(()=>l.getCollapse),n=()=>{const r=e(a);l.setCollapse(!r)};return(r,u)=>{const h=Z;return _(),B("div",{class:P(e(o)),onClick:n},[t(h,{color:c.color,icon:e(a)?"ep:expand":"ep:fold",size:18,class:"cursor-pointer"},null,8,["color","icon"])],2)}}}),{t:Ue}=Q(),Ia=()=>({required:c=>({required:!0,message:c||Ue("common.required")}),lengthRange:c=>{const{min:s,max:o,message:l}=c;return{min:s,max:o,message:l||Ue("common.lengthRange",{min:s,max:o})}},notSpace:c=>({validator:(s,o,l)=>{(o==null?void 0:o.indexOf(" "))!==-1?l(new Error(c||Ue("common.notSpace"))):l()}}),notSpecialCharacters:c=>({validator:(s,o,l)=>{/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi.test(o)?l(new Error(c||Ue("common.notSpecialCharacters"))):l()}})}),it=xl("lock",{state:()=>({lockInfo:{}}),getters:{getLockInfo(){return this.lockInfo}},actions:{setLockInfo(c){this.lockInfo=c},resetLockInfo(){this.lockInfo={}},unLock(c){var s;return((s=this.lockInfo)==null?void 0:s.password)===c&&(this.resetLockInfo(),!0)}},persist:!0}),Ta={class:"flex flex-col items-center"},Ma=["src"],Sa={class:"text-14px my-10px text-[var(--top-header-text-color)]"},La=R({__name:"LockDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(c,{emit:s}){const{getPrefixCls:o}=q(),l=o("lock-dialog"),{required:a}=Ia(),{t:n}=Q(),r=it(),u=c,h=nt(),b=d(()=>h.user.avatar||Be),f=d(()=>h.user.nickname??"Admin"),m=s,v=d({get:()=>u.modelValue,set:$=>{m("update:modelValue",$)}}),k=S(n("lock.lockScreen")),x=S({password:void 0}),i=et({password:[a()]}),C=S(),y=async()=>{C&&await C.value.validate()&&(v.value=!1,r.setLockInfo({...x.value,isLock:!0}))};return($,j)=>{const T=jt,L=yl,U=_l,V=At,z=Hl;return _(),F(z,{modelValue:e(v),"onUpdate:modelValue":j[1]||(j[1]=A=>ze(v)?v.value=A:null),width:"500px","max-height":"170px",class:P(e(l)),title:e(k)},{footer:w(()=>[t(V,{type:"primary",onClick:y},{default:w(()=>[ue(I(e(n)("lock.lock")),1)]),_:1})]),default:w(()=>[g("div",Ta,[g("img",{src:e(b),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,Ma),g("span",Sa,I(e(f)),1)]),t(U,{ref_key:"formRef",ref:C,model:e(x),rules:e(i),"label-width":"80px"},{default:w(()=>[t(L,{label:e(n)("lock.lockPassword"),prop:"password"},{default:w(()=>[t(T,{type:"password",modelValue:e(x).password,"onUpdate:modelValue":j[0]||(j[0]=A=>e(x).password=A),placeholder:"\u8BF7\u8F93\u5165"+e(n)("lock.lockPassword"),clearable:"","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","class","title"])}}}),Va=ee(La,[["__scopeId","data-v-e7f7cd6a"]]),Ra=kl,za={class:"flex w-screen h-screen justify-center items-center"},ja={class:"flex flex-col items-center"},Aa=["src"],Oa={class:"text-14px my-10px text-[var(--logo-title-text-color)]"},Ba={class:"absolute bottom-5 w-full text-gray-300 xl:text-xl 2xl:text-3xl text-center enter-y"},Fa={class:"text-5xl mb-4 enter-x"},qa={class:"text-3xl"},Ea={class:"text-2xl"},Da=R({__name:"LockPage",setup(c){const s=Oe(),{replace:o}=fe(),l=nt(),a=S(""),n=S(!1),r=S(!1),u=S(!0),{getPrefixCls:h}=q(),b=h("lock-page"),f=d(()=>l.user.avatar||Be),m=d(()=>l.user.nickname??"Admin"),v=it(),{hour:k,month:x,minute:i,meridiem:C,year:y,day:$,week:j}=((V=!0)=>{let z;const A=et({year:0,month:0,week:"",day:0,hour:"",minute:"",second:0,meridiem:""}),J=()=>{const W=Ra(),pe=W.format("HH"),Pe=W.format("mm"),Je=W.get("s");A.year=W.get("y"),A.month=W.get("M")+1,A.week="\u661F\u671F"+["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"][W.day()],A.day=W.get("date"),A.hour=pe,A.minute=Pe,A.second=Je,A.meridiem=W.format("A")};function le(){J(),clearInterval(z),z=setInterval(()=>J(),1e3)}function X(){clearInterval(z)}return Cl(()=>{V&&le()}),$l(()=>{X()}),{...Pl(A),start:le,stop:X}})(!0),{t:T}=Q();async function L(){await l.loginOut().catch(()=>{}),Il(),s.delAllViews(),v.resetLockInfo(),o("/login")}function U(V=!1){u.value=V}return(V,z)=>{const A=Z,J=jt,le=At;return _(),B("div",{class:P([e(b),"fixed inset-0 flex h-screen w-screen bg-black items-center justify-center"])},[de(g("div",{class:P([`${e(b)}__unlock`,"absolute top-0 left-1/2 flex pt-5 h-16 items-center justify-center sm:text-md xl:text-xl text-white flex-col cursor-pointer transform translate-x-1/2"]),onClick:z[0]||(z[0]=X=>U(!1))},[t(A,{icon:"ep:lock"}),g("span",null,I(e(T)("lock.unlock")),1)],2),[[je,e(u)]]),g("div",za,[g("div",{class:P([`${e(b)}__hour`,"relative mr-5 md:mr-20 w-2/5 h-2/5 md:h-4/5"])},[g("span",null,I(e(k)),1),de(g("span",{class:"meridiem absolute left-5 top-5 text-md xl:text-xl"},I(e(C)),513),[[je,e(u)]])],2),g("div",{class:P(`${e(b)}__minute w-2/5 h-2/5 md:h-4/5 `)},[g("span",null,I(e(i)),1)],2)]),t(Qe,{name:"fade-slide"},{default:w(()=>[de(g("div",{class:P(`${e(b)}-entry`)},[g("div",{class:P(`${e(b)}-entry-content`)},[g("div",ja,[g("img",{src:e(f),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,Aa),g("span",Oa,I(e(m)),1)]),t(J,{type:"password",placeholder:e(T)("lock.placeholder"),class:"enter-x",modelValue:e(a),"onUpdate:modelValue":z[1]||(z[1]=X=>ze(a)?a.value=X:null)},null,8,["placeholder","modelValue"]),e(r)?(_(),B("span",{key:0,class:P(`text-14px ${e(b)}-entry__err-msg enter-x`)},I(e(T)("lock.message")),3)):se("",!0),g("div",{class:P(`${e(b)}-entry__footer enter-x`)},[t(le,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:e(n),onClick:z[2]||(z[2]=X=>U(!0))},{default:w(()=>[ue(I(e(T)("common.back")),1)]),_:1},8,["disabled"]),t(le,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:e(n),onClick:L},{default:w(()=>[ue(I(e(T)("lock.backToLogin")),1)]),_:1},8,["disabled"]),t(le,{type:"primary",class:"mt-2",size:"small",link:"",onClick:z[3]||(z[3]=X=>async function(){if(!a.value)return;let W=a.value;try{n.value=!0;const pe=await v.unLock(W);r.value=!pe}finally{n.value=!1}}()),disabled:e(n)},{default:w(()=>[ue(I(e(T)("lock.entrySystem")),1)]),_:1},8,["disabled"])],2)],2)],2),[[je,!e(u)]])]),_:1}),g("div",Ba,[de(g("div",Fa,[ue(I(e(k))+":"+I(e(i))+" ",1),g("span",qa,I(e(C)),1)],512),[[je,!e(u)]]),g("div",Ea,I(e(y))+"/"+I(e(x))+"/"+I(e($))+" "+I(e(j)),1)])],2)}}}),Ha=ee(Da,[["__scopeId","data-v-3e502faf"]]),Ua={class:"flex items-center"},Wa={class:"pl-[5px] text-14px text-[var(--top-header-text-color)] <lg:hidden"},Na=R({name:"UserInfo",__name:"UserInfo",setup(c){const{t:s}=Q(),{push:o,replace:l}=fe(),a=nt(),n=Oe(),{getPrefixCls:r}=q(),u=r("user-info"),h=d(()=>a.user.avatar||Be),b=d(()=>a.user.nickname??"Admin"),f=it(),m=d(()=>{var y;return((y=f.getLockInfo)==null?void 0:y.isLock)??!1}),v=S(!1),k=()=>{v.value=!0},x=async()=>{try{await Ml.confirm(s("common.loginOutMessage"),s("common.reminder"),{confirmButtonText:s("common.ok"),cancelButtonText:s("common.cancel"),type:"warning"}),await a.loginOut(),n.delAllViews(),l("/login?redirect=/index")}catch{}},i=async()=>{o("/user/profile")},C=()=>{window.open("https://doc.iocoder.cn/")};return(y,$)=>{const j=Dl,T=Z,L=lt,U=tt,V=at;return _(),B(Y,null,[t(V,{class:P(["custom-hover",e(u)]),trigger:"click"},{dropdown:w(()=>[t(U,null,{default:w(()=>[t(L,null,{default:w(()=>[t(T,{icon:"ep:tools"}),g("div",{onClick:i},I(e(s)("common.profile")),1)]),_:1}),t(L,null,{default:w(()=>[t(T,{icon:"ep:menu"}),g("div",{onClick:C},I(e(s)("common.document")),1)]),_:1}),t(L,{divided:""},{default:w(()=>[t(T,{icon:"ep:lock"}),g("div",{onClick:k},I(e(s)("lock.lockScreen")),1)]),_:1}),t(L,{divided:"",onClick:x},{default:w(()=>[t(T,{icon:"ep:switch-button"}),g("div",null,I(e(s)("common.loginOut")),1)]),_:1})]),_:1})]),default:w(()=>[g("div",Ua,[t(j,{src:e(h),alt:"",class:"w-[calc(var(--logo-height)-25px)] rounded-[50%]"},null,8,["src"]),g("span",Wa,I(e(b)),1)])]),_:1},8,["class"]),e(v)?(_(),F(Va,{key:0,modelValue:e(v),"onUpdate:modelValue":$[0]||($[0]=z=>ze(v)?v.value=z:null)},null,8,["modelValue"])):se("",!0),(_(),F(Tl,{to:"body"},[t(Qe,{name:"fade-bottom",mode:"out-in"},{default:w(()=>[e(m)?(_(),F(Ha,{key:0})):se("",!0)]),_:1})]))],64)}}}),Za=ee(Na,[["__scopeId","data-v-a34fa7e6"]]),Ga=R({name:"ScreenFull",__name:"Screenfull",props:{color:rt.string.def("")},setup(c){const{getPrefixCls:s}=q(),o=s("screenfull"),{toggle:l,isFullscreen:a}=Sl(),n=()=>{l()};return(r,u)=>(_(),B("div",{class:P(e(o)),onClick:n},[t(e(Z),{color:c.color,icon:e(a)?"zmdi:fullscreen-exit":"zmdi:fullscreen",size:18},null,8,["color","icon"])],2))}}),Wt=(c,s="")=>{var l;const o=[];for(const a of c){const n=a==null?void 0:a.meta;if(n.hidden&&!n.canTo)continue;const r=n.alwaysShow||((l=a.children)==null?void 0:l.length)!==1?{...a}:{...a.children[0],path:ie(a.path,a.children[0].path)};r.path=ie(s,r.path),r.children&&(r.children=Wt(r.children,r.path)),r&&o.push(r)}return o},{getPrefixCls:Ja}=q(),Xa=Ja("breadcrumb"),Ya=G(),Ka=d(()=>Ya.getBreadcrumbIcon),Qa=R({name:"Breadcrumb",setup(){const{currentRoute:c}=fe(),{t:s}=Q(),o=S([]),l=Le(),a=d(()=>{const n=l.getRouters;return Wt(n)});return ge(()=>c.value,n=>{n.path.startsWith("/redirect/")||(()=>{const r=c.value.matched.slice(-1)[0].path;o.value=jl(e(a),u=>u.path===r)})()},{immediate:!0}),()=>{let n;return t(ta,{separator:"/",class:`${Xa} flex items-center h-full ml-[10px]`},{default:()=>{return[t(Ll,{appear:!0,"enter-active-class":"animate__animated animate__fadeInRight"},(r=n=zl(e(o)).map(u=>{const h=!u.redirect||u.redirect==="noredirect",b=u.meta;return t(la,{to:{path:h?"":u.path},key:u.name},{default:()=>{var f,m;return[b!=null&&b.icon&&Ka.value?t("div",{class:"flex items-center"},[t(Z,{icon:b.icon,class:"mr-[2px]",svgClass:"inline-block"},null),s((f=u==null?void 0:u.meta)==null?void 0:f.title)]):s((m=u==null?void 0:u.meta)==null?void 0:m.title)]}})}),typeof r=="function"||Object.prototype.toString.call(r)==="[object Object]"&&!Vt(r)?n:{default:()=>[n]}))];var r}})}}}),eo=ee(Qa,[["__scopeId","data-v-1ed4b66c"]]),to=R({name:"SizeDropdown",__name:"SizeDropdown",props:{color:rt.string.def("")},setup(c){const{getPrefixCls:s}=q(),o=s("size-dropdown"),{t:l}=Q(),a=G(),n=d(()=>a.sizeMap),r=u=>{a.setCurrentSize(u)};return(u,h)=>{const b=Z,f=lt,m=tt,v=at;return _(),F(v,{class:P(e(o)),trigger:"click",onCommand:r},{dropdown:w(()=>[t(m,null,{default:w(()=>[(_(!0),B(Y,null,Re(e(n),k=>(_(),F(f,{key:k,command:k},{default:w(()=>[ue(I(e(l)(`size.${k}`)),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:w(()=>[t(b,{color:c.color,size:18,class:"cursor-pointer",icon:"mdi:format-size"},null,8,["color"])]),_:1},8,["class"])}}}),{getPrefixCls:lo,variables:ao}=q(),oo=lo("tool-header"),me=G(),ro=d(()=>me.getBreadcrumb),no=d(()=>me.getHamburger),so=d(()=>me.getScreenfull),io=d(()=>me.search),co=d(()=>me.getSize),Nt=d(()=>me.getLayout),uo=d(()=>me.getLocale),mo=d(()=>me.getMessage),We=ee(R({name:"ToolHeader",setup:()=>()=>t("div",{id:`${ao.namespace}-tool-header`,class:[oo,"h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between","dark:bg-[var(--el-bg-color)]"]},[Nt.value!=="top"?t("div",{class:"h-full flex items-center"},[no.value&&Nt.value!=="cutMenu"?t(Pa,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,ro.value?t(eo,{class:"lt-md:hidden"},null):void 0]):void 0,t("div",{class:"h-full flex items-center"},[so.value?t(Ga,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,io.value?t(Vl,{isModal:!1},null):void 0,co.value?t(to,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,uo.value?t(Ul,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,mo.value?t($a,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,t(Za,null,null)])])}),[["__scopeId","data-v-7577d039"]]),{getPrefixCls:vo}=q(),ve=vo("layout"),oe=G(),Ne=d(()=>oe.getPageLoading),re=d(()=>oe.getTagsView),E=d(()=>oe.getCollapse),$e=d(()=>oe.logo),H=d(()=>oe.getFixedHeader),te=d(()=>oe.getMobile),he=d(()=>oe.getFixedMenu),Ze=()=>({renderClassic:()=>t(Y,null,[t("div",{class:["absolute top-0 left-0 h-full layout-border__right",{"!fixed z-3000":te.value}]},[$e.value?t(De,{class:["bg-[var(--left-menu-bg-color)] relative",{"!pl-0":te.value&&E.value,"w-[var(--left-menu-min-width)]":oe.getCollapse,"w-[var(--left-menu-max-width)]":!oe.getCollapse}],style:"transition: all var(--transition-time-02);"},null):void 0,t(Fe,{class:[{"!h-[calc(100%-var(--logo-height))]":$e.value}]},null)]),t("div",{class:[`${ve}-content`,"absolute top-0 h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":E.value&&!te.value&&!te.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!E.value&&!te.value&&!te.value,"fixed !w-full !left-0":te.value}],style:"transition: all var(--transition-time-02);"},[de(t(ce,{class:[`${ve}-content-scrollbar`,{"!h-[calc(100%-var(--top-tool-height)-var(--tags-view-height))] mt-[calc(var(--top-tool-height)+var(--tags-view-height))]":H.value}]},{default:()=>[t("div",{class:[{"fixed top-0 left-0 z-10":H.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)]":E.value&&H.value&&!te.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)]":!E.value&&H.value&&!te.value,"!w-full !left-0":te.value}],style:"transition: all var(--transition-time-02);"},[t(We,{class:["bg-[var(--top-header-bg-color)]",{"layout-border__bottom":!re.value}]},null),re.value?t(Ee,{class:"layout-border__top layout-border__bottom"},null):void 0]),t(He,null,null)]}),[[Ae("loading"),Ne.value]])])]),renderTopLeft:()=>t(Y,null,[t("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom dark:bg-[var(--el-bg-color)]"},[$e.value?t(De,{class:"custom-hover"},null):void 0,t(We,{class:"flex-1"},null)]),t("div",{class:"absolute left-0 top-[var(--logo-height)] h-[calc(100%-var(--logo-height))] w-full flex"},[t(Fe,{class:"relative layout-border__right !h-full"},null),t("div",{class:[`${ve}-content`,"h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":E.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!E.value}],style:"transition: all var(--transition-time-02);"},[de(t(ce,{class:[`${ve}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":H.value&&re.value}]},{default:()=>[re.value?t(Ee,{class:["layout-border__bottom absolute",{"!fixed top-0 left-0 z-10":H.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)] mt-[var(--logo-height)]":E.value&&H.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)] mt-[var(--logo-height)]":!E.value&&H.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,t(He,null,null)]}),[[Ae("loading"),Ne.value]])])])]),renderTop:()=>t(Y,null,[t("div",{class:["flex items-center justify-between bg-[var(--top-header-bg-color)] relative",{"layout-border__bottom":!re.value}]},[$e.value?t(De,{class:"custom-hover"},null):void 0,t(Fe,{class:"h-[var(--top-tool-height)] flex-1 px-10px"},null),t(We,null,null)]),t("div",{class:[`${ve}-content`,"w-full h-[calc(100%-var(--top-tool-height))]"]},[de(t(ce,{class:[`${ve}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":H.value&&re.value}]},{default:()=>[re.value?t(Ee,{class:["layout-border__bottom layout-border__top relative",{"!fixed w-full top-[var(--top-tool-height)] left-0":H.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,t(He,null,null)]}),[[Ae("loading"),Ne.value]])])]),renderCutMenu:()=>t(Y,null,[t("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom"},[$e.value?t(De,{class:"custom-hover !pr-15px"},null):void 0,t(We,{class:"flex-1"},null)]),t("div",{class:"absolute left-0 top-[var(--logo-height)] h-[calc(100%-var(--logo-height))] w-full flex"},[t(da,null,null),t("div",{class:[`${ve}-content`,"h-[100%]",{"w-[calc(100%-var(--tab-menu-min-width))] left-[var(--tab-menu-min-width)]":E.value&&!he.value,"w-[calc(100%-var(--tab-menu-max-width))] left-[var(--tab-menu-max-width)]":!E.value&&!he.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":E.value&&he.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":!E.value&&he.value}],style:"transition: all var(--transition-time-02);"},[de(t(ce,{class:[`${ve}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":H.value&&re.value}]},{default:()=>[re.value?t(Ee,{class:["relative layout-border__bottom",{"!fixed top-0 left-0 z-10":H.value,"w-[calc(100%-var(--tab-menu-min-width))] !left-[var(--tab-menu-min-width)] mt-[var(--logo-height)]":E.value&&H.value&&!he.value,"w-[calc(100%-var(--tab-menu-max-width))] !left-[var(--tab-menu-max-width)] mt-[var(--logo-height)]":!E.value&&H.value&&!he.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] !left-[calc(var(--tab-menu-min-width)+var(--left-menu-max-width))] mt-[var(--logo-height)]":E.value&&H.value&&he.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] !left-[calc(var(--tab-menu-max-width)+var(--left-menu-max-width))] mt-[var(--logo-height)]":!E.value&&H.value&&he.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,t(He,null,null)]}),[[Ae("loading"),Ne.value]])])])])}),{getPrefixCls:ho}=q(),Zt=ho("layout"),Ge=G(),fo=d(()=>Ge.getMobile),po=d(()=>Ge.getCollapse),Gt=d(()=>Ge.getLayout),go=()=>{Ge.setCollapse(!0)},bo=()=>{switch(e(Gt)){case"classic":const{renderClassic:c}=Ze();return c();case"topLeft":const{renderTopLeft:s}=Ze();return s();case"top":const{renderTop:o}=Ze();return o();case"cutMenu":const{renderCutMenu:l}=Ze();return l()}},wo=ee(R({name:"Layout",setup:()=>()=>t("section",{class:[Zt,`${Zt}__${Gt.value}`,"w-[100%] h-[100%] relative"]},[fo.value&&!po.value?t("div",{class:"absolute left-0 top-0 z-99 h-full w-full bg-[var(--el-color-black)] opacity-30",onClick:go},null):void 0,bo(),t(aa,null,null)])}),[["__scopeId","data-v-6f987dce"]]);export{wo as default};
