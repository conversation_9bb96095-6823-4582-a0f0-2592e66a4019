import{_ as c}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as g,r as m,A as L,o as T,w as l,g as a,H as o,t as s,a as e,D as i,m as S}from"./index-CRsFgzy0.js";import{E,a as w}from"./el-descriptions-item-lelixL8M.js";import{_ as D}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{f as I}from"./formatTime-DhdtkSIS.js";const V=g({name:"SystemLoginLogDetail",__name:"LoginLogDetail",setup(Y,{expose:p}){const r=m(!1),n=m(!1),t=m({});return p({open:async d=>{r.value=!0,n.value=!0;try{t.value=d}finally{n.value=!1}}}),(d,_)=>{const u=w,f=D,y=E,v=c;return T(),L(v,{modelValue:e(r),"onUpdate:modelValue":_[0]||(_[0]=b=>S(r)?r.value=b:null),title:"\u8BE6\u60C5",width:"800"},{default:l(()=>[a(y,{column:1,border:""},{default:l(()=>[a(u,{label:"\u65E5\u5FD7\u7F16\u53F7","min-width":"120"},{default:l(()=>[o(s(e(t).id),1)]),_:1}),a(u,{label:"\u64CD\u4F5C\u7C7B\u578B"},{default:l(()=>[a(f,{type:e(i).SYSTEM_LOGIN_TYPE,value:e(t).logType},null,8,["type","value"])]),_:1}),a(u,{label:"\u7528\u6237\u540D\u79F0"},{default:l(()=>[o(s(e(t).username),1)]),_:1}),a(u,{label:"\u767B\u5F55\u5730\u5740"},{default:l(()=>[o(s(e(t).userIp),1)]),_:1}),a(u,{label:"\u6D4F\u89C8\u5668"},{default:l(()=>[o(s(e(t).userAgent),1)]),_:1}),a(u,{label:"\u767B\u9646\u7ED3\u679C"},{default:l(()=>[a(f,{type:e(i).SYSTEM_LOGIN_RESULT,value:e(t).result},null,8,["type","value"])]),_:1}),a(u,{label:"\u767B\u5F55\u65E5\u671F"},{default:l(()=>[o(s(e(I)(e(t).createTime)),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{V as _};
