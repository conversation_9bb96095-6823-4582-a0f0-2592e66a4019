import{_ as c}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as d}from"./Descriptions-BeaU49XW.js";import{g as p}from"./index-DWTWNHcr.js";import{a as f}from"./account.data-D1BkkXq2.js";import{d as _,r as e,A as v,o as y,w as h,g as A,a as t,m as V}from"./index-CRsFgzy0.js";const g=_({name:"SystemMailAccountDetail",__name:"MailAccountDetail",setup(w,{expose:r}){const a=e(!1),l=e(!1),o=e();return r({open:async s=>{a.value=!0,l.value=!0;try{o.value=await p(s)}finally{l.value=!1}}}),(s,m)=>{const u=d,n=c;return y(),v(n,{modelValue:t(a),"onUpdate:modelValue":m[0]||(m[0]=i=>V(a)?a.value=i:null),title:"\u8BE6\u60C5"},{default:h(()=>[A(u,{data:t(o),schema:t(f).detailSchema},null,8,["data","schema"])]),_:1},8,["modelValue"])}}});export{g as _};
