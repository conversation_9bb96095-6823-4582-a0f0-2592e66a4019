import{d as g,b as h,p as k,r as u,A,o as F,w as r,J as C,g as i,a as l,M as R,G as x,H as y,m as j}from"./index-CRsFgzy0.js";import{_ as E}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as G}from"./Form-BF4H89jq.js";import{g as H,c as J,u as U}from"./index-DWTWNHcr.js";import{a as q,r as z}from"./account.data-D1BkkXq2.js";const B=g({name:"SystemMailAccountForm",__name:"MailAccountForm",emits:["success"],setup(D,{expose:_,emit:w}){const{t:c}=h(),n=k(),e=u(!1),f=u(""),s=u(!1),d=u(""),o=u();_({open:async(t,a)=>{if(e.value=!0,f.value=c("action."+t),d.value=t,a){s.value=!0;try{const m=await H(a);o.value.setValues(m)}finally{s.value=!1}}}});const M=w,S=async()=>{if(o&&await o.value.getElFormRef().validate()){s.value=!0;try{const t=o.value.formModel;d.value==="create"?(await J(t),n.success(c("common.createSuccess"))):(await U(t),n.success(c("common.updateSuccess"))),e.value=!1,M("success")}finally{s.value=!1}}};return(t,a)=>{const m=G,v=x,V=E,b=R;return F(),A(V,{modelValue:l(e),"onUpdate:modelValue":a[1]||(a[1]=p=>j(e)?e.value=p:null),title:l(f)},{footer:r(()=>[i(v,{disabled:l(s),type:"primary",onClick:S},{default:r(()=>a[2]||(a[2]=[y("\u786E \u5B9A")])),_:1},8,["disabled"]),i(v,{onClick:a[0]||(a[0]=p=>e.value=!1)},{default:r(()=>a[3]||(a[3]=[y("\u53D6 \u6D88")])),_:1})]),default:r(()=>[C(i(m,{ref_key:"formRef",ref:o,rules:l(z),schema:l(q).formSchema},null,8,["rules","schema"]),[[b,l(s)]])]),_:1},8,["modelValue","title"])}}});export{B as _};
