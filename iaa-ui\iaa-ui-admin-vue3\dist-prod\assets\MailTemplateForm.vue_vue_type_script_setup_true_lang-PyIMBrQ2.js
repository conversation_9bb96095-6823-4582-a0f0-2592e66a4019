import{f as x,D as C,d as H,b as V,p as Y,r as m,A as E,o as I,w as d,J as O,g as f,a as i,M as R,G as U,H as P,m as j,__tla as G}from"./index-CRsFgzy0.js";import{_ as J}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as N}from"./Form-BF4H89jq.js";import{g as q,c as z,u as B}from"./index-DOw66Ggq.js";import{d as K}from"./formatTime-DhdtkSIS.js";import{b as L}from"./index-DWTWNHcr.js";import{r as a}from"./formRules-V2Qetfkc.js";import{u as Q}from"./useCrudSchemas-CNYomGr4.js";let T,h,W=Promise.all([(()=>{try{return G}catch{}})()]).then(async()=>{let c,b,v;c=await L(),b=x({name:[a],code:[a],accountId:[a],label:[a],content:[a],params:[a],status:[a]}),v=x([{label:"\u6A21\u677F\u7F16\u7801",field:"code",isSearch:!0,search:{componentProps:{style:{width:"240px"}}}},{label:"\u6A21\u677F\u540D\u79F0",field:"name",isSearch:!0,search:{componentProps:{style:{width:"240px"}}}},{label:"\u6A21\u677F\u6807\u9898",field:"title"},{label:"\u6A21\u677F\u5185\u5BB9",field:"content",form:{component:"Editor",componentProps:{valueHtml:"",height:200}}},{label:"\u90AE\u7BB1\u8D26\u53F7",field:"accountId",width:"200px",formatter:(M,y,p)=>{var l;return(l=c.find(n=>n.id===p))==null?void 0:l.mail},search:{show:!0,component:"Select",api:()=>c,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"},style:{width:"240px"}}},form:{component:"Select",api:()=>c,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"}}}},{label:"\u53D1\u9001\u4EBA\u540D\u79F0",field:"nickname"},{label:"\u5F00\u542F\u72B6\u6001",field:"status",isSearch:!0,dictType:C.COMMON_STATUS,dictClass:"number",search:{componentProps:{style:{width:"240px"}}}},{label:"\u5907\u6CE8",field:"remark",isTable:!1},{label:"\u521B\u5EFA\u65F6\u95F4",field:"createTime",isForm:!1,formatter:K,search:{show:!0,component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD HH:mm:ss",type:"daterange",defaultTime:[new Date("1 00:00:00"),new Date("1 23:59:59")],style:{width:"240px"}}}},{label:"\u64CD\u4F5C",field:"action",isForm:!1}]),{allSchemas:h}=Q(v),T=H({name:"SystemMailTemplateForm",__name:"MailTemplateForm",emits:["success"],setup(M,{expose:y,emit:p}){const{t:l}=V(),n=Y(),t=m(!1),w=m(""),s=m(!1),_=m(""),r=m();y({open:async(o,e)=>{if(t.value=!0,w.value=l("action."+o),_.value=o,e){s.value=!0;try{const u=await q(e);r.value.setValues(u)}finally{s.value=!1}}}});const g=p,k=async()=>{if(r&&await r.value.getElFormRef().validate()){s.value=!0;try{const o=r.value.formModel;_.value==="create"?(await z(o),n.success(l("common.createSuccess"))):(await B(o),n.success(l("common.updateSuccess"))),t.value=!1,g("success")}finally{s.value=!1}}};return(o,e)=>{const u=N,S=U,D=J,A=R;return I(),E(D,{modelValue:i(t),"onUpdate:modelValue":e[1]||(e[1]=F=>j(t)?t.value=F:null),"max-height":500,scroll:!0,title:i(w),width:800},{footer:d(()=>[f(S,{disabled:i(s),type:"primary",onClick:k},{default:d(()=>e[2]||(e[2]=[P("\u786E \u5B9A")])),_:1},8,["disabled"]),f(S,{onClick:e[0]||(e[0]=F=>t.value=!1)},{default:d(()=>e[3]||(e[3]=[P("\u53D6 \u6D88")])),_:1})]),default:d(()=>[O(f(u,{ref_key:"formRef",ref:r,rules:i(b),schema:i(h).formSchema},null,8,["rules","schema"]),[[A,i(s)]])]),_:1},8,["modelValue","title"])}}})});export{T as _,W as __tla,h as a};
