import{d as V,a2 as b,r as x,q as _,A as o,o as l,w as u,c as y,F as g,y as w,B as L,i as M,g as S,H as h,t as j,a as r,m as k,x as q}from"./index-CRsFgzy0.js";import{E as z}from"./el-avatar-Nl9DW69B.js";import{a as A}from"./index-DMoHDlyG.js";const B={class:"flex items-center gap-x-8px"},E=V({name:"MemberLevelSelect",__name:"MemberLevelSelect",props:{modelValue:{type:Number,default:void 0}},emits:["update:modelValue"],setup(d,{emit:n}){const p=d,c=n,a=b({get:()=>p.modelValue,set(t){c("update:modelValue",t)}}),s=x([]);return _(()=>{(async()=>s.value=await A())()}),(t,m)=>{const i=z,f=L,v=q;return l(),o(v,{modelValue:r(a),"onUpdate:modelValue":m[0]||(m[0]=e=>k(a)?a.value=e:null),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u7B49\u7EA7",clearable:"",class:"!w-240px"},{default:u(()=>[(l(!0),y(g,null,w(r(s),e=>(l(),o(f,{key:e.id,label:e.name,value:e.id},{default:u(()=>[M("span",B,[S(i,{src:e.icon,size:"small"},null,8,["src"]),h(" "+j(e.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])}}});export{E as _};
