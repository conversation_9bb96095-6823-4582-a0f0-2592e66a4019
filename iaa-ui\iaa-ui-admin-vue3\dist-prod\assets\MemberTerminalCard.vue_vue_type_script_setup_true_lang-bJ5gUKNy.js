import{d,r as u,f,q as c,J as b,M as h,a as t,A as v,o as g,w as l,g as m,k as w,R as _,D as C}from"./index-CRsFgzy0.js";import{_ as M}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{b as T}from"./member-DKMsSqvz.js";import{C as y}from"./CardTitle-DCrrQp54.js";const A=d({name:"MemberTerminalCard",__name:"MemberTerminalCard",setup(L){const r=u(!0),o=f({tooltip:{trigger:"item",confine:!0,formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right"},roseType:"area",series:[{name:"\u4F1A\u5458\u7EC8\u7AEF",type:"pie",label:{show:!1},labelLine:{show:!1},data:[]}]});return c(()=>{(async()=>{r.value=!0;const n=await T(),i=_(C.TERMINAL);o.series[0].data=i.map(e=>{var a;const s=(a=n.find(p=>p.terminal===e.value))==null?void 0:a.userCount;return{name:e.label,value:s||0}}),r.value=!1})()}),(n,i)=>{const e=M,s=w,a=h;return b((g(),v(s,{shadow:"never"},{header:l(()=>[m(t(y),{title:"\u4F1A\u5458\u7EC8\u7AEF"})]),default:l(()=>[m(e,{height:300,options:t(o)},null,8,["options"])]),_:1})),[[a,t(r)]])}}});export{A as _};
