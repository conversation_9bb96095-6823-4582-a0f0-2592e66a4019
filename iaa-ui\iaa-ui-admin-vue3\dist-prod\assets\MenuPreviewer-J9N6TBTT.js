import{d as M,a2 as N,c as o,o as u,F as O,g as m,a3 as h,a as v,m as T,w as g,i as k,X as b,H as j,I as A,t as y,_ as D}from"./index-CRsFgzy0.js";import{V}from"./vuedraggable.umd-V1xhRSm3.js";const F={class:"menu_bottom"},H=["onClick"],L={key:0,class:"submenu"},P={class:"menu_bottom subtitle"},S=["onClick"],X=["onClick"],q=D(M({__name:"MenuPreviewer",props:{modelValue:{},activeIndex:{},parentIndex:{},accountId:{}},emits:["update:modelValue","menu-clicked","submenu-clicked"],setup(f,{emit:C}){const d=f,i=C,s=N({get:()=>d.modelValue,set:n=>i("update:modelValue",n)}),E=()=>{const n=s.value.length,l={name:"\u83DC\u5355\u540D\u79F0",children:[],reply:{type:"text",accountId:d.accountId}};s.value[n]=l,I(l,n-1)},I=(n,l)=>{i("menu-clicked",n,l)},x=(n,l,a)=>{i("submenu-clicked",n,l,a)},w=({oldIndex:n,newIndex:l})=>{if(d.activeIndex==="__MENU_NOT_SELECTED__")return;let a=new Array(s.value.length).fill(!1);a[d.parentIndex]=!0;const[e]=a.splice(n,1);a.splice(l,0,e);const t=a.indexOf(!0),c=s.value[t];i("menu-clicked",c,t)},U=({newIndex:n})=>{var t;const l=d.parentIndex,a=n,e=(t=s.value[l])==null?void 0:t.children;if(e&&(e==null?void 0:e.length)>0){const c=e[a];i("submenu-clicked",c,l,a)}};return(n,l)=>{const a=A;return u(),o(O,null,[m(v(V),{modelValue:v(s),"onUpdate:modelValue":l[0]||(l[0]=e=>T(s)?s.value=e:null),"item-key":"id","ghost-class":"draggable-ghost",animation:400,onEnd:w},{item:g(({element:e,index:t})=>[k("div",F,[k("div",{onClick:c=>I(e,t),class:b(["menu_item",{active:d.activeIndex===`${t}`}])},[m(a,{icon:"ep:fold",color:"black"}),j(y(e.name),1)],10,H),d.parentIndex===t&&e.children?(u(),o("div",L,[m(v(V),{modelValue:e.children,"onUpdate:modelValue":c=>e.children=c,"item-key":"id","ghost-class":"draggable-ghost",animation:400,onEnd:U},{item:g(({element:c,index:r})=>[k("div",P,[e.children?(u(),o("div",{key:0,class:b(["menu_subItem",{active:d.activeIndex===`${t}-${r}`}]),onClick:p=>x(c,t,r)},y(c.name),11,S)):h("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"]),!e.children||e.children.length<5?(u(),o("div",{key:0,class:"menu_bottom menu_addicon",onClick:c=>((r,p)=>{const _=p.children.length,$={name:"\u5B50\u83DC\u5355\u540D\u79F0",reply:{type:"text",accountId:d.accountId}};p.children[_]=$,x(p.children[_],r,_)})(t,e)},[m(a,{icon:"ep:plus",class:"plus"})],8,X)):h("",!0)])):h("",!0)])]),_:1},8,["modelValue"]),v(s).length<3?(u(),o("div",{key:0,class:"menu_bottom menu_addicon",onClick:E},[m(a,{icon:"ep:plus",class:"plus"})])):h("",!0)],64)}}}),[["__scopeId","data-v-d1e067b7"]]);export{q as default};
