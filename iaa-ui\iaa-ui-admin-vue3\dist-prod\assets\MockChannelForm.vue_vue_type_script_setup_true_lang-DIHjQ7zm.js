import{d as U,b as A,p as J,r as i,c as g,o as m,g as r,a as l,m as R,w as o,J as B,A as b,s as E,v as T,aB as j,F as q,y as D,cF as L,D as G,aC as H,H as f,t as P,P as z,M as K,G as Q}from"./index-CRsFgzy0.js";import{_ as W}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{C as X}from"./constants-uird_4gU.js";import{g as Y,c as Z,u as $}from"./index-amKeQZTY.js";const aa=U({name:"MockChannelForm",__name:"MockChannelForm",emits:["success"],setup(ea,{expose:w,emit:V}){const{t:p}=A(),v=J(),d=i(!1),_=i(""),n=i(!1),e=i({appId:"",code:"",status:void 0,feeRate:0,remark:"",config:{name:"mock-conf"}}),h={status:[{required:!0,message:"\u6E20\u9053\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]},c=i();w({open:async(s,a)=>{d.value=!0,n.value=!0,x(s,a);try{const u=await Y(s,a);u&&u.id&&(e.value=u,e.value.config=JSON.parse(u.config)),_.value=e.value.id?"\u7F16\u8F91\u652F\u4ED8\u6E20\u9053":"\u521B\u5EFA\u652F\u4ED8\u6E20\u9053"}finally{n.value=!1}}});const C=V,S=async()=>{if(c&&await c.value.validate()){n.value=!0;try{const s={...e.value};s.config=JSON.stringify(e.value.config),s.id?(await $(s),v.success(p("common.updateSuccess"))):(await Z(s),v.success(p("common.createSuccess"))),d.value=!1,C("success")}finally{n.value=!1}}},x=(s,a)=>{var u;e.value={appId:s,code:a,status:X.ENABLE,remark:"",feeRate:0,config:{name:"mock-conf"}},(u=c.value)==null||u.resetFields()};return(s,a)=>{const u=H,F=j,k=T,M=z,I=E,y=Q,N=W,O=K;return m(),g("div",null,[r(N,{modelValue:l(d),"onUpdate:modelValue":a[3]||(a[3]=t=>R(d)?d.value=t:null),title:l(_),width:"800px"},{footer:o(()=>[r(y,{disabled:l(n),type:"primary",onClick:S},{default:o(()=>a[4]||(a[4]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),r(y,{onClick:a[2]||(a[2]=t=>d.value=!1)},{default:o(()=>a[5]||(a[5]=[f("\u53D6 \u6D88")])),_:1})]),default:o(()=>[B((m(),b(I,{ref_key:"formRef",ref:c,model:l(e),rules:h,"label-width":"100px"},{default:o(()=>[r(k,{label:"\u6E20\u9053\u72B6\u6001","label-width":"180px",prop:"status"},{default:o(()=>[r(F,{modelValue:l(e).status,"onUpdate:modelValue":a[0]||(a[0]=t=>l(e).status=t)},{default:o(()=>[(m(!0),g(q,null,D(l(L)(l(G).COMMON_STATUS),t=>(m(),b(u,{key:parseInt(t.value),value:parseInt(t.value)},{default:o(()=>[f(P(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(k,{label:"\u5907\u6CE8","label-width":"180px",prop:"remark"},{default:o(()=>[r(M,{modelValue:l(e).remark,"onUpdate:modelValue":a[1]||(a[1]=t=>l(e).remark=t),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[O,l(n)]])]),_:1},8,["modelValue","title"])])}}});export{aa as _};
