import{d as D,p as E,r as i,f as G,A as c,o as d,w as s,J as v,s as N,a as l,g as u,c as y,v as j,P as A,aB as H,F as V,y as _,R as J,D as Y,aC as z,H as b,t as K,a6 as h,x as L,B as O,M as Q,G as W,m as X}from"./index-CRsFgzy0.js";import{_ as Z}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{g as $}from"./index-D4y5Z4cM.js";import{g as ee,s as ae}from"./index-DrUVbRqZ.js";const le=D({name:"SystemNotifyTemplateSendForm",__name:"NotifyTemplateSendForm",setup(te,{expose:w}){const I=E(),p=i(!1),n=i(!1),t=i({content:"",params:{},userId:void 0,userType:2,templateCode:"",templateParams:new Map}),g=G({userId:[{required:!0,message:"\u7528\u6237\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],templateCode:[{required:!0,message:"\u6A21\u7248\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],templateParams:{}}),f=i(),P=i([]);w({open:async m=>{p.value=!0,U(),n.value=!0;try{const e=await ee(m);t.value.content=e.content,t.value.params=e.params,t.value.templateCode=e.code,t.value.templateParams=e.params.reduce((o,r)=>(o[r]="",o),{}),g.templateParams=e.params.reduce((o,r)=>(o[r]={required:!0,message:"\u53C2\u6570 "+r+" \u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"},o),{})}finally{n.value=!1}P.value=await $()}});const C=async()=>{if(f&&await f.value.validate()){n.value=!0;try{const m=t.value,e=await ae(m);e&&I.success("\u63D0\u4EA4\u53D1\u9001\u6210\u529F\uFF01\u53D1\u9001\u7ED3\u679C\uFF0C\u89C1\u53D1\u9001\u65E5\u5FD7\u7F16\u53F7\uFF1A"+e),p.value=!1}finally{n.value=!1}}},U=()=>{var m;t.value={content:"",params:{},mobile:"",templateCode:"",templateParams:new Map,userType:2},(m=f.value)==null||m.resetFields()};return(m,e)=>{const o=A,r=j,k=z,x=H,F=O,S=L,q=N,T=W,M=Z,R=Q;return d(),c(M,{modelValue:l(p),"onUpdate:modelValue":e[5]||(e[5]=a=>X(p)?p.value=a:null),title:"\u6D4B\u8BD5\u53D1\u9001","max-height":500},{footer:s(()=>[u(T,{disabled:l(n),type:"primary",onClick:C},{default:s(()=>e[6]||(e[6]=[b("\u786E \u5B9A")])),_:1},8,["disabled"]),u(T,{onClick:e[4]||(e[4]=a=>p.value=!1)},{default:s(()=>e[7]||(e[7]=[b("\u53D6 \u6D88")])),_:1})]),default:s(()=>[v((d(),c(q,{ref_key:"formRef",ref:f,model:l(t),rules:l(g),"label-width":"140px"},{default:s(()=>[u(r,{label:"\u6A21\u677F\u5185\u5BB9",prop:"content"},{default:s(()=>[u(o,{modelValue:l(t).content,"onUpdate:modelValue":e[0]||(e[0]=a=>l(t).content=a),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u5185\u5BB9",readonly:"",type:"textarea"},null,8,["modelValue"])]),_:1}),u(r,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:s(()=>[u(x,{modelValue:l(t).userType,"onUpdate:modelValue":e[1]||(e[1]=a=>l(t).userType=a)},{default:s(()=>[(d(!0),y(V,null,_(l(J)(l(Y).USER_TYPE),a=>(d(),c(k,{key:a.value,value:a.value},{default:s(()=>[b(K(a.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),v(u(r,{label:"\u63A5\u6536\u4EBAID",prop:"userId"},{default:s(()=>[u(o,{modelValue:l(t).userId,"onUpdate:modelValue":e[2]||(e[2]=a=>l(t).userId=a),style:{width:"160px"}},null,8,["modelValue"])]),_:1},512),[[h,l(t).userType===1]]),v(u(r,{label:"\u63A5\u6536\u4EBA",prop:"userId"},{default:s(()=>[u(S,{modelValue:l(t).userId,"onUpdate:modelValue":e[3]||(e[3]=a=>l(t).userId=a),placeholder:"\u8BF7\u9009\u62E9\u63A5\u6536\u4EBA"},{default:s(()=>[(d(!0),y(V,null,_(l(P),a=>(d(),c(F,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},512),[[h,l(t).userType===2]]),(d(!0),y(V,null,_(l(t).params,a=>(d(),c(r,{key:a,label:"\u53C2\u6570 {"+a+"}",prop:"templateParams."+a},{default:s(()=>[u(o,{modelValue:l(t).templateParams[a],"onUpdate:modelValue":B=>l(t).templateParams[a]=B,placeholder:"\u8BF7\u8F93\u5165 "+a+" \u53C2\u6570"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model","rules"])),[[R,l(n)]])]),_:1},8,["modelValue"])}}});export{le as _};
