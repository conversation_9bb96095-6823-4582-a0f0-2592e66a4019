import{_ as v}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as I,r as o,A as f,o as m,w as l,g as s,a3 as n,H as r,t,a as e,m as h}from"./index-CRsFgzy0.js";import{E as x,a as U}from"./el-descriptions-item-lelixL8M.js";import{f as w}from"./formatTime-DhdtkSIS.js";const A=I({name:"SystemOperateLogDetail",__name:"OperateLogDetail",setup(L,{expose:p}){const d=o(!1),_=o(!1),a=o({});return p({open:async b=>{d.value=!0,_.value=!0;try{a.value=b}finally{_.value=!1}}}),(b,i)=>{const u=U,c=x,y=v;return m(),f(y,{modelValue:e(d),"onUpdate:modelValue":i[0]||(i[0]=g=>h(d)?d.value=g:null),"max-height":500,scroll:!0,title:"\u8BE6\u60C5",width:"800"},{default:l(()=>[s(c,{column:1,border:""},{default:l(()=>[s(u,{label:"\u65E5\u5FD7\u4E3B\u952E","min-width":"120"},{default:l(()=>[r(t(e(a).id),1)]),_:1}),e(a).traceId?(m(),f(u,{key:0,label:"\u94FE\u8DEF\u8FFD\u8E2A"},{default:l(()=>[r(t(e(a).traceId),1)]),_:1})):n("",!0),s(u,{label:"\u64CD\u4F5C\u4EBA\u7F16\u53F7"},{default:l(()=>[r(t(e(a).userId),1)]),_:1}),s(u,{label:"\u64CD\u4F5C\u4EBA\u540D\u5B57"},{default:l(()=>[r(t(e(a).userName),1)]),_:1}),s(u,{label:"\u64CD\u4F5C\u4EBA IP"},{default:l(()=>[r(t(e(a).userIp),1)]),_:1}),s(u,{label:"\u64CD\u4F5C\u4EBA UA"},{default:l(()=>[r(t(e(a).userAgent),1)]),_:1}),s(u,{label:"\u64CD\u4F5C\u6A21\u5757"},{default:l(()=>[r(t(e(a).type),1)]),_:1}),s(u,{label:"\u64CD\u4F5C\u540D"},{default:l(()=>[r(t(e(a).subType),1)]),_:1}),s(u,{label:"\u64CD\u4F5C\u5185\u5BB9"},{default:l(()=>[r(t(e(a).action),1)]),_:1}),e(a).extra?(m(),f(u,{key:1,label:"\u64CD\u4F5C\u62D3\u5C55\u53C2\u6570"},{default:l(()=>[r(t(e(a).extra),1)]),_:1})):n("",!0),s(u,{label:"\u8BF7\u6C42 URL"},{default:l(()=>[r(t(e(a).requestMethod)+" "+t(e(a).requestUrl),1)]),_:1}),s(u,{label:"\u64CD\u4F5C\u65F6\u95F4"},{default:l(()=>[r(t(e(w)(e(a).createTime)),1)]),_:1}),s(u,{label:"\u4E1A\u52A1\u7F16\u53F7"},{default:l(()=>[r(t(e(a).bizId),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{A as _};
