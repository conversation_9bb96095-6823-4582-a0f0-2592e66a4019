import{as as w,d as h,u as g,f as y,bE as x,q as A,A as S,o as t,w as s,i as n,c as d,F as k,y as N,g as c,t as P,a as p,k as W}from"./index-CRsFgzy0.js";import{_ as C}from"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";import{a as U}from"./spu-BHhhuUrI.js";import{g as _}from"./trade-Dp4I9n8g.js";import{C as O}from"./CardTitle-DCrrQp54.js";const T={class:"flex flex-row flex-wrap items-center gap-8 p-4"},F=["onClick"],D={class:"text-center"},I=h({name:"OperationDataCard",__name:"OperationDataCard",setup(b){const m=g(),a=y({orderUndelivered:{name:"\u5F85\u53D1\u8D27\u8BA2\u5355",value:9,routerName:"TradeOrder"},orderAfterSaleApply:{name:"\u9000\u6B3E\u4E2D\u8BA2\u5355",value:4,routerName:"TradeAfterSale"},orderWaitePickUp:{name:"\u5F85\u6838\u9500\u8BA2\u5355",value:0,routerName:"TradeOrder"},productAlertStock:{name:"\u5E93\u5B58\u9884\u8B66",value:0,routerName:"ProductSpu"},productForSale:{name:"\u4E0A\u67B6\u5546\u54C1",value:0,routerName:"ProductSpu"},productInWarehouse:{name:"\u4ED3\u5E93\u5546\u54C1",value:0,routerName:"ProductSpu"},withdrawAuditing:{name:"\u63D0\u73B0\u5F85\u5BA1\u6838",value:0,routerName:"TradeBrokerageWithdraw"},rechargePrice:{name:"\u8D26\u6237\u5145\u503C",value:0,prefix:"\uFFE5",decimals:2,routerName:"PayWalletRecharge"}}),l=async()=>{const e=await _();e.undelivered!=null&&(a.orderUndelivered.value=e.undelivered),e.afterSaleApply!=null&&(a.orderAfterSaleApply.value=e.afterSaleApply),e.pickUp!=null&&(a.orderWaitePickUp.value=e.pickUp),e.auditingWithdraw!=null&&(a.withdrawAuditing.value=e.auditingWithdraw)},u=async()=>{const e=await U();a.productForSale.value=e[0],a.productInWarehouse.value=e[1],a.productAlertStock.value=e[3]},o=async()=>{const e=await(async()=>await w.get({url:"/statistics/pay/summary"}))();a.rechargePrice.value=e.rechargePrice};return x(()=>{l(),u(),o()}),A(()=>{l(),u(),o()}),(e,j)=>{const v=C,f=W;return t(),S(f,{shadow:"never"},{header:s(()=>[c(p(O),{title:"\u8FD0\u8425\u6570\u636E"})]),default:s(()=>[n("div",T,[(t(!0),d(k,null,N(p(a),r=>(t(),d("div",{key:r.name,class:"h-20 w-20% flex flex-col cursor-pointer items-center justify-center gap-2",onClick:q=>{return i=r.routerName,void m.push({name:i});var i}},[c(v,{decimals:r.decimals,"end-val":r.value,prefix:r.prefix,class:"text-3xl"},null,8,["decimals","end-val","prefix"]),n("span",D,P(r.name),1)],8,F))),128))])]),_:1})}}});export{I as _};
