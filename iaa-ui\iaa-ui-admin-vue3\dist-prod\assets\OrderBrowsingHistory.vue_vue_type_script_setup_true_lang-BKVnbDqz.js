import m from"./OrderItem-DgbO8iIn.js";import{e as i}from"./index-CtK0H6z7.js";import{c}from"./concat-BrdO5wJ1.js";import{d,r as l,f as g,a2 as v,c as y,o as u,y as f,A as w,a as x,F as H}from"./index-CRsFgzy0.js";const I=d({name:"OrderBrowsingHistory",__name:"OrderBrowsingHistory",setup(N,{expose:n}){const r=l([]),e=l(0),a=g({pageNo:1,pageSize:10,userId:0}),p=v(()=>e.value>0&&Math.ceil(e.value/a.pageSize)===a.pageNo);return n({getHistoryList:async s=>{a.userId=s.userId;const o=await i(a);e.value=o.total,r.value=o.list},loadMore:async()=>{if(p.value)return;a.pageNo+=1;const s=await i(a);e.value=s.total,c(r.value,s.list)}}),(s,o)=>(u(!0),y(H,null,f(x(r),t=>(u(),w(m,{key:t.id,order:t,class:"mb-10px"},null,8,["order"]))),128))}});export{I as _};
