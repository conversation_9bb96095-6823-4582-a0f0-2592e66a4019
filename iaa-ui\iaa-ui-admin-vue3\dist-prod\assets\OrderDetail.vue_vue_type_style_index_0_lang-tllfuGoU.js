import{_ as N}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as T,r as f,A as _,o as m,w as a,g as e,aE as g,H as d,t as u,a as l,a3 as x,D as h,j as F,dv as P,m as A}from"./index-CRsFgzy0.js";import{E,a as I}from"./el-descriptions-item-lelixL8M.js";import{_ as U}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{a as k}from"./index-fp-tOOOC.js";import{f as i}from"./formatTime-DhdtkSIS.js";const R=T({name:"PayOrderDetail",__name:"OrderDetail",setup(j,{expose:w}){const r=f(!1),c=f(!1),t=f({extension:{}});return w({open:async b=>{r.value=!0,c.value=!0;try{t.value=await k(b),t.value.extension||(t.value.extension={})}finally{c.value=!1}}}),(b,p)=>{const n=g,s=I,y=U,o=E,v=F,z=P,O=N;return m(),_(O,{modelValue:l(r),"onUpdate:modelValue":p[0]||(p[0]=D=>A(r)?r.value=D:null),title:"\u8BA2\u5355\u8BE6\u60C5",width:"700px"},{default:a(()=>[e(o,{column:2,"label-class-name":"desc-label"},{default:a(()=>[e(s,{label:"\u5546\u6237\u5355\u53F7"},{default:a(()=>[e(n,{size:"small"},{default:a(()=>[d(u(l(t).merchantOrderId),1)]),_:1})]),_:1}),e(s,{label:"\u652F\u4ED8\u5355\u53F7"},{default:a(()=>[l(t).no?(m(),_(n,{key:0,type:"warning",size:"small"},{default:a(()=>[d(u(l(t).no),1)]),_:1})):x("",!0)]),_:1}),e(s,{label:"\u5E94\u7528\u7F16\u53F7"},{default:a(()=>[d(u(l(t).appId),1)]),_:1}),e(s,{label:"\u5E94\u7528\u540D\u79F0"},{default:a(()=>[d(u(l(t).appName),1)]),_:1}),e(s,{label:"\u652F\u4ED8\u72B6\u6001"},{default:a(()=>[e(y,{type:l(h).PAY_ORDER_STATUS,value:l(t).status,size:"small"},null,8,["type","value"])]),_:1}),e(s,{label:"\u652F\u4ED8\u91D1\u989D"},{default:a(()=>[e(n,{type:"success",size:"small"},{default:a(()=>[d("\uFFE5"+u((l(t).price/100).toFixed(2)),1)]),_:1})]),_:1}),e(s,{label:"\u624B\u7EED\u8D39"},{default:a(()=>[e(n,{type:"warning",size:"small"},{default:a(()=>[d(" \uFFE5"+u((l(t).channelFeePrice/100).toFixed(2)),1)]),_:1})]),_:1}),e(s,{label:"\u624B\u7EED\u8D39\u6BD4\u4F8B"},{default:a(()=>[d(u((l(t).channelFeeRate/100).toFixed(2))+"% ",1)]),_:1}),e(s,{label:"\u652F\u4ED8\u65F6\u95F4"},{default:a(()=>[d(u(l(i)(l(t).successTime)),1)]),_:1}),e(s,{label:"\u5931\u6548\u65F6\u95F4"},{default:a(()=>[d(u(l(i)(l(t).expireTime)),1)]),_:1}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:a(()=>[d(u(l(i)(l(t).createTime)),1)]),_:1}),e(s,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:a(()=>[d(u(l(i)(l(t).updateTime)),1)]),_:1})]),_:1}),e(v),e(o,{column:2,"label-class-name":"desc-label"},{default:a(()=>[e(s,{label:"\u5546\u54C1\u6807\u9898"},{default:a(()=>[d(u(l(t).subject),1)]),_:1}),e(s,{label:"\u5546\u54C1\u63CF\u8FF0"},{default:a(()=>[d(u(l(t).body),1)]),_:1}),e(s,{label:"\u652F\u4ED8\u6E20\u9053"},{default:a(()=>[e(y,{type:l(h).PAY_CHANNEL_CODE,value:l(t).channelCode},null,8,["type","value"])]),_:1}),e(s,{label:"\u652F\u4ED8 IP"},{default:a(()=>[d(u(l(t).userIp),1)]),_:1}),e(s,{label:"\u6E20\u9053\u5355\u53F7"},{default:a(()=>[l(t).channelOrderNo?(m(),_(n,{key:0,size:"mini",type:"success"},{default:a(()=>[d(u(l(t).channelOrderNo),1)]),_:1})):x("",!0)]),_:1}),e(s,{label:"\u6E20\u9053\u7528\u6237"},{default:a(()=>[d(u(l(t).channelUserId),1)]),_:1}),e(s,{label:"\u9000\u6B3E\u91D1\u989D"},{default:a(()=>[e(n,{size:"mini",type:"danger"},{default:a(()=>[d(" \uFFE5"+u((l(t).refundPrice/100).toFixed(2)),1)]),_:1})]),_:1}),e(s,{label:"\u901A\u77E5 URL"},{default:a(()=>[d(u(l(t).notifyUrl),1)]),_:1})]),_:1}),e(v),e(o,{column:1,"label-class-name":"desc-label",direction:"vertical",border:""},{default:a(()=>[e(s,{label:"\u652F\u4ED8\u901A\u9053\u5F02\u6B65\u56DE\u8C03\u5185\u5BB9"},{default:a(()=>[e(z,{style:{"white-space":"pre-wrap","word-break":"break-word"}},{default:a(()=>[d(u(l(t).extension.channelNotifyData),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{R as _};
