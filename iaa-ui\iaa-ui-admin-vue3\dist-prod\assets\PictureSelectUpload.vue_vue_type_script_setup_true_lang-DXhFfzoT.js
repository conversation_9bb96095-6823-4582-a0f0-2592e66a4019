import z from"./picture-CTjip5lJ.js";import{d as x,p as C,c as P,o as _,i as b,a as k,dN as E}from"./index-CRsFgzy0.js";const T=["src"],U=x({name:"PictureSelectUpload",__name:"PictureSelectUpload",emits:["send-picture"],setup(j,{emit:m}){const d=C(),u=m,f=async()=>{const n=await async function(g={}){const{multiple:y,accept:s,limit:h,fileSize:w}={multiple:!0,accept:"image/jpeg, image/png, image/gif",limit:1,fileSize:500,...g},e=document.createElement("input");e.type="file",e.style.display="none",y&&(e.multiple=!0),s&&(e.accept=s),document.body.appendChild(e),e.click();try{return await new Promise((t,r)=>{e.addEventListener("change",a=>{var p;const i=Array.from(((p=a==null?void 0:a.target)==null?void 0:p.files)||[]);if(document.body.removeChild(e),i.length>h)return void r({errorType:"limit",files:i});const o=i.filter(c=>c.size/1048576>w);if(o.length>0)return void r({errorType:"fileSize",files:o});const v=i.map((c,S)=>({file:c,uid:Date.now()+S}));t(v)})})}catch(t){throw t}}();d.success("\u56FE\u7247\u53D1\u9001\u4E2D\u8BF7\u7A0D\u7B49\u3002\u3002\u3002");const l=await E({file:n[0].file});u("send-picture",l.data)};return(n,l)=>(_(),P("div",null,[b("img",{src:k(z),class:"w-35px h-35px",onClick:f},null,8,T)]))}});export{U as _};
