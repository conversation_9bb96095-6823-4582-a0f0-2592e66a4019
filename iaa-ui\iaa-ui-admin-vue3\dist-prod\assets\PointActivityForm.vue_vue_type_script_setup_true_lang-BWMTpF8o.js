import{f as F,d as Q,b as T,p as j,r as c,c as q,o as _,F as z,g as l,a as i,m as B,w as n,J as K,A as E,a3 as W,G as Y,H as C,L as $,an as X,M as Z,aP as ee,aQ as ae,aG as oe,aO as te}from"./index-CRsFgzy0.js";import{_ as le}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as se}from"./Form-BF4H89jq.js";import{_ as ue}from"./SpuSelect.vue_vue_type_script_setup_true_lang-tSVtv9n7.js";import{_ as ie}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-DGosLJYC.js";import{r as R}from"./formRules-V2Qetfkc.js";import{u as re}from"./useCrudSchemas-CNYomGr4.js";import{P as b}from"./index-gOhrLNGh.js";import{b as ne}from"./spu-BHhhuUrI.js";import{g as ce}from"./index-CQgbu5O7.js";const pe=F({spuId:[R],sort:[R]}),de=F([{label:"\u6392\u5E8F",field:"sort",form:{component:"InputNumber",value:0},table:{width:80}},{label:"\u79EF\u5206\u5546\u57CE\u6D3B\u52A8\u5546\u54C1",field:"spuId",isTable:!0,isSearch:!1,form:{colProps:{span:24}},table:{width:300}},{label:"\u5907\u6CE8",field:"remark",isSearch:!1,form:{component:"Input",componentProps:{type:"textarea",rows:4},colProps:{span:24}},table:{width:300}}]),{allSchemas:me}=re(de),fe=Q({name:"PromotionSeckillActivityForm",__name:"PointActivityForm",emits:["success"],setup(ve,{expose:L,emit:D}){const{t:y}=T(),S=j(),m=c(!1),I=c(""),f=c(!1),P=c(""),d=c(),V=c(!1),U=c(),A=c(),G=[{name:"productConfig.stock",rule:o=>o>=1,message:"\u5546\u54C1\u53EF\u5151\u6362\u5E93\u5B58\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 1 \uFF01\uFF01\uFF01"},{name:"productConfig.point",rule:o=>o>=1,message:"\u5546\u54C1\u6240\u9700\u5151\u6362\u79EF\u5206\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 1 \uFF01\uFF01\uFF01"},{name:"productConfig.count",rule:o=>o>=1,message:"\u5546\u54C1\u53EF\u5151\u6362\u6B21\u6570\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 1 \uFF01\uFF01\uFF01"}],g=c([]),h=c([]),M=(o,e)=>{d.value.setValues({spuId:o}),x(o,e)},x=async(o,e,s)=>{var w;const u=[],p=await ne([o]);if(p.length==0)return;g.value=[];const t=p[0],v=e===void 0?t==null?void 0:t.skus:(w=t==null?void 0:t.skus)==null?void 0:w.filter(a=>e.includes(a.id));v==null||v.forEach(a=>{let r={skuId:a.id,stock:0,price:0,point:0,count:0};if(s!==void 0){const k=s.find(O=>O.skuId===a.id);k&&(k.price=te(k.price)),r=k||r}a.productConfig=r}),t.skus=v,u.push({spuId:t.id,spuDetail:t,propertyList:ce(t)}),g.value.push(t),h.value=u};L({open:async(o,e)=>{var s;if(m.value=!0,I.value=y("action."+o),P.value=o,await N(),e){f.value=!0;try{const u=await b.getPointActivity(e);V.value=!0,await x(u.spuId,(s=u.products)==null?void 0:s.map(p=>p.skuId),u.products),d.value.setValues(u)}finally{f.value=!1}}}});const H=D,J=async()=>{if(d&&await d.value.getElFormRef().validate()){f.value=!0;try{const o=ee(A.value.getSkuConfigs("productConfig"));o.forEach(s=>{s.price=ae(s.price)});const e=d.value.formModel;e.products=o,P.value==="create"?(await b.createPointActivity(e),S.success(y("common.createSuccess"))):(await b.updatePointActivity(e),S.success(y("common.updateSuccess"))),m.value=!1,H("success")}finally{f.value=!1}}},N=async()=>{g.value=[],h.value=[],V.value=!1,await oe(),d.value.getElFormRef().resetFields()};return(o,e)=>{const s=Y,u=X,p=$,t=se,v=le,w=Z;return _(),q(z,null,[l(v,{modelValue:i(m),"onUpdate:modelValue":e[2]||(e[2]=a=>B(m)?m.value=a:null),title:i(I),width:"65%"},{footer:n(()=>[l(s,{disabled:i(f),type:"primary",onClick:J},{default:n(()=>e[4]||(e[4]=[C("\u786E \u5B9A")])),_:1},8,["disabled"]),l(s,{onClick:e[1]||(e[1]=a=>m.value=!1)},{default:n(()=>e[5]||(e[5]=[C("\u53D6 \u6D88")])),_:1})]),default:n(()=>[K((_(),E(t,{ref_key:"formRef",ref:d,isCol:!0,rules:i(pe),schema:i(me).formSchema},{spuId:n(()=>[i(V)?W("",!0):(_(),E(s,{key:0,onClick:e[0]||(e[0]=a=>i(U).open())},{default:n(()=>e[3]||(e[3]=[C("\u9009\u62E9\u5546\u54C1")])),_:1})),l(i(ie),{ref_key:"spuAndSkuListRef",ref:A,"rule-config":G,"spu-list":i(g),"spu-property-list-p":i(h)},{default:n(()=>[l(p,{align:"center",label:"\u53EF\u5151\u6362\u5E93\u5B58","min-width":"168"},{default:n(({row:a})=>[l(u,{modelValue:a.productConfig.stock,"onUpdate:modelValue":r=>a.productConfig.stock=r,max:a.stock,min:0,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue","max"])]),_:1}),l(p,{align:"center",label:"\u53EF\u5151\u6362\u6B21\u6570","min-width":"168"},{default:n(({row:a})=>[l(u,{modelValue:a.productConfig.count,"onUpdate:modelValue":r=>a.productConfig.count=r,min:0,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(p,{align:"center",label:"\u6240\u9700\u79EF\u5206","min-width":"168"},{default:n(({row:a})=>[l(u,{modelValue:a.productConfig.point,"onUpdate:modelValue":r=>a.productConfig.point=r,min:0,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(p,{align:"center",label:"\u6240\u9700\u91D1\u989D(\u5143)","min-width":"168"},{default:n(({row:a})=>[l(u,{modelValue:a.productConfig.price,"onUpdate:modelValue":r=>a.productConfig.price=r,min:0,precision:2,step:.1,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["spu-list","spu-property-list-p"])]),_:1},8,["rules","schema"])),[[w,i(f)]])]),_:1},8,["modelValue","title"]),l(i(ue),{ref_key:"spuSelectRef",ref:U,isSelectSku:!0,onConfirm:M},null,512)],64)}}});export{fe as _};
