import{d as I,ah as v,e6 as S,a2 as U,r as b,aK as j,c as n,o as t,i as m,g as i,A as E,a3 as L,F as h,y as N,w as g,J as R,I as T,a6 as q,bi as B,a as V,aU as _,_ as F}from"./index-CRsFgzy0.js";import{E as J}from"./el-image-BQpHFDaE.js";import{_ as K}from"./PointTableSelect.vue_vue_type_script_setup_true_lang-BbZQs1ao.js";import{P as M}from"./index-gOhrLNGh.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import"./color-CIFUYK2M.js";import"./formatter-D3GpDdeL.js";import"./formatTime-DhdtkSIS.js";const X={class:"flex flex-wrap items-center gap-8px"},z={class:"relative h-full w-full"},D=F(I({name:"PointShowcase",__name:"PointShowcase",props:{modelValue:S([Number,Array]).isRequired,limit:v.number.def(Number.MAX_VALUE),disabled:v.bool.def(!1)},emits:["update:modelValue","change"],setup(o,{emit:y}){const a=o,w=U(()=>!a.disabled&&(!a.limit||l.value.length<a.limit)),l=b([]);j(()=>a.modelValue,async()=>{const e=_(a.modelValue)?a.modelValue:a.modelValue?[a.modelValue]:[];e.length!==0?(l.value.length===0||l.value.some(r=>!e.includes(r.id)))&&(l.value=await M.getPointActivityListByIds(e)):l.value=[]},{immediate:!0});const c=b(),k=()=>{c.value.open(l.value)},x=e=>{l.value=_(e)?e:[e],d()},s=y,d=()=>{if(a.limit===1){const e=l.value.length>0?l.value[0]:null;s("update:modelValue",(e==null?void 0:e.id)||0),s("change",e)}else s("update:modelValue",l.value.map(e=>e.id)),s("change",l.value)};return(e,r)=>{const A=J,p=T,f=B;return t(),n(h,null,[m("div",X,[(t(!0),n(h,null,N(V(l),(u,C)=>(t(),n("div",{key:u.id,class:"select-box spu-pic"},[i(f,{content:u.name},{default:g(()=>[m("div",z,[i(A,{src:u.picUrl,class:"h-full w-full"},null,8,["src"]),R(i(p,{class:"del-icon",icon:"ep:circle-close-filled",onClick:G=>(P=>{l.value.splice(P,1),d()})(C)},null,8,["onClick"]),[[q,!o.disabled]])])]),_:2},1032,["content"])]))),128)),V(w)?(t(),E(f,{key:0,content:"\u9009\u62E9\u6D3B\u52A8"},{default:g(()=>[m("div",{class:"select-box",onClick:k},[i(p,{icon:"ep:plus"})])]),_:1})):L("",!0)]),i(K,{ref_key:"pointActivityTableSelectRef",ref:c,multiple:o.limit!=1,onChange:x},null,8,["multiple"])],64)}}}),[["__scopeId","data-v-4d3bfbb6"]]);export{D as default};
