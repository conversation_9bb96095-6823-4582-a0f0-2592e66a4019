import{d as ee,ah as ae,r as s,a2 as le,A as w,o as f,bZ as te,w as u,g as l,G as oe,H as h,J as ue,s as ie,a as o,v as re,x as ne,c as se,F as de,y as pe,R as me,D as I,B as ve,I as ce,K as fe,L as he,a4 as we,m as C,aC as ge,t as _e,M as be,bl as B}from"./index-CRsFgzy0.js";import{_ as ye}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ke}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as xe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as Ce}from"./el-image-BQpHFDaE.js";import{P as Se}from"./index-gOhrLNGh.js";import{f as Ue}from"./formatter-D3GpDdeL.js";import{d as Ne}from"./formatTime-DhdtkSIS.js";const Pe=ee({name:"PointTableSelect",__name:"PointTableSelect",props:{multiple:ae.bool.def(!1)},setup(S,{expose:F,emit:R}){const U=s(0),m=s([]),V=s(!1),p=s(!1),r=s({pageNo:1,pageSize:10,name:null,status:void 0}),j=le(()=>t=>(t.totalStock||0)-(t.stock||0));F({open:t=>{v.value=[],n.value={},d.value=!1,g.value=!1,t&&t.length>0&&(v.value=[...t],n.value=Object.fromEntries(t.map(e=>[e.id,!0]))),p.value=!0,N()}});const k=async()=>{V.value=!0;try{const t=await Se.getPointActivityPage(r.value);m.value=t.list,U.value=t.total,m.value.forEach(e=>n.value[e.id]=n.value[e.id]||!1),E()}finally{V.value=!1}},q=()=>{r.value.pageNo=1,k()},N=()=>{r.value={pageNo:1,pageSize:10,name:null,status:void 0},k()},d=s(!1),g=s(!1),v=s([]),n=s({}),_=s(),D=()=>{p.value=!1,P(B,[...v.value])},P=R,G=t=>{d.value=t,g.value=!1,m.value.forEach(e=>T(t,e,!1))},T=(t,e,x)=>{if(t)v.value.push(e),n.value[e.id]=!0;else{const b=H(e);b>-1&&(v.value.splice(b,1),n.value[e.id]=!1,d.value=!1)}x&&E()},H=t=>v.value.findIndex(e=>e.id===t.id),E=()=>{d.value=m.value.every(t=>n.value[t.id]),g.value=!d.value&&m.value.some(t=>n.value[t.id])};return(t,e)=>{const x=ve,b=ne,M=re,O=ce,y=oe,J=ie,z=we,i=he,K=ge,L=Ce,Q=xe,Z=fe,$=ke,W=Ve,X=ye,Y=be;return f(),w(X,{modelValue:o(p),"onUpdate:modelValue":e[6]||(e[6]=a=>C(p)?p.value=a:null),appendToBody:!0,title:"\u9009\u62E9\u6D3B\u52A8",width:"70%"},te({default:u(()=>[l(W,null,{default:u(()=>[l(J,{ref:"queryFormRef",inline:!0,model:o(r),class:"-mb-15px","label-width":"68px"},{default:u(()=>[l(M,{label:"\u6D3B\u52A8\u72B6\u6001",prop:"status"},{default:u(()=>[l(b,{modelValue:o(r).status,"onUpdate:modelValue":e[0]||(e[0]=a=>o(r).status=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u72B6\u6001"},{default:u(()=>[(f(!0),se(de,null,pe(o(me)(o(I).COMMON_STATUS),a=>(f(),w(x,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(M,null,{default:u(()=>[l(y,{onClick:q},{default:u(()=>[l(O,{class:"mr-5px",icon:"ep:search"}),e[7]||(e[7]=h(" \u641C\u7D22 "))]),_:1}),l(y,{onClick:N},{default:u(()=>[l(O,{class:"mr-5px",icon:"ep:refresh"}),e[8]||(e[8]=h(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),ue((f(),w(Z,{data:o(m),"show-overflow-tooltip":""},{default:u(()=>[S.multiple?(f(),w(i,{key:0,width:"55"},{header:u(()=>[l(z,{modelValue:o(d),"onUpdate:modelValue":e[1]||(e[1]=a=>C(d)?d.value=a:null),indeterminate:o(g),onChange:G},null,8,["modelValue","indeterminate"])]),default:u(({row:a})=>[l(z,{modelValue:o(n)[a.id],"onUpdate:modelValue":c=>o(n)[a.id]=c,onChange:c=>T(c,a,!0)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):(f(),w(i,{key:1,label:"#",width:"55"},{default:u(({row:a})=>[l(K,{modelValue:o(_),"onUpdate:modelValue":e[2]||(e[2]=c=>C(_)?_.value=c:null),value:a.id,onChange:c=>{return P(B,A=a),p.value=!1,void(_.value=A.id);var A}},{default:u(()=>e[9]||(e[9]=[h(" \xA0 ")])),_:2},1032,["modelValue","value","onChange"])]),_:1})),l(i,{label:"\u6D3B\u52A8\u7F16\u53F7","min-width":"80",prop:"id"}),l(i,{label:"\u5546\u54C1\u56FE\u7247","min-width":"80",prop:"spuName"},{default:u(a=>[l(L,{"preview-src-list":[a.row.picUrl],src:a.row.picUrl,class:"h-40px w-40px","preview-teleported":""},null,8,["preview-src-list","src"])]),_:1}),l(i,{label:"\u5546\u54C1\u6807\u9898","min-width":"300",prop:"spuName"}),l(i,{formatter:o(Ue),label:"\u539F\u4EF7","min-width":"100",prop:"marketPrice"},null,8,["formatter"]),l(i,{label:"\u539F\u4EF7","min-width":"100",prop:"marketPrice"}),l(i,{align:"center",label:"\u6D3B\u52A8\u72B6\u6001","min-width":"100",prop:"status"},{default:u(a=>[l(Q,{type:o(I).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),l(i,{align:"center",label:"\u5E93\u5B58","min-width":"80",prop:"stock"}),l(i,{align:"center",label:"\u603B\u5E93\u5B58","min-width":"80",prop:"totalStock"}),l(i,{align:"center",label:"\u5DF2\u5151\u6362\u6570\u91CF","min-width":"100",prop:"redeemedQuantity"},{default:u(({row:a})=>[h(_e(o(j)(a)),1)]),_:1}),l(i,{formatter:o(Ne),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[Y,o(V)]]),l($,{limit:o(r).pageSize,"onUpdate:limit":e[3]||(e[3]=a=>o(r).pageSize=a),page:o(r).pageNo,"onUpdate:page":e[4]||(e[4]=a=>o(r).pageNo=a),total:o(U),onPagination:k},null,8,["limit","page","total"])]),_:1})]),_:2},[S.multiple?{name:"footer",fn:u(()=>[l(y,{type:"primary",onClick:D},{default:u(()=>e[10]||(e[10]=[h("\u786E \u5B9A")])),_:1}),l(y,{onClick:e[5]||(e[5]=a=>p.value=!1)},{default:u(()=>e[11]||(e[11]=[h("\u53D6 \u6D88")])),_:1})]),key:"0"}:void 0]),1032,["modelValue"])}}});export{Pe as _};
