import{d as L,r as g,f as b,q as T,c as U,o as C,F as O,g as e,k as N,w as o,E as W,h as q,a as r,J as A,M as Y,A as z,L as j,D as p,K as k,aS as w,dD as F,eN as y,eL as _}from"./index-CRsFgzy0.js";import{_ as J}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as K}from"./el-skeleton-item-CZ5buDOR.js";import{_ as B}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as G}from"./portrait-DlC7eMLK.js";const H=L({name:"PortraitCustomerIndustry",__name:"PortraitCustomerIndustry",props:{queryParams:{}},setup(v,{expose:I}){const S=v,l=g(!1),f=g([]),n=b({title:{text:"\u5168\u90E8\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u5168\u90E8\u5BA2\u6237"}}},series:[{name:"\u5168\u90E8\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),d=b({title:{text:"\u6210\u4EA4\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u6210\u4EA4\u5BA2\u6237"}}},series:[{name:"\u6210\u4EA4\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),h=async()=>{l.value=!0;const s=await G.getCustomerIndustry(S.queryParams);n.series&&n.series[0]&&n.series[0].data&&(n.series[0].data=s.map(a=>({name:w(p.CRM_CUSTOMER_INDUSTRY,a.industryId),value:a.customerCount}))),d.series&&d.series[0]&&d.series[0].data&&(d.series[0].data=s.map(a=>({name:w(p.CRM_CUSTOMER_INDUSTRY,a.industryId),value:a.dealCount}))),R(s),f.value=s,l.value=!1};I({loadData:h});const R=s=>{if(F(s))return;const a=s,u=y(a.map(t=>t.customerCount)),m=y(a.map(t=>t.dealCount));a.forEach(t=>{t.industryPortion=t.customerCount===0?0:_(t.customerCount,u),t.dealPortion=t.dealCount===0?0:_(t.dealCount,m)})};return T(()=>{h()}),(s,a)=>{const u=B,m=K,t=q,x=W,c=N,i=j,P=J,D=k,M=Y;return C(),U(O,null,[e(c,{shadow:"never"},{default:o(()=>[e(x,{gutter:20},{default:o(()=>[e(t,{span:12},{default:o(()=>[e(m,{loading:r(l),animated:""},{default:o(()=>[e(u,{height:500,options:r(n)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(t,{span:12},{default:o(()=>[e(m,{loading:r(l),animated:""},{default:o(()=>[e(u,{height:500,options:r(d)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1}),e(c,{class:"mt-16px",shadow:"never"},{default:o(()=>[A((C(),z(D,{data:r(f)},{default:o(()=>[e(i,{align:"center",label:"\u5E8F\u53F7",type:"index",width:"80"}),e(i,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:o(E=>[e(P,{type:r(p).CRM_CUSTOMER_INDUSTRY,value:E.row.industryId},null,8,["type","value"])]),_:1}),e(i,{align:"center",label:"\u5BA2\u6237\u4E2A\u6570","min-width":"200",prop:"customerCount"}),e(i,{align:"center",label:"\u6210\u4EA4\u4E2A\u6570","min-width":"200",prop:"dealCount"}),e(i,{align:"center",label:"\u884C\u4E1A\u5360\u6BD4(%)","min-width":"200",prop:"industryPortion"}),e(i,{align:"center",label:"\u6210\u4EA4\u5360\u6BD4(%)","min-width":"200",prop:"dealPortion"})]),_:1},8,["data"])),[[M,r(l)]])]),_:1})],64)}}});export{H as _};
