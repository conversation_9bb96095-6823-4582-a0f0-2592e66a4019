import{d as D,r as v,f as b,q as T,c as V,o as g,F as W,g as e,k as q,w as o,E as A,h as U,a as s,J as z,M as I,A as j,L as k,D as p,K as F,aS as C,dD as J,eN as w,eL as _}from"./index-CRsFgzy0.js";import{_ as K}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as N}from"./el-skeleton-item-CZ5buDOR.js";import{_ as B}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as G}from"./portrait-DlC7eMLK.js";const H=D({name:"PortraitCustomerLevel",__name:"PortraitCustomerLevel",props:{queryParams:{}},setup(L,{expose:E}){const y=L,i=v(!1),f=v([]),n=b({title:{text:"\u5168\u90E8\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u5168\u90E8\u5BA2\u6237"}}},series:[{name:"\u5168\u90E8\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),d=b({title:{text:"\u6210\u4EA4\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u6210\u4EA4\u5BA2\u6237"}}},series:[{name:"\u6210\u4EA4\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),h=async()=>{i.value=!0;const l=await G.getCustomerLevel(y.queryParams);n.series&&n.series[0]&&n.series[0].data&&(n.series[0].data=l.map(t=>({name:C(p.CRM_CUSTOMER_LEVEL,t.level),value:t.customerCount}))),d.series&&d.series[0]&&d.series[0].data&&(d.series[0].data=l.map(t=>({name:C(p.CRM_CUSTOMER_LEVEL,t.level),value:t.dealCount}))),P(l),f.value=l,i.value=!1};E({loadData:h});const P=l=>{if(J(l))return;const t=l,u=w(t.map(a=>a.customerCount)),m=w(t.map(a=>a.dealCount));t.forEach(a=>{a.levelPortion=a.customerCount===0?0:_(a.customerCount,u),a.dealPortion=a.dealCount===0?0:_(a.dealCount,m)})};return T(()=>{h()}),(l,t)=>{const u=B,m=N,a=U,R=A,c=q,r=k,S=K,x=F,M=I;return g(),V(W,null,[e(c,{shadow:"never"},{default:o(()=>[e(R,{gutter:20},{default:o(()=>[e(a,{span:12},{default:o(()=>[e(m,{loading:s(i),animated:""},{default:o(()=>[e(u,{height:500,options:s(n)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(a,{span:12},{default:o(()=>[e(m,{loading:s(i),animated:""},{default:o(()=>[e(u,{height:500,options:s(d)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1}),e(c,{class:"mt-16px",shadow:"never"},{default:o(()=>[z((g(),j(x,{data:s(f)},{default:o(()=>[e(r,{align:"center",label:"\u5E8F\u53F7",type:"index",width:"80"}),e(r,{align:"center",label:"\u5BA2\u6237\u7EA7\u522B",prop:"level",width:"200"},{default:o(O=>[e(S,{type:s(p).CRM_CUSTOMER_LEVEL,value:O.row.level},null,8,["type","value"])]),_:1}),e(r,{align:"center",label:"\u5BA2\u6237\u4E2A\u6570","min-width":"200",prop:"customerCount"}),e(r,{align:"center",label:"\u6210\u4EA4\u4E2A\u6570","min-width":"200",prop:"dealCount"}),e(r,{align:"center",label:"\u7EA7\u522B\u5360\u6BD4(%)","min-width":"200",prop:"levelPortion"}),e(r,{align:"center",label:"\u6210\u4EA4\u5360\u6BD4(%)","min-width":"200",prop:"dealPortion"})]),_:1},8,["data"])),[[M,s(i)]])]),_:1})],64)}}});export{H as _};
