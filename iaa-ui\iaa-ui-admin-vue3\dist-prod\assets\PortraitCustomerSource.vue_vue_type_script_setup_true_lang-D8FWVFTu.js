import{d as U,r as g,f as C,q as D,c as T,o as b,F as W,g as e,k as q,w as o,E as A,h as z,a as s,J as I,M as j,A as k,L as F,D as p,K as J,aS as w,dD as K,eN as _,eL as v}from"./index-CRsFgzy0.js";import{_ as N}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as B}from"./el-skeleton-item-CZ5buDOR.js";import{_ as G}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as H}from"./portrait-DlC7eMLK.js";const Q=U({name:"PortraitCustomerSource",__name:"PortraitCustomerSource",props:{queryParams:{}},setup(S,{expose:R}){const y=S,i=g(!1),c=g([]),n=C({title:{text:"\u5168\u90E8\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u5168\u90E8\u5BA2\u6237"}}},series:[{name:"\u5168\u90E8\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),d=C({title:{text:"\u6210\u4EA4\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u6210\u4EA4\u5BA2\u6237"}}},series:[{name:"\u6210\u4EA4\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),f=async()=>{i.value=!0;const r=await H.getCustomerSource(y.queryParams);n.series&&n.series[0]&&n.series[0].data&&(n.series[0].data=r.map(t=>({name:w(p.CRM_CUSTOMER_SOURCE,t.source),value:t.customerCount}))),d.series&&d.series[0]&&d.series[0].data&&(d.series[0].data=r.map(t=>({name:w(p.CRM_CUSTOMER_SOURCE,t.source),value:t.dealCount}))),x(r),c.value=r,i.value=!1};R({loadData:f});const x=r=>{if(K(r))return;const t=r,u=_(t.map(a=>a.customerCount)),m=_(t.map(a=>a.dealCount));t.forEach(a=>{a.sourcePortion=a.customerCount===0?0:v(a.customerCount,u),a.dealPortion=a.dealCount===0?0:v(a.dealCount,m)})};return D(()=>{f()}),(r,t)=>{const u=G,m=B,a=z,E=A,h=q,l=F,O=N,P=J,M=j;return b(),T(W,null,[e(h,{shadow:"never"},{default:o(()=>[e(E,{gutter:20},{default:o(()=>[e(a,{span:12},{default:o(()=>[e(m,{loading:s(i),animated:""},{default:o(()=>[e(u,{height:500,options:s(n)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(a,{span:12},{default:o(()=>[e(m,{loading:s(i),animated:""},{default:o(()=>[e(u,{height:500,options:s(d)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1}),e(h,{class:"mt-16px",shadow:"never"},{default:o(()=>[I((b(),k(P,{data:s(c)},{default:o(()=>[e(l,{align:"center",label:"\u5E8F\u53F7",type:"index",width:"80"}),e(l,{align:"center",label:"\u5BA2\u6237\u6765\u6E90",prop:"source",width:"100"},{default:o(L=>[e(O,{type:s(p).CRM_CUSTOMER_SOURCE,value:L.row.source},null,8,["type","value"])]),_:1}),e(l,{align:"center",label:"\u5BA2\u6237\u4E2A\u6570","min-width":"200",prop:"customerCount"}),e(l,{align:"center",label:"\u6210\u4EA4\u4E2A\u6570","min-width":"200",prop:"dealCount"}),e(l,{align:"center",label:"\u6765\u6E90\u5360\u6BD4(%)","min-width":"200",prop:"sourcePortion"}),e(l,{align:"center",label:"\u6210\u4EA4\u5360\u6BD4(%)","min-width":"200",prop:"dealPortion"})]),_:1},8,["data"])),[[M,s(i)]])]),_:1})],64)}}});export{Q as _};
