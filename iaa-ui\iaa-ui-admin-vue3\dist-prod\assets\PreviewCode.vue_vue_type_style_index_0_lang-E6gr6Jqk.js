import{d as ce,b as oe,p as le,r as H,f as ue,q as de,O as ge,A as P,o as L,w as N,i as V,J,k as me,g as Z,aF as be,cr as _e,a as w,M as pe,l as he,c as fe,F as ye,y as ve,n as Ee,G as Ae,H as we,t as Ne,m as xe,eu as Se}from"./index-CRsFgzy0.js";import{_ as ke}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as Oe}from"./tree-COGD3qag.js";import{p as Re}from"./index-DjgTygXx.js";import{H as x}from"./index-p2LMQUWl.js";import{j as U}from"./java-CqAR0c3a.js";const W="[A-Za-z$_][0-9A-Za-z$_]*",Ce=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],Ie=["true","false","null","undefined","NaN","Infinity"],Q=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],X=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],Y=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],Te=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],Me=[].concat(Y,Q,X);function je(e){const n=e.regex,a=W,E="<>",y="</>",l={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(f,k)=>{const O=f[0].length+f.index,R=f.input[O];if(R==="<"||R===",")return void k.ignoreMatch();let C;R===">"&&(((B,{after:G})=>{const K="</"+B[0].slice(1);return B.input.indexOf(K,G)!==-1})(f,{after:O})||k.ignoreMatch());const z=f.input.substring(O);((C=z.match(/^\s*=/))||(C=z.match(/^\s+extends\s+/))&&C.index===0)&&k.ignoreMatch()}},s={$pattern:W,keyword:Ce,literal:Ie,built_in:Me,"variable.language":Te},v="[0-9](_?[0-9])*",b=`\\.(${v})`,g="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",_={className:"number",variants:[{begin:`(\\b(${g})((${b})|\\.)?|(${b}))[eE][+-]?(${v})\\b`},{begin:`\\b(${g})\\b((${b})\\b|\\.)?|(${b})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},r={className:"subst",begin:"\\$\\{",end:"\\}",keywords:s,contains:[]},t={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,r],subLanguage:"xml"}},o={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,r],subLanguage:"css"}},A={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,r],subLanguage:"graphql"}},c={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,r]},d={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:a+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},m=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,t,o,A,c,{match:/\$\d+/},_];r.contains=m.concat({begin:/\{/,end:/\}/,keywords:s,contains:["self"].concat(m)});const p=[].concat(d,r.contains),i=p.concat([{begin:/(\s*)\(/,end:/\)/,keywords:s,contains:["self"].concat(p)}]),u={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:s,contains:i},I={variants:[{match:[/class/,/\s+/,a,/\s+/,/extends/,/\s+/,n.concat(a,"(",n.concat(/\./,a),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,a],scope:{1:"keyword",3:"title.class"}}]},h={relevance:0,match:n.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...Q,...X]}},D={variants:[{match:[/function/,/\s+/,a,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[u],illegal:/%/},T={match:n.concat(/\b/,(S=[...Y,"super","import"].map(f=>`${f}\\s*\\(`),n.concat("(?!",S.join("|"),")")),a,n.lookahead(/\s*\(/)),className:"title.function",relevance:0};var S;const M={begin:n.concat(/\./,n.lookahead(n.concat(a,/(?![0-9A-Za-z$_(])/))),end:a,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},j={match:[/get|set/,/\s+/,a,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},u]},$="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",q={match:[/const|var|let/,/\s+/,a,/\s*/,/=\s*/,/(async\s*)?/,n.lookahead($)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[u]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:s,exports:{PARAMS_CONTAINS:i,CLASS_REFERENCE:h},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,t,o,A,c,d,{match:/\$\d+/},_,h,{scope:"attr",match:a+n.lookahead(":"),relevance:0},q,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[d,e.REGEXP_MODE,{className:"function",begin:$,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:s,contains:i}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:E,end:y},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:l.begin,"on:begin":l.isTrulyOpeningTag,end:l.end}],subLanguage:"xml",contains:[{begin:l.begin,end:l.end,skip:!0,contains:["self"]}]}]},D,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[u,e.inherit(e.TITLE_MODE,{begin:a,className:"title.function"})]},{match:/\.\.\./,relevance:0},M,{match:"\\$"+a,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[u]},T,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},I,j,{match:/\$[(.]/}]}}function $e(e){const n=e.regex,a=e.COMMENT("--","$"),E=["abs","acos","array_agg","asin","atan","avg","cast","ceil","ceiling","coalesce","corr","cos","cosh","count","covar_pop","covar_samp","cume_dist","dense_rank","deref","element","exp","extract","first_value","floor","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","last_value","lead","listagg","ln","log","log10","lower","max","min","mod","nth_value","ntile","nullif","percent_rank","percentile_cont","percentile_disc","position","position_regex","power","rank","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","row_number","sin","sinh","sqrt","stddev_pop","stddev_samp","substring","substring_regex","sum","tan","tanh","translate","translate_regex","treat","trim","trim_array","unnest","upper","value_of","var_pop","var_samp","width_bucket"],y=E,l=["abs","acos","all","allocate","alter","and","any","are","array","array_agg","array_max_cardinality","as","asensitive","asin","asymmetric","at","atan","atomic","authorization","avg","begin","begin_frame","begin_partition","between","bigint","binary","blob","boolean","both","by","call","called","cardinality","cascaded","case","cast","ceil","ceiling","char","char_length","character","character_length","check","classifier","clob","close","coalesce","collate","collect","column","commit","condition","connect","constraint","contains","convert","copy","corr","corresponding","cos","cosh","count","covar_pop","covar_samp","create","cross","cube","cume_dist","current","current_catalog","current_date","current_default_transform_group","current_path","current_role","current_row","current_schema","current_time","current_timestamp","current_path","current_role","current_transform_group_for_type","current_user","cursor","cycle","date","day","deallocate","dec","decimal","decfloat","declare","default","define","delete","dense_rank","deref","describe","deterministic","disconnect","distinct","double","drop","dynamic","each","element","else","empty","end","end_frame","end_partition","end-exec","equals","escape","every","except","exec","execute","exists","exp","external","extract","false","fetch","filter","first_value","float","floor","for","foreign","frame_row","free","from","full","function","fusion","get","global","grant","group","grouping","groups","having","hold","hour","identity","in","indicator","initial","inner","inout","insensitive","insert","int","integer","intersect","intersection","interval","into","is","join","json_array","json_arrayagg","json_exists","json_object","json_objectagg","json_query","json_table","json_table_primitive","json_value","lag","language","large","last_value","lateral","lead","leading","left","like","like_regex","listagg","ln","local","localtime","localtimestamp","log","log10","lower","match","match_number","match_recognize","matches","max","member","merge","method","min","minute","mod","modifies","module","month","multiset","national","natural","nchar","nclob","new","no","none","normalize","not","nth_value","ntile","null","nullif","numeric","octet_length","occurrences_regex","of","offset","old","omit","on","one","only","open","or","order","out","outer","over","overlaps","overlay","parameter","partition","pattern","per","percent","percent_rank","percentile_cont","percentile_disc","period","portion","position","position_regex","power","precedes","precision","prepare","primary","procedure","ptf","range","rank","reads","real","recursive","ref","references","referencing","regr_avgx","regr_avgy","regr_count","regr_intercept","regr_r2","regr_slope","regr_sxx","regr_sxy","regr_syy","release","result","return","returns","revoke","right","rollback","rollup","row","row_number","rows","running","savepoint","scope","scroll","search","second","seek","select","sensitive","session_user","set","show","similar","sin","sinh","skip","smallint","some","specific","specifictype","sql","sqlexception","sqlstate","sqlwarning","sqrt","start","static","stddev_pop","stddev_samp","submultiset","subset","substring","substring_regex","succeeds","sum","symmetric","system","system_time","system_user","table","tablesample","tan","tanh","then","time","timestamp","timezone_hour","timezone_minute","to","trailing","translate","translate_regex","translation","treat","trigger","trim","trim_array","true","truncate","uescape","union","unique","unknown","unnest","update","upper","user","using","value","values","value_of","var_pop","var_samp","varbinary","varchar","varying","versioning","when","whenever","where","width_bucket","window","with","within","without","year","add","asc","collation","desc","final","first","last","view"].filter(g=>!E.includes(g)),s={match:n.concat(/\b/,n.either(...y),/\s*\(/),relevance:0,keywords:{built_in:y}};function v(g){return n.concat(/\b/,n.either(...g.map(_=>_.replace(/\s+/,"\\s+"))),/\b/)}const b={scope:"keyword",match:v(["create table","insert into","primary key","foreign key","not null","alter table","add constraint","grouping sets","on overflow","character set","respect nulls","ignore nulls","nulls first","nulls last","depth first","breadth first"]),relevance:0};return{name:"SQL",case_insensitive:!0,illegal:/[{}]|<\//,keywords:{$pattern:/\b[\w\.]+/,keyword:function(g,{exceptions:_,when:r}={}){const t=r;return _=_||[],g.map(o=>o.match(/\|\d+$/)||_.includes(o)?o:t(o)?`${o}|0`:o)}(l,{when:g=>g.length<3}),literal:["true","false","unknown"],type:["bigint","binary","blob","boolean","char","character","clob","date","dec","decfloat","decimal","float","int","integer","interval","nchar","nclob","national","numeric","real","row","smallint","time","timestamp","varchar","varying","varbinary"],built_in:["current_catalog","current_date","current_default_transform_group","current_path","current_role","current_schema","current_transform_group_for_type","current_user","session_user","system_time","system_user","current_time","localtime","current_timestamp","localtimestamp"]},contains:[{scope:"type",match:v(["double precision","large object","with timezone","without timezone"])},b,s,{scope:"variable",match:/@[a-z0-9][a-z0-9_]*/},{scope:"string",variants:[{begin:/'/,end:/'/,contains:[{match:/''/}]}]},{begin:/"/,end:/"/,contains:[{match:/""/}]},e.C_NUMBER_MODE,e.C_BLOCK_COMMENT_MODE,a,{scope:"operator",match:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,relevance:0}]}}const F="[A-Za-z$_][0-9A-Za-z$_]*",ee=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],ae=["true","false","null","undefined","NaN","Infinity"],ne=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],te=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],re=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],se=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],ie=[].concat(re,ne,te);function ze(e){const n=e.regex,a=F,E="<>",y="</>",l={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(f,k)=>{const O=f[0].length+f.index,R=f.input[O];if(R==="<"||R===",")return void k.ignoreMatch();let C;R===">"&&(((B,{after:G})=>{const K="</"+B[0].slice(1);return B.input.indexOf(K,G)!==-1})(f,{after:O})||k.ignoreMatch());const z=f.input.substring(O);((C=z.match(/^\s*=/))||(C=z.match(/^\s+extends\s+/))&&C.index===0)&&k.ignoreMatch()}},s={$pattern:F,keyword:ee,literal:ae,built_in:ie,"variable.language":se},v="[0-9](_?[0-9])*",b=`\\.(${v})`,g="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",_={className:"number",variants:[{begin:`(\\b(${g})((${b})|\\.)?|(${b}))[eE][+-]?(${v})\\b`},{begin:`\\b(${g})\\b((${b})\\b|\\.)?|(${b})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},r={className:"subst",begin:"\\$\\{",end:"\\}",keywords:s,contains:[]},t={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,r],subLanguage:"xml"}},o={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,r],subLanguage:"css"}},A={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,r],subLanguage:"graphql"}},c={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,r]},d={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:a+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},m=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,t,o,A,c,{match:/\$\d+/},_];r.contains=m.concat({begin:/\{/,end:/\}/,keywords:s,contains:["self"].concat(m)});const p=[].concat(d,r.contains),i=p.concat([{begin:/(\s*)\(/,end:/\)/,keywords:s,contains:["self"].concat(p)}]),u={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:s,contains:i},I={variants:[{match:[/class/,/\s+/,a,/\s+/,/extends/,/\s+/,n.concat(a,"(",n.concat(/\./,a),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,a],scope:{1:"keyword",3:"title.class"}}]},h={relevance:0,match:n.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...ne,...te]}},D={variants:[{match:[/function/,/\s+/,a,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[u],illegal:/%/},T={match:n.concat(/\b/,(S=[...re,"super","import"].map(f=>`${f}\\s*\\(`),n.concat("(?!",S.join("|"),")")),a,n.lookahead(/\s*\(/)),className:"title.function",relevance:0};var S;const M={begin:n.concat(/\./,n.lookahead(n.concat(a,/(?![0-9A-Za-z$_(])/))),end:a,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},j={match:[/get|set/,/\s+/,a,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},u]},$="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",q={match:[/const|var|let/,/\s+/,a,/\s*/,/=\s*/,/(async\s*)?/,n.lookahead($)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[u]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:s,exports:{PARAMS_CONTAINS:i,CLASS_REFERENCE:h},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,t,o,A,c,d,{match:/\$\d+/},_,h,{scope:"attr",match:a+n.lookahead(":"),relevance:0},q,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[d,e.REGEXP_MODE,{className:"function",begin:$,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:s,contains:i}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:E,end:y},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:l.begin,"on:begin":l.isTrulyOpeningTag,end:l.end}],subLanguage:"xml",contains:[{begin:l.begin,end:l.end,skip:!0,contains:["self"]}]}]},D,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[u,e.inherit(e.TITLE_MODE,{begin:a,className:"title.function"})]},{match:/\.\.\./,relevance:0},M,{match:"\\$"+a,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[u]},T,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},I,j,{match:/\$[(.]/}]}}function Be(e){const n=e.regex,a=ze(e),E=F,y=["any","void","number","boolean","string","object","never","symbol","bigint","unknown"],l={begin:[/namespace/,/\s+/,e.IDENT_RE],beginScope:{1:"keyword",3:"title.class"}},s={beginKeywords:"interface",end:/\{/,excludeEnd:!0,keywords:{keyword:"interface extends",built_in:y},contains:[a.exports.CLASS_REFERENCE]},v={$pattern:F,keyword:ee.concat(["type","interface","public","private","protected","implements","declare","abstract","readonly","enum","override","satisfies"]),literal:ae,built_in:ie.concat(y),"variable.language":se},b={className:"meta",begin:"@"+E},g=(t,o,A)=>{const c=t.contains.findIndex(d=>d.label===o);if(c===-1)throw new Error("can not find mode to replace");t.contains.splice(c,1,A)};Object.assign(a.keywords,v),a.exports.PARAMS_CONTAINS.push(b);const _=a.contains.find(t=>t.scope==="attr"),r=Object.assign({},_,{match:n.concat(E,n.lookahead(/\s*\?:/))});return a.exports.PARAMS_CONTAINS.push([a.exports.CLASS_REFERENCE,_,r]),a.contains=a.contains.concat([b,l,s,r]),g(a,"shebang",e.SHEBANG()),g(a,"use_strict",{className:"meta",relevance:10,begin:/^\s*['"]use strict['"]/}),a.contains.find(t=>t.label==="func.def").relevance=0,Object.assign(a,{name:"TypeScript",aliases:["ts","tsx","mts","cts"]}),a}const Le={class:"flex"},Ze={class:"hljs"},De=ce({name:"InfraCodegenPreviewCode",__name:"PreviewCode",setup(e,{expose:n}){const{t:a}=oe(),E=le(),y=H(!1),l=H(!1),s=ue({fileTree:[],activeName:""}),v=H(),b=async(r,t)=>{if(t&&!t.isLeaf)return!1;s.activeName=r.id};n({open:async r=>{y.value=!0;try{l.value=!0;const t=await Re(r);v.value=t;let o=g(t);s.fileTree=Oe(o,"id","parentId","children","/"),s.activeName=t[0].filePath}finally{l.value=!1}}});const g=r=>{let t={},o=[];for(const A of r){let c=A.filePath.split("/"),d="";if(c[c.length-1].indexOf(".java")>=0){let m=[];for(let p=0;p<c.length;p++){let i=c[p];if(i!=="java"){m.push(i);continue}m.push(i);let u="";for(;p<c.length&&(i=c[p+1],i!=="controller"&&i!=="convert"&&i!=="dal"&&i!=="enums"&&i!=="service"&&i!=="vo"&&i!=="mysql"&&i!=="dataobject");)u=u?u+"."+i:i,p++;u&&m.push(u)}c=m}for(let m=0;m<c.length;m++){let p=d;d=d.length===0?c[m]:d.replaceAll(".","/")+"/"+c[m],t[d]||(t[d]=!0,o.push({id:d,label:c[m],parentId:p||"/"}))}}return o},_=r=>{const t=r.filePath.substring(r.filePath.lastIndexOf(".")+1);return x.highlight(t,r.code||"",!0).value||"&nbsp;"};return de(async()=>{x.registerLanguage("java",U),x.registerLanguage("xml",U),x.registerLanguage("html",U),x.registerLanguage("vue",U),x.registerLanguage("javascript",je),x.registerLanguage("sql",$e),x.registerLanguage("typescript",Be)}),(r,t)=>{const o=_e,A=be,c=me,d=Ae,m=Ee,p=he,i=ke,u=pe,I=ge("dompurify-html");return L(),P(i,{modelValue:w(y),"onUpdate:modelValue":t[1]||(t[1]=h=>xe(y)?y.value=h:null),"align-center":"",class:"app-infra-codegen-preview-container",title:"\u4EE3\u7801\u9884\u89C8",width:"80%"},{default:N(()=>[V("div",Le,[J((L(),P(c,{gutter:12,class:"w-1/3","element-loading-text":"\u751F\u6210\u6587\u4EF6\u76EE\u5F55\u4E2D...",shadow:"hover"},{default:N(()=>[Z(A,{height:"calc(100vh - 88px - 40px)"},{default:N(()=>[Z(o,{ref:"treeRef",data:w(s).fileTree,"expand-on-click-node":!1,"default-expand-all":"","highlight-current":"","node-key":"id",onNodeClick:b},null,8,["data"])]),_:1})]),_:1})),[[u,w(l)]]),J((L(),P(c,{gutter:12,class:"ml-3 w-2/3","element-loading-text":"\u52A0\u8F7D\u4EE3\u7801\u4E2D...",shadow:"hover"},{default:N(()=>[Z(p,{modelValue:w(s).activeName,"onUpdate:modelValue":t[0]||(t[0]=h=>w(s).activeName=h)},{default:N(()=>[(L(!0),fe(ye,null,ve(w(v),h=>(L(),P(m,{key:h.filePath,label:h.filePath.substring(h.filePath.lastIndexOf("/")+1),name:h.filePath},{default:N(()=>[Z(d,{class:"float-right",text:"",type:"primary",onClick:D=>(async T=>{const{copy:S,copied:M,isSupported:j}=Se({source:T});j?(await S(),w(M)&&E.success(a("common.copySuccess"))):E.error(a("common.copyError"))})(h.code)},{default:N(()=>[we(Ne(w(a)("common.copy")),1)]),_:2},1032,["onClick"]),Z(A,{height:"600px"},{default:N(()=>[V("pre",null,[J(V("code",Ze,null,512),[[I,_(h)]])])]),_:2},1024)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1})),[[u,w(l)]])])]),_:1},8,["modelValue"])}}});export{De as _};
