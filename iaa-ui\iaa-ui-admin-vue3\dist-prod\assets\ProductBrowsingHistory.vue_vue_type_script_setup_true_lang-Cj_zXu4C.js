import{as as d,d as g,r as l,f as m,a2 as y,c as v,o as u,y as f,A as w,a as k,F as I}from"./index-CRsFgzy0.js";import N from"./ProductItem-CSRkJo9p.js";import{c as x}from"./concat-BrdO5wJ1.js";const p=i=>d.get({url:"/product/browse-history/page",params:i}),H=g({name:"ProductBrowsingHistory",__name:"ProductBrowsingHistory",setup(i,{expose:c}){const o=l([]),t=l(0),s=m({pageNo:1,pageSize:10,userId:0,userDeleted:!1}),n=y(()=>t.value>0&&Math.ceil(t.value/s.pageSize)===s.pageNo);return c({getHistoryList:async e=>{s.userId=e.userId;const r=await p(s);t.value=r.total,o.value=r.list},loadMore:async()=>{if(n.value)return;s.pageNo+=1;const e=await p(s);t.value=e.total,x(o.value,e.list)}}),(e,r)=>(u(!0),v(I,null,f(k(o),a=>(u(),w(N,{key:a.id,picUrl:a.picUrl,price:a.price,"sales-count":a.salesCount,"spu-id":a.spuId,stock:a.stock,title:a.spuName,class:"mb-10px"},null,8,["picUrl","price","sales-count","spu-id","stock","title"]))),128))}});export{H as _};
