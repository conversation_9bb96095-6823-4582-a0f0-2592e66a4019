import{d as A,b as B,p as G,r as o,f as H,A as c,o as i,w as t,J,s as M,a,g as n,v as R,x as z,c as D,B as E,F as K,y as L,P as N,M as O,G as Q,H as w,m as T}from"./index-CRsFgzy0.js";import{_ as W}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{a as X,g as Y,c as Z,u as $}from"./index-C5r8YBwc.js";const ee=A({name:"CrmProductCategoryForm",__name:"ProductCategoryForm",emits:["success"],setup(ae,{expose:I,emit:k}){const{t:p}=B(),v=G(),r=o(!1),f=o(""),d=o(!1),y=o(""),u=o({id:void 0,name:void 0,parentId:void 0}),C=H({name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],parentId:[{required:!0,message:"\u7236\u7EA7\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),m=o(),_=o([]);I({open:async(s,e)=>{if(r.value=!0,f.value=p("action."+s),y.value=s,x(),e){d.value=!0;try{u.value=await X(e)}finally{d.value=!1}}_.value=await Y({parentId:0})}});const F=k,h=async()=>{if(m&&await m.value.validate()){d.value=!0;try{const s=u.value;y.value==="create"?(await Z(s),v.success(p("common.createSuccess"))):(await $(s),v.success(p("common.updateSuccess"))),r.value=!1,F("success")}finally{d.value=!1}}},x=()=>{var s;u.value={id:void 0,name:void 0,parentId:void 0},(s=m.value)==null||s.resetFields()};return(s,e)=>{const b=E,U=z,g=R,q=N,P=M,V=Q,S=W,j=O;return i(),c(S,{title:a(f),modelValue:a(r),"onUpdate:modelValue":e[3]||(e[3]=l=>T(r)?r.value=l:null)},{footer:t(()=>[n(V,{onClick:h,type:"primary",disabled:a(d)},{default:t(()=>e[4]||(e[4]=[w("\u786E \u5B9A")])),_:1},8,["disabled"]),n(V,{onClick:e[2]||(e[2]=l=>r.value=!1)},{default:t(()=>e[5]||(e[5]=[w("\u53D6 \u6D88")])),_:1})]),default:t(()=>[J((i(),c(P,{ref_key:"formRef",ref:m,model:a(u),rules:a(C),"label-width":"100px"},{default:t(()=>[n(g,{label:"\u7236\u7EA7\u5206\u7C7B",prop:"parentId"},{default:t(()=>[n(U,{modelValue:a(u).parentId,"onUpdate:modelValue":e[0]||(e[0]=l=>a(u).parentId=l),placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u5206\u7C7B"},{default:t(()=>[(i(),c(b,{key:0,label:"\u9876\u7EA7\u5206\u7C7B",value:0})),(i(!0),D(K,null,L(a(_),l=>(i(),c(b,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(g,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[n(q,{modelValue:a(u).name,"onUpdate:modelValue":e[1]||(e[1]=l=>a(u).name=l),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,a(d)]])]),_:1},8,["title","modelValue"])}}});export{ee as _};
