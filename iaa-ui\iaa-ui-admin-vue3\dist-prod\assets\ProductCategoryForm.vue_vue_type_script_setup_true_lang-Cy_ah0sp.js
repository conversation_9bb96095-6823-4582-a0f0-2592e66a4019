import{d as N,b as O,p as R,r as d,f as T,A as y,o as c,w as o,J as j,s as D,a,g as s,v as G,P as H,aB as J,c as z,F as K,y as Q,R as W,D as X,aC as Y,H as V,t as Z,M as $,G as ee,m as ae}from"./index-CRsFgzy0.js";import{_ as le}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{E as te}from"./el-tree-select-BijZG_HG.js";import{P as n}from"./index-DjmZXe-0.js";import{d as oe,h as se}from"./tree-COGD3qag.js";import{C as w}from"./constants-uird_4gU.js";const re=N({name:"ProductCategoryForm",__name:"ProductCategoryForm",emits:["success"],setup(de,{expose:P,emit:I}){const{t:p}=O(),_=R(),u=d(!1),b=d(""),m=d(!1),C=d(""),t=d({id:void 0,parentId:void 0,name:void 0,code:void 0,sort:void 0,status:w.ENABLE}),U=T({parentId:[{required:!0,message:"\u4E0A\u7EA7\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],code:[{required:!0,message:"\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=d(),f=d();P({open:async(r,e)=>{if(u.value=!0,b.value=p("action."+r),C.value=r,q(),e){m.value=!0;try{t.value=await n.getProductCategory(e)}finally{m.value=!1}}await A()}});const k=I,E=async()=>{await v.value.validate(),m.value=!0;try{const r=t.value;C.value==="create"?(await n.createProductCategory(r),_.success(p("common.createSuccess"))):(await n.updateProductCategory(r),_.success(p("common.updateSuccess"))),u.value=!1,k("success")}finally{m.value=!1}},q=()=>{var r;t.value={id:void 0,parentId:void 0,name:void 0,code:void 0,sort:void 0,status:w.ENABLE},(r=v.value)==null||r.resetFields()},A=async()=>{f.value=[];const r=await n.getProductCategoryList(),e={id:0,name:"\u9876\u7EA7\u4EA7\u54C1\u5206\u7C7B",children:[]};e.children=se(r,"id","parentId"),f.value.push(e)};return(r,e)=>{const x=te,i=G,g=H,F=Y,L=J,S=D,h=ee,B=le,M=$;return c(),y(B,{title:a(b),modelValue:a(u),"onUpdate:modelValue":e[6]||(e[6]=l=>ae(u)?u.value=l:null)},{footer:o(()=>[s(h,{onClick:E,type:"primary",disabled:a(m)},{default:o(()=>e[7]||(e[7]=[V("\u786E \u5B9A")])),_:1},8,["disabled"]),s(h,{onClick:e[5]||(e[5]=l=>u.value=!1)},{default:o(()=>e[8]||(e[8]=[V("\u53D6 \u6D88")])),_:1})]),default:o(()=>[j((c(),y(S,{ref_key:"formRef",ref:v,model:a(t),rules:a(U),"label-width":"100px"},{default:o(()=>[s(i,{label:"\u4E0A\u7EA7\u7F16\u53F7",prop:"parentId"},{default:o(()=>[s(x,{modelValue:a(t).parentId,"onUpdate:modelValue":e[0]||(e[0]=l=>a(t).parentId=l),data:a(f),props:a(oe),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u7F16\u53F7"},null,8,["modelValue","data","props"])]),_:1}),s(i,{label:"\u540D\u79F0",prop:"name"},{default:o(()=>[s(g,{modelValue:a(t).name,"onUpdate:modelValue":e[1]||(e[1]=l=>a(t).name=l),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1}),s(i,{label:"\u7F16\u7801",prop:"code"},{default:o(()=>[s(g,{modelValue:a(t).code,"onUpdate:modelValue":e[2]||(e[2]=l=>a(t).code=l),placeholder:"\u8BF7\u8F93\u5165\u7F16\u7801"},null,8,["modelValue"])]),_:1}),s(i,{label:"\u6392\u5E8F",prop:"sort"},{default:o(()=>[s(g,{modelValue:a(t).sort,"onUpdate:modelValue":e[3]||(e[3]=l=>a(t).sort=l),placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F"},null,8,["modelValue"])]),_:1}),s(i,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[s(L,{modelValue:a(t).status,"onUpdate:modelValue":e[4]||(e[4]=l=>a(t).status=l)},{default:o(()=>[(c(!0),z(K,null,Q(a(W)(a(X).COMMON_STATUS),l=>(c(),y(F,{key:l.value,value:l.value},{default:o(()=>[V(Z(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[M,a(m)]])]),_:1},8,["title","modelValue"])}}});export{re as _};
