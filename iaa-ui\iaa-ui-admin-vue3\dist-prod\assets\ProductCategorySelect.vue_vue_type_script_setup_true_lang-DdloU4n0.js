import{d as c,ah as d,e6 as i,a2 as h,r as V,q as b,A as f,o as y,a as t,m as g}from"./index-CRsFgzy0.js";import{E as w}from"./el-tree-select-BijZG_HG.js";import{h as I,d as k}from"./tree-COGD3qag.js";import{g as v}from"./category--cl9fhwU.js";const x=c({name:"ProductCategorySelect",__name:"ProductCategorySelect",props:{modelValue:i([Number,Array]),multiple:d.bool.def(!1),parentId:d.number.def(void 0)},emits:["update:modelValue"],setup(a,{emit:m}){const l=a,o=h({get:()=>l.modelValue,set:e=>{p("update:modelValue",e)}}),p=m,r=V([]);return b(async()=>{const e=await v({parentId:l.parentId});r.value=I(e,"id","parentId")}),(e,s)=>{const u=w;return y(),f(u,{modelValue:t(o),"onUpdate:modelValue":s[0]||(s[0]=n=>g(o)?o.value=n:null),data:t(r),props:t(k),multiple:a.multiple,"show-checkbox":a.multiple,class:"w-1/1","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","data","props","multiple","show-checkbox"])}}});export{x as _};
