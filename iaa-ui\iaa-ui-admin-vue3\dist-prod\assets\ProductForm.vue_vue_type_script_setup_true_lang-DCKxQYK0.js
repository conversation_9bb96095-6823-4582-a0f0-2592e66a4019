import{d as H,b as J,p as Z,r as i,f as j,A as v,o as c,w as o,J as K,s as Q,a as l,g as e,E as W,h as X,v as Y,P as $,x as ee,c as I,F as C,y as x,B as ae,aB as le,R as de,D as oe,aC as ue,H as g,t as re,an as te,M as se,G as ie,m as pe}from"./index-CRsFgzy0.js";import{_ as me}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{E as ne}from"./el-tree-select-BijZG_HG.js";import{P as b}from"./index-v67yau6-.js";import{P as ce}from"./index-DjmZXe-0.js";import{P as fe}from"./index-CrB20wZB.js";import{C as ve}from"./constants-uird_4gU.js";import{d as Ve,h as _e}from"./tree-COGD3qag.js";const ge=H({name:"ProductForm",__name:"ProductForm",emits:["success"],setup(be,{expose:D,emit:S}){const{t:V}=J(),y=Z(),p=i(!1),P=i(""),m=i(!1),h=i(""),u=i({id:void 0,name:void 0,barCode:void 0,categoryId:void 0,unitId:void 0,status:void 0,standard:void 0,remark:void 0,expiryDay:void 0,weight:void 0,purchasePrice:void 0,salePrice:void 0,minPrice:void 0}),q=j({name:[{required:!0,message:"\u4EA7\u54C1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],barCode:[{required:!0,message:"\u4EA7\u54C1\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],categoryId:[{required:!0,message:"\u4EA7\u54C1\u5206\u7C7B\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],unitId:[{required:!0,message:"\u5355\u4F4D\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u4EA7\u54C1\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),_=i(),w=i([]),U=i([]);D({open:async(s,a)=>{if(p.value=!0,P.value=V("action."+s),h.value=s,A(),a){m.value=!0;try{u.value=await b.getProduct(a)}finally{m.value=!1}}const n=await ce.getProductCategorySimpleList();w.value=_e(n,"id","parentId"),U.value=await fe.getProductUnitSimpleList()}});const E=S,F=async()=>{await _.value.validate(),m.value=!0;try{const s=u.value;h.value==="create"?(await b.createProduct(s),y.success(V("common.createSuccess"))):(await b.updateProduct(s),y.success(V("common.updateSuccess"))),p.value=!1,E("success")}finally{m.value=!1}},A=()=>{var s;u.value={id:void 0,name:void 0,barCode:void 0,categoryId:void 0,unitId:void 0,status:ve.ENABLE,standard:void 0,remark:void 0,expiryDay:void 0,weight:void 0,purchasePrice:void 0,salePrice:void 0,minPrice:void 0},(s=_.value)==null||s.resetFields()};return(s,a)=>{const n=$,r=Y,t=X,L=ne,M=ae,B=ee,N=ue,R=le,f=te,T=W,O=Q,k=ie,z=me,G=se;return c(),v(z,{title:l(P),modelValue:l(p),"onUpdate:modelValue":a[13]||(a[13]=d=>pe(p)?p.value=d:null)},{footer:o(()=>[e(k,{onClick:F,type:"primary",disabled:l(m)},{default:o(()=>a[14]||(a[14]=[g("\u786E \u5B9A")])),_:1},8,["disabled"]),e(k,{onClick:a[12]||(a[12]=d=>p.value=!1)},{default:o(()=>a[15]||(a[15]=[g("\u53D6 \u6D88")])),_:1})]),default:o(()=>[K((c(),v(O,{ref_key:"formRef",ref:_,model:l(u),rules:l(q),"label-width":"100px"},{default:o(()=>[e(T,{gutter:20},{default:o(()=>[e(t,{span:12},{default:o(()=>[e(r,{label:"\u540D\u79F0",prop:"name"},{default:o(()=>[e(n,{modelValue:l(u).name,"onUpdate:modelValue":a[0]||(a[0]=d=>l(u).name=d),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u6761\u7801",prop:"barCode"},{default:o(()=>[e(n,{modelValue:l(u).barCode,"onUpdate:modelValue":a[1]||(a[1]=d=>l(u).barCode=d),placeholder:"\u8BF7\u8F93\u5165\u6761\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u5206\u7C7B",prop:"categoryId"},{default:o(()=>[e(L,{modelValue:l(u).categoryId,"onUpdate:modelValue":a[2]||(a[2]=d=>l(u).categoryId=d),data:l(w),props:l(Ve),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u9009\u62E9\u5206\u7C7B",class:"w-1/1"},null,8,["modelValue","data","props"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u5355\u4F4D",prop:"unitId"},{default:o(()=>[e(B,{modelValue:l(u).unitId,"onUpdate:modelValue":a[3]||(a[3]=d=>l(u).unitId=d),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u5355\u4F4D",class:"w-1/1"},{default:o(()=>[(c(!0),I(C,null,x(l(U),d=>(c(),v(M,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[e(R,{modelValue:l(u).status,"onUpdate:modelValue":a[4]||(a[4]=d=>l(u).status=d)},{default:o(()=>[(c(!0),I(C,null,x(l(de)(l(oe).COMMON_STATUS),d=>(c(),v(N,{key:d.value,value:d.value},{default:o(()=>[g(re(d.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u89C4\u683C",prop:"standard"},{default:o(()=>[e(n,{modelValue:l(u).standard,"onUpdate:modelValue":a[5]||(a[5]=d=>l(u).standard=d),placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u4FDD\u8D28\u671F\u5929\u6570",prop:"expiryDay"},{default:o(()=>[e(f,{modelValue:l(u).expiryDay,"onUpdate:modelValue":a[6]||(a[6]=d=>l(u).expiryDay=d),placeholder:"\u8BF7\u8F93\u5165\u4FDD\u8D28\u671F\u5929\u6570",min:0,precision:0,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u91CD\u91CF\uFF08kg\uFF09",prop:"weight"},{default:o(()=>[e(f,{modelValue:l(u).weight,"onUpdate:modelValue":a[7]||(a[7]=d=>l(u).weight=d),placeholder:"\u8BF7\u8F93\u5165\u91CD\u91CF\uFF08kg\uFF09",min:0,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u91C7\u8D2D\u4EF7\u683C",prop:"purchasePrice"},{default:o(()=>[e(f,{modelValue:l(u).purchasePrice,"onUpdate:modelValue":a[8]||(a[8]=d=>l(u).purchasePrice=d),placeholder:"\u8BF7\u8F93\u5165\u91C7\u8D2D\u4EF7\u683C\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u9500\u552E\u4EF7\u683C",prop:"salePrice"},{default:o(()=>[e(f,{modelValue:l(u).salePrice,"onUpdate:modelValue":a[9]||(a[9]=d=>l(u).salePrice=d),placeholder:"\u8BF7\u8F93\u5165\u9500\u552E\u4EF7\u683C\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:12},{default:o(()=>[e(r,{label:"\u6700\u4F4E\u4EF7\u683C",prop:"minPrice"},{default:o(()=>[e(f,{modelValue:l(u).minPrice,"onUpdate:modelValue":a[10]||(a[10]=d=>l(u).minPrice=d),placeholder:"\u8BF7\u8F93\u5165\u6700\u4F4E\u4EF7\u683C\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(t,{span:24},{default:o(()=>[e(r,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[e(n,{type:"textarea",modelValue:l(u).remark,"onUpdate:modelValue":a[11]||(a[11]=d=>l(u).remark=d),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[G,l(m)]])]),_:1},8,["title","modelValue"])}}});export{ge as _};
