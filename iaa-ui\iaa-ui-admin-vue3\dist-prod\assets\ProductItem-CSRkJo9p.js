import{d as o,u as d,c as n,o as m,bu as i,i as s,g as p,t as a,a as f,aR as v,G as y,w as g,H as w,_ as b}from"./index-CRsFgzy0.js";import{E as x}from"./el-image-BQpHFDaE.js";const S={class:"product-warp-left mr-24px"},I={class:"product-warp-right"},k={class:"description"},C={class:"my-5px"},N={class:"mr-20px"},h={class:"flex justify-between items-center"},P={class:"price"},U=b(o({name:"ProductItem",__name:"ProductItem",props:{spuId:{type:Number,default:0},picUrl:{type:String,default:"https://img1.baidu.com/it/u=1601695551,235775011&fm=26&fmt=auto"},title:{type:String,default:""},price:{type:[String,Number],default:""},salesCount:{type:[String,Number],default:""},stock:{type:[String,Number],default:""}},setup(t){const{push:l}=d();return(_,e)=>{const c=x,u=y;return m(),n("div",{class:"product-warp",style:{cursor:"pointer"},onClick:e[1]||(e[1]=i(j=>{return r=t.spuId,void l({name:"ProductSpuDetail",params:{id:r}});var r},["stop"]))},[s("div",S,[p(c,{"initial-index":0,"preview-src-list":[t.picUrl],src:t.picUrl,class:"product-warp-left-img",fit:"contain","preview-teleported":"",onClick:e[0]||(e[0]=i(()=>{},["stop"]))},null,8,["preview-src-list","src"])]),s("div",I,[s("div",k,a(t.title),1),s("div",C,[s("span",N,"\u5E93\u5B58: "+a(t.stock||0),1),s("span",null,"\u9500\u91CF: "+a(t.salesCount||0),1)]),s("div",h,[s("span",P,"\uFFE5"+a(f(v)(t.price)),1),p(u,{size:"small",text:"",type:"primary"},{default:g(()=>e[2]||(e[2]=[w("\u8BE6\u60C5")])),_:1})])])])}}}),[["__scopeId","data-v-eb625f86"]]);export{U as default};
