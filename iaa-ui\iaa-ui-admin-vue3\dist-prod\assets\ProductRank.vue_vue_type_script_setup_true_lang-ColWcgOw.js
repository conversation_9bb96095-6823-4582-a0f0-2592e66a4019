import{d as U,r as n,f as x,q as S,A as d,o as w,w as p,J as R,g as e,K as z,a as o,L as N,M as D,i as F,k as I,f6 as j}from"./index-CRsFgzy0.js";import{_ as q}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{E as A}from"./el-image-BQpHFDaE.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-CWLo4KQn.js";import{P as J}from"./product-D_J6xZ01.js";import{C as K}from"./CardTitle-DCrrQp54.js";import{f as L}from"./formatter-D3GpDdeL.js";const M={class:"flex flex-row items-center justify-between"},$=U({name:"ProductRank",__name:"ProductRank",setup(B){const b=a=>`${a.browseConvertPercent}%`,f=a=>{r.sortingFields=[j(a)],l()},g=a=>{r.times=a,l()},h=n(),r=x({pageNo:1,pageSize:10,times:[],sortingFields:{}}),m=n(!1),c=n(0),u=n([]),l=async()=>{m.value=!0;try{const a=await J.getProductStatisticsRankPage(r);u.value=a.list,c.value=a.total}finally{m.value=!1}};return S(async()=>{await l()}),(a,i)=>{const v=E,t=N,P=A,C=z,y=q,_=I,k=D;return w(),d(_,{shadow:"never"},{header:p(()=>[F("div",M,[e(o(K),{title:"\u5546\u54C1\u6392\u884C"}),e(v,{ref_key:"shortcutDateRangePicker",ref:h,onChange:g},null,512)])]),default:p(()=>[R((w(),d(C,{data:o(u),onSortChange:f},{default:p(()=>[e(t,{label:"\u5546\u54C1 ID",prop:"spuId","min-width":"70"}),e(t,{label:"\u5546\u54C1\u56FE\u7247",align:"center",prop:"picUrl",width:"80"},{default:p(({row:s})=>[e(P,{src:s.picUrl,"preview-src-list":[s.picUrl],class:"h-30px w-30px","preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),e(t,{label:"\u5546\u54C1\u540D\u79F0",prop:"name","min-width":"200","show-overflow-tooltip":!0}),e(t,{label:"\u6D4F\u89C8\u91CF",prop:"browseCount","min-width":"90",sortable:"custom"}),e(t,{label:"\u8BBF\u5BA2\u6570",prop:"browseUserCount","min-width":"90",sortable:"custom"}),e(t,{label:"\u52A0\u8D2D\u4EF6\u6570",prop:"cartCount","min-width":"105",sortable:"custom"}),e(t,{label:"\u4E0B\u5355\u4EF6\u6570",prop:"orderCount","min-width":"105",sortable:"custom"}),e(t,{label:"\u652F\u4ED8\u4EF6\u6570",prop:"orderPayCount","min-width":"105",sortable:"custom"}),e(t,{label:"\u652F\u4ED8\u91D1\u989D",prop:"orderPayPrice","min-width":"105",sortable:"custom",formatter:o(L)},null,8,["formatter"]),e(t,{label:"\u6536\u85CF\u6570",prop:"favoriteCount","min-width":"90",sortable:"custom"}),e(t,{label:"\u8BBF\u5BA2-\u652F\u4ED8\u8F6C\u5316\u7387(%)",prop:"browseConvertPercent","min-width":"180",sortable:"custom",formatter:b})]),_:1},8,["data"])),[[k,o(m)]]),e(y,{total:o(c),page:o(r).pageNo,"onUpdate:page":i[0]||(i[0]=s=>o(r).pageNo=s),limit:o(r).pageSize,"onUpdate:limit":i[1]||(i[1]=s=>o(r).pageSize=s),onPagination:l},null,8,["total","page","limit"])]),_:1})}}});export{$ as _};
