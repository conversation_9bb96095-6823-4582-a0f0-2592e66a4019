import{d as q,b as A,p as G,r as m,f as H,A as V,o as b,w as t,J as j,s as D,a,g as d,v as J,P as M,M as R,G as z,H as k,m as B}from"./index-CRsFgzy0.js";import{_ as E}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{g as I,e as K,i as L}from"./property-CzC0z5Eb.js";const N=q({name:"ProductPropertyForm",__name:"PropertyForm",emits:["success"],setup(O,{expose:g,emit:w}){const{t:n}=A(),c=G(),o=m(!1),p=m(""),r=m(!1),f=m(""),s=m({id:void 0,name:""}),P=H({name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=m();g({open:async(l,e)=>{if(o.value=!0,p.value=n("action."+l),f.value=l,F(),e){r.value=!0;try{s.value=await I(e)}finally{r.value=!1}}}});const h=w,x=async()=>{if(i&&await i.value.validate()){r.value=!0;try{const l=s.value;f.value==="create"?(await K(l),c.success(n("common.createSuccess"))):(await L(l),c.success(n("common.updateSuccess"))),o.value=!1,h("success")}finally{r.value=!1}}},F=()=>{var l;s.value={id:void 0,name:""},(l=i.value)==null||l.resetFields()};return(l,e)=>{const v=M,y=J,U=D,_=z,C=E,S=R;return b(),V(C,{modelValue:a(o),"onUpdate:modelValue":e[3]||(e[3]=u=>B(o)?o.value=u:null),title:a(p)},{footer:t(()=>[d(_,{disabled:a(r),type:"primary",onClick:x},{default:t(()=>e[4]||(e[4]=[k("\u786E \u5B9A")])),_:1},8,["disabled"]),d(_,{onClick:e[2]||(e[2]=u=>o.value=!1)},{default:t(()=>e[5]||(e[5]=[k("\u53D6 \u6D88")])),_:1})]),default:t(()=>[j((b(),V(U,{ref_key:"formRef",ref:i,model:a(s),rules:a(P),"label-width":"80px"},{default:t(()=>[d(y,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[d(v,{modelValue:a(s).name,"onUpdate:modelValue":e[0]||(e[0]=u=>a(s).name=u),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1}),d(y,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[d(v,{modelValue:a(s).remark,"onUpdate:modelValue":e[1]||(e[1]=u=>a(s).remark=u),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[S,a(r)]])]),_:1},8,["modelValue","title"])}}});export{N as _};
