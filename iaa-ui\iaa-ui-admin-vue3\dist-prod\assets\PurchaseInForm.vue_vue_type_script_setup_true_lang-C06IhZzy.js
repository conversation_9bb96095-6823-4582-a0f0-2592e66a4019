import{d as le,b as te,p as oe,r as n,f as ue,a2 as de,aK as re,c as U,o as c,F as w,g as a,a as t,m as R,w as o,J as ie,A as f,s as se,E as ne,h as ce,v as me,P as pe,C as fe,G as ve,H as y,I as _e,x as be,y as q,B as Ve,c2 as Pe,l as he,n as Ie,an as Ue,ea as k,M as we,a3 as ye,eb as ke}from"./index-CRsFgzy0.js";import{_ as ge}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as xe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{P as g}from"./index-C_Usxdvp.js";import{_ as Se}from"./PurchaseInItemForm.vue_vue_type_script_setup_true_lang-BFm0vNQA.js";import{A as Ce}from"./index-DkE0YaRG.js";import{_ as Te}from"./PurchaseOrderInEnableList.vue_vue_type_script_setup_true_lang-BzHizmsl.js";import{g as Fe}from"./index-D4y5Z4cM.js";import{S as Ne}from"./index-BXOKnbFS.js";const Le=le({name:"PurchaseInForm",__name:"PurchaseInForm",emits:["success"],setup(Ae,{expose:B,emit:G}){const{t:v}=te(),x=oe(),m=n(!1),S=n(""),p=n(!1),_=n(""),l=n({id:void 0,supplierId:void 0,accountId:void 0,inTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),H=ue({supplierId:[{required:!0,message:"\u4F9B\u5E94\u5546\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],inTime:[{required:!0,message:"\u5165\u5E93\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),b=de(()=>_.value==="detail"),V=n(),C=n([]),P=n([]),J=n([]),h=n("item"),T=n();re(()=>l.value,d=>{if(!d)return;const e=d.items.reduce((r,i)=>r+i.totalPrice,0),s=d.discountPercent!=null?ke(e,d.discountPercent/100):0;l.value.discountPrice=s,l.value.totalPrice=e-s+d.otherPrice},{deep:!0}),B({open:async(d,e)=>{if(m.value=!0,S.value=v("action."+d),_.value=d,Y(),e){p.value=!0;try{l.value=await g.getPurchaseIn(e)}finally{p.value=!1}}C.value=await Ne.getSupplierSimpleList(),J.value=await Fe(),P.value=await Ce.getAccountSimpleList();const s=P.value.find(r=>r.defaultStatus);s&&(l.value.accountId=s.id)}});const F=n(),M=()=>{F.value.open()},O=d=>{l.value.orderId=d.id,l.value.orderNo=d.no,l.value.supplierId=d.supplierId,l.value.accountId=d.accountId,l.value.discountPercent=d.discountPercent,l.value.remark=d.remark,l.value.fileUrl=d.fileUrl,d.items.forEach(e=>{e.totalCount=e.count,e.count=e.totalCount-e.inCount,e.orderItemId=e.id,e.id=void 0}),l.value.items=d.items.filter(e=>e.count>0)},K=G,W=async()=>{await V.value.validate(),await T.value.validate(),p.value=!0;try{const d=l.value;_.value==="create"?(await g.createPurchaseIn(d),x.success(v("common.createSuccess"))):(await g.updatePurchaseIn(d),x.success(v("common.updateSuccess"))),m.value=!1,K("success")}finally{p.value=!1}},Y=()=>{var d;l.value={id:void 0,supplierId:void 0,accountId:void 0,inTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(d=V.value)==null||d.resetFields()};return(d,e)=>{const s=pe,r=me,i=ce,j=fe,z=_e,I=ve,N=Ve,L=be,D=Pe,A=ne,Q=Ie,X=he,Z=xe,E=Ue,$=se,ee=ge,ae=we;return c(),U(w,null,[a(ee,{title:t(S),modelValue:t(m),"onUpdate:modelValue":e[13]||(e[13]=u=>R(m)?m.value=u:null),width:"1440"},{footer:o(()=>[t(b)?ye("",!0):(c(),f(I,{key:0,onClick:W,type:"primary",disabled:t(p)},{default:o(()=>e[15]||(e[15]=[y(" \u786E \u5B9A ")])),_:1},8,["disabled"])),a(I,{onClick:e[12]||(e[12]=u=>m.value=!1)},{default:o(()=>e[16]||(e[16]=[y("\u53D6 \u6D88")])),_:1})]),default:o(()=>[ie((c(),f($,{ref_key:"formRef",ref:V,model:t(l),rules:t(H),"label-width":"100px",disabled:t(b)},{default:o(()=>[a(A,{gutter:20},{default:o(()=>[a(i,{span:8},{default:o(()=>[a(r,{label:"\u5165\u5E93\u5355\u53F7",prop:"no"},{default:o(()=>[a(s,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":e[0]||(e[0]=u=>t(l).no=u),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u5165\u5E93\u65F6\u95F4",prop:"inTime"},{default:o(()=>[a(j,{modelValue:t(l).inTime,"onUpdate:modelValue":e[1]||(e[1]=u=>t(l).inTime=u),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u5165\u5E93\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:o(()=>[a(s,{modelValue:t(l).orderNo,"onUpdate:modelValue":e[2]||(e[2]=u=>t(l).orderNo=u),readonly:""},{append:o(()=>[a(I,{onClick:M},{default:o(()=>[a(z,{icon:"ep:search"}),e[14]||(e[14]=y(" \u9009\u62E9 "))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:o(()=>[a(L,{modelValue:t(l).supplierId,"onUpdate:modelValue":e[3]||(e[3]=u=>t(l).supplierId=u),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:o(()=>[(c(!0),U(w,null,q(t(C),u=>(c(),f(N,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(i,{span:16},{default:o(()=>[a(r,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[a(s,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":e[4]||(e[4]=u=>t(l).remark=u),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:o(()=>[a(D,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":e[5]||(e[5]=u=>t(l).fileUrl=u),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(Z,null,{default:o(()=>[a(X,{modelValue:t(h),"onUpdate:modelValue":e[6]||(e[6]=u=>R(h)?h.value=u:null),class:"-mt-15px -mb-10px"},{default:o(()=>[a(Q,{label:"\u5165\u5E93\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:o(()=>[a(Se,{ref_key:"itemFormRef",ref:T,items:t(l).items,disabled:t(b)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(A,{gutter:20},{default:o(()=>[a(i,{span:8},{default:o(()=>[a(r,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:o(()=>[a(E,{modelValue:t(l).discountPercent,"onUpdate:modelValue":e[7]||(e[7]=u=>t(l).discountPercent=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u4ED8\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:o(()=>[a(s,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":e[8]||(e[8]=u=>t(l).discountPrice=u),formatter:t(k)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:o(()=>[a(s,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(k)},null,8,["model-value","formatter"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:o(()=>[a(E,{modelValue:t(l).otherPrice,"onUpdate:modelValue":e[9]||(e[9]=u=>t(l).otherPrice=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:o(()=>[a(L,{modelValue:t(l).accountId,"onUpdate:modelValue":e[10]||(e[10]=u=>t(l).accountId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:o(()=>[(c(!0),U(w,null,q(t(P),u=>(c(),f(N,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:o(()=>[a(r,{label:"\u5E94\u4ED8\u91D1\u989D"},{default:o(()=>[a(s,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":e[11]||(e[11]=u=>t(l).totalPrice=u),formatter:t(k)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[ae,t(p)]])]),_:1},8,["title","modelValue"]),a(Te,{ref_key:"purchaseOrderInEnableListRef",ref:F,onSuccess:O},null,512)],64)}}});export{Le as _};
