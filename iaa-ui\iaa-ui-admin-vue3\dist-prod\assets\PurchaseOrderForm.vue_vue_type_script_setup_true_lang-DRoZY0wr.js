import{d as Z,b as $,p as ee,r as i,f as le,a2 as ae,aK as te,A as f,o as n,w as d,J as de,s as oe,a,g as e,E as ue,h as re,v as se,P as ie,C as ce,x as ne,c as O,F as q,y as A,B as me,c2 as pe,l as fe,m as C,n as ve,an as _e,ea as L,M as Ve,a3 as be,G as Pe,H as E,eb as he}from"./index-CRsFgzy0.js";import{_ as we}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Ue}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{P as w}from"./index-DNPnNHGA.js";import{_ as ye}from"./PurchaseOrderItemForm.vue_vue_type_script_setup_true_lang-sFFBxHek.js";import{S as ge}from"./index-BXOKnbFS.js";import{g as ke}from"./index-D4y5Z4cM.js";import{A as Ie}from"./index-DkE0YaRG.js";const xe=Z({name:"PurchaseOrderForm",__name:"PurchaseOrderForm",emits:["success"],setup(Se,{expose:G,emit:J}){const{t:v}=$(),U=ee(),m=i(!1),y=i(""),p=i(!1),_=i(""),o=i({id:void 0,supplierId:void 0,accountId:void 0,orderTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[],no:void 0}),K=le({supplierId:[{required:!0,message:"\u4F9B\u5E94\u5546\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],orderTime:[{required:!0,message:"\u8BA2\u5355\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=ae(()=>_.value==="detail"),b=i(),g=i([]),P=i([]),R=i([]),h=i("item"),k=i();te(()=>o.value,u=>{if(!u)return;const l=u.items.reduce((r,s)=>r+s.totalPrice,0),c=u.discountPercent!=null?he(l,u.discountPercent/100):0;o.value.discountPrice=c,o.value.totalPrice=l-c},{deep:!0}),G({open:async(u,l)=>{if(m.value=!0,y.value=v("action."+u),_.value=u,D(),l){p.value=!0;try{o.value=await w.getPurchaseOrder(l)}finally{p.value=!1}}g.value=await ge.getSupplierSimpleList(),R.value=await ke(),P.value=await Ie.getAccountSimpleList();const c=P.value.find(r=>r.defaultStatus);c&&(o.value.accountId=c.id)}});const z=J,B=async()=>{await b.value.validate(),await k.value.validate(),p.value=!0;try{const u=o.value;_.value==="create"?(await w.createPurchaseOrder(u),U.success(v("common.createSuccess"))):(await w.updatePurchaseOrder(u),U.success(v("common.updateSuccess"))),m.value=!1,z("success")}finally{p.value=!1}},D=()=>{var u;o.value={id:void 0,supplierId:void 0,accountId:void 0,orderTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[]},(u=b.value)==null||u.resetFields()};return(u,l)=>{const c=ie,r=se,s=re,H=ce,I=me,x=ne,M=pe,S=ue,Q=ve,j=fe,N=Ue,T=_e,W=oe,F=Pe,X=we,Y=Ve;return n(),f(X,{title:a(y),modelValue:a(m),"onUpdate:modelValue":l[12]||(l[12]=t=>C(m)?m.value=t:null),width:"1080"},{footer:d(()=>[a(V)?be("",!0):(n(),f(F,{key:0,onClick:B,type:"primary",disabled:a(p)},{default:d(()=>l[13]||(l[13]=[E(" \u786E \u5B9A ")])),_:1},8,["disabled"])),e(F,{onClick:l[11]||(l[11]=t=>m.value=!1)},{default:d(()=>l[14]||(l[14]=[E("\u53D6 \u6D88")])),_:1})]),default:d(()=>[de((n(),f(W,{ref_key:"formRef",ref:b,model:a(o),rules:a(K),"label-width":"100px",disabled:a(V)},{default:d(()=>[e(S,{gutter:20},{default:d(()=>[e(s,{span:8},{default:d(()=>[e(r,{label:"\u8BA2\u5355\u5355\u53F7",prop:"no"},{default:d(()=>[e(c,{disabled:"",modelValue:a(o).no,"onUpdate:modelValue":l[0]||(l[0]=t=>a(o).no=t),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:d(()=>[e(r,{label:"\u8BA2\u5355\u65F6\u95F4",prop:"orderTime"},{default:d(()=>[e(H,{modelValue:a(o).orderTime,"onUpdate:modelValue":l[1]||(l[1]=t=>a(o).orderTime=t),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u8BA2\u5355\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:d(()=>[e(r,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:d(()=>[e(x,{modelValue:a(o).supplierId,"onUpdate:modelValue":l[2]||(l[2]=t=>a(o).supplierId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:d(()=>[(n(!0),O(q,null,A(a(g),t=>(n(),f(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:16},{default:d(()=>[e(r,{label:"\u5907\u6CE8",prop:"remark"},{default:d(()=>[e(c,{type:"textarea",modelValue:a(o).remark,"onUpdate:modelValue":l[3]||(l[3]=t=>a(o).remark=t),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:d(()=>[e(r,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:d(()=>[e(M,{"is-show-tip":!1,modelValue:a(o).fileUrl,"onUpdate:modelValue":l[4]||(l[4]=t=>a(o).fileUrl=t),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(N,null,{default:d(()=>[e(j,{modelValue:a(h),"onUpdate:modelValue":l[5]||(l[5]=t=>C(h)?h.value=t:null),class:"-mt-15px -mb-10px"},{default:d(()=>[e(Q,{label:"\u8BA2\u5355\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:d(()=>[e(ye,{ref_key:"itemFormRef",ref:k,items:a(o).items,disabled:a(V)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(S,{gutter:20},{default:d(()=>[e(s,{span:8},{default:d(()=>[e(r,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:d(()=>[e(T,{modelValue:a(o).discountPercent,"onUpdate:modelValue":l[6]||(l[6]=t=>a(o).discountPercent=t),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:d(()=>[e(r,{label:"\u4ED8\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:d(()=>[e(c,{disabled:"",modelValue:a(o).discountPrice,"onUpdate:modelValue":l[7]||(l[7]=t=>a(o).discountPrice=t),formatter:a(L)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:d(()=>[e(r,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:d(()=>[e(c,{disabled:"",modelValue:a(o).totalPrice,"onUpdate:modelValue":l[8]||(l[8]=t=>a(o).totalPrice=t),formatter:a(L)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:d(()=>[e(r,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:d(()=>[e(x,{modelValue:a(o).accountId,"onUpdate:modelValue":l[9]||(l[9]=t=>a(o).accountId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:d(()=>[(n(!0),O(q,null,A(a(P),t=>(n(),f(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:d(()=>[e(r,{label:"\u652F\u4ED8\u8BA2\u91D1",prop:"depositPrice"},{default:d(()=>[e(T,{modelValue:a(o).depositPrice,"onUpdate:modelValue":l[10]||(l[10]=t=>a(o).depositPrice=t),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u652F\u4ED8\u8BA2\u91D1",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[Y,a(p)]])]),_:1},8,["title","modelValue"])}}});export{xe as _};
