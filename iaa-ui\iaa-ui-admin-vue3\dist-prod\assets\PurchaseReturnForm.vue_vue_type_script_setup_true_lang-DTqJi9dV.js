import{d as le,b as te,p as ue,r as n,f as re,a2 as oe,aK as de,c as U,o as c,F as w,g as a,a as t,m as L,w as u,J as ie,A as f,s as se,E as ne,h as ce,v as me,P as pe,C as fe,G as ve,H as y,I as _e,x as be,y as q,B as Ve,c2 as Pe,l as he,n as Ie,an as Ue,ea as k,M as we,a3 as ye,eb as ke}from"./index-CRsFgzy0.js";import{_ as ge}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Re}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{P as g}from"./index-BZnyXCD7.js";import{_ as xe}from"./PurchaseReturnItemForm.vue_vue_type_script_setup_true_lang-DH7wKaDJ.js";import{S as Se}from"./index-BXOKnbFS.js";import{A as Ce}from"./index-DkE0YaRG.js";import{_ as Ne}from"./PurchaseOrderReturnEnableList.vue_vue_type_script_setup_true_lang-CFTBTY33.js";import{g as Te}from"./index-D4y5Z4cM.js";const Fe=le({name:"PurchaseReturnForm",__name:"PurchaseReturnForm",emits:["success"],setup(Ae,{expose:B,emit:H}){const{t:v}=te(),R=ue(),m=n(!1),x=n(""),p=n(!1),_=n(""),l=n({id:void 0,supplierId:void 0,accountId:void 0,returnTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),J=re({supplierId:[{required:!0,message:"\u4F9B\u5E94\u5546\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnTime:[{required:!0,message:"\u9000\u8D27\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),b=oe(()=>_.value==="detail"),V=n(),S=n([]),P=n([]),M=n([]),h=n("item"),C=n();de(()=>l.value,o=>{if(!o)return;const e=o.items.reduce((d,i)=>d+i.totalPrice,0),s=o.discountPercent!=null?ke(e,o.discountPercent/100):0;l.value.discountPrice=s,l.value.totalPrice=e-s+o.otherPrice},{deep:!0}),B({open:async(o,e)=>{if(m.value=!0,x.value=v("action."+o),_.value=o,W(),e){p.value=!0;try{l.value=await g.getPurchaseReturn(e)}finally{p.value=!1}}S.value=await Se.getSupplierSimpleList(),M.value=await Te(),P.value=await Ce.getAccountSimpleList();const s=P.value.find(d=>d.defaultStatus);s&&(l.value.accountId=s.id)}});const N=n(),D=()=>{N.value.open()},G=o=>{l.value.orderId=o.id,l.value.orderNo=o.no,l.value.supplierId=o.supplierId,l.value.accountId=o.accountId,l.value.discountPercent=o.discountPercent,l.value.remark=o.remark,l.value.fileUrl=o.fileUrl,o.items.forEach(e=>{e.count=e.inCount-e.returnCount,e.orderItemId=e.id,e.id=void 0}),l.value.items=o.items.filter(e=>e.count>0)},K=H,O=async()=>{await V.value.validate(),await C.value.validate(),p.value=!0;try{const o=l.value;_.value==="create"?(await g.createPurchaseReturn(o),R.success(v("common.createSuccess"))):(await g.updatePurchaseReturn(o),R.success(v("common.updateSuccess"))),m.value=!1,K("success")}finally{p.value=!1}},W=()=>{var o;l.value={id:void 0,supplierId:void 0,accountId:void 0,returnTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(o=V.value)==null||o.resetFields()};return(o,e)=>{const s=pe,d=me,i=ce,Y=fe,j=_e,I=ve,T=Ve,F=be,z=Pe,A=ne,Q=Ie,X=he,Z=Re,E=Ue,$=se,ee=ge,ae=we;return c(),U(w,null,[a(ee,{title:t(x),modelValue:t(m),"onUpdate:modelValue":e[13]||(e[13]=r=>L(m)?m.value=r:null),width:"1440"},{footer:u(()=>[t(b)?ye("",!0):(c(),f(I,{key:0,onClick:O,type:"primary",disabled:t(p)},{default:u(()=>e[15]||(e[15]=[y(" \u786E \u5B9A ")])),_:1},8,["disabled"])),a(I,{onClick:e[12]||(e[12]=r=>m.value=!1)},{default:u(()=>e[16]||(e[16]=[y("\u53D6 \u6D88")])),_:1})]),default:u(()=>[ie((c(),f($,{ref_key:"formRef",ref:V,model:t(l),rules:t(J),"label-width":"100px",disabled:t(b)},{default:u(()=>[a(A,{gutter:20},{default:u(()=>[a(i,{span:8},{default:u(()=>[a(d,{label:"\u9000\u8D27\u5355\u53F7",prop:"no"},{default:u(()=>[a(s,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":e[0]||(e[0]=r=>t(l).no=r),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u9000\u8D27\u65F6\u95F4",prop:"returnTime"},{default:u(()=>[a(Y,{modelValue:t(l).returnTime,"onUpdate:modelValue":e[1]||(e[1]=r=>t(l).returnTime=r),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u9000\u8D27\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:u(()=>[a(s,{modelValue:t(l).orderNo,"onUpdate:modelValue":e[2]||(e[2]=r=>t(l).orderNo=r),readonly:""},{append:u(()=>[a(I,{onClick:D},{default:u(()=>[a(j,{icon:"ep:search"}),e[14]||(e[14]=y(" \u9009\u62E9 "))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:u(()=>[a(F,{modelValue:t(l).supplierId,"onUpdate:modelValue":e[3]||(e[3]=r=>t(l).supplierId=r),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:u(()=>[(c(!0),U(w,null,q(t(S),r=>(c(),f(T,{key:r.id,label:r.name,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(i,{span:16},{default:u(()=>[a(d,{label:"\u5907\u6CE8",prop:"remark"},{default:u(()=>[a(s,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":e[4]||(e[4]=r=>t(l).remark=r),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:u(()=>[a(z,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":e[5]||(e[5]=r=>t(l).fileUrl=r),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(Z,null,{default:u(()=>[a(X,{modelValue:t(h),"onUpdate:modelValue":e[6]||(e[6]=r=>L(h)?h.value=r:null),class:"-mt-15px -mb-10px"},{default:u(()=>[a(Q,{label:"\u9000\u8D27\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:u(()=>[a(xe,{ref_key:"itemFormRef",ref:C,items:t(l).items,disabled:t(b)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(A,{gutter:20},{default:u(()=>[a(i,{span:8},{default:u(()=>[a(d,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:u(()=>[a(E,{modelValue:t(l).discountPercent,"onUpdate:modelValue":e[7]||(e[7]=r=>t(l).discountPercent=r),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u9000\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:u(()=>[a(s,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":e[8]||(e[8]=r=>t(l).discountPrice=r),formatter:t(k)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:u(()=>[a(s,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(k)},null,8,["model-value","formatter"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:u(()=>[a(E,{modelValue:t(l).otherPrice,"onUpdate:modelValue":e[9]||(e[9]=r=>t(l).otherPrice=r),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:u(()=>[a(F,{modelValue:t(l).accountId,"onUpdate:modelValue":e[10]||(e[10]=r=>t(l).accountId=r),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:u(()=>[(c(!0),U(w,null,q(t(P),r=>(c(),f(T,{key:r.id,label:r.name,value:r.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(i,{span:8},{default:u(()=>[a(d,{label:"\u5E94\u9000\u91D1\u989D",prop:"totalPrice"},{default:u(()=>[a(s,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":e[11]||(e[11]=r=>t(l).totalPrice=r),formatter:t(k)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[ae,t(p)]])]),_:1},8,["title","modelValue"]),a(Ne,{ref_key:"purchaseOrderReturnEnableListRef",ref:N,onSuccess:G},null,512)],64)}}});export{Fe as _};
