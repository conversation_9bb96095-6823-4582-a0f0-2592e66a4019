import{d as R,r as b,f as A,aK as v,q as j,J as G,M as H,a as n,A as h,o as V,w as l,g as e,K as J,a3 as y,L as M,v as z,x as D,c as O,F as Q,y as T,B as X,P as Y,eP as x,an as Z,ea as w,G as ee,H as ae,s as le,eN as te,eb as k}from"./index-CRsFgzy0.js";import{S as oe}from"./index-CxlZ4TTH.js";import{W as de}from"./index-mM5XVEAg.js";const re=R({__name:"PurchaseReturnItemForm",props:{items:{},disabled:{type:Boolean}},setup(I,{expose:N}){const q=I,B=b(!1),m=b([]),P=A({warehouseId:[{required:!0,message:"\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),g=b([]),_=b([]),U=b(void 0);v(()=>q.items,async i=>{i.forEach(o=>{var r;o.warehouseId==null&&(o.warehouseId=(r=U.value)==null?void 0:r.id),o.stockCount===null&&o.warehouseId!=null&&E(o)}),m.value=i},{immediate:!0}),v(()=>m.value,i=>{i&&i.length!==0&&i.forEach(o=>{o.totalProductPrice=k(o.productPrice,o.count),o.taxPrice=k(o.totalProductPrice,o.taxPercent/100),o.totalProductPrice!=null?o.totalPrice=o.totalProductPrice+(o.taxPrice||0):o.totalPrice=void 0})},{deep:!0});const S=i=>{const{columns:o,data:r}=i,p=[];return o.forEach((c,u)=>{if(u!==0)if(["count","totalProductPrice","taxPrice","totalPrice"].includes(c.property)){const s=te(r.map(f=>Number(f[c.property])));p[u]=c.property==="count"?x(s):w(s)}else p[u]="";else p[u]="\u5408\u8BA1"}),p},E=async i=>{if(!i.productId)return;const o=await oe.getStockCount(i.productId);i.stockCount=o||0};return N({validate:()=>g.value.validate()}),j(async()=>{_.value=await de.getWarehouseSimpleList(),U.value=_.value.find(i=>i.defaultStatus)}),(i,o)=>{const r=M,p=X,c=D,u=z,s=Y,f=Z,L=ee,W=J,F=le,K=H;return G((V(),h(F,{ref_key:"formRef",ref:g,model:n(m),rules:n(P),"label-width":"0px","inline-message":!0,disabled:i.disabled},{default:l(()=>[e(W,{data:n(m),"show-summary":"","summary-method":S,class:"-mt-10px"},{default:l(()=>{var C,$;return[e(r,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(r,{label:"\u4ED3\u5E93\u540D\u79F0","min-width":"125"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.warehouseId`,rules:n(P).warehouseId,class:"mb-0px!"},{default:l(()=>[e(c,{modelValue:a.warehouseId,"onUpdate:modelValue":d=>a.warehouseId=d,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",onChange:d=>i.onChangeWarehouse(d,a)},{default:l(()=>[(V(!0),O(Q,null,T(n(_),d=>(V(),h(p,{key:d.id,label:d.name,value:d.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.productName,"onUpdate:modelValue":t=>a.productName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(r,{label:"\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":t=>a.stockCount=t,formatter:n(x)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(r,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":t=>a.productBarCode=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(r,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":t=>a.productUnitName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),((C=n(m)[0])==null?void 0:C.inCount)!=null?(V(),h(r,{key:0,label:"\u5DF2\u51FA\u5E93",fixed:"right","min-width":"80"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.inCount,"onUpdate:modelValue":t=>a.inCount=t,formatter:n(x)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1})):y("",!0),(($=n(m)[0])==null?void 0:$.returnCount)!=null?(V(),h(r,{key:1,label:"\u5DF2\u9000\u8D27",fixed:"right","min-width":"80"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.returnCount,"onUpdate:modelValue":t=>a.returnCount=t,formatter:n(x)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1})):y("",!0),e(r,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.count`,rules:n(P).count,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.count,"onUpdate:modelValue":d=>a.count=d,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productPrice,"onUpdate:modelValue":d=>a.productPrice=d,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u91D1\u989D",prop:"totalProductPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.totalProductPrice`,class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.totalProductPrice,"onUpdate:modelValue":d=>a.totalProductPrice=d,formatter:n(w)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u7A0E\u7387\uFF08%\uFF09",fixed:"right","min-width":"115"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.taxPercent`,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.taxPercent,"onUpdate:modelValue":d=>a.taxPercent=d,"controls-position":"right",min:0,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u7A0E\u989D",prop:"taxPrice",fixed:"right","min-width":"120"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.taxPrice`,class:"mb-0px!"},{default:l(()=>[e(u,{prop:`${t}.taxPrice`,class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.taxPrice,"onUpdate:modelValue":d=>a.taxPrice=d,formatter:n(w)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u7A0E\u989D\u5408\u8BA1",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(s,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":d=>a.totalPrice=d,formatter:n(w)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.remark`,class:"mb-0px!"},{default:l(()=>[e(s,{modelValue:a.remark,"onUpdate:modelValue":d=>a.remark=d,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e(L,{disabled:n(m).length===1,onClick:t=>{return d=a,void m.value.splice(d,1);var d},link:""},{default:l(()=>o[0]||(o[0]=[ae(" \u2014 ")])),_:2},1032,["disabled","onClick"])]),_:1})]}),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[K,n(B)]])}}});export{re as _};
