import{d as x,r as d,f as v,q as w,c as _,o as p,F as k,g as a,k as P,w as o,a as e,J as R,M as A,A as q,L as I,e9 as L,K as D,eO as E}from"./index-CRsFgzy0.js";import{E as F}from"./el-skeleton-item-CZ5buDOR.js";import{_ as G}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{S as J}from"./rank-DcFXIK-C.js";const K=x({name:"ReceivablePriceRank",__name:"ReceivablePriceRank",props:{queryParams:{}},setup(c,{expose:u}){const g=c,t=d(!1),i=d([]),s=v({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09"},yAxis:{type:"category",name:"\u7B7E\u8BA2\u4EBA",nameGap:30}}),l=async()=>{t.value=!0;const r=await J.getReceivablePriceRank(g.queryParams);s.dataset&&s.dataset.source&&(s.dataset.source=E(r).reverse()),i.value=r,t.value=!1};return u({loadData:l}),w(()=>{l()}),(r,M)=>{const b=G,f=F,m=P,n=I,h=D,y=A;return p(),_(k,null,[a(m,{shadow:"never"},{default:o(()=>[a(f,{loading:e(t),animated:""},{default:o(()=>[a(b,{height:500,options:e(s)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(m,{shadow:"never",class:"mt-16px"},{default:o(()=>[R((p(),q(h,{data:e(i)},{default:o(()=>[a(n,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(n,{label:"\u7B7E\u8BA2\u4EBA",align:"center",prop:"nickname","min-width":"200"}),a(n,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(n,{label:"\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",align:"center",prop:"count","min-width":"200",formatter:e(L)},null,8,["formatter"])]),_:1},8,["data"])),[[y,e(t)]])]),_:1})],64)}}});export{K as _};
