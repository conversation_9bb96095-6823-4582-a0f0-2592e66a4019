import{d as R,p as U,b as h,r as n,f as j,A as p,o as c,w as o,J as q,s as A,a,g as i,v as H,P as J,M,G as S,H as v,m as z}from"./index-CRsFgzy0.js";import{_ as B}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{r as D}from"./comment-D7UpITKm.js";const E=R({name:"ProductComment",__name:"ReplyForm",emits:["success"],setup(G,{expose:f,emit:y}){const _=U(),{t:C}=h(),s=n(!1),u=n(!1),t=n({id:void 0,replyContent:void 0}),b=j({replyContent:[{required:!0,message:"\u56DE\u590D\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),r=n();f({open:async l=>{w(),t.value.id=l,s.value=!0}});const V=y,g=async()=>{var l;if(await((l=r==null?void 0:r.value)==null?void 0:l.validate())){u.value=!0;try{await D(t.value),_.success(C("common.createSuccess")),s.value=!1,V("success")}finally{u.value=!1}}},w=()=>{var l;t.value={id:void 0,replyContent:void 0},(l=r.value)==null||l.resetFields()};return(l,e)=>{const k=H,x=A,m=S,F=B,P=M;return c(),p(F,{title:"\u56DE\u590D",modelValue:a(s),"onUpdate:modelValue":e[2]||(e[2]=d=>z(s)?s.value=d:null)},{footer:o(()=>[i(m,{onClick:g,type:"primary",disabled:a(u)},{default:o(()=>e[3]||(e[3]=[v("\u786E \u5B9A ")])),_:1},8,["disabled"]),i(m,{onClick:e[1]||(e[1]=d=>s.value=!1)},{default:o(()=>e[4]||(e[4]=[v("\u53D6 \u6D88")])),_:1})]),default:o(()=>[q((c(),p(x,{ref_key:"formRef",ref:r,model:a(t),rules:a(b),"label-width":"100px"},{default:o(()=>[i(k,{label:"\u56DE\u590D\u5185\u5BB9",prop:"replyContent"},{default:o(()=>[i(a(J),{type:"textarea",modelValue:a(t).replyContent,"onUpdate:modelValue":e[0]||(e[0]=d=>a(t).replyContent=d)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[P,a(u)]])]),_:1},8,["modelValue"])}}});export{E as _};
