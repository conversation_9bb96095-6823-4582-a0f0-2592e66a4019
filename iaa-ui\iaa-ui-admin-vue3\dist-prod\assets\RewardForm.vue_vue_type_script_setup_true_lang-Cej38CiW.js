import{as as f,d as K,b as Q,p as W,r as n,f as X,A as g,o as p,w as r,J as Z,s as ee,a as l,g as d,a3 as R,v as ae,P as le,C as te,aB as oe,c as h,F as k,y as O,R as U,D as q,aC as re,H as V,t as x,m as G,M as de,G as ue,aR as Y,aP as se,e$ as F,aG as ce}from"./index-CRsFgzy0.js";import{_ as pe}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as ie}from"./RewardRule.vue_vue_type_script_setup_true_lang-DDACf8cK.js";import ne from"./SpuShowcase-DtIWpNCA.js";import{k as i,l as _}from"./constants-uird_4gU.js";import{_ as me}from"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";const ye=async m=>await f.get({url:"/promotion/reward-activity/page",params:m}),ve=async m=>await f.delete({url:"/promotion/reward-activity/delete?id="+m}),fe=async m=>await f.put({url:"/promotion/reward-activity/close?id="+m}),ge=K({name:"ProductBrandForm",__name:"RewardForm",emits:["success"],setup(m,{expose:L,emit:N}){const{t:T}=Q(),w=W(),y=n(!1),b=n(""),v=n(!1),C=n(""),a=n({conditionType:_.PRICE.type,productScope:i.ALL.scope,rules:[]}),D=X({name:[{required:!0,message:"\u6D3B\u52A8\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],startAndEndTime:[{required:!0,message:"\u6D3B\u52A8\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],conditionType:[{required:!0,message:"\u6761\u4EF6\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],productScope:[{required:!0,message:"\u5546\u54C1\u8303\u56F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productSpuIds:[{required:!0,message:"\u5546\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productCategoryIds:[{required:!0,message:"\u5546\u54C1\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),S=n(),I=n();L({open:async(s,e)=>{var o;if(y.value=!0,b.value=T("action."+s),C.value=s,H(),e){v.value=!0;try{const u=await(async c=>await f.get({url:"/promotion/reward-activity/get?id="+c}))(e);u.startAndEndTime=[u.startTime,u.endTime],(o=u.rules)==null||o.forEach(c=>{c.discountPrice=Y(c.discountPrice||0),u.conditionType===_.PRICE.type&&(c.limit=Y(c.limit||0))}),a.value=u,await J()}finally{v.value=!1}}}});const M=N,B=async()=>{var s;if(S.value&&await S.value.validate()){v.value=!0;try{(s=I.value)==null||s.setRuleCoupon();const e=se(a.value);e.startTime=e.startAndEndTime[0],e.endTime=e.startAndEndTime[1],delete e.startAndEndTime,e.rules.forEach(o=>{o.discountPrice=F(o.discountPrice||0),e.conditionType===_.PRICE.type&&(o.limit=F(o.limit||0))}),function(o){switch(a.value.productScope){case i.SPU.scope:o.productScopeValues=a.value.productSpuIds;break;case i.CATEGORY.scope:o.productScopeValues=Array.isArray(a.value.productCategoryIds)?a.value.productCategoryIds:[a.value.productCategoryIds]}}(e),C.value==="create"?(await(async o=>await f.post({url:"/promotion/reward-activity/create",data:o}))(e),w.success(T("common.createSuccess"))):(await(async o=>await f.put({url:"/promotion/reward-activity/update",data:o}))(e),w.success(T("common.updateSuccess"))),y.value=!1,M("success")}finally{v.value=!1}}},H=()=>{a.value={conditionType:_.PRICE.type,productScope:i.ALL.scope,rules:[]}},J=async()=>{switch(a.value.productScope){case i.SPU.scope:a.value.productSpuIds=a.value.productScopeValues;break;case i.CATEGORY.scope:await ce();let s=a.value.productScopeValues;Array.isArray(s)&&s.length===1&&(s=s[0]),a.value.productCategoryIds=s}};return(s,e)=>{const o=le,u=ae,c=te,A=re,E=oe,$=ee,P=ue,j=pe,z=de;return p(),g(j,{modelValue:l(y),"onUpdate:modelValue":e[9]||(e[9]=t=>G(y)?y.value=t:null),title:l(b),width:"65%"},{footer:r(()=>[d(P,{disabled:l(v),type:"primary",onClick:B},{default:r(()=>e[10]||(e[10]=[V("\u786E \u5B9A")])),_:1},8,["disabled"]),d(P,{onClick:e[8]||(e[8]=t=>y.value=!1)},{default:r(()=>e[11]||(e[11]=[V("\u53D6 \u6D88")])),_:1})]),default:r(()=>[Z((p(),g($,{ref_key:"formRef",ref:S,model:l(a),rules:l(D),"label-width":"80px"},{default:r(()=>[d(u,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name"},{default:r(()=>[d(o,{modelValue:l(a).name,"onUpdate:modelValue":e[0]||(e[0]=t=>l(a).name=t),placeholder:"\u8BF7\u8F93\u5165\u6D3B\u52A8\u540D\u79F0"},null,8,["modelValue"])]),_:1}),d(u,{label:"\u6D3B\u52A8\u65F6\u95F4",prop:"startAndEndTime"},{default:r(()=>[d(c,{modelValue:l(a).startAndEndTime,"onUpdate:modelValue":e[1]||(e[1]=t=>l(a).startAndEndTime=t),"end-placeholder":l(T)("common.endTimeText"),"start-placeholder":l(T)("common.startTimeText"),"range-separator":"-",type:"datetimerange","value-format":"x"},null,8,["modelValue","end-placeholder","start-placeholder"])]),_:1}),d(u,{label:"\u6761\u4EF6\u7C7B\u578B",prop:"conditionType"},{default:r(()=>[d(E,{modelValue:l(a).conditionType,"onUpdate:modelValue":e[2]||(e[2]=t=>l(a).conditionType=t)},{default:r(()=>[(p(!0),h(k,null,O(l(U)(l(q).PROMOTION_CONDITION_TYPE),t=>(p(),g(A,{key:t.value,label:t.value},{default:r(()=>[V(x(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(u,{label:"\u4F18\u60E0\u8BBE\u7F6E"},{default:r(()=>[d(ie,{ref_key:"rewardRuleRef",ref:I,modelValue:l(a),"onUpdate:modelValue":e[3]||(e[3]=t=>G(a)?a.value=t:null)},null,8,["modelValue"])]),_:1}),d(u,{label:"\u6D3B\u52A8\u8303\u56F4",prop:"productScope"},{default:r(()=>[d(E,{modelValue:l(a).productScope,"onUpdate:modelValue":e[4]||(e[4]=t=>l(a).productScope=t)},{default:r(()=>[(p(!0),h(k,null,O(l(U)(l(q).PROMOTION_PRODUCT_SCOPE),t=>(p(),g(A,{key:t.value,label:t.value},{default:r(()=>[V(x(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(a).productScope===l(i).SPU.scope?(p(),g(u,{key:0,prop:"productSpuIds"},{default:r(()=>[d(ne,{modelValue:l(a).productSpuIds,"onUpdate:modelValue":e[5]||(e[5]=t=>l(a).productSpuIds=t)},null,8,["modelValue"])]),_:1})):R("",!0),l(a).productScope===l(i).CATEGORY.scope?(p(),g(u,{key:1,label:"\u5206\u7C7B",prop:"productCategoryIds"},{default:r(()=>[d(me,{modelValue:l(a).productCategoryIds,"onUpdate:modelValue":e[6]||(e[6]=t=>l(a).productCategoryIds=t),multiple:!0},null,8,["modelValue"])]),_:1})):R("",!0),d(u,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[d(o,{modelValue:l(a).remark,"onUpdate:modelValue":e[7]||(e[7]=t=>l(a).remark=t),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[z,l(v)]])]),_:1},8,["modelValue","title"])}}});export{ge as _,fe as c,ve as d,ye as g};
