import{d as I,f5 as T,r as A,A as i,o as s,E as L,w as a,c as S,a3 as U,g as l,a as d,F as j,y as F,h as H,i as f,t as k,G as q,H as u,s as z,v as B,an as J,P as K,a0 as M,aE as N,dD as w}from"./index-CRsFgzy0.js";import O from"./RewardRuleCouponSelect-Dq55MwAi.js";import{l as R}from"./constants-uird_4gU.js";const Q={class:"font-bold"},W=I({name:"RewardRule",__name:"RewardRule",props:{modelValue:{}},emits:["update:modelValue","deleteRule"],setup(h,{expose:b,emit:C}){const n=T(h,"modelValue",C),c=A(),g=()=>{w(n.value.rules)&&(n.value.rules=[]),n.value.rules.push({limit:0,discountPrice:0,freeDelivery:!1,point:0})};return b({setRuleCoupon:()=>{var V;w(c.value)||((V=c.value)==null||V.forEach(e=>e.setGiveCouponList()))}}),(V,e)=>{const _=q,y=J,x=K,r=B,p=H,P=M,E=z,D=N,G=L;return s(),i(G,null,{default:a(()=>[d(n).rules?(s(!0),S(j,{key:0},F(d(n).rules,(o,m)=>(s(),i(p,{key:m,span:24},{default:a(()=>[f("span",Q,"\u6D3B\u52A8\u5C42\u7EA7"+k(m+1),1),m!==0?(s(),i(_,{key:0,link:"",type:"danger",onClick:t=>{return v=m,void n.value.rules.splice(v,1);var v}},{default:a(()=>e[0]||(e[0]=[u(" \u5220\u9664 ")])),_:2},1032,["onClick"])):U("",!0),l(E,{ref_for:!0,ref:"formRef",model:o},{default:a(()=>[l(r,{label:"\u4F18\u60E0\u95E8\u69DB:","label-width":"100px",prop:"limit"},{default:a(()=>[e[1]||(e[1]=u(" \u6EE1 ")),d(R).PRICE.type===d(n).conditionType?(s(),i(y,{key:0,modelValue:o.limit,"onUpdate:modelValue":t=>o.limit=t,min:0,precision:2,step:.1,class:"w-150px! p-x-20px!",placeholder:"",type:"number","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])):(s(),i(x,{key:1,modelValue:o.limit,"onUpdate:modelValue":t=>o.limit=t,min:0,class:"w-150px! p-x-20px!",placeholder:"",type:"number"},null,8,["modelValue","onUpdate:modelValue"])),u(" "+k(d(R).PRICE.type===d(n).conditionType?"\u5143":"\u4EF6"),1)]),_:2},1024),l(r,{label:"\u4F18\u60E0\u5185\u5BB9:","label-width":"100px"},{default:a(()=>[l(p,{span:24},{default:a(()=>[e[4]||(e[4]=u(" \u8BA2\u5355\u91D1\u989D\u4F18\u60E0 ")),l(r,null,{default:a(()=>[e[2]||(e[2]=u(" \u51CF ")),l(y,{modelValue:o.discountPrice,"onUpdate:modelValue":t=>o.discountPrice=t,min:0,precision:2,step:.1,class:"w-150px! p-x-20px!","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"]),e[3]||(e[3]=u(" \u5143 "))]),_:2},1024)]),_:2},1024),l(p,{span:24},{default:a(()=>[e[5]||(e[5]=f("span",null,"\u5305\u90AE\uFF1A",-1)),l(P,{modelValue:o.freeDelivery,"onUpdate:modelValue":t=>o.freeDelivery=t,"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(p,{span:24},{default:a(()=>[e[8]||(e[8]=f("span",null,"\u9001\u79EF\u5206\uFF1A",-1)),l(r,null,{default:a(()=>[e[6]||(e[6]=u(" \u9001 ")),l(x,{modelValue:o.point,"onUpdate:modelValue":t=>o.point=t,class:"w-150px! p-x-20px!",placeholder:"",type:"number"},null,8,["modelValue","onUpdate:modelValue"]),e[7]||(e[7]=u(" \u79EF\u5206 "))]),_:2},1024)]),_:2},1024),l(p,{span:24},{default:a(()=>[e[9]||(e[9]=f("span",null,"\u9001\u4F18\u60E0\u5238\uFF1A",-1)),l(O,{ref_for:!0,ref_key:"rewardRuleCouponSelectRef",ref:c,modelValue:o,"onUpdate:modelValue":t=>o=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1024)]),_:2},1032,["model"])]),_:2},1024))),128)):U("",!0),l(p,{span:24,class:"mt-10px"},{default:a(()=>[l(_,{type:"primary",onClick:g},{default:a(()=>e[10]||(e[10]=[u("\u6DFB\u52A0\u4F18\u60E0\u89C4\u5219")])),_:1})]),_:1}),l(p,{span:24},{default:a(()=>[l(D,{type:"warning"},{default:a(()=>e[11]||(e[11]=[u(" \u8D60\u9001\u79EF\u5206\u4E3A 0 \u65F6\u4E0D\u8D60\u9001\u3002\u672A\u9009\u4F18\u60E0\u5238\u65F6\u4E0D\u8D60\u9001\u3002")])),_:1})]),_:1})]),_:1})}}});export{W as _};
