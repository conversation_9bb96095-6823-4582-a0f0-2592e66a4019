import{d as G,b as J,p as q,r as d,f as z,A as w,o as C,w as l,J as B,s as D,a as o,g as s,v as L,aE as O,H as c,t as V,k as P,cr as Q,a0 as T,m as h,M as W,G as X}from"./index-CRsFgzy0.js";import{_ as Y}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as Z,h as $}from"./tree-COGD3qag.js";import{g as ee}from"./index-De4pDBF3.js";import{g as ae,a as le}from"./index-CyE4zHnc.js";const se=G({name:"SystemRoleAssignMenuForm",__name:"RoleAssignMenuForm",emits:["success"],setup(te,{expose:g,emit:b}){const{t:I}=J(),M=q(),n=d(!1),i=d(!1),t=z({id:void 0,name:"",code:"",menuIds:[]}),p=d(),y=d([]),m=d(!1),u=d(),v=d(!1);g({open:async a=>{n.value=!0,F(),y.value=$(await ee()),t.id=a.id,t.name=a.name,t.code=a.code,i.value=!0;try{t.value.menuIds=await ae(a.id),t.value.menuIds.forEach(e=>{u.value.setChecked(e,!0,!1)})}finally{i.value=!1}}});const R=b,A=async()=>{if(p&&await p.value.validate()){i.value=!0;try{const a={roleId:t.id,menuIds:[...u.value.getCheckedKeys(!1),...u.value.getHalfCheckedKeys()]};await le(a),M.success(I("common.updateSuccess")),n.value=!1,R("success")}finally{i.value=!1}}},F=()=>{var a,e;v.value=!1,m.value=!1,t.value={id:void 0,name:"",code:"",menuIds:[]},(a=u.value)==null||a.setCheckedNodes([]),(e=p.value)==null||e.resetFields()},U=()=>{u.value.setCheckedNodes(v.value?y.value:[])},E=()=>{var e;const a=(e=u.value)==null?void 0:e.store.nodesMap;for(let f in a)a[f].expanded!==m.value&&(a[f].expanded=m.value)};return(a,e)=>{const f=O,_=L,k=T,H=Q,K=P,N=D,x=X,S=Y,j=W;return C(),w(S,{modelValue:o(n),"onUpdate:modelValue":e[3]||(e[3]=r=>h(n)?n.value=r:null),title:"\u83DC\u5355\u6743\u9650"},{footer:l(()=>[s(x,{disabled:o(i),type:"primary",onClick:A},{default:l(()=>e[6]||(e[6]=[c("\u786E \u5B9A")])),_:1},8,["disabled"]),s(x,{onClick:e[2]||(e[2]=r=>n.value=!1)},{default:l(()=>e[7]||(e[7]=[c("\u53D6 \u6D88")])),_:1})]),default:l(()=>[B((C(),w(N,{ref_key:"formRef",ref:p,model:o(t),"label-width":"80px"},{default:l(()=>[s(_,{label:"\u89D2\u8272\u540D\u79F0"},{default:l(()=>[s(f,null,{default:l(()=>[c(V(o(t).name),1)]),_:1})]),_:1}),s(_,{label:"\u89D2\u8272\u6807\u8BC6"},{default:l(()=>[s(f,null,{default:l(()=>[c(V(o(t).code),1)]),_:1})]),_:1}),s(_,{label:"\u83DC\u5355\u6743\u9650"},{default:l(()=>[s(K,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:l(()=>[e[4]||(e[4]=c(" \u5168\u9009/\u5168\u4E0D\u9009: ")),s(k,{modelValue:o(v),"onUpdate:modelValue":e[0]||(e[0]=r=>h(v)?v.value=r:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:U},null,8,["modelValue"]),e[5]||(e[5]=c(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),s(k,{modelValue:o(m),"onUpdate:modelValue":e[1]||(e[1]=r=>h(m)?m.value=r:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:E},null,8,["modelValue"])]),default:l(()=>[s(H,{ref_key:"treeRef",ref:u,data:o(y),props:o(Z),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u5019","node-key":"id","show-checkbox":""},null,8,["data","props"])]),_:1})]),_:1})]),_:1},8,["model"])),[[j,o(i)]])]),_:1},8,["modelValue"])}}});export{se as _};
