import{d as B,b as H,p as J,r as u,f as K,A as h,o as f,w as d,J as Y,a3 as j,s as z,a,g as s,v as L,aE as Q,H as n,t as w,x as W,c as X,F as Z,y as $,R as ee,D as ae,B as le,M as te,k as oe,cr as de,a0 as se,m as y,G as ue,aG as ce}from"./index-CRsFgzy0.js";import{_ as ne}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as pe,h as ie}from"./tree-COGD3qag.js";import{q as C}from"./constants-uird_4gU.js";import{g as me}from"./index-C0yL_L5C.js";import{b as re}from"./index-CyE4zHnc.js";const ve=B({name:"SystemRoleDataPermissionForm",__name:"RoleDataPermissionForm",emits:["success"],setup(fe,{expose:D,emit:E}){const{t:U}=H(),M=J(),p=u(!1),_=u(!1),o=K({id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]}),V=u(),x=u([]),i=u(!0),m=u(),r=u(!1),v=u(!0);D({open:async l=>{var e;p.value=!0,g(),x.value=ie(await me()),o.id=l.id,o.name=l.name,o.code=l.code,o.dataScope=l.dataScope,await ce(),(e=l.dataScopeDeptIds)==null||e.forEach(c=>{m.value.setChecked(c,!0,!1)})}});const R=E,T=async()=>{_.value=!0;try{const l={roleId:o.id,dataScope:o.dataScope,dataScopeDeptIds:o.dataScope!==C.DEPT_CUSTOM?[]:m.value.getCheckedKeys(!1)};await re(l),M.success(U("common.updateSuccess")),p.value=!1,R("success")}finally{_.value=!1}},g=()=>{var l,e;r.value=!1,i.value=!0,v.value=!0,o.value={id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]},(l=m.value)==null||l.setCheckedNodes([]),(e=V.value)==null||e.resetFields()},F=()=>{var e;const l=(e=m.value)==null?void 0:e.store.nodesMap;for(let c in l)l[c].expanded!==i.value&&(l[c].expanded=i.value)};return(l,e)=>{const c=Q,S=L,I=le,P=W,A=z,k=se,G=de,N=oe,b=ue,O=ne,q=te;return f(),h(O,{modelValue:a(p),"onUpdate:modelValue":e[6]||(e[6]=t=>y(p)?p.value=t:null),title:"\u6570\u636E\u6743\u9650",width:"800"},{footer:d(()=>[s(b,{disabled:a(_),type:"primary",onClick:T},{default:d(()=>e[10]||(e[10]=[n("\u786E \u5B9A")])),_:1},8,["disabled"]),s(b,{onClick:e[5]||(e[5]=t=>p.value=!1)},{default:d(()=>e[11]||(e[11]=[n("\u53D6 \u6D88")])),_:1})]),default:d(()=>[Y((f(),h(A,{ref_key:"formRef",ref:V,model:a(o),"label-width":"80px"},{default:d(()=>[s(S,{label:"\u89D2\u8272\u540D\u79F0"},{default:d(()=>[s(c,null,{default:d(()=>[n(w(a(o).name),1)]),_:1})]),_:1}),s(S,{label:"\u89D2\u8272\u6807\u8BC6"},{default:d(()=>[s(c,null,{default:d(()=>[n(w(a(o).code),1)]),_:1})]),_:1}),s(S,{label:"\u6743\u9650\u8303\u56F4"},{default:d(()=>[s(P,{modelValue:a(o).dataScope,"onUpdate:modelValue":e[0]||(e[0]=t=>a(o).dataScope=t)},{default:d(()=>[(f(!0),X(Z,null,$(a(ee)(a(ae).SYSTEM_DATA_SCOPE),t=>(f(),h(I,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[q,a(_)]]),a(o).dataScope===a(C).DEPT_CUSTOM?(f(),h(S,{key:0,label:"\u6743\u9650\u8303\u56F4","label-width":"80px"},{default:d(()=>[s(N,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:d(()=>[e[7]||(e[7]=n(" \u5168\u9009/\u5168\u4E0D\u9009: ")),s(k,{modelValue:a(r),"onUpdate:modelValue":e[1]||(e[1]=t=>y(r)?r.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:e[2]||(e[2]=t=>{m.value.setCheckedNodes(r.value?x.value:[])})},null,8,["modelValue"]),e[8]||(e[8]=n(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),s(k,{modelValue:a(i),"onUpdate:modelValue":e[3]||(e[3]=t=>y(i)?i.value=t:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:F},null,8,["modelValue"]),e[9]||(e[9]=n(" \u7236\u5B50\u8054\u52A8(\u9009\u4E2D\u7236\u8282\u70B9\uFF0C\u81EA\u52A8\u9009\u62E9\u5B50\u8282\u70B9): ")),s(k,{modelValue:a(v),"onUpdate:modelValue":e[4]||(e[4]=t=>y(v)?v.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":""},null,8,["modelValue"])]),default:d(()=>[s(G,{ref_key:"treeRef",ref:m,"check-strictly":!a(v),data:a(x),props:a(pe),"default-expand-all":"","empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E","node-key":"id","show-checkbox":""},null,8,["check-strictly","data","props"])]),_:1})]),_:1})):j("",!0)]),_:1},8,["modelValue"])}}});export{ve as _};
