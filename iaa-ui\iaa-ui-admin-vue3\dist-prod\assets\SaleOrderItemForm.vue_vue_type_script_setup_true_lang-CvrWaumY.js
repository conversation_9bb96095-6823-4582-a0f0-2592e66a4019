import{d as O,r as V,f as R,aK as k,q as z,c as y,o as P,F as C,J as D,A as v,a3 as Q,M as T,a as c,s as W,w as a,g as e,K as X,L as Y,v as Z,x as ee,y as le,B as ae,P as te,eP as I,an as oe,ea as h,G as de,H as N,E as re,eN as ue,eb as B}from"./index-CRsFgzy0.js";import{P as ie}from"./index-v67yau6-.js";import{S as ce}from"./index-CxlZ4TTH.js";const ne=O({__name:"SaleOrderItemForm",props:{items:{},disabled:{type:Boolean}},setup(S,{expose:q}){const E=S,j=V(!1),s=V([]),_=R({productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),g=V([]),w=V([]);k(()=>E.items,async i=>{s.value=i},{immediate:!0}),k(()=>s.value,i=>{i&&i.length!==0&&i.forEach(d=>{d.totalProductPrice=B(d.productPrice,d.count),d.taxPrice=B(d.totalProductPrice,d.taxPercent/100),d.totalProductPrice!=null?d.totalPrice=d.totalProductPrice+(d.taxPrice||0):d.totalPrice=void 0})},{deep:!0});const F=i=>{const{columns:d,data:u}=i,p=[];return d.forEach((m,r)=>{if(r!==0)if(["count","totalProductPrice","taxPrice","totalPrice"].includes(m.property)){const n=ue(u.map(f=>Number(f[m.property])));p[r]=m.property==="count"?I(n):h(n)}else p[r]="";else p[r]="\u5408\u8BA1"}),p},U=()=>{s.value.push({id:void 0,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalProductPrice:void 0,taxPercent:void 0,taxPrice:void 0,totalPrice:void 0,remark:void 0})},K=async i=>{if(!i.productId)return;const d=await ce.getStockCount(i.productId);i.stockCount=d||0};return q({validate:()=>g.value.validate()}),z(async()=>{w.value=await ie.getProductSimpleList(),s.value.length===0&&U()}),(i,d)=>{const u=Y,p=ae,m=ee,r=Z,n=te,f=oe,$=de,L=X,A=W,G=re,H=T;return P(),y(C,null,[D((P(),v(A,{ref_key:"formRef",ref:g,model:c(s),rules:c(_),"label-width":"0px","inline-message":!0,disabled:i.disabled},{default:a(()=>[e(L,{data:c(s),"show-summary":"","summary-method":F,class:"-mt-10px"},{default:a(()=>[e(u,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(u,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.productId`,rules:c(_).productId,class:"mb-0px!"},{default:a(()=>[e(m,{modelValue:l.productId,"onUpdate:modelValue":t=>l.productId=t,clearable:"",filterable:"",onChange:t=>((J,x)=>{const b=w.value.find(M=>M.id===J);b&&(x.productUnitName=b.unitName,x.productBarCode=b.barCode,x.productPrice=b.salePrice),K(x)})(t,l),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:a(()=>[(P(!0),y(C,null,le(c(w),t=>(P(),v(p,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u5E93\u5B58","min-width":"100"},{default:a(({row:l})=>[e(r,{class:"mb-0px!"},{default:a(()=>[e(n,{disabled:"",modelValue:l.stockCount,"onUpdate:modelValue":o=>l.stockCount=o,formatter:c(I)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(u,{label:"\u6761\u7801","min-width":"150"},{default:a(({row:l})=>[e(r,{class:"mb-0px!"},{default:a(()=>[e(n,{disabled:"",modelValue:l.productBarCode,"onUpdate:modelValue":o=>l.productBarCode=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u5355\u4F4D","min-width":"80"},{default:a(({row:l})=>[e(r,{class:"mb-0px!"},{default:a(()=>[e(n,{disabled:"",modelValue:l.productUnitName,"onUpdate:modelValue":o=>l.productUnitName=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.count`,rules:c(_).count,class:"mb-0px!"},{default:a(()=>[e(f,{modelValue:l.count,"onUpdate:modelValue":t=>l.count=t,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.productPrice`,class:"mb-0px!"},{default:a(()=>[e(f,{modelValue:l.productPrice,"onUpdate:modelValue":t=>l.productPrice=t,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u91D1\u989D",prop:"totalProductPrice",fixed:"right","min-width":"100"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.totalProductPrice`,class:"mb-0px!"},{default:a(()=>[e(n,{disabled:"",modelValue:l.totalProductPrice,"onUpdate:modelValue":t=>l.totalProductPrice=t,formatter:c(h)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u7387\uFF08%\uFF09",fixed:"right","min-width":"115"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.taxPercent`,class:"mb-0px!"},{default:a(()=>[e(f,{modelValue:l.taxPercent,"onUpdate:modelValue":t=>l.taxPercent=t,"controls-position":"right",min:0,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u989D",prop:"taxPrice",fixed:"right","min-width":"120"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.taxPrice`,class:"mb-0px!"},{default:a(()=>[e(r,{prop:`${o}.taxPrice`,class:"mb-0px!"},{default:a(()=>[e(n,{disabled:"",modelValue:l.taxPrice,"onUpdate:modelValue":t=>l.taxPrice=t,formatter:c(h)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u989D\u5408\u8BA1",prop:"totalPrice",fixed:"right","min-width":"100"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.totalPrice`,class:"mb-0px!"},{default:a(()=>[e(n,{disabled:"",modelValue:l.totalPrice,"onUpdate:modelValue":t=>l.totalPrice=t,formatter:c(h)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u5907\u6CE8","min-width":"150"},{default:a(({row:l,$index:o})=>[e(r,{prop:`${o}.remark`,class:"mb-0px!"},{default:a(()=>[e(n,{modelValue:l.remark,"onUpdate:modelValue":t=>l.remark=t,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:a(({$index:l})=>[e($,{onClick:o=>{return t=l,void s.value.splice(t,1);var t},link:""},{default:a(()=>d[0]||(d[0]=[N("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[H,c(j)]]),i.disabled?Q("",!0):(P(),v(G,{key:0,justify:"center",class:"mt-3"},{default:a(()=>[e($,{onClick:U,round:""},{default:a(()=>d[1]||(d[1]=[N("+ \u6DFB\u52A0\u91C7\u8D2D\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{ne as _};
