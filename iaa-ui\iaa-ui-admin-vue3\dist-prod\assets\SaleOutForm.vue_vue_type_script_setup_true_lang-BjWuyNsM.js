import{d as le,b as te,p as oe,r as n,f as ue,a2 as de,aK as re,c as v,o as c,F as _,g as a,a as t,m as q,w as o,J as se,A as f,s as ie,E as ne,h as ce,v as me,P as pe,C as fe,G as ve,H as g,I as _e,x as be,y as S,B as Ve,c2 as Pe,l as Ue,n as Ie,an as he,ea as C,M as we,a3 as ye,eb as ke}from"./index-CRsFgzy0.js";import{_ as ge}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{S as x}from"./index-CL5SS_JF.js";import{_ as Ce}from"./SaleOutItemForm.vue_vue_type_script_setup_true_lang-CM54uJf9.js";import{C as xe}from"./index-COzCXl3z.js";import{A as Oe}from"./index-DkE0YaRG.js";import{_ as Te}from"./SaleOrderOutEnableList.vue_vue_type_script_setup_true_lang-DjwrBTgF.js";import{g as Fe}from"./index-D4y5Z4cM.js";const Ne=le({name:"SaleOutForm",__name:"SaleOutForm",emits:["success"],setup(Ae,{expose:B,emit:G}){const{t:b}=te(),O=oe(),m=n(!1),T=n(""),p=n(!1),V=n(""),l=n({id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,outTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),H=ue({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],outTime:[{required:!0,message:"\u51FA\u5E93\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),P=de(()=>V.value==="detail"),U=n(),F=n([]),I=n([]),N=n([]),h=n("item"),A=n();re(()=>l.value,d=>{if(!d)return;const e=d.items.reduce((r,s)=>r+s.totalPrice,0),i=d.discountPercent!=null?ke(e,d.discountPercent/100):0;l.value.discountPrice=i,l.value.totalPrice=e-i+d.otherPrice},{deep:!0}),B({open:async(d,e)=>{if(m.value=!0,T.value=b("action."+d),V.value=d,Y(),e){p.value=!0;try{l.value=await x.getSaleOut(e)}finally{p.value=!1}}F.value=await xe.getCustomerSimpleList(),N.value=await Fe(),I.value=await Oe.getAccountSimpleList();const i=I.value.find(r=>r.defaultStatus);i&&(l.value.accountId=i.id)}});const E=n(),K=()=>{E.value.open()},M=d=>{l.value.orderId=d.id,l.value.orderNo=d.no,l.value.customerId=d.customerId,l.value.accountId=d.accountId,l.value.saleUserId=d.saleUserId,l.value.discountPercent=d.discountPercent,l.value.remark=d.remark,l.value.fileUrl=d.fileUrl,d.items.forEach(e=>{e.totalCount=e.count,e.count=e.totalCount-e.outCount,e.orderItemId=e.id,e.id=void 0}),l.value.items=d.items.filter(e=>e.count>0)},J=G,W=async()=>{await U.value.validate(),await A.value.validate(),p.value=!0;try{const d=l.value;V.value==="create"?(await x.createSaleOut(d),O.success(b("common.createSuccess"))):(await x.updateSaleOut(d),O.success(b("common.updateSuccess"))),m.value=!1,J("success")}finally{p.value=!1}},Y=()=>{var d;l.value={id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,outTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(d=U.value)==null||d.resetFields()};return(d,e)=>{const i=pe,r=me,s=ce,j=fe,z=_e,w=ve,y=Ve,k=be,D=Pe,L=ne,Q=Ie,X=Ue,Z=Se,R=he,$=ie,ee=ge,ae=we;return c(),v(_,null,[a(ee,{title:t(T),modelValue:t(m),"onUpdate:modelValue":e[14]||(e[14]=u=>q(m)?m.value=u:null),width:"1440"},{footer:o(()=>[t(P)?ye("",!0):(c(),f(w,{key:0,onClick:W,type:"primary",disabled:t(p)},{default:o(()=>e[16]||(e[16]=[g(" \u786E \u5B9A ")])),_:1},8,["disabled"])),a(w,{onClick:e[13]||(e[13]=u=>m.value=!1)},{default:o(()=>e[17]||(e[17]=[g("\u53D6 \u6D88")])),_:1})]),default:o(()=>[se((c(),f($,{ref_key:"formRef",ref:U,model:t(l),rules:t(H),"label-width":"100px",disabled:t(P)},{default:o(()=>[a(L,{gutter:20},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(r,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:o(()=>[a(i,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":e[0]||(e[0]=u=>t(l).no=u),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"outTime"},{default:o(()=>[a(j,{modelValue:t(l).outTime,"onUpdate:modelValue":e[1]||(e[1]=u=>t(l).outTime=u),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u51FA\u5E93\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:o(()=>[a(i,{modelValue:t(l).orderNo,"onUpdate:modelValue":e[2]||(e[2]=u=>t(l).orderNo=u),readonly:""},{append:o(()=>[a(w,{onClick:K},{default:o(()=>[a(z,{icon:"ep:search"}),e[15]||(e[15]=g(" \u9009\u62E9 "))]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5BA2\u6237",prop:"customerId"},{default:o(()=>[a(k,{modelValue:t(l).customerId,"onUpdate:modelValue":e[3]||(e[3]=u=>t(l).customerId=u),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:o(()=>[(c(!0),v(_,null,S(t(F),u=>(c(),f(y,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u9500\u552E\u4EBA\u5458",prop:"saleUserId"},{default:o(()=>[a(k,{modelValue:t(l).saleUserId,"onUpdate:modelValue":e[4]||(e[4]=u=>t(l).saleUserId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u4EBA\u5458",class:"!w-1/1"},{default:o(()=>[(c(!0),v(_,null,S(t(N),u=>(c(),f(y,{key:u.id,label:u.nickname,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:16},{default:o(()=>[a(r,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[a(i,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":e[5]||(e[5]=u=>t(l).remark=u),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:o(()=>[a(D,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":e[6]||(e[6]=u=>t(l).fileUrl=u),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(Z,null,{default:o(()=>[a(X,{modelValue:t(h),"onUpdate:modelValue":e[7]||(e[7]=u=>q(h)?h.value=u:null),class:"-mt-15px -mb-10px"},{default:o(()=>[a(Q,{label:"\u51FA\u5E93\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:o(()=>[a(Ce,{ref_key:"itemFormRef",ref:A,items:t(l).items,disabled:t(P)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(L,{gutter:20},{default:o(()=>[a(s,{span:8},{default:o(()=>[a(r,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:o(()=>[a(R,{modelValue:t(l).discountPercent,"onUpdate:modelValue":e[8]||(e[8]=u=>t(l).discountPercent=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u6536\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:o(()=>[a(i,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":e[9]||(e[9]=u=>t(l).discountPrice=u),formatter:t(C)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:o(()=>[a(i,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(C)},null,8,["model-value","formatter"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:o(()=>[a(R,{modelValue:t(l).otherPrice,"onUpdate:modelValue":e[10]||(e[10]=u=>t(l).otherPrice=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:o(()=>[a(k,{modelValue:t(l).accountId,"onUpdate:modelValue":e[11]||(e[11]=u=>t(l).accountId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:o(()=>[(c(!0),v(_,null,S(t(I),u=>(c(),f(y,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(s,{span:8},{default:o(()=>[a(r,{label:"\u5E94\u6536\u91D1\u989D"},{default:o(()=>[a(i,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":e[12]||(e[12]=u=>t(l).totalPrice=u),formatter:t(C)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[ae,t(p)]])]),_:1},8,["title","modelValue"]),a(Te,{ref_key:"saleOrderOutEnableListRef",ref:E,onSuccess:M},null,512)],64)}}});export{Ne as _};
