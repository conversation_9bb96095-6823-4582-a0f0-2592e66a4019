import{d as B,r as n,f as G,A as g,o as u,w as o,g as e,s as K,a,v as Q,P as q,Q as A,x as J,c as k,F as j,y as W,B as X,C as Z,G as $,H as d,I as ee,J as le,K as ae,L as te,e9 as N,aE as oe,t as re,ea as ie,M as se,m as ne,aG as pe}from"./index-CRsFgzy0.js";import{_ as ue}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{b as ce}from"./formatTime-DhdtkSIS.js";import{P as fe}from"./index-v67yau6-.js";import{S as ge}from"./index-CL5SS_JF.js";const be={key:0},_e=B({name:"SaleOutReceiptEnableList",__name:"SaleOutReceiptEnableList",emits:["success"],setup(we,{expose:I,emit:C}){const w=n([]),v=n(0),b=n(!1),p=n(!1),r=G({pageNo:1,pageSize:10,no:void 0,productId:void 0,outTime:[],receiptEnable:!0,customerId:void 0}),y=n(),h=n([]),m=n([]),T=s=>{m.value=s};I({open:async s=>{p.value=!0,await pe(),r.customerId=s,await x(),h.value=await fe.getProductSimpleList()}});const U=C,L=()=>{try{U("success",m.value)}finally{p.value=!1}},V=async()=>{b.value=!0;try{const s=await ge.getSaleOutPage(r);w.value=s.list,v.value=s.total}finally{b.value=!1}},x=()=>{y.value.resetFields(),_()},_=()=>{r.pageNo=1,m.value=[],V()};return(s,l)=>{const D=q,c=Q,E=X,H=J,O=Z,P=ee,f=$,Y=K,S=me,i=te,z=oe,F=de,M=ue,R=se;return u(),g(M,{title:"\u9009\u62E9\u9500\u552E\u51FA\u5E93\uFF08\u4EC5\u5C55\u793A\u53EF\u6536\u6B3E\uFF09",modelValue:a(p),"onUpdate:modelValue":l[6]||(l[6]=t=>ne(p)?p.value=t:null),appendToBody:!0,scroll:!0,width:"1080"},{footer:o(()=>[e(f,{disabled:!a(m).length,type:"primary",onClick:L},{default:o(()=>l[9]||(l[9]=[d(" \u786E \u5B9A ")])),_:1},8,["disabled"]),e(f,{onClick:l[5]||(l[5]=t=>p.value=!1)},{default:o(()=>l[10]||(l[10]=[d("\u53D6 \u6D88")])),_:1})]),default:o(()=>[e(S,null,{default:o(()=>[e(Y,{class:"-mb-15px",model:a(r),ref_key:"queryFormRef",ref:y,inline:!0,"label-width":"68px"},{default:o(()=>[e(c,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:o(()=>[e(D,{modelValue:a(r).no,"onUpdate:modelValue":l[0]||(l[0]=t=>a(r).no=t),placeholder:"\u8BF7\u8F93\u5165\u51FA\u5E93\u5355\u53F7",clearable:"",onKeyup:A(_,["enter"]),class:"!w-160px"},null,8,["modelValue"])]),_:1}),e(c,{label:"\u4EA7\u54C1",prop:"productId"},{default:o(()=>[e(H,{modelValue:a(r).productId,"onUpdate:modelValue":l[1]||(l[1]=t=>a(r).productId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-160px"},{default:o(()=>[(u(!0),k(j,null,W(a(h),t=>(u(),g(E,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"orderTime"},{default:o(()=>[e(O,{modelValue:a(r).outTime,"onUpdate:modelValue":l[2]||(l[2]=t=>a(r).outTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-160px"},null,8,["modelValue","default-time"])]),_:1}),e(c,null,{default:o(()=>[e(f,{onClick:_},{default:o(()=>[e(P,{icon:"ep:search",class:"mr-5px"}),l[7]||(l[7]=d(" \u641C\u7D22"))]),_:1}),e(f,{onClick:x},{default:o(()=>[e(P,{icon:"ep:refresh",class:"mr-5px"}),l[8]||(l[8]=d(" \u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(S,null,{default:o(()=>[le((u(),g(a(ae),{data:a(w),"show-overflow-tooltip":!0,stripe:!0,onSelectionChange:T},{default:o(()=>[e(i,{width:"30",label:"\u9009\u62E9",type:"selection"}),e(i,{"min-width":"180",label:"\u51FA\u5E93\u5355\u53F7",align:"center",prop:"no"}),e(i,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),e(i,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),e(i,{label:"\u51FA\u5E93\u65F6\u95F4",align:"center",prop:"outTime",formatter:a(ce),width:"120px"},null,8,["formatter"]),e(i,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),e(i,{label:"\u5E94\u6536\u91D1\u989D",align:"center",prop:"totalPrice",formatter:a(N)},null,8,["formatter"]),e(i,{label:"\u5DF2\u6536\u91D1\u989D",align:"center",prop:"receiptPrice",formatter:a(N)},null,8,["formatter"]),e(i,{label:"\u672A\u6536\u91D1\u989D",align:"center"},{default:o(t=>[t.row.receiptPrice===t.row.totalPrice?(u(),k("span",be,"0")):(u(),g(z,{key:1,type:"danger"},{default:o(()=>[d(re(a(ie)(t.row.totalPrice-t.row.receiptPrice)),1)]),_:2},1024))]),_:1})]),_:1},8,["data"])),[[R,a(b)]]),e(F,{limit:a(r).pageSize,"onUpdate:limit":l[3]||(l[3]=t=>a(r).pageSize=t),page:a(r).pageNo,"onUpdate:page":l[4]||(l[4]=t=>a(r).pageNo=t),total:a(v),onPagination:V},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue"])}}});export{_e as _};
