import{f as D,d as G,b as H,p as J,r as n,c as O,o as L,F as Q,g as r,a as s,m as j,w as u,J as q,A as z,G as B,H as P,L as K,an as W,M as X,aP as Z,aQ as $,aG as ee,aO as ae}from"./index-CRsFgzy0.js";import{_ as te}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as le}from"./Form-BF4H89jq.js";import{_ as oe}from"./SpuSelect.vue_vue_type_script_setup_true_lang-tSVtv9n7.js";import{_ as se}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-DGosLJYC.js";import{b as Y}from"./formatTime-DhdtkSIS.js";import{S as ie}from"./seckillConfig-Cd7LBLml.js";import{r as c}from"./formRules-V2Qetfkc.js";import{u as re}from"./useCrudSchemas-CNYomGr4.js";import{g as ne,c as ue,u as ce}from"./seckillActivity-2Lhy_umM.js";import{b as me}from"./spu-BHhhuUrI.js";import{g as pe}from"./index-CQgbu5O7.js";const de=D({spuId:[c],name:[c],startTime:[c],endTime:[c],sort:[c],configIds:[c],totalLimitCount:[c],singleLimitCount:[c],totalStock:[c]}),fe=D([{label:"\u79D2\u6740\u6D3B\u52A8\u540D\u79F0",field:"name",isSearch:!0,form:{colProps:{span:24}},table:{width:120}},{label:"\u6D3B\u52A8\u5F00\u59CB\u65F6\u95F4",field:"startTime",formatter:Y,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u6D3B\u52A8\u7ED3\u675F\u65F6\u95F4",field:"endTime",formatter:Y,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u79D2\u6740\u65F6\u6BB5",field:"configIds",form:{component:"Select",componentProps:{multiple:!0,optionsAlias:{labelField:"name",valueField:"id"}},api:ie.getSimpleSeckillConfigList},table:{width:300}},{label:"\u603B\u9650\u8D2D\u6570\u91CF",field:"totalLimitCount",form:{component:"InputNumber",value:0},table:{width:120}},{label:"\u5355\u6B21\u9650\u591F\u6570\u91CF",field:"singleLimitCount",form:{component:"InputNumber",value:0},table:{width:120}},{label:"\u6392\u5E8F",field:"sort",form:{component:"InputNumber",value:0},table:{width:80}},{label:"\u79D2\u6740\u6D3B\u52A8\u5546\u54C1",field:"spuId",isTable:!0,isSearch:!1,form:{colProps:{span:24}},table:{width:300}},{label:"\u5907\u6CE8",field:"remark",isSearch:!1,form:{component:"Input",componentProps:{type:"textarea",rows:4},colProps:{span:24}},table:{width:300}}]),{allSchemas:ke}=re(fe),ve=G({name:"PromotionSeckillActivityForm",__name:"SeckillActivityForm",emits:["success"],setup(be,{expose:M,emit:R}){const{t:w}=H(),S=J(),f=n(!1),C=n(""),k=n(!1),_=n(""),m=n(),I=n(),V=n(),x=[{name:"productConfig.stock",rule:a=>a>=1,message:"\u5546\u54C1\u79D2\u6740\u5E93\u5B58\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 1 \uFF01\uFF01\uFF01"},{name:"productConfig.seckillPrice",rule:a=>a>=.01,message:"\u5546\u54C1\u79D2\u6740\u4EF7\u683C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 0.01 \uFF01\uFF01\uFF01"}],b=n([]),y=n([]),A=(a,e)=>{m.value.setValues({spuId:a}),F(a,e)},F=async(a,e,o)=>{var g;const i=[],p=await me([a]);if(p.length==0)return;b.value=[];const l=p[0],v=e===void 0?l==null?void 0:l.skus:(g=l==null?void 0:l.skus)==null?void 0:g.filter(t=>e.includes(t.id));v==null||v.forEach(t=>{let d={skuId:t.id,stock:0,seckillPrice:0};if(o!==void 0){const h=o.find(N=>N.skuId===t.id);h&&(h.seckillPrice=ae(h.seckillPrice)),d=h||d}t.productConfig=d}),l.skus=v,i.push({spuId:l.id,spuDetail:l,propertyList:pe(l)}),b.value.push(l),y.value=i};M({open:async(a,e)=>{var o;if(f.value=!0,C.value=w("action."+a),_.value=a,await U(),e){k.value=!0;try{const i=await ne(e);await F(i.spuId,(o=i.products)==null?void 0:o.map(p=>p.skuId),i.products),m.value.setValues(i)}finally{k.value=!1}}}});const E=R,T=async()=>{if(m&&await m.value.getElFormRef().validate()){k.value=!0;try{const a=Z(V.value.getSkuConfigs("productConfig"));a.forEach(o=>{o.seckillPrice=$(o.seckillPrice)});const e=m.value.formModel;e.products=a,_.value==="create"?(await ue(e),S.success(w("common.createSuccess"))):(await ce(e),S.success(w("common.updateSuccess"))),f.value=!1,E("success")}finally{k.value=!1}}},U=async()=>{b.value=[],y.value=[],await ee(),m.value.getElFormRef().resetFields()};return(a,e)=>{const o=B,i=W,p=K,l=le,v=te,g=X;return L(),O(Q,null,[r(v,{modelValue:s(f),"onUpdate:modelValue":e[2]||(e[2]=t=>j(f)?f.value=t:null),title:s(C),width:"65%"},{footer:u(()=>[r(o,{disabled:s(k),type:"primary",onClick:T},{default:u(()=>e[4]||(e[4]=[P("\u786E \u5B9A")])),_:1},8,["disabled"]),r(o,{onClick:e[1]||(e[1]=t=>f.value=!1)},{default:u(()=>e[5]||(e[5]=[P("\u53D6 \u6D88")])),_:1})]),default:u(()=>[q((L(),z(l,{ref_key:"formRef",ref:m,isCol:!0,rules:s(de),schema:s(ke).formSchema},{spuId:u(()=>[r(o,{onClick:e[0]||(e[0]=t=>s(I).open())},{default:u(()=>e[3]||(e[3]=[P("\u9009\u62E9\u5546\u54C1")])),_:1}),r(s(se),{ref_key:"spuAndSkuListRef",ref:V,"rule-config":x,"spu-list":s(b),"spu-property-list-p":s(y)},{default:u(()=>[r(p,{align:"center",label:"\u79D2\u6740\u5E93\u5B58","min-width":"168"},{default:u(({row:t})=>[r(i,{modelValue:t.productConfig.stock,"onUpdate:modelValue":d=>t.productConfig.stock=d,min:0,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),r(p,{align:"center",label:"\u79D2\u6740\u4EF7\u683C(\u5143)","min-width":"168"},{default:u(({row:t})=>[r(i,{modelValue:t.productConfig.seckillPrice,"onUpdate:modelValue":d=>t.productConfig.seckillPrice=d,min:0,precision:2,step:.1,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["spu-list","spu-property-list-p"])]),_:1},8,["rules","schema"])),[[g,s(k)]])]),_:1},8,["modelValue","title"]),r(s(oe),{ref_key:"spuSelectRef",ref:I,isSelectSku:!0,onConfirm:A},null,512)],64)}}});export{ve as _};
