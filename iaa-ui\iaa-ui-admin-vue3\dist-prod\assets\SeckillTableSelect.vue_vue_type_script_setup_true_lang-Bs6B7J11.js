import{d as ue,ah as ie,r as s,q as re,A as b,o as w,bZ as se,w as u,g as l,G as ne,H as m,J as de,s as pe,a as t,v as me,P as ve,Q as ce,x as fe,c as he,F as we,y as _e,R as be,D as A,B as ge,I as Ve,K as ye,L as Ce,a4 as ke,m as x,aC as xe,t as S,M as Se,aR as Ue,bl as E}from"./index-CRsFgzy0.js";import{_ as Te}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Ne}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as Ye}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as De}from"./el-image-BQpHFDaE.js";import{h as Oe}from"./tree-COGD3qag.js";import{g as Pe}from"./category--cl9fhwU.js";import{a as ze}from"./seckillActivity-2Lhy_umM.js";import{f as Ie}from"./formatter-D3GpDdeL.js";import{f as K,d as Re}from"./formatTime-DhdtkSIS.js";const Ae=ue({name:"SeckillTableSelect",__name:"SeckillTableSelect",props:{multiple:ie.bool.def(!1)},emits:["change"],setup(U,{expose:q,emit:B}){const T=s(0),v=s([]),k=s(!1),p=s(!1),r=s({pageNo:1,pageSize:10,name:null,status:void 0});q({open:o=>{c.value=[],n.value={},d.value=!1,V.value=!1,o&&o.length>0&&(c.value=[...o],n.value=Object.fromEntries(o.map(e=>[e.id,!0]))),p.value=!0,N()}});const g=async()=>{k.value=!0;try{const o=await ze(r.value);v.value=o.list,T.value=o.total,v.value.forEach(e=>n.value[e.id]=n.value[e.id]||!1),O()}finally{k.value=!1}},M=()=>{r.value.pageNo=1,g()},N=()=>{r.value={pageNo:1,pageSize:10,name:void 0,createTime:[]},g()},F=o=>{const e=Math.min(...o.map(_=>_.seckillPrice));return`\uFFE5${Ue(e)}`},d=s(!1),V=s(!1),c=s([]),n=s({}),y=s(),H=()=>{p.value=!1,Y(E,[...c.value])},Y=B,j=o=>{d.value=o,V.value=!1,v.value.forEach(e=>D(o,e,!1))},D=(o,e,_)=>{if(o)c.value.push(e),n.value[e.id]=!0;else{const f=G(e);f>-1&&(c.value.splice(f,1),n.value[e.id]=!1,d.value=!1)}_&&O()},G=o=>c.value.findIndex(e=>e.id===o.id),O=()=>{d.value=v.value.every(o=>n.value[o.id]),V.value=!d.value&&v.value.some(o=>n.value[o.id])},P=s(),J=s();return re(async()=>{await g(),P.value=await Pe({}),J.value=Oe(P.value,"id","parentId")}),(o,e)=>{const _=ve,f=me,L=ge,Q=fe,z=Ve,C=ne,Z=pe,I=ke,i=Ce,$=xe,W=De,X=Ye,ee=ye,ae=Ne,le=Me,te=Te,oe=Se;return w(),b(te,{modelValue:t(p),"onUpdate:modelValue":e[7]||(e[7]=a=>x(p)?p.value=a:null),appendToBody:!0,title:"\u9009\u62E9\u6D3B\u52A8",width:"70%"},se({default:u(()=>[l(le,null,{default:u(()=>[l(Z,{ref:"queryFormRef",inline:!0,model:t(r),class:"-mb-15px","label-width":"68px"},{default:u(()=>[l(f,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name"},{default:u(()=>[l(_,{modelValue:t(r).name,"onUpdate:modelValue":e[0]||(e[0]=a=>t(r).name=a),placeholder:"\u8BF7\u8F93\u5165\u6D3B\u52A8\u540D\u79F0",clearable:"",onKeyup:ce(M,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(f,{label:"\u6D3B\u52A8\u72B6\u6001",prop:"status"},{default:u(()=>[l(Q,{modelValue:t(r).status,"onUpdate:modelValue":e[1]||(e[1]=a=>t(r).status=a),placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u72B6\u6001",clearable:"",class:"!w-240px"},{default:u(()=>[(w(!0),he(we,null,_e(t(be)(t(A).COMMON_STATUS),a=>(w(),b(L,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(f,null,{default:u(()=>[l(C,{onClick:M},{default:u(()=>[l(z,{class:"mr-5px",icon:"ep:search"}),e[8]||(e[8]=m(" \u641C\u7D22 "))]),_:1}),l(C,{onClick:N},{default:u(()=>[l(z,{class:"mr-5px",icon:"ep:refresh"}),e[9]||(e[9]=m(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),de((w(),b(ee,{data:t(v),"show-overflow-tooltip":""},{default:u(()=>[U.multiple?(w(),b(i,{key:0,width:"55"},{header:u(()=>[l(I,{modelValue:t(d),"onUpdate:modelValue":e[2]||(e[2]=a=>x(d)?d.value=a:null),indeterminate:t(V),onChange:j},null,8,["modelValue","indeterminate"])]),default:u(({row:a})=>[l(I,{modelValue:t(n)[a.id],"onUpdate:modelValue":h=>t(n)[a.id]=h,onChange:h=>D(h,a,!0)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):(w(),b(i,{key:1,label:"#",width:"55"},{default:u(({row:a})=>[l($,{value:a.id,modelValue:t(y),"onUpdate:modelValue":e[3]||(e[3]=h=>x(y)?y.value=h:null),onChange:h=>{return Y(E,R=a),p.value=!1,void(y.value=R.id);var R}},{default:u(()=>e[10]||(e[10]=[m(" \xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1})),l(i,{label:"\u6D3B\u52A8\u7F16\u53F7",prop:"id","min-width":"80"}),l(i,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name","min-width":"140"}),l(i,{label:"\u6D3B\u52A8\u65F6\u95F4","min-width":"210"},{default:u(a=>[m(S(t(K)(a.row.startTime,"YYYY-MM-DD"))+" ~ "+S(t(K)(a.row.endTime,"YYYY-MM-DD")),1)]),_:1}),l(i,{label:"\u5546\u54C1\u56FE\u7247",prop:"spuName","min-width":"80"},{default:u(a=>[l(W,{src:a.row.picUrl,class:"h-40px w-40px","preview-src-list":[a.row.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),l(i,{label:"\u5546\u54C1\u6807\u9898",prop:"spuName","min-width":"300"}),l(i,{label:"\u539F\u4EF7",prop:"marketPrice","min-width":"100",formatter:t(Ie)},null,8,["formatter"]),l(i,{label:"\u62FC\u56E2\u4EF7",prop:"seckillPrice","min-width":"100"},{default:u(a=>[m(S(F(a.row.products)),1)]),_:1}),l(i,{label:"\u5F00\u56E2\u7EC4\u6570",prop:"groupCount","min-width":"100"}),l(i,{label:"\u6210\u56E2\u7EC4\u6570",prop:"groupSuccessCount","min-width":"100"}),l(i,{label:"\u8D2D\u4E70\u6B21\u6570",prop:"recordCount","min-width":"100"}),l(i,{label:"\u6D3B\u52A8\u72B6\u6001",align:"center",prop:"status","min-width":"100"},{default:u(a=>[l(X,{type:t(A).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),l(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(Re),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[oe,t(k)]]),l(ae,{limit:t(r).pageSize,"onUpdate:limit":e[4]||(e[4]=a=>t(r).pageSize=a),page:t(r).pageNo,"onUpdate:page":e[5]||(e[5]=a=>t(r).pageNo=a),total:t(T),onPagination:g},null,8,["limit","page","total"])]),_:1})]),_:2},[U.multiple?{name:"footer",fn:u(()=>[l(C,{type:"primary",onClick:H},{default:u(()=>e[11]||(e[11]=[m("\u786E \u5B9A")])),_:1}),l(C,{onClick:e[6]||(e[6]=a=>p.value=!1)},{default:u(()=>e[12]||(e[12]=[m("\u53D6 \u6D88")])),_:1})]),key:"0"}:void 0]),1032,["modelValue"])}}});export{Ae as _};
