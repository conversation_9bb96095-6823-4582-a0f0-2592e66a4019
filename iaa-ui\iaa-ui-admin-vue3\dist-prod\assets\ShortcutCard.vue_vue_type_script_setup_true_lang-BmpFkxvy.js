import{d as u,u as m,A as d,o as a,w as n,i as r,c as g,F as p,y as b,X as f,g as t,I as C,t as x,a as w,k as h}from"./index-CRsFgzy0.js";import{C as N}from"./CardTitle-DCrrQp54.js";const v={class:"flex flex-row flex-wrap gap-8 p-4"},y=["onClick"],k=u({name:"ShortcutCard",__name:"ShortcutCard",setup(S){const s=m(),i=[{name:"\u7528\u6237\u7BA1\u7406",icon:"ep:user-filled",bgColor:"bg-red-400",routerName:"MemberUser"},{name:"\u5546\u54C1\u7BA1\u7406",icon:"fluent-mdl2:product",bgColor:"bg-orange-400",routerName:"ProductSpu"},{name:"\u8BA2\u5355\u7BA1\u7406",icon:"ep:list",bgColor:"bg-yellow-500",routerName:"TradeOrder"},{name:"\u552E\u540E\u7BA1\u7406",icon:"ri:refund-2-line",bgColor:"bg-green-600",routerName:"TradeAfterSale"},{name:"\u5206\u9500\u7BA1\u7406",icon:"fa-solid:project-diagram",bgColor:"bg-cyan-500",routerName:"TradeBrokerageUser"},{name:"\u4F18\u60E0\u5238",icon:"ep:ticket",bgColor:"bg-blue-500",routerName:"PromotionCoupon"},{name:"\u62FC\u56E2\u6D3B\u52A8",icon:"fa:group",bgColor:"bg-purple-500",routerName:"PromotionBargainActivity"},{name:"\u4F63\u91D1\u63D0\u73B0",icon:"vaadin:money-withdraw",bgColor:"bg-rose-500",routerName:"TradeBrokerageWithdraw"}];return(T,_)=>{const l=C,c=h;return a(),d(c,{shadow:"never"},{header:n(()=>[t(w(N),{title:"\u5FEB\u6377\u5165\u53E3"})]),default:n(()=>[r("div",v,[(a(),g(p,null,b(i,e=>r("div",{key:e.name,class:"h-20 w-20% flex flex-col cursor-pointer items-center justify-center gap-2",onClick:j=>{return o=e.routerName,void s.push({name:o});var o}},[r("div",{class:f([e.bgColor,"h-48px w-48px flex items-center justify-center rounded text-white"])},[t(l,{icon:e.icon,class:"text-7.5!"},null,8,["icon"])],2),r("span",null,x(e.name),1)],8,y)),64))])]),_:1})}}});export{k as _};
