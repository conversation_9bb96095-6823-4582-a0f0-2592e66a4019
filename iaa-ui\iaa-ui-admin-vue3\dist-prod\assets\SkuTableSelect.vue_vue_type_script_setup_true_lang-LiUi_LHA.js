import{d as T,ah as j,p as K,r as i,q,aK as A,A as f,o as h,w as a,J as B,a as s,K as E,g as t,L as H,aC as J,m as _,H as c,t as g,aR as L,M}from"./index-CRsFgzy0.js";import{_ as N}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{E as R}from"./el-image-BQpHFDaE.js";import{g as z}from"./spu-BHhhuUrI.js";const D=T({name:"SkuTableSelect",__name:"SkuTableSelect",props:{spuId:j.number.def(null)},emits:["change"],setup(b,{expose:V,emit:x}){const p=b;K();const w=i([]),m=i(!1),o=i(!1),r=i(),y=x;return V({open:()=>{o.value=!0}}),q(async()=>{}),A(()=>p.spuId,()=>{p.spuId&&(async()=>{m.value=!0;try{const v=await z(p.spuId);w.value=v.skus}finally{m.value=!1}})()}),(v,l)=>{const I=J,n=H,S=R,U=N,k=M;return h(),f(U,{modelValue:s(o),"onUpdate:modelValue":l[1]||(l[1]=e=>_(o)?o.value=e:null),appendToBody:!0,title:"\u9009\u62E9\u89C4\u683C",width:"700"},{default:a(()=>[B((h(),f(s(E),{data:s(w),"show-overflow-tooltip":""},{default:a(()=>[t(n,{label:"#",width:"55"},{default:a(({row:e})=>[t(I,{value:e.id,modelValue:s(r),"onUpdate:modelValue":l[0]||(l[0]=u=>_(r)?r.value=u:null),onChange:u=>(d=>{y("change",d),o.value=!1,r.value=void 0})(e)},{default:a(()=>l[2]||(l[2]=[c("\xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1}),t(n,{label:"\u56FE\u7247","min-width":"80"},{default:a(({row:e})=>[t(S,{src:e.picUrl,class:"h-30px w-30px","preview-src-list":[e.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),t(n,{label:"\u89C4\u683C",align:"center","min-width":"80"},{default:a(({row:e})=>{var u,d;return[c(g((d=(u=e.properties)==null?void 0:u.map(C=>C.valueName))==null?void 0:d.join(" ")),1)]}),_:1}),t(n,{align:"center",label:"\u9500\u552E\u4EF7(\u5143)","min-width":"80"},{default:a(({row:e})=>[c(g(s(L)(e.price)),1)]),_:1})]),_:1},8,["data"])),[[k,s(m)]])]),_:1},8,["modelValue"])}}});export{D as _};
