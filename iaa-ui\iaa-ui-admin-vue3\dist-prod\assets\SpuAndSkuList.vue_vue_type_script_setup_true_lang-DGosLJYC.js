import{d as A,p as E,r as m,aK as g,A as h,o as v,w as o,g as t,a3 as D,L as K,a as l,b3 as U,H as _,t as z,aO as B,G,K as H,aV as O}from"./index-CRsFgzy0.js";import{E as R}from"./el-image-BQpHFDaE.js";import{_ as T}from"./SkuList.vue_vue_type_script_setup_true_lang-ChNgeWhW.js";const V=A({name:"PromotionSpuAndSkuList",__name:"SpuAndSkuList",props:{spuList:{},ruleConfig:{},spuPropertyListP:{},deletable:{type:Boolean}},emits:["delete"],setup(b,{expose:x,emit:L}){const C=E(),w=b,p=m([]),k=m(),d=m([]),y=m([]);x({getSkuConfigs:e=>{k.value.validateSku();const r=[];return d.value.forEach(a=>{var u;(u=a.spuDetail.skus)==null||u.forEach(f=>{r.push(f[e])})}),r}});const S=L;return g(()=>w.spuList,e=>{e&&(p.value=e)},{deep:!0,immediate:!0}),g(()=>w.spuPropertyListP,e=>{e&&(d.value=e,setTimeout(()=>{y.value=e.map(r=>r.spuId+"")},200))},{deep:!0,immediate:!0}),(e,r)=>{const a=K,u=R,f=G,I=H;return v(),h(I,{data:l(p),"expand-row-keys":l(y),"row-key":"id"},{default:o(()=>[t(a,{type:"expand",width:"30"},{default:o(({row:i})=>{var c,s;return[t(l(T),{ref_key:"skuListRef",ref:k,"is-activity-component":!0,"prop-form-data":(c=l(d).find(n=>n.spuId===i.id))==null?void 0:c.spuDetail,"property-list":(s=l(d).find(n=>n.spuId===i.id))==null?void 0:s.propertyList,"rule-config":e.ruleConfig},{extension:o(()=>[U(e.$slots,"default")]),_:2},1032,["prop-form-data","property-list","rule-config"])]}),_:3}),t(a,{key:"id",align:"center",label:"\u5546\u54C1\u7F16\u53F7",prop:"id"}),t(a,{label:"\u5546\u54C1\u56FE","min-width":"80"},{default:o(({row:i})=>[t(u,{src:i.picUrl,class:"h-30px w-30px",onClick:c=>{return s=i.picUrl,void O({zIndex:99999999,urlList:[s]});var s}},null,8,["src","onClick"])]),_:1}),t(a,{"show-overflow-tooltip":!0,label:"\u5546\u54C1\u540D\u79F0","min-width":"300",prop:"name"}),t(a,{align:"center",label:"\u5546\u54C1\u552E\u4EF7","min-width":"90",prop:"price"},{default:o(({row:i})=>[_(z(l(B)(i.price)),1)]),_:1}),t(a,{align:"center",label:"\u9500\u91CF","min-width":"90",prop:"salesCount"}),t(a,{align:"center",label:"\u5E93\u5B58","min-width":"90",prop:"stock"}),l(p).length>1&&e.deletable?(v(),h(a,{key:0,align:"center",label:"\u64CD\u4F5C","min-width":"90"},{default:o(i=>[t(f,{link:"",type:"primary",onClick:c=>(async s=>{await C.confirm("\u662F\u5426\u5220\u9664\u5546\u54C1\u7F16\u53F7\u4E3A"+s+"\u7684\u6570\u636E\uFF1F");const n=p.value.findIndex(P=>P.id==s);p.value.splice(n,1),S("delete",s)})(i.row.id)},{default:o(()=>r[0]||(r[0]=[_(" \u5220\u9664")])),_:2},1032,["onClick"])]),_:1})):D("",!0)]),_:3},8,["data","expand-row-keys"])}}});export{V as _};
