import{d as ae,ah as le,r as i,q as te,A as b,o as x,a as t,m as I,bZ as oe,w as u,g as l,G as ue,H as h,J as de,s as ie,v as ne,P as re,Q as pe,C as se,I as me,M as ce,K as ve,L as fe,a4 as ge,aC as he,i as we,t as ye,bl as J}from"./index-CRsFgzy0.js";import{_ as _e}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as Ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as be}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{E as xe}from"./el-image-BQpHFDaE.js";import{E as Ue}from"./el-tree-select-BijZG_HG.js";import{h as Ce,d as ke}from"./tree-COGD3qag.js";import{g as Ie}from"./category--cl9fhwU.js";import{d as Te}from"./spu-BHhhuUrI.js";const Se=ae({name:"SpuTableSelect",__name:"SpuTableSelect",props:{multiple:le.bool.def(!1)},emits:["change"],setup(T,{expose:K,emit:P}){const S=i(0),c=i([]),U=i(!1),s=i(!1),d=i({pageNo:1,pageSize:10,tabType:0,name:"",categoryId:null,createTime:[]});K({open:o=>{v.value=[],n.value={},p.value=!1,y.value=!1,o&&o.length>0&&(v.value=[...o],n.value=Object.fromEntries(o.map(e=>[e.id,!0]))),s.value=!0,Y()}});const w=async()=>{U.value=!0;try{const o=await Te(d.value);c.value=o.list,S.value=o.total,c.value.forEach(e=>n.value[e.id]=n.value[e.id]||!1),E()}finally{U.value=!1}},N=()=>{d.value.pageNo=1,w()},Y=()=>{d.value={pageNo:1,pageSize:10,tabType:0,name:"",categoryId:null,createTime:[]},w()},p=i(!1),y=i(!1),v=i([]),n=i({}),_=i(),j=()=>{s.value=!1,z(J,[...v.value])},z=P,A=o=>{p.value=o,y.value=!1,c.value.forEach(e=>D(o,e,!1))},D=(o,e,k)=>{if(o)v.value.push(e),n.value[e.id]=!0;else{const m=B(e);m>-1&&(v.value.splice(m,1),n.value[e.id]=!1,p.value=!1)}k&&E()},B=o=>v.value.findIndex(e=>e.id===o.id),E=()=>{p.value=c.value.every(o=>n.value[o.id]),y.value=!p.value&&c.value.some(o=>n.value[o.id])},C=i(),H=i();return te(async()=>{await w(),C.value=await Ie({}),H.value=Ce(C.value,"id","parentId")}),(o,e)=>{const k=re,m=ne,F=Ue,G=se,M=me,V=ue,L=ie,q=ge,f=fe,O=he,Q=xe,R=ve,Z=be,W=Ve,X=_e,$=ce;return x(),b(X,{modelValue:t(s),"onUpdate:modelValue":e[8]||(e[8]=a=>I(s)?s.value=a:null),appendToBody:!0,title:"\u9009\u62E9\u5546\u54C1",width:"70%"},oe({default:u(()=>[l(W,null,{default:u(()=>[l(L,{ref:"queryFormRef",inline:!0,model:t(d),class:"-mb-15px","label-width":"68px"},{default:u(()=>[l(m,{label:"\u5546\u54C1\u540D\u79F0",prop:"name"},{default:u(()=>[l(k,{modelValue:t(d).name,"onUpdate:modelValue":e[0]||(e[0]=a=>t(d).name=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",onKeyup:pe(N,["enter"])},null,8,["modelValue"])]),_:1}),l(m,{label:"\u5546\u54C1\u5206\u7C7B",prop:"categoryId"},{default:u(()=>[l(F,{modelValue:t(d).categoryId,"onUpdate:modelValue":e[1]||(e[1]=a=>t(d).categoryId=a),data:t(H),props:t(ke),"check-strictly":"",class:"!w-240px","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","data","props"])]),_:1}),l(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:u(()=>[l(G,{modelValue:t(d).createTime,"onUpdate:modelValue":e[2]||(e[2]=a=>t(d).createTime=a),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(m,null,{default:u(()=>[l(V,{onClick:N},{default:u(()=>[l(M,{class:"mr-5px",icon:"ep:search"}),e[9]||(e[9]=h(" \u641C\u7D22 "))]),_:1}),l(V,{onClick:Y},{default:u(()=>[l(M,{class:"mr-5px",icon:"ep:refresh"}),e[10]||(e[10]=h(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"]),de((x(),b(R,{data:t(c),"show-overflow-tooltip":""},{default:u(()=>[T.multiple?(x(),b(f,{key:0,width:"55"},{header:u(()=>[l(q,{modelValue:t(p),"onUpdate:modelValue":e[3]||(e[3]=a=>I(p)?p.value=a:null),indeterminate:t(y),onChange:A},null,8,["modelValue","indeterminate"])]),default:u(({row:a})=>[l(q,{modelValue:t(n)[a.id],"onUpdate:modelValue":r=>t(n)[a.id]=r,onChange:r=>D(r,a,!0)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1})):(x(),b(f,{key:1,label:"#",width:"55"},{default:u(({row:a})=>[l(O,{value:a.id,modelValue:t(_),"onUpdate:modelValue":e[4]||(e[4]=r=>I(_)?_.value=r:null),onChange:r=>{return z(J,g=a),s.value=!1,void(_.value=g.id);var g}},{default:u(()=>e[11]||(e[11]=[h(" \xA0 ")])),_:2},1032,["value","modelValue","onChange"])]),_:1})),l(f,{key:"id",align:"center",label:"\u5546\u54C1\u7F16\u53F7",prop:"id","min-width":"60"}),l(f,{label:"\u5546\u54C1\u56FE","min-width":"80"},{default:u(({row:a})=>[l(Q,{src:a.picUrl,class:"h-30px w-30px","preview-src-list":[a.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),l(f,{label:"\u5546\u54C1\u540D\u79F0","min-width":"200",prop:"name"}),l(f,{label:"\u5546\u54C1\u5206\u7C7B","min-width":"100",prop:"categoryId"},{default:u(({row:a})=>{var r,g;return[we("span",null,ye((g=(r=t(C))==null?void 0:r.find(ee=>ee.id===a.categoryId))==null?void 0:g.name),1)]}),_:1})]),_:1},8,["data"])),[[$,t(U)]]),l(Z,{limit:t(d).pageSize,"onUpdate:limit":e[5]||(e[5]=a=>t(d).pageSize=a),page:t(d).pageNo,"onUpdate:page":e[6]||(e[6]=a=>t(d).pageNo=a),total:t(S),onPagination:w},null,8,["limit","page","total"])]),_:1})]),_:2},[T.multiple?{name:"footer",fn:u(()=>[l(V,{type:"primary",onClick:j},{default:u(()=>e[12]||(e[12]=[h("\u786E \u5B9A")])),_:1}),l(V,{onClick:e[7]||(e[7]=a=>s.value=!1)},{default:u(()=>e[13]||(e[13]=[h("\u53D6 \u6D88")])),_:1})]),key:"0"}:void 0]),1032,["modelValue"])}}});export{Se as _};
