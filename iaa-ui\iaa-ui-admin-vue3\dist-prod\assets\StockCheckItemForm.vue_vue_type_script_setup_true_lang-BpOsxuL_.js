import{d as z,r as h,f as D,aK as S,q as O,c as C,o as m,F as U,J as Q,A as g,a3 as T,M as X,a as i,s as Y,w as l,g as e,K as Z,L as ee,v as ae,x as le,y as q,B as oe,P as te,eP as k,an as de,ea as E,G as ue,H as L,E as re,eN as se,eb as ie}from"./index-CRsFgzy0.js";import{P as ne}from"./index-v67yau6-.js";import{W as ce}from"./index-mM5XVEAg.js";import{S as pe}from"./index-CxlZ4TTH.js";const me=z({__name:"StockCheckItemForm",props:{items:{},disabled:{type:Boolean}},setup(j,{expose:F}){const H=j,K=h(!1),c=h([]),V=D({inId:[{required:!0,message:"\u76D8\u70B9\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],warehouseId:[{required:!0,message:"\u4ED3\u5E93\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),I=h([]),_=h([]),x=h([]),P=h(void 0);S(()=>H.items,async t=>{c.value=t},{immediate:!0}),S(()=>c.value,t=>{t&&t.length!==0&&t.forEach(d=>{d.stockCount!=null&&d.actualCount!=null?d.count=d.actualCount-d.stockCount:d.count=void 0,d.totalPrice=ie(d.productPrice,d.count)})},{deep:!0});const W=t=>{const{columns:d,data:s}=t,p=[];return d.forEach((f,r)=>{if(r!==0)if(["count","totalPrice"].includes(f.property)){const n=se(s.map(w=>Number(w[f.property])));p[r]=f.property==="count"?k(n):E(n)}else p[r]="";else p[r]="\u5408\u8BA1"}),p},y=()=>{var d;const t={id:void 0,warehouseId:(d=P.value)==null?void 0:d.id,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,actualCount:void 0,count:void 0,totalPrice:void 0,remark:void 0};c.value.push(t)},$=async t=>{if(!t.productId||!t.warehouseId)return;const d=await pe.getStock2(t.productId,t.warehouseId);t.stockCount=d?d.count:0,t.actualCount=t.stockCount};return F({validate:()=>I.value.validate()}),O(async()=>{_.value=await ne.getProductSimpleList(),x.value=await ce.getWarehouseSimpleList(),P.value=x.value.find(t=>t.defaultStatus),c.value.length===0&&y()}),(t,d)=>{const s=ee,p=oe,f=le,r=ae,n=te,w=de,N=ue,A=Z,G=Y,J=re,M=X;return m(),C(U,null,[Q((m(),g(G,{ref_key:"formRef",ref:I,model:i(c),rules:i(V),"label-width":"0px","inline-message":!0,disabled:t.disabled},{default:l(()=>[e(A,{data:i(c),"show-summary":"","summary-method":W,class:"-mt-10px"},{default:l(()=>[e(s,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(s,{label:"\u4ED3\u5E93\u540D\u5B57","min-width":"125"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.warehouseId`,rules:i(V).warehouseId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.warehouseId,"onUpdate:modelValue":o=>a.warehouseId=o,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93\u540D\u5B57",onChange:o=>((B,b)=>{$(b)})(0,a)},{default:l(()=>[(m(!0),C(U,null,q(i(x),o=>(m(),g(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.productId`,rules:i(V).productId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productId,"onUpdate:modelValue":o=>a.productId=o,clearable:"",filterable:"",onChange:o=>((B,b)=>{const v=_.value.find(R=>R.id===B);v&&(b.productUnitName=v.unitName,b.productBarCode=v.barCode,b.productPrice=v.minPrice),$(b)})(o,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(m(!0),C(U,null,q(i(_),o=>(m(),g(p,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u8D26\u9762\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":u=>a.stockCount=u,formatter:i(k)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(s,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":u=>a.productBarCode=u},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(s,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":u=>a.productUnitName=u},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(s,{label:"\u5B9E\u9645\u5E93\u5B58",fixed:"right","min-width":"140"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.actualCount`,rules:i(V).actualCount,class:"mb-0px!"},{default:l(()=>[e(w,{modelValue:a.actualCount,"onUpdate:modelValue":o=>a.actualCount=o,"controls-position":"right",precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u76C8\u4E8F\u6570\u91CF",prop:"count",fixed:"right","min-width":"110"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.count`,rules:i(V).count,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.count,"onUpdate:modelValue":o=>a.count=o,formatter:i(k),class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(w,{modelValue:a.productPrice,"onUpdate:modelValue":o=>a.productPrice=o,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(s,{label:"\u5408\u8BA1\u91D1\u989D",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":o=>a.totalPrice=o,formatter:i(E)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(s,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:u})=>[e(r,{prop:`${u}.remark`,class:"mb-0px!"},{default:l(()=>[e(n,{modelValue:a.remark,"onUpdate:modelValue":o=>a.remark=o,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(s,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e(N,{onClick:u=>{return o=a,void c.value.splice(o,1);var o},link:""},{default:l(()=>d[0]||(d[0]=[L("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[M,i(K)]]),t.disabled?T("",!0):(m(),g(J,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e(N,{onClick:y,round:""},{default:l(()=>d[1]||(d[1]=[L("+ \u6DFB\u52A0\u76D8\u70B9\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{me as _};
