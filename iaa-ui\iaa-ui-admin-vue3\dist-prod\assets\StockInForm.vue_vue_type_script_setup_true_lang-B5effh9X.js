import{as as r,d as K,b as N,p as O,r as i,f as Q,a2 as W,A as v,o as f,w as t,J as X,g as l,s as Z,a,E as ee,h as ae,v as le,P as te,C as se,x as oe,c as ue,F as de,y as ie,B as re,c2 as ne,M as pe,l as me,m as F,n as ce,a3 as fe,G as ve,H as C}from"./index-CRsFgzy0.js";import{_ as ke}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ye}from"./StockInItemForm.vue_vue_type_script_setup_true_lang-CRBCai2n.js";import{S as be}from"./index-BXOKnbFS.js";const k={getStockInPage:async u=>await r.get({url:"/erp/stock-in/page",params:u}),getStockIn:async u=>await r.get({url:"/erp/stock-in/get?id="+u}),createStockIn:async u=>await r.post({url:"/erp/stock-in/create",data:u}),updateStockIn:async u=>await r.put({url:"/erp/stock-in/update",data:u}),updateStockInStatus:async(u,_)=>await r.put({url:"/erp/stock-in/update-status",params:{id:u,status:_}}),deleteStockIn:async u=>await r.delete({url:"/erp/stock-in/delete",params:{ids:u.join(",")}}),exportStockIn:async u=>await r.download({url:"/erp/stock-in/export-excel",params:u})},we=K({name:"StockInForm",__name:"StockInForm",emits:["success"],setup(u,{expose:_,emit:A}){const{t:y}=N(),I=O(),n=i(!1),g=i(""),p=i(!1),b=i(""),o=i({id:void 0,supplierId:void 0,inTime:void 0,remark:void 0,fileUrl:"",items:[]}),B=Q({inTime:[{required:!0,message:"\u5165\u5E93\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),w=W(()=>b.value==="detail"),V=i(),U=i([]),S=i("item"),x=i();_({open:async(d,e)=>{if(n.value=!0,g.value=y("action."+d),b.value=d,M(),e){p.value=!0;try{o.value=await k.getStockIn(e)}finally{p.value=!1}}U.value=await be.getSupplierSimpleList()}});const E=A,G=async()=>{await V.value.validate(),await x.value.validate(),p.value=!0;try{const d=o.value;b.value==="create"?(await k.createStockIn(d),I.success(y("common.createSuccess"))):(await k.updateStockIn(d),I.success(y("common.updateSuccess"))),n.value=!1,E("success")}finally{p.value=!1}},M=()=>{var d;o.value={id:void 0,supplierId:void 0,inTime:void 0,remark:void 0,fileUrl:void 0,items:[]},(d=V.value)==null||d.resetFields()};return(d,e)=>{const h=te,m=le,c=ae,P=se,R=re,j=oe,q=ne,D=ee,H=Z,J=ce,L=me,Y=_e,T=ve,$=ke,z=pe;return f(),v($,{title:a(g),modelValue:a(n),"onUpdate:modelValue":e[7]||(e[7]=s=>F(n)?n.value=s:null),width:"1080"},{footer:t(()=>[a(w)?fe("",!0):(f(),v(T,{key:0,onClick:G,type:"primary",disabled:a(p)},{default:t(()=>e[8]||(e[8]=[C(" \u786E \u5B9A ")])),_:1},8,["disabled"])),l(T,{onClick:e[6]||(e[6]=s=>n.value=!1)},{default:t(()=>e[9]||(e[9]=[C("\u53D6 \u6D88")])),_:1})]),default:t(()=>[X((f(),v(H,{ref_key:"formRef",ref:V,model:a(o),rules:a(B),"label-width":"100px",disabled:a(w)},{default:t(()=>[l(D,{gutter:20},{default:t(()=>[l(c,{span:8},{default:t(()=>[l(m,{label:"\u5165\u5E93\u5355\u53F7",prop:"no"},{default:t(()=>[l(h,{disabled:"",modelValue:a(o).no,"onUpdate:modelValue":e[0]||(e[0]=s=>a(o).no=s),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{span:8},{default:t(()=>[l(m,{label:"\u5165\u5E93\u65F6\u95F4",prop:"inTime"},{default:t(()=>[l(P,{modelValue:a(o).inTime,"onUpdate:modelValue":e[1]||(e[1]=s=>a(o).inTime=s),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u5165\u5E93\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{span:8},{default:t(()=>[l(m,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:t(()=>[l(j,{modelValue:a(o).supplierId,"onUpdate:modelValue":e[2]||(e[2]=s=>a(o).supplierId=s),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:t(()=>[(f(!0),ue(de,null,ie(a(U),s=>(f(),v(R,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(c,{span:16},{default:t(()=>[l(m,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[l(h,{type:"textarea",modelValue:a(o).remark,"onUpdate:modelValue":e[3]||(e[3]=s=>a(o).remark=s),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),l(c,{span:8},{default:t(()=>[l(m,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[l(q,{"is-show-tip":!1,modelValue:a(o).fileUrl,"onUpdate:modelValue":e[4]||(e[4]=s=>a(o).fileUrl=s),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[z,a(p)]]),l(Y,null,{default:t(()=>[l(L,{modelValue:a(S),"onUpdate:modelValue":e[5]||(e[5]=s=>F(S)?S.value=s:null),class:"-mt-15px -mb-10px"},{default:t(()=>[l(J,{label:"\u5165\u5E93\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[l(ye,{ref_key:"itemFormRef",ref:x,items:a(o).items,disabled:a(w)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["title","modelValue"])}}});export{k as S,we as _};
