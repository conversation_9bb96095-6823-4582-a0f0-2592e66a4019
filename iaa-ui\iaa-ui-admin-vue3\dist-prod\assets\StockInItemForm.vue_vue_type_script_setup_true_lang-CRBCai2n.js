import{d as z,r as h,f as D,aK as B,q as O,c as U,o as c,F as I,J as Q,A as v,a3 as T,M as X,a as s,s as Y,w as l,g as e,K as Z,L as ee,v as ae,x as le,y as S,B as oe,P as de,eP as q,an as te,ea as E,G as re,H as L,E as ue,eN as ie,eb as se}from"./index-CRsFgzy0.js";import{P as ne}from"./index-v67yau6-.js";import{W as pe}from"./index-mM5XVEAg.js";import{S as me}from"./index-CxlZ4TTH.js";const ce=z({__name:"StockInItemForm",props:{items:{},disabled:{type:Boolean}},setup(j,{expose:F}){const H=j,K=h(!1),p=h([]),w=D({inId:[{required:!0,message:"\u5165\u5E93\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],warehouseId:[{required:!0,message:"\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),k=h([]),_=h([]),x=h([]),P=h(void 0);B(()=>H.items,async d=>{p.value=d},{immediate:!0}),B(()=>p.value,d=>{d&&d.length!==0&&d.forEach(r=>{r.totalPrice=se(r.productPrice,r.count)})},{deep:!0});const W=d=>{const{columns:r,data:i}=d,m=[];return r.forEach((f,u)=>{if(u!==0)if(["count","totalPrice"].includes(f.property)){const n=ie(i.map(V=>Number(V[f.property])));m[u]=f.property==="count"?q(n):E(n)}else m[u]="";else m[u]="\u5408\u8BA1"}),m},y=()=>{var r;const d={id:void 0,warehouseId:(r=P.value)==null?void 0:r.id,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalPrice:void 0,remark:void 0};p.value.push(d)},C=async d=>{if(!d.productId||!d.warehouseId)return;const r=await me.getStock2(d.productId,d.warehouseId);d.stockCount=r?r.count:0};return F({validate:()=>k.value.validate()}),O(async()=>{_.value=await ne.getProductSimpleList(),x.value=await pe.getWarehouseSimpleList(),P.value=x.value.find(d=>d.defaultStatus),p.value.length===0&&y()}),(d,r)=>{const i=ee,m=oe,f=le,u=ae,n=de,V=te,$=re,A=Z,G=Y,J=ue,M=X;return c(),U(I,null,[Q((c(),v(G,{ref_key:"formRef",ref:k,model:s(p),rules:s(w),"label-width":"0px","inline-message":!0,disabled:d.disabled},{default:l(()=>[e(A,{data:s(p),"show-summary":"","summary-method":W,class:"-mt-10px"},{default:l(()=>[e(i,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(i,{label:"\u4ED3\u5E93\u540D\u79F0","min-width":"125"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.warehouseId`,rules:s(w).warehouseId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.warehouseId,"onUpdate:modelValue":o=>a.warehouseId=o,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",onChange:o=>((N,b)=>{C(b)})(0,a)},{default:l(()=>[(c(!0),U(I,null,S(s(x),o=>(c(),v(m,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.productId`,rules:s(w).productId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productId,"onUpdate:modelValue":o=>a.productId=o,clearable:"",filterable:"",onChange:o=>((N,b)=>{const g=_.value.find(R=>R.id===N);g&&(b.productUnitName=g.unitName,b.productBarCode=g.barCode,b.productPrice=g.minPrice),C(b)})(o,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(c(!0),U(I,null,S(s(_),o=>(c(),v(m,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":t=>a.stockCount=t,formatter:s(q)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(i,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":t=>a.productBarCode=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(u,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":t=>a.productUnitName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(i,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.count`,rules:s(w).count,class:"mb-0px!"},{default:l(()=>[e(V,{modelValue:a.count,"onUpdate:modelValue":o=>a.count=o,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(V,{modelValue:a.productPrice,"onUpdate:modelValue":o=>a.productPrice=o,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5408\u8BA1\u91D1\u989D",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":o=>a.totalPrice=o,formatter:s(E)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(i,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:t})=>[e(u,{prop:`${t}.remark`,class:"mb-0px!"},{default:l(()=>[e(n,{modelValue:a.remark,"onUpdate:modelValue":o=>a.remark=o,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(i,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e($,{onClick:t=>{return o=a,void p.value.splice(o,1);var o},link:""},{default:l(()=>r[0]||(r[0]=[L("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[M,s(K)]]),d.disabled?T("",!0):(c(),v(J,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e($,{onClick:y,round:""},{default:l(()=>r[1]||(r[1]=[L("+ \u6DFB\u52A0\u5165\u5E93\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{ce as _};
