import{as as m,d as B,b as L,p as O,r,f as W,a2 as N,A as b,o as S,w as t,J as Q,g as l,s as X,a,E as Y,h as Z,v as $,P as ee,C as ae,c2 as le,M as te,l as oe,m as T,n as se,a3 as de,G as ue,H as F}from"./index-CRsFgzy0.js";import{_ as re}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ie}from"./StockMoveItemForm.vue_vue_type_script_setup_true_lang-BnTRdV7k.js";const n={getStockMovePage:async o=>await m.get({url:"/erp/stock-move/page",params:o}),getStockMove:async o=>await m.get({url:"/erp/stock-move/get?id="+o}),createStockMove:async o=>await m.post({url:"/erp/stock-move/create",data:o}),updateStockMove:async o=>await m.put({url:"/erp/stock-move/update",data:o}),updateStockMoveStatus:async(o,f)=>await m.put({url:"/erp/stock-move/update-status",params:{id:o,status:f}}),deleteStockMove:async o=>await m.delete({url:"/erp/stock-move/delete",params:{ids:o.join(",")}}),exportStockMove:async o=>await m.download({url:"/erp/stock-move/export-excel",params:o})},ce=B({name:"StockMoveForm",__name:"StockMoveForm",emits:["success"],setup(o,{expose:f,emit:C}){const{t:k}=L(),g=O(),i=r(!1),M=r(""),c=r(!1),_=r(""),s=r({id:void 0,customerId:void 0,moveTime:void 0,remark:void 0,fileUrl:"",items:[]}),I=W({moveTime:[{required:!0,message:"\u8C03\u5EA6\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),y=N(()=>_.value==="detail"),w=r(),V=r("item"),U=r();f({open:async(u,e)=>{if(i.value=!0,M.value=k("action."+u),_.value=u,j(),e){c.value=!0;try{s.value=await n.getStockMove(e)}finally{c.value=!1}}}});const P=C,R=async()=>{await w.value.validate(),await U.value.validate(),c.value=!0;try{const u=s.value;_.value==="create"?(await n.createStockMove(u),g.success(k("common.createSuccess"))):(await n.updateStockMove(u),g.success(k("common.updateSuccess"))),i.value=!1,P("success")}finally{c.value=!1}},j=()=>{var u;s.value={id:void 0,customerId:void 0,moveTime:void 0,remark:void 0,fileUrl:void 0,items:[]},(u=w.value)==null||u.resetFields()};return(u,e)=>{const x=ee,p=$,v=Z,q=ae,z=le,A=Y,D=X,E=se,G=oe,H=me,h=ue,J=re,K=te;return S(),b(J,{title:a(M),modelValue:a(i),"onUpdate:modelValue":e[6]||(e[6]=d=>T(i)?i.value=d:null),width:"1080"},{footer:t(()=>[a(y)?de("",!0):(S(),b(h,{key:0,onClick:R,type:"primary",disabled:a(c)},{default:t(()=>e[7]||(e[7]=[F(" \u786E \u5B9A ")])),_:1},8,["disabled"])),l(h,{onClick:e[5]||(e[5]=d=>i.value=!1)},{default:t(()=>e[8]||(e[8]=[F("\u53D6 \u6D88")])),_:1})]),default:t(()=>[Q((S(),b(D,{ref_key:"formRef",ref:w,model:a(s),rules:a(I),"label-width":"100px",disabled:a(y)},{default:t(()=>[l(A,{gutter:20},{default:t(()=>[l(v,{span:8},{default:t(()=>[l(p,{label:"\u8C03\u5EA6\u5355\u53F7",prop:"no"},{default:t(()=>[l(x,{disabled:"",modelValue:a(s).no,"onUpdate:modelValue":e[0]||(e[0]=d=>a(s).no=d),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),l(v,{span:8},{default:t(()=>[l(p,{label:"\u8C03\u5EA6\u65F6\u95F4",prop:"moveTime"},{default:t(()=>[l(q,{modelValue:a(s).moveTime,"onUpdate:modelValue":e[1]||(e[1]=d=>a(s).moveTime=d),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u8C03\u5EA6\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),l(v,{span:16},{default:t(()=>[l(p,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[l(x,{type:"textarea",modelValue:a(s).remark,"onUpdate:modelValue":e[2]||(e[2]=d=>a(s).remark=d),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),l(v,{span:8},{default:t(()=>[l(p,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[l(z,{"is-show-tip":!1,modelValue:a(s).fileUrl,"onUpdate:modelValue":e[3]||(e[3]=d=>a(s).fileUrl=d),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[K,a(c)]]),l(H,null,{default:t(()=>[l(G,{modelValue:a(V),"onUpdate:modelValue":e[4]||(e[4]=d=>T(V)?V.value=d:null),class:"-mt-15px -mb-10px"},{default:t(()=>[l(E,{label:"\u8C03\u5EA6\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[l(ie,{ref_key:"itemFormRef",ref:U,items:a(s).items,disabled:a(y)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["title","modelValue"])}}});export{n as S,ce as _};
