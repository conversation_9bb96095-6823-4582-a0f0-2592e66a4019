import{d as z,r as h,f as D,aK as q,q as O,c as x,o as n,F as I,J as Q,A as g,a3 as T,M as X,a as i,s as Y,w as o,g as e,K as Z,L as ee,v as le,x as ae,y as k,B as oe,P as de,eP as B,an as te,ea as S,G as re,H as E,E as ue,eN as se,eb as ie}from"./index-CRsFgzy0.js";import{P as ne}from"./index-v67yau6-.js";import{W as me}from"./index-mM5XVEAg.js";import{S as pe}from"./index-CxlZ4TTH.js";const ce=z({__name:"StockMoveItemForm",props:{items:{},disabled:{type:Boolean}},setup(L,{expose:j}){const F=L,G=h(!1),c=h([]),V=D({inId:[{required:!0,message:"\u8C03\u5EA6\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],fromWarehouseId:[{required:!0,message:"\u8C03\u51FA\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],toWarehouseId:[{required:!0,message:"\u8C03\u5165\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),P=h([]),U=h([]),v=h([]),y=h(void 0);q(()=>F.items,async d=>{c.value=d},{immediate:!0}),q(()=>c.value,d=>{d&&d.length!==0&&d.forEach(r=>{r.totalPrice=ie(r.productPrice,r.count)})},{deep:!0});const K=d=>{const{columns:r,data:s}=d,m=[];return r.forEach((f,u)=>{if(u!==0)if(["count","totalPrice"].includes(f.property)){const p=se(s.map(_=>Number(_[f.property])));m[u]=f.property==="count"?B(p):S(p)}else m[u]="";else m[u]="\u5408\u8BA1"}),m},W=()=>{var r;const d={id:void 0,fromWarehouseId:(r=y.value)==null?void 0:r.id,toWarehouseId:void 0,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalPrice:void 0,remark:void 0};c.value.push(d)},C=async d=>{if(!d.productId||!d.fromWarehouseId)return;const r=await pe.getStock2(d.productId,d.fromWarehouseId);d.stockCount=r?r.count:0};return j({validate:()=>P.value.validate()}),O(async()=>{U.value=await ne.getProductSimpleList(),v.value=await me.getWarehouseSimpleList(),y.value=v.value.find(d=>d.defaultStatus),c.value.length===0&&W()}),(d,r)=>{const s=ee,m=oe,f=ae,u=le,p=de,_=te,$=re,M=Z,A=Y,H=ue,J=X;return n(),x(I,null,[Q((n(),g(A,{ref_key:"formRef",ref:P,model:i(c),rules:i(V),"label-width":"0px","inline-message":!0,disabled:d.disabled},{default:o(()=>[e(M,{data:i(c),"show-summary":"","summary-method":K,class:"-mt-10px"},{default:o(()=>[e(s,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(s,{label:"\u8C03\u51FA\u4ED3\u5E93","min-width":"125"},{default:o(({row:l,$index:t})=>[e(u,{prop:`${t}.fromWarehouseId`,rules:i(V).fromWarehouseId,class:"mb-0px!"},{default:o(()=>[e(f,{modelValue:l.fromWarehouseId,"onUpdate:modelValue":a=>l.fromWarehouseId=a,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8C03\u51FA\u4ED3\u5E93",onChange:a=>((N,b)=>{C(b)})(0,l)},{default:o(()=>[(n(!0),x(I,null,k(i(v),a=>(n(),g(m,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u8C03\u5165\u4ED3\u5E93","min-width":"125"},{default:o(({row:l,$index:t})=>[e(u,{prop:`${t}.toWarehouseId`,rules:i(V).toWarehouseId,class:"mb-0px!"},{default:o(()=>[e(f,{modelValue:l.toWarehouseId,"onUpdate:modelValue":a=>l.toWarehouseId=a,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8C03\u5165\u4ED3\u5E93"},{default:o(()=>[(n(!0),x(I,null,k(i(v),a=>(n(),g(m,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:o(({row:l,$index:t})=>[e(u,{prop:`${t}.productId`,rules:i(V).productId,class:"mb-0px!"},{default:o(()=>[e(f,{modelValue:l.productId,"onUpdate:modelValue":a=>l.productId=a,clearable:"",filterable:"",onChange:a=>((N,b)=>{const w=U.value.find(R=>R.id===N);w&&(b.productUnitName=w.unitName,b.productBarCode=w.barCode,b.productPrice=w.minPrice),C(b)})(a,l),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:o(()=>[(n(!0),x(I,null,k(i(U),a=>(n(),g(m,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u5E93\u5B58","min-width":"100"},{default:o(({row:l})=>[e(u,{class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:l.stockCount,"onUpdate:modelValue":t=>l.stockCount=t,formatter:i(B)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(s,{label:"\u6761\u7801","min-width":"150"},{default:o(({row:l})=>[e(u,{class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:l.productBarCode,"onUpdate:modelValue":t=>l.productBarCode=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(s,{label:"\u5355\u4F4D","min-width":"80"},{default:o(({row:l})=>[e(u,{class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:l.productUnitName,"onUpdate:modelValue":t=>l.productUnitName=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(s,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:o(({row:l,$index:t})=>[e(u,{prop:`${t}.count`,rules:i(V).count,class:"mb-0px!"},{default:o(()=>[e(_,{modelValue:l.count,"onUpdate:modelValue":a=>l.count=a,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(s,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:o(({row:l,$index:t})=>[e(u,{prop:`${t}.productPrice`,class:"mb-0px!"},{default:o(()=>[e(_,{modelValue:l.productPrice,"onUpdate:modelValue":a=>l.productPrice=a,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(s,{label:"\u5408\u8BA1\u91D1\u989D",prop:"totalPrice",fixed:"right","min-width":"100"},{default:o(({row:l,$index:t})=>[e(u,{prop:`${t}.totalPrice`,class:"mb-0px!"},{default:o(()=>[e(p,{disabled:"",modelValue:l.totalPrice,"onUpdate:modelValue":a=>l.totalPrice=a,formatter:i(S)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(s,{label:"\u5907\u6CE8","min-width":"150"},{default:o(({row:l,$index:t})=>[e(u,{prop:`${t}.remark`,class:"mb-0px!"},{default:o(()=>[e(p,{modelValue:l.remark,"onUpdate:modelValue":a=>l.remark=a,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(s,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:o(({$index:l})=>[e($,{onClick:t=>{return a=l,void c.value.splice(a,1);var a},link:""},{default:o(()=>r[0]||(r[0]=[E("\u2014")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[J,i(G)]]),d.disabled?T("",!0):(n(),g(H,{key:0,justify:"center",class:"mt-3"},{default:o(()=>[e($,{onClick:W,round:""},{default:o(()=>r[1]||(r[1]=[E("+ \u6DFB\u52A0\u8C03\u5EA6\u4EA7\u54C1")])),_:1})]),_:1}))],64)}}});export{ce as _};
