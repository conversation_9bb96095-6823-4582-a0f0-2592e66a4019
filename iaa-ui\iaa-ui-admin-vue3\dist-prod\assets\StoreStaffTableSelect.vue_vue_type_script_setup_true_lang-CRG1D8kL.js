import{d as ee,r as s,f as le,A as k,o as V,w as o,g as l,E as ae,h as te,s as oe,a as t,v as ue,P as de,Q as Y,x as se,c as ne,F as re,y as ie,R as pe,D as A,B as me,C as ce,G as fe,H as w,I as ve,J as _e,K as be,L as ge,a4 as he,m as O,M as ye}from"./index-CRsFgzy0.js";import{_ as Ve}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{_ as we}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as xe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as Ue}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{d as ke}from"./formatTime-DhdtkSIS.js";import{a as Ce}from"./index-D4y5Z4cM.js";import{_ as Se}from"./DeptTree.vue_vue_type_script_setup_true_lang-Cj24ZQp6.js";const Te=ee({__name:"StoreStaffTableSelect",emits:["change"],setup(Ne,{expose:z,emit:D}){const r=s(!1),x=s(!1),b=s([]),i=s({}),p=s(!1),m=s(!0),C=s(0),c=s([]),d=le({pageNo:1,pageSize:10,username:void 0,mobile:void 0,status:void 0,deptId:void 0,roleId:5,createTime:[]}),S=s(),g=async()=>{m.value=!0;try{const u=await Ce(d);c.value=u.list,C.value=u.total}finally{m.value=!1}},h=()=>{d.pageNo=1,g()},E=()=>{var u;(u=S.value)==null||u.resetFields(),h()},F=async u=>{d.deptId=u.id,await g()};z({open:async()=>{p.value=!0,m.value=!0;try{await g()}finally{m.value=!1}}});const H=u=>{r.value=u,x.value=!1,c.value.forEach(e=>T(u,e,!1))},T=(u,e,f)=>{if(u)b.value.push(e),i.value[e.id]=!0;else{const v=K(e);v>-1&&(b.value.splice(v,1),i.value[e.id]=!1,r.value=!1)}f&&P()},K=u=>b.value.findIndex(e=>e.id===u.id),P=()=>{r.value=c.value.every(u=>i.value[u.id]),x.value=!r.value&&c.value.some(u=>i.value[u.id])},R=()=>{p.value=!1,q("change",[...b.value])},q=D;return(u,e)=>{const f=Ue,v=te,N=de,_=ue,B=me,G=se,J=ce,M=ve,y=fe,L=oe,I=he,n=ge,Q=xe,j=be,W=we,X=ae,Z=Ve,$=ye;return V(),k(Z,{title:"\u9009\u62E9\u5E97\u5458",modelValue:t(p),"onUpdate:modelValue":e[8]||(e[8]=a=>O(p)?p.value=a:null),width:"60%"},{footer:o(()=>[l(y,{type:"primary",onClick:R},{default:o(()=>e[11]||(e[11]=[w("\u786E \u5B9A")])),_:1}),l(y,{onClick:e[7]||(e[7]=a=>p.value=!1)},{default:o(()=>e[12]||(e[12]=[w("\u53D6 \u6D88")])),_:1})]),default:o(()=>[l(X,{gutter:20},{default:o(()=>[l(v,{span:4,xs:24},{default:o(()=>[l(f,{class:"h-1/1"},{default:o(()=>[l(Se,{onNodeClick:F})]),_:1})]),_:1}),l(v,{span:20,xs:24},{default:o(()=>[l(f,null,{default:o(()=>[l(L,{class:"-mb-15px",model:t(d),ref_key:"queryFormRef",ref:S,inline:!0,"label-width":"68px"},{default:o(()=>[l(_,{label:"\u7528\u6237\u540D\u79F0",prop:"username"},{default:o(()=>[l(N,{modelValue:t(d).username,"onUpdate:modelValue":e[0]||(e[0]=a=>t(d).username=a),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0",clearable:"",onKeyup:Y(h,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(_,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:o(()=>[l(N,{modelValue:t(d).mobile,"onUpdate:modelValue":e[1]||(e[1]=a=>t(d).mobile=a),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801",clearable:"",onKeyup:Y(h,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(_,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[l(G,{modelValue:t(d).status,"onUpdate:modelValue":e[2]||(e[2]=a=>t(d).status=a),placeholder:"\u7528\u6237\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(V(!0),ne(re,null,ie(t(pe)(t(A).COMMON_STATUS),a=>(V(),k(B,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(_,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:o(()=>[l(J,{modelValue:t(d).createTime,"onUpdate:modelValue":e[3]||(e[3]=a=>t(d).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"datetimerange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F",class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(_,null,{default:o(()=>[l(y,{onClick:h},{default:o(()=>[l(M,{icon:"ep:search"}),e[9]||(e[9]=w("\u641C\u7D22"))]),_:1}),l(y,{onClick:E},{default:o(()=>[l(M,{icon:"ep:refresh"}),e[10]||(e[10]=w("\u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(f,null,{default:o(()=>[_e((V(),k(j,{data:t(c)},{default:o(()=>[l(n,{width:"55"},{header:o(()=>[l(I,{modelValue:t(r),"onUpdate:modelValue":e[4]||(e[4]=a=>O(r)?r.value=a:null),indeterminate:t(x),onChange:H},null,8,["modelValue","indeterminate"])]),default:o(({row:a})=>[l(I,{modelValue:t(i)[a.id],"onUpdate:modelValue":U=>t(i)[a.id]=U,onChange:U=>T(U,a,!0)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),l(n,{label:"\u7528\u6237\u7F16\u53F7",align:"center",key:"id",prop:"id"}),l(n,{label:"\u7528\u6237\u540D\u79F0",align:"center",prop:"username","show-overflow-tooltip":!0}),l(n,{label:"\u7528\u6237\u6635\u79F0",align:"center",prop:"nickname","show-overflow-tooltip":!0}),l(n,{label:"\u90E8\u95E8",align:"center",key:"deptName",prop:"deptName","show-overflow-tooltip":!0}),l(n,{label:"\u624B\u673A\u53F7\u7801",align:"center",prop:"mobile",width:"120"}),l(n,{label:"\u72B6\u6001",key:"status"},{default:o(a=>[l(Q,{type:t(A).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),l(n,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(ke),width:"180"},null,8,["formatter"])]),_:1},8,["data"])),[[$,t(m)]]),l(W,{total:t(C),page:t(d).pageNo,"onUpdate:page":e[5]||(e[5]=a=>t(d).pageNo=a),limit:t(d).pageSize,"onUpdate:limit":e[6]||(e[6]=a=>t(d).pageSize=a),onPagination:g},null,8,["total","page","limit"])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])}}});export{Te as _};
