import{_ as r}from"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";import{d as t,ah as s,c as i,o as n,i as e,t as m,g as o}from"./index-CRsFgzy0.js";const d={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},u={class:"flex items-center justify-between text-gray-500"},f={class:"flex flex-row items-baseline justify-between"},p=t({name:"ErpSummaryCard",__name:"SummaryCard",props:{title:s.string.def("").isRequired,value:s.number.def(0).isRequired},setup:a=>(c,x)=>{const l=r;return n(),i("div",d,[e("div",u,[e("span",null,m(a.title),1)]),e("div",f,[o(l,{prefix:"\uFFE5","end-val":a.value,decimals:2,duration:500,class:"text-3xl"},null,8,["end-val"])])])}});export{p as _};
