import{a as l,d as M,a2 as k,r as x,c as _,o as d,g as a,E as U,w as t,a3 as f,A as j,h as N,G as P,I as S,H as z,t as C,cz as O,m as q,_ as A}from"./index-CRsFgzy0.js";import D from"./main-3TzxBWwr.js";import{W as E}from"./main-87AQXDxa.js";var v=(e=>(e.News="news",e.Image="image",e.Voice="voice",e.Video="video",e.Music="music",e.Text="text",e))(v||{}),i=(e=>(e.Published="1",e.Draft="2",e))(i||{});const G=e=>({accountId:l(e).accountId,type:l(e).type,name:null,content:null,mediaId:null,url:null,title:null,description:null,thumbMediaId:null,thumbMediaUrl:null,musicUrl:null,hqMusicUrl:null,introduction:null,articles:[]}),H={key:0,class:"select-item"},w=A(M({__name:"TabNews",props:{modelValue:{},newsType:{}},emits:["update:modelValue"],setup(e,{emit:h}){const T=e,b=h,s=k({get:()=>T.modelValue,set:n=>b("update:modelValue",n)}),u=x(!1),g=n=>{u.value=!1,s.value.articles=n.content.newsItem},I=()=>{s.value.articles=[]};return(n,c)=>{const r=S,p=P,o=N,m=U,V=O;return d(),_("div",null,[a(m,null,{default:t(()=>[l(s).articles&&l(s).articles.length>0?(d(),_("div",H,[a(l(D),{articles:l(s).articles},null,8,["articles"]),a(o,{class:"ope-row"},{default:t(()=>[a(p,{type:"danger",circle:"",onClick:I},{default:t(()=>[a(r,{icon:"ep:delete"})]),_:1})]),_:1})])):f("",!0),l(s).content?f("",!0):(d(),j(o,{key:1,span:24},{default:t(()=>[a(m,{style:{"text-align":"center"},align:"middle"},{default:t(()=>[a(o,{span:24},{default:t(()=>[a(p,{type:"success",onClick:c[0]||(c[0]=y=>u.value=!0)},{default:t(()=>[z(C(n.newsType===l(i).Published?"\u9009\u62E9\u5DF2\u53D1\u5E03\u56FE\u6587":"\u9009\u62E9\u8349\u7A3F\u7BB1\u56FE\u6587")+" ",1),a(r,{icon:"ep:circle-check"})]),_:1})]),_:1})]),_:1})]),_:1})),a(V,{title:"\u9009\u62E9\u56FE\u6587",modelValue:l(u),"onUpdate:modelValue":c[1]||(c[1]=y=>q(u)?u.value=y:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:t(()=>[a(l(E),{type:"news","account-id":l(s).accountId,newsType:n.newsType,onSelectMaterial:g},null,8,["account-id","newsType"])]),_:1},8,["modelValue"])]),_:1})])}}}),[["__scopeId","data-v-bddd0a87"]]),R=Object.freeze(Object.defineProperty({__proto__:null,default:w},Symbol.toStringTag,{value:"Module"}));export{i as N,v as R,w as T,R as a,G as c};
