import{d as p,a2 as m,A as c,o as i,P as V,a as f,m as v}from"./index-CRsFgzy0.js";const x=p({__name:"TabText",props:{modelValue:{}},emits:["update:modelValue","input","success"],setup(o,{emit:s}){const u=o,a=s,t=m({get:()=>u.modelValue,set:e=>{a("update:modelValue",e),a("input",e)}}),n=e=>{e.key==="Enter"&&(e.getModifierState("Control")?(t.value+=`
`,e.preventDefault()):(e.preventDefault(),a("success")))};return(e,l)=>{const r=V;return i(),c(r,{type:"textarea",rows:5,placeholder:"\u8BF7\u8F93\u5165\u6D88\u606F(ctrl+Enter \u6362\u884C\uFF0CEnter \u53D1\u9001)",modelValue:f(t),"onUpdate:modelValue":l[0]||(l[0]=d=>v(t)?t.value=d:null),onKeydown:n},null,8,["modelValue"])}}});export{x as _};
