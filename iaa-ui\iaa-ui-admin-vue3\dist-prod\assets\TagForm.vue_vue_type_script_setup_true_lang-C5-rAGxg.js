import{d as T,b as U,p as q,r as o,A as y,o as b,w as r,J as A,s as j,a as s,g as d,v as G,P as H,M as J,G as P,H as g,m as R}from"./index-CRsFgzy0.js";import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{g as B,c as D,u as E}from"./index-C6FPxIVQ.js";const K=T({name:"MpTagForm",__name:"TagForm",emits:["success"],setup(L,{expose:w,emit:V}){const{t:i}=U(),v=q(),u=o(!1),f=o(""),t=o(!1),p=o(""),l=o({accountId:-1,name:""}),x={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u6807\u7B7E\u540D\u79F0",trigger:"blur"}]},c=o(null),k=V;w({open:async(a,e,m)=>{if(u.value=!0,f.value=i("action."+a),p.value=a,I(),l.value.accountId=e,m){t.value=!0;try{l.value=await B(m)}finally{t.value=!1}}}});const F=async()=>{var a;if(c&&await((a=c.value)==null?void 0:a.validate())){t.value=!0;try{const e=l.value;p.value==="create"?(await D(e),v.success(i("common.createSuccess"))):(await E(e),v.success(i("common.updateSuccess"))),u.value=!1,k("success")}finally{t.value=!1}}},I=()=>{var a;l.value={accountId:-1,name:""},(a=c.value)==null||a.resetFields()};return(a,e)=>{const m=H,h=G,C=j,_=P,M=z,S=J;return b(),y(M,{modelValue:s(u),"onUpdate:modelValue":e[2]||(e[2]=n=>R(u)?u.value=n:null),title:s(f)},{footer:r(()=>[d(_,{disabled:s(t),type:"primary",onClick:F},{default:r(()=>e[3]||(e[3]=[g("\u786E \u5B9A")])),_:1},8,["disabled"]),d(_,{onClick:e[1]||(e[1]=n=>u.value=!1)},{default:r(()=>e[4]||(e[4]=[g("\u53D6 \u6D88")])),_:1})]),default:r(()=>[A((b(),y(C,{ref_key:"formRef",ref:c,model:s(l),rules:x,"label-width":"80px"},{default:r(()=>[d(h,{label:"\u6807\u7B7E\u540D\u79F0",prop:"name"},{default:r(()=>[d(m,{modelValue:s(l).name,"onUpdate:modelValue":e[0]||(e[0]=n=>s(l).name=n),placeholder:"\u8BF7\u8F93\u5165\u6807\u7B7E\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[S,s(t)]])]),_:1},8,["modelValue","title"])}}});export{K as _};
