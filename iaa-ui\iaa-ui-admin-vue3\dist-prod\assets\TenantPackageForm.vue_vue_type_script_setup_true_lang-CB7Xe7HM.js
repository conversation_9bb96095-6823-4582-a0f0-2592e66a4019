import{d as D,b as G,p as J,r,f as L,A as g,o as y,w as u,J as j,s as z,a,g as t,v as Q,P as W,k as X,cr as Y,H as f,a0 as Z,m as h,aB as $,c as ee,F as ae,y as le,R as se,D as ue,aC as te,t as oe,M as re,G as de}from"./index-CRsFgzy0.js";import{_ as ne}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{C as N}from"./constants-uird_4gU.js";import{d as me,h as ce}from"./tree-COGD3qag.js";import{a as ie,c as ve,u as pe}from"./index-5Q3zFXXS.js";import{g as fe}from"./index-De4pDBF3.js";const ke=D({name:"SystemTenantPackageForm",__name:"TenantPackageForm",emits:["success"],setup(ye,{expose:A,emit:E}){const{t:V}=G(),b=J(),m=r(!1),x=r(""),c=r(!1),C=r(""),o=r({id:null,name:null,remark:null,menuIds:[],status:N.ENABLE}),F=L({name:[{required:!0,message:"\u5957\u9910\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],menuIds:[{required:!0,message:"\u5173\u8054\u7684\u83DC\u5355\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),k=r(),_=r([]),i=r(!1),n=r(),p=r(!1);A({open:async(s,e)=>{if(m.value=!0,x.value=V("action."+s),C.value=s,S(),_.value=ce(await fe()),e){c.value=!0;try{const d=await ie(e);o.value=d,d.menuIds.forEach(v=>{n.value.setChecked(v,!0,!1)})}finally{c.value=!1}}}});const I=E,M=async()=>{if(k&&await k.value.validate()){c.value=!0;try{const s=o.value;s.menuIds=[...n.value.getCheckedKeys(!1),...n.value.getHalfCheckedKeys()],C.value==="create"?(await ve(s),b.success(V("common.createSuccess"))):(await pe(s),b.success(V("common.updateSuccess"))),m.value=!1,I("success")}finally{c.value=!1}}},S=()=>{var s,e;p.value=!1,i.value=!1,o.value={id:null,name:null,remark:null,menuIds:[],status:N.ENABLE},(s=n.value)==null||s.setCheckedNodes([]),(e=k.value)==null||e.resetFields()},T=()=>{n.value.setCheckedNodes(p.value?_.value:[])},q=()=>{var e;const s=(e=n.value)==null?void 0:e.store.nodesMap;for(let d in s)s[d].expanded!==i.value&&(s[d].expanded=i.value)};return(s,e)=>{const d=W,v=Q,w=Z,B=X,P=te,R=$,H=z,U=de,K=ne,O=re;return y(),g(K,{modelValue:a(m),"onUpdate:modelValue":e[6]||(e[6]=l=>h(m)?m.value=l:null),title:a(x)},{footer:u(()=>[t(U,{disabled:a(c),type:"primary",onClick:M},{default:u(()=>e[9]||(e[9]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),t(U,{onClick:e[5]||(e[5]=l=>m.value=!1)},{default:u(()=>e[10]||(e[10]=[f("\u53D6 \u6D88")])),_:1})]),default:u(()=>[j((y(),g(H,{ref_key:"formRef",ref:k,model:a(o),rules:a(F),"label-width":"80px"},{default:u(()=>[t(v,{label:"\u5957\u9910\u540D",prop:"name"},{default:u(()=>[t(d,{modelValue:a(o).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).name=l),placeholder:"\u8BF7\u8F93\u5165\u5957\u9910\u540D"},null,8,["modelValue"])]),_:1}),t(v,{label:"\u83DC\u5355\u6743\u9650"},{default:u(()=>[t(B,{class:"w-full h-400px !overflow-y-scroll",shadow:"never"},{header:u(()=>[e[7]||(e[7]=f(" \u5168\u9009/\u5168\u4E0D\u9009: ")),t(w,{modelValue:a(p),"onUpdate:modelValue":e[1]||(e[1]=l=>h(p)?p.value=l:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:T},null,8,["modelValue"]),e[8]||(e[8]=f(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: ")),t(w,{modelValue:a(i),"onUpdate:modelValue":e[2]||(e[2]=l=>h(i)?i.value=l:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:q},null,8,["modelValue"])]),default:u(()=>[t(a(Y),{ref_key:"treeRef",ref:n,data:a(_),props:a(me),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u5019","node-key":"id","show-checkbox":""},null,8,["data","props"])]),_:1})]),_:1}),t(v,{label:"\u72B6\u6001",prop:"status"},{default:u(()=>[t(R,{modelValue:a(o).status,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).status=l)},{default:u(()=>[(y(!0),ee(ae,null,le(a(se)(a(ue).COMMON_STATUS),l=>(y(),g(P,{key:l.value,value:l.value},{default:u(()=>[f(oe(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,{label:"\u5907\u6CE8",prop:"remark"},{default:u(()=>[t(d,{modelValue:a(o).remark,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[O,a(c)]])]),_:1},8,["modelValue","title"])}}});export{ke as _};
