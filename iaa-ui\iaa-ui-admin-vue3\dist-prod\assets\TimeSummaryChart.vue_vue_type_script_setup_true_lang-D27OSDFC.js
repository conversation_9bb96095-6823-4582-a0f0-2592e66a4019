import{d,ah as s,f as p,aK as u,A as c,o as h,w as i,g as o,a as r,k as g}from"./index-CRsFgzy0.js";import{_ as x}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{C as y}from"./CardTitle-DCrrQp54.js";const f=d({name:"MemberStatisticsCard",__name:"TimeSummaryChart",props:{title:s.string.def("").isRequired,value:s.object.isRequired},setup(n){const t=n,e=p({dataset:{dimensions:["time","price"],source:[]},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50},series:[{name:"\u91D1\u989D",type:"line",smooth:!0,areaStyle:{}}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:t.title}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",boundaryGap:!1,axisTick:{show:!1}},yAxis:{axisTick:{show:!1}}});return u(()=>t.value,a=>{a&&e.dataset&&e.dataset.source&&(e.dataset.source=a)}),(a,b)=>{const l=x,m=g;return h(),c(m,{shadow:"never"},{header:i(()=>[o(r(y),{title:t.title},null,8,["title"])]),default:i(()=>[o(l,{height:300,options:r(e)},null,8,["options"])]),_:1})}}});export{f as _};
