import{d as o,ah as s,c as i,o as l,F as p,i as c,g as e,t as r,bi as m,w as u,I as d}from"./index-CRsFgzy0.js";const f=o({name:"Toolt<PERSON>",__name:"Tooltip",props:{title:s.string.def(""),message:s.string.def(""),icon:s.string.def("ep:question-filled")},setup:t=>(g,_)=>{const a=d,n=m;return l(),i(p,null,[c("span",null,r(t.title),1),e(n,{content:t.message,placement:"top"},{default:u(()=>[e(a,{icon:t.icon,class:"relative top-1px ml-1px"},null,8,["icon"])]),_:1},8,["content"])],64)}});export{f as _};
