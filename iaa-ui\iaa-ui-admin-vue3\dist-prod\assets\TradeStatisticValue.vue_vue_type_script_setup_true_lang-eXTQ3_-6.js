import{_ as f}from"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";import{d as m,ah as a,c as x,o as c,i as t,A as u,a3 as g,t as o,w as b,g as s,I as v,bi as y,X as _,a as l,dW as r,H as w}from"./index-CRsFgzy0.js";const S={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},T={class:"flex items-center justify-between text-gray-500"},h={class:"mb-4 text-3xl"},j={class:"flex flex-row gap-1 text-sm"},V=m({name:"TradeStatisticValue",__name:"TradeStatisticValue",props:{tooltip:a.string.def(""),title:a.string.def(""),prefix:a.string.def(""),value:a.number.def(0),decimals:a.number.def(0),percent:a.oneOfType([Number,String]).def(0)},setup:e=>(k,n)=>{const i=v,p=y,d=f;return c(),x("div",S,[t("div",T,[t("span",null,o(e.title),1),e.tooltip?(c(),u(p,{key:0,content:e.tooltip,placement:"top-start"},{default:b(()=>[s(i,{icon:"ep:warning"})]),_:1},8,["content"])):g("",!0)]),t("div",h,[s(d,{prefix:e.prefix,"end-val":e.value,decimals:e.decimals},null,8,["prefix","end-val","decimals"])]),t("div",j,[n[0]||(n[0]=t("span",{class:"text-gray-500"},"\u73AF\u6BD4",-1)),t("span",{class:_(l(r)(e.percent)>0?"text-red-500":"text-green-500")},[w(o(Math.abs(l(r)(e.percent)))+"% ",1),s(i,{icon:l(r)(e.percent)>0?"ep:caret-top":"ep:caret-bottom",class:"!text-sm"},null,8,["icon"])],2)])])}});export{V as _};
