import{d as A,r as O,f as M,q as D,A as C,o as c,w as u,g as f,a as p,i as T,aB as V,m as j,c as I,F as L,y as q,cf as B,H as F,t as G,k as H,ck as r,aR as b}from"./index-CRsFgzy0.js";import{_ as R}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{a as U}from"./trade-Dp4I9n8g.js";import{f as h}from"./formatTime-DhdtkSIS.js";import{C as X}from"./CardTitle-DCrrQp54.js";const Z={class:"flex flex-row items-center justify-between"},z={class:"flex flex-row items-center gap-2"},E=A({name:"TradeTrendCard",__name:"TradeTrendCard",setup(J){const n=O(1),v=O(!0),x=new Map().set(1,{name:"30\u5929",series:[{name:"\u8BA2\u5355\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u8BA2\u5355\u6570\u91CF",type:"line",smooth:!0,data:[]}]}).set(7,{name:"\u5468",series:[{name:"\u4E0A\u5468\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u672C\u5468\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u4E0A\u5468\u6570\u91CF",type:"line",smooth:!0,data:[]},{name:"\u672C\u5468\u6570\u91CF",type:"line",smooth:!0,data:[]}]}).set(30,{name:"\u6708",series:[{name:"\u4E0A\u6708\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u672C\u6708\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u4E0A\u6708\u6570\u91CF",type:"line",smooth:!0,data:[]},{name:"\u672C\u6708\u6570\u91CF",type:"line",smooth:!0,data:[]}]}).set(365,{name:"\u5E74",series:[{name:"\u53BB\u5E74\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u4ECA\u5E74\u91D1\u989D",type:"bar",smooth:!0,data:[]},{name:"\u53BB\u5E74\u6570\u91CF",type:"line",smooth:!0,data:[]},{name:"\u4ECA\u5E74\u6570\u91CF",type:"line",smooth:!0,data:[]}]}),d=M({grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50,data:[]},series:[],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u8BA2\u5355\u91CF\u8D8B\u52BF"}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",inverse:!0,boundaryGap:!1,axisTick:{show:!1},data:[],axisLabel:{formatter:e=>{switch(n.value){case 1:return h(e,"MM-DD");case 7:let t=h(e,"ddd");return t=="0"&&(t="\u65E5"),"\u5468"+t;case 30:return h(e,"D");case 365:return h(e,"M")+"\u6708";default:return e}}}},yAxis:{axisTick:{show:!1}}}),g=async()=>{let e,t;switch(n.value){case 7:e=r().startOf("week"),t=r().endOf("week");break;case 30:e=r().startOf("month"),t=r().endOf("month");break;case 365:e=r().startOf("year"),t=r().endOf("year");break;default:e=r().subtract(30,"day").startOf("d"),t=r().endOf("d")}await _(e,t)},_=async(e,t)=>{var l,o,i,w,k,P;v.value=!0;const y=await U(n.value,e,t),m=[],s=[...x.get(n.value).series];for(let a of y)m.push(a.value.date),s.length===2?(s[0].data.push(b(((l=a==null?void 0:a.value)==null?void 0:l.orderPayPrice)||0)),s[1].data.push(((o=a==null?void 0:a.value)==null?void 0:o.orderPayCount)||0)):(s[0].data.push(b(((i=a==null?void 0:a.reference)==null?void 0:i.orderPayPrice)||0)),s[1].data.push(b(((w=a==null?void 0:a.value)==null?void 0:w.orderPayPrice)||0)),s[2].data.push(((k=a==null?void 0:a.reference)==null?void 0:k.orderPayCount)||0),s[3].data.push(((P=a==null?void 0:a.value)==null?void 0:P.orderPayCount)||0));d.xAxis.data=m,d.series=s,d.legend.data=s.map(a=>a.name),v.value=!1};return D(()=>{g()}),(e,t)=>{const y=B,m=V,s=R,l=H;return c(),C(l,{shadow:"never"},{header:u(()=>[T("div",Z,[f(p(X),{title:"\u4EA4\u6613\u91CF\u8D8B\u52BF"}),T("div",z,[f(m,{modelValue:p(n),"onUpdate:modelValue":t[0]||(t[0]=o=>j(n)?n.value=o:null),onChange:g},{default:u(()=>[(c(!0),I(L,null,q(p(x).entries(),([o,i])=>(c(),C(y,{key:o,value:o},{default:u(()=>[F(G(i.name),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])])])]),default:u(()=>[f(s,{height:300,options:p(d)},null,8,["options"])]),_:1})}}});export{E as _};
