import{_ as z}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as D,r as p,A as r,o as f,w as a,g as e,i as E,aE as I,H as u,t as n,a as l,a3 as _,D as h,j as w,dv as U,G as C,m as P}from"./index-CRsFgzy0.js";import{E as j,a as L}from"./el-descriptions-item-lelixL8M.js";import{_ as R}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{g as S}from"./index-TugyyuJQ.js";import{f as N}from"./formatTime-DhdtkSIS.js";const V={style:{"text-align":"right"}},F=D({name:"PayTransferDetail",__name:"TransferDetail",setup(H,{expose:g}){const o=p(!1),b=p(!1),t=p({});return g({open:async y=>{o.value=!0,b.value=!0;try{t.value=await S(y)}finally{b.value=!1}}}),(y,d)=>{const i=I,s=L,v=R,m=j,c=w,k=U,x=C,A=z;return f(),r(A,{modelValue:l(o),"onUpdate:modelValue":d[1]||(d[1]=T=>P(o)?o.value=T:null),title:"\u8F6C\u8D26\u5355\u8BE6\u60C5",width:"700px"},{default:a(()=>[e(m,{column:2,"label-class-name":"desc-label"},{default:a(()=>[e(s,{label:"\u5546\u6237\u5355\u53F7"},{default:a(()=>[e(i,{size:"small"},{default:a(()=>[u(n(l(t).merchantTransferId),1)]),_:1})]),_:1}),e(s,{label:"\u8F6C\u8D26\u5355\u53F7"},{default:a(()=>[l(t).no?(f(),r(i,{key:0,type:"warning",size:"small"},{default:a(()=>[u(n(l(t).no),1)]),_:1})):_("",!0)]),_:1}),e(s,{label:"\u5E94\u7528\u7F16\u53F7"},{default:a(()=>[u(n(l(t).appId),1)]),_:1}),e(s,{label:"\u8F6C\u8D26\u72B6\u6001"},{default:a(()=>[e(v,{type:l(h).PAY_TRANSFER_STATUS,value:l(t).status,size:"small"},null,8,["type","value"])]),_:1}),e(s,{label:"\u8F6C\u8D26\u91D1\u989D"},{default:a(()=>[e(i,{type:"success",size:"small"},{default:a(()=>[u("\uFFE5"+n((l(t).price/100).toFixed(2)),1)]),_:1})]),_:1}),e(s,{label:"\u8F6C\u8D26\u65F6\u95F4"},{default:a(()=>[u(n(l(N)(l(t).successTime)),1)]),_:1}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:a(()=>[u(n(l(N)(l(t).createTime)),1)]),_:1})]),_:1}),e(c),e(m,{column:2,"label-class-name":"desc-label"},{default:a(()=>[e(s,{label:"\u6536\u6B3E\u4EBA\u59D3\u540D"},{default:a(()=>[u(n(l(t).userName),1)]),_:1}),l(t).type===1?(f(),r(s,{key:0,label:"\u652F\u4ED8\u5B9D\u767B\u5F55\u8D26\u53F7"},{default:a(()=>[u(n(l(t).alipayLogonId),1)]),_:1})):_("",!0),l(t).type===2?(f(),r(s,{key:1,label:"\u5FAE\u4FE1 openid"},{default:a(()=>[u(n(l(t).openid),1)]),_:1})):_("",!0),e(s,{label:"\u652F\u4ED8\u6E20\u9053"},{default:a(()=>[e(v,{type:l(h).PAY_CHANNEL_CODE,value:l(t).channelCode},null,8,["type","value"])]),_:1}),e(s,{label:"\u652F\u4ED8 IP"},{default:a(()=>[u(n(l(t).userIp),1)]),_:1}),e(s,{label:"\u6E20\u9053\u5355\u53F7"},{default:a(()=>[l(t).channelTransferNo?(f(),r(i,{key:0,size:"mini",type:"success"},{default:a(()=>[u(n(l(t).channelTransferNo),1)]),_:1})):_("",!0)]),_:1}),e(s,{label:"\u901A\u77E5 URL"},{default:a(()=>[u(n(l(t).notifyUrl),1)]),_:1})]),_:1}),e(c),e(m,{column:1,"label-class-name":"desc-label",direction:"vertical",border:""},{default:a(()=>[e(s,{label:"\u8F6C\u8D26\u6E20\u9053\u901A\u77E5\u5185\u5BB9"},{default:a(()=>[e(k,null,{default:a(()=>[u(n(l(t).channelNotifyData),1)]),_:1})]),_:1})]),_:1}),e(c),E("div",V,[e(x,{onClick:d[0]||(d[0]=T=>o.value=!1)},{default:a(()=>d[2]||(d[2]=[u("\u53D6 \u6D88")])),_:1})])]),_:1},8,["modelValue"])}}});export{F as _};
