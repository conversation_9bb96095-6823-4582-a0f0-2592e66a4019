import{d as g,p as I,bv as v,r as U,f as h,A as x,o as H,w as o,g as w,G as A,H as E,i as F,b3 as G,a as e,bh as j,_ as S}from"./index-CRsFgzy0.js";import{b as $,a as k,H as q,U as z}from"./upload-BFmHmgdU.js";import{U as l}from"./useUpload-D8UAyHOj.js";const B={class:"el-upload__tip",style:{"margin-left":"5px"}},C=S(g({__name:"UploadFile",props:{type:{}},emits:["uploaded"],setup(p,{emit:d}){const t=I(),n=p,u=v("accountId"),r=U([]),c=d,s=h({type:l.Image,title:"",introduction:"",accountId:u}),m=n.type===l.Image?$:k,f=a=>{if(a.code!==0)return t.alertError("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;r.value=[],s.title="",s.introduction="",t.notifySuccess("\u4E0A\u4F20\u6210\u529F"),c("uploaded")},_=a=>t.error("\u4E0A\u4F20\u5931\u8D25: "+a.message);return(a,i)=>{const y=A,b=j;return H(),x(b,{action:e(z),headers:e(q),multiple:"",limit:1,"file-list":e(r),data:e(s),"on-error":_,"before-upload":e(m),"on-success":f},{tip:o(()=>[F("span",B,[G(a.$slots,"default",{},void 0,!0)])]),default:o(()=>[w(y,{type:"primary",plain:""},{default:o(()=>i[0]||(i[0]=[E(" \u70B9\u51FB\u4E0A\u4F20 ")])),_:1})]),_:3},8,["action","headers","file-list","data","before-upload"])}}}),[["__scopeId","data-v-c21a7348"]]);export{C as default};
