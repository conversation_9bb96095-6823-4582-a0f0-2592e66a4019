import{d as H,b as J,p as M,r,f as O,a2 as P,aO as _,aQ as p,A as y,o as V,w as s,J as j,s as D,a as n,g as l,v as Q,P as R,aB as S,aC as z,H as v,an as E,M as K,G as L,m as N}from"./index-CRsFgzy0.js";import{_ as W}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{g as X}from"./index-BCuY8xr3.js";import{g as Y,u as Z}from"./index-BqI6YY4H.js";const $=H({name:"UpdateBalanceForm",__name:"UserBalanceUpdateForm",emits:["success"],setup(aa,{expose:B,emit:w}){const{t:k}=J(),f=M(),d=r(!1),c=r(!1),e=r({id:void 0,nickname:void 0,balance:"0",changeBalance:0,changeType:1}),T=O({changeBalance:[{required:!0,message:"\u53D8\u52A8\u4F59\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),m=r();B({open:async o=>{if(d.value=!0,F(),o){c.value=!0;try{const a=await X(o),i=await Y({userId:a.id||0});e.value.id=a.id,e.value.nickname=a.nickname,e.value.balance=_(i.balance),e.value.changeType=1,e.value.changeBalance=0}finally{c.value=!1}}}});const U=w,x=async()=>{if(m&&await m.value.validate())if(e.value.changeBalance<=0)f.error("\u53D8\u52A8\u4F59\u989D\u4E0D\u80FD\u4E3A\u96F6");else if(p(b.value)<0)f.error("\u53D8\u52A8\u540E\u7684\u4F59\u989D\u4E0D\u80FD\u5C0F\u4E8E 0");else{c.value=!0;try{await Z({userId:e.value.id,balance:p(e.value.changeBalance)*e.value.changeType}),f.success(k("common.updateSuccess")),d.value=!1,U("success")}finally{c.value=!1}}},F=()=>{var o;e.value={id:void 0,nickname:void 0,balance:"0",changeBalance:0,changeType:1},(o=m.value)==null||o.resetFields()},b=P(()=>_(p(e.value.balance)+p(e.value.changeBalance)*e.value.changeType));return(o,a)=>{const i=R,t=Q,g=z,C=S,I=E,q=D,h=L,A=W,G=K;return V(),y(A,{modelValue:n(d),"onUpdate:modelValue":a[5]||(a[5]=u=>N(d)?d.value=u:null),title:"\u4FEE\u6539\u7528\u6237\u4F59\u989D",width:"600"},{footer:s(()=>[l(h,{disabled:n(c),type:"primary",onClick:x},{default:s(()=>a[8]||(a[8]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),l(h,{onClick:a[4]||(a[4]=u=>d.value=!1)},{default:s(()=>a[9]||(a[9]=[v("\u53D6 \u6D88")])),_:1})]),default:s(()=>[j((V(),y(q,{ref_key:"formRef",ref:m,model:n(e),rules:n(T),"label-width":"130px"},{default:s(()=>[l(t,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:s(()=>[l(i,{modelValue:n(e).id,"onUpdate:modelValue":a[0]||(a[0]=u=>n(e).id=u),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(t,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:s(()=>[l(i,{modelValue:n(e).nickname,"onUpdate:modelValue":a[1]||(a[1]=u=>n(e).nickname=u),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(t,{label:"\u53D8\u52A8\u524D\u4F59\u989D(\u5143)",prop:"balance"},{default:s(()=>[l(i,{"model-value":n(e).balance,class:"!w-240px",disabled:""},null,8,["model-value"])]),_:1}),l(t,{label:"\u53D8\u52A8\u7C7B\u578B",prop:"changeType"},{default:s(()=>[l(C,{modelValue:n(e).changeType,"onUpdate:modelValue":a[2]||(a[2]=u=>n(e).changeType=u)},{default:s(()=>[l(g,{label:1},{default:s(()=>a[6]||(a[6]=[v("\u589E\u52A0")])),_:1}),l(g,{label:-1},{default:s(()=>a[7]||(a[7]=[v("\u51CF\u5C11")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(t,{label:"\u53D8\u52A8\u4F59\u989D(\u5143)",prop:"changeBalance"},{default:s(()=>[l(I,{modelValue:n(e).changeBalance,"onUpdate:modelValue":a[3]||(a[3]=u=>n(e).changeBalance=u),min:0,precision:2,step:.1,class:"!w-240px"},null,8,["modelValue"])]),_:1}),l(t,{label:"\u53D8\u52A8\u540E\u4F59\u989D(\u5143)"},{default:s(()=>[l(i,{"model-value":n(b),class:"!w-240px",disabled:""},null,8,["model-value"])]),_:1})]),_:1},8,["model","rules"])),[[G,n(c)]])]),_:1},8,["modelValue"])}}});export{$ as _};
