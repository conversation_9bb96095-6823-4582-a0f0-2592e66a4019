import{d as P,b as T,p as X,r as m,f as Y,A as c,o as p,w as a,J as $,s as j,a as l,g as e,E as z,h as K,v as O,P as Q,a3 as I,x as W,c as E,F as S,y as F,R as Z,D as ee,B as le,M as ae,G as de,H as q,m as se}from"./index-CRsFgzy0.js";import{_ as ue}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{E as oe}from"./el-tree-select-BijZG_HG.js";import{C as A}from"./constants-uird_4gU.js";import{d as te,h as re}from"./tree-COGD3qag.js";import{g as me}from"./index-BD8pFnai.js";import{g as pe}from"./index-C0yL_L5C.js";import{b as ne,c as ie,u as fe}from"./index-D4y5Z4cM.js";const ce=P({name:"SystemUserForm",__name:"UserForm",emits:["success"],setup(_e,{expose:B,emit:R}){const{t:b}=T(),V=X(),n=m(!1),g=m(""),i=m(!1),k=m(""),u=m({nickname:"",deptId:"",mobile:"",email:"",id:void 0,username:"",password:"",sex:void 0,postIds:[],remark:"",status:A.ENABLE,roleIds:[]}),C=Y({username:[{required:!0,message:"\u7528\u6237\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],nickname:[{required:!0,message:"\u7528\u6237\u6635\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],password:[{required:!0,message:"\u7528\u6237\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],email:[{type:"email",message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740",trigger:["blur","change"]}],mobile:[{pattern:/^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}]}),v=m(),y=m([]),h=m([]);B({open:async(o,d)=>{if(n.value=!0,g.value=b("action."+o),k.value=o,D(),d){i.value=!0;try{u.value=await ne(d)}finally{i.value=!1}}y.value=re(await pe()),h.value=await me()}});const L=R,M=async()=>{if(v&&await v.value.validate()){i.value=!0;try{const o=u.value;k.value==="create"?(await ie(o),V.success(b("common.createSuccess"))):(await fe(o),V.success(b("common.updateSuccess"))),n.value=!1,L("success")}finally{i.value=!1}}},D=()=>{var o;u.value={nickname:"",deptId:"",mobile:"",email:"",id:void 0,username:"",password:"",sex:void 0,postIds:[],remark:"",status:A.ENABLE,roleIds:[]},(o=v.value)==null||o.resetFields()};return(o,d)=>{const f=Q,t=O,r=K,G=oe,_=z,w=le,U=W,H=j,x=de,J=ue,N=ae;return p(),c(J,{modelValue:l(n),"onUpdate:modelValue":d[10]||(d[10]=s=>se(n)?n.value=s:null),title:l(g)},{footer:a(()=>[e(x,{disabled:l(i),type:"primary",onClick:M},{default:a(()=>d[11]||(d[11]=[q("\u786E \u5B9A")])),_:1},8,["disabled"]),e(x,{onClick:d[9]||(d[9]=s=>n.value=!1)},{default:a(()=>d[12]||(d[12]=[q("\u53D6 \u6D88")])),_:1})]),default:a(()=>[$((p(),c(H,{ref_key:"formRef",ref:v,model:l(u),rules:l(C),"label-width":"80px"},{default:a(()=>[e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:a(()=>[e(f,{modelValue:l(u).nickname,"onUpdate:modelValue":d[0]||(d[0]=s=>l(u).nickname=s),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:a(()=>[e(G,{modelValue:l(u).deptId,"onUpdate:modelValue":d[1]||(d[1]=s=>l(u).deptId=s),data:l(y),props:l(te),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8"},null,8,["modelValue","data","props"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:a(()=>[e(f,{modelValue:l(u).mobile,"onUpdate:modelValue":d[2]||(d[2]=s=>l(u).mobile=s),maxlength:"11",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u90AE\u7BB1",prop:"email"},{default:a(()=>[e(f,{modelValue:l(u).email,"onUpdate:modelValue":d[3]||(d[3]=s=>l(u).email=s),maxlength:"50",placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[l(u).id===void 0?(p(),c(t,{key:0,label:"\u7528\u6237\u540D\u79F0",prop:"username"},{default:a(()=>[e(f,{modelValue:l(u).username,"onUpdate:modelValue":d[4]||(d[4]=s=>l(u).username=s),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u540D\u79F0"},null,8,["modelValue"])]),_:1})):I("",!0)]),_:1}),e(r,{span:12},{default:a(()=>[l(u).id===void 0?(p(),c(t,{key:0,label:"\u7528\u6237\u5BC6\u7801",prop:"password"},{default:a(()=>[e(f,{modelValue:l(u).password,"onUpdate:modelValue":d[5]||(d[5]=s=>l(u).password=s),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u5BC6\u7801","show-password":"",type:"password"},null,8,["modelValue"])]),_:1})):I("",!0)]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u7528\u6237\u6027\u522B"},{default:a(()=>[e(U,{modelValue:l(u).sex,"onUpdate:modelValue":d[6]||(d[6]=s=>l(u).sex=s),placeholder:"\u8BF7\u9009\u62E9"},{default:a(()=>[(p(!0),E(S,null,F(l(Z)(l(ee).SYSTEM_USER_SEX),s=>(p(),c(w,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5C97\u4F4D"},{default:a(()=>[e(U,{modelValue:l(u).postIds,"onUpdate:modelValue":d[7]||(d[7]=s=>l(u).postIds=s),multiple:"",placeholder:"\u8BF7\u9009\u62E9"},{default:a(()=>[(p(!0),E(S,null,F(l(h),s=>(p(),c(w,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(_,null,{default:a(()=>[e(r,{span:24},{default:a(()=>[e(t,{label:"\u5907\u6CE8"},{default:a(()=>[e(f,{modelValue:l(u).remark,"onUpdate:modelValue":d[8]||(d[8]=s=>l(u).remark=s),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[N,l(i)]])]),_:1},8,["modelValue","title"])}}});export{ce as _};
