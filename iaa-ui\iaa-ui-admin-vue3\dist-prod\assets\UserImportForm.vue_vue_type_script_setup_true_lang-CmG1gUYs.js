import{_ as S}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as j,p as B,r as n,A as H,o as L,w as r,g as d,bh as O,a as o,m as x,i as f,I as q,H as v,a4 as D,a5 as R,G as E,d2 as J,ed as K,aG as M}from"./index-CRsFgzy0.js";import{i as N}from"./index-D4y5Z4cM.js";import{d as P}from"./download-oWiM5xVU.js";const Q={class:"el-upload__tip text-center"},T={class:"el-upload__tip"},W=j({name:"SystemUserImportForm",__name:"UserImportForm",emits:["success"],setup(X,{expose:U,emit:g}){const m=B(),u=n(!1),s=n(!1),_=n(),h=n(),p=n([]),c=n(0);U({open:()=>{u.value=!0,c.value=0,p.value=[],C()}});const b=async()=>{p.value.length!=0?(h.value={Authorization:"Bearer "+K(),"tenant-id":J()},s.value=!0,_.value.submit()):m.error("\u8BF7\u4E0A\u4F20\u6587\u4EF6")},V=g,k=a=>{if(a.code!==0)return m.error(a.msg),void(s.value=!1);const e=a.data;let l="\u4E0A\u4F20\u6210\u529F\u6570\u91CF\uFF1A"+e.createUsernames.length+";";for(let t of e.createUsernames)l+="< "+t+" >";l+="\u66F4\u65B0\u6210\u529F\u6570\u91CF\uFF1A"+e.updateUsernames.length+";";for(const t of e.updateUsernames)l+="< "+t+" >";l+="\u66F4\u65B0\u5931\u8D25\u6570\u91CF\uFF1A"+Object.keys(e.failureUsernames).length+";";for(const t in e.failureUsernames)l+="< "+t+": "+e.failureUsernames[t]+" >";m.alert(l),s.value=!1,u.value=!1,V("success")},w=()=>{m.error("\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01"),s.value=!1},C=async()=>{var a;s.value=!1,await M(),(a=_.value)==null||a.clearFiles()},F=()=>{m.error("\u6700\u591A\u53EA\u80FD\u4E0A\u4F20\u4E00\u4E2A\u6587\u4EF6\uFF01")},I=async()=>{const a=await N();P.excel(a,"\u7528\u6237\u5BFC\u5165\u6A21\u7248.xls")};return(a,e)=>{const l=q,t=D,z=R,A=O,y=E,G=S;return L(),H(G,{modelValue:o(u),"onUpdate:modelValue":e[3]||(e[3]=i=>x(u)?u.value=i:null),title:"\u7528\u6237\u5BFC\u5165",width:"400"},{footer:r(()=>[d(y,{disabled:o(s),type:"primary",onClick:b},{default:r(()=>e[8]||(e[8]=[v("\u786E \u5B9A")])),_:1},8,["disabled"]),d(y,{onClick:e[2]||(e[2]=i=>u.value=!1)},{default:r(()=>e[9]||(e[9]=[v("\u53D6 \u6D88")])),_:1})]),default:r(()=>[d(A,{ref_key:"uploadRef",ref:_,"file-list":o(p),"onUpdate:fileList":e[1]||(e[1]=i=>x(p)?p.value=i:null),action:"http://shouhou.iaa360.com/admin-api/system/user/import?updateSupport="+o(c),"auto-upload":!1,disabled:o(s),headers:o(h),limit:1,"on-error":w,"on-exceed":F,"on-success":k,accept:".xlsx, .xls",drag:""},{tip:r(()=>[f("div",Q,[f("div",T,[d(t,{modelValue:o(c),"onUpdate:modelValue":e[0]||(e[0]=i=>x(c)?c.value=i:null)},null,8,["modelValue"]),e[4]||(e[4]=v(" \u662F\u5426\u66F4\u65B0\u5DF2\u7ECF\u5B58\u5728\u7684\u7528\u6237\u6570\u636E "))]),e[6]||(e[6]=f("span",null,"\u4EC5\u5141\u8BB8\u5BFC\u5165 xls\u3001xlsx \u683C\u5F0F\u6587\u4EF6\u3002",-1)),d(z,{underline:!1,style:{"font-size":"12px","vertical-align":"baseline"},type:"primary",onClick:I},{default:r(()=>e[5]||(e[5]=[v(" \u4E0B\u8F7D\u6A21\u677F ")])),_:1})])]),default:r(()=>[d(l,{icon:"ep:upload"}),e[7]||(e[7]=f("div",{class:"el-upload__text"},[v("\u5C06\u6587\u4EF6\u62D6\u5230\u6B64\u5904\uFF0C\u6216"),f("em",null,"\u70B9\u51FB\u4E0A\u4F20")],-1))]),_:1},8,["file-list","action","disabled","headers"])]),_:1},8,["modelValue"])}}});export{W as _};
