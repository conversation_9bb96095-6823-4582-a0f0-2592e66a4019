import{d as F,b as q,p as L,r as m,f as M,A as c,o as f,w as s,J as S,s as j,a as l,g as o,v as A,P as G,M as H,G as J,H as V,m as P}from"./index-CRsFgzy0.js";import{_ as R}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{g as z,a as B}from"./index-BCuY8xr3.js";import{_ as D}from"./MemberLevelSelect.vue_vue_type_script_setup_true_lang-CNuv1v1P.js";const E=F({__name:"UserLevelUpdateForm",emits:["success"],setup(K,{expose:_,emit:b}){const{t:y}=q(),k=L(),u=m(!1),r=m(!1),a=m({id:void 0,nickname:void 0,levelId:void 0,reason:void 0}),w=M({reason:[{required:!0,message:"\u4FEE\u6539\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=m();_({open:async t=>{if(u.value=!0,g(),t){r.value=!0;try{a.value=await z(t)}finally{r.value=!1}}}});const x=b,U=async()=>{if(i&&await i.value.validate()){r.value=!0;try{await B(a.value),k.success(y("common.updateSuccess")),u.value=!1,x("success")}finally{r.value=!1}}},g=()=>{var t;a.value={id:void 0,nickname:void 0,levelId:void 0,reason:void 0},(t=i.value)==null||t.resetFields()};return(t,e)=>{const p=G,n=A,h=j,v=J,I=R,C=H;return f(),c(I,{title:"\u4FEE\u6539\u7528\u6237\u7B49\u7EA7",modelValue:l(u),"onUpdate:modelValue":e[5]||(e[5]=d=>P(u)?u.value=d:null),width:"600"},{footer:s(()=>[o(v,{onClick:U,type:"primary",disabled:l(r)},{default:s(()=>e[6]||(e[6]=[V("\u786E \u5B9A")])),_:1},8,["disabled"]),o(v,{onClick:e[4]||(e[4]=d=>u.value=!1)},{default:s(()=>e[7]||(e[7]=[V("\u53D6 \u6D88")])),_:1})]),default:s(()=>[S((f(),c(h,{ref_key:"formRef",ref:i,model:l(a),rules:l(w),"label-width":"100px"},{default:s(()=>[o(n,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:s(()=>[o(p,{modelValue:l(a).id,"onUpdate:modelValue":e[0]||(e[0]=d=>l(a).id=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(n,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:s(()=>[o(p,{modelValue:l(a).nickname,"onUpdate:modelValue":e[1]||(e[1]=d=>l(a).nickname=d),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u6635\u79F0",class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(n,{label:"\u7528\u6237\u7B49\u7EA7",prop:"levelId"},{default:s(()=>[o(D,{modelValue:l(a).levelId,"onUpdate:modelValue":e[2]||(e[2]=d=>l(a).levelId=d)},null,8,["modelValue"])]),_:1}),o(n,{label:"\u4FEE\u6539\u539F\u56E0",prop:"reason"},{default:s(()=>[o(p,{type:"textarea",modelValue:l(a).reason,"onUpdate:modelValue":e[3]||(e[3]=d=>l(a).reason=d),placeholder:"\u8BF7\u8F93\u5165\u4FEE\u6539\u539F\u56E0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[C,l(r)]])]),_:1},8,["modelValue"])}}});export{E as _};
