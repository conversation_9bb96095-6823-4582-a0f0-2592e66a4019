import{d as G,b as H,p as j,r as c,f as D,a2 as I,A as _,o as y,w as u,J,s as M,a,g as o,v as R,P as S,an as z,aB as E,aC as K,H as r,m as b,M as L,G as N}from"./index-CRsFgzy0.js";import{_ as O}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{g as Q,b as W}from"./index-BCuY8xr3.js";const X=G({name:"UpdatePointForm",__name:"UserPointUpdateForm",emits:["success"],setup(Y,{expose:P,emit:w}){const{t:U}=H(),v=j(),d=c(!1),s=c(!1),l=c({id:void 0,nickname:void 0,point:0,changePoint:0,changeType:1}),x=D({changePoint:[{required:!0,message:"\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=c();P({open:async t=>{if(d.value=!0,C(),t){s.value=!0;try{l.value=await Q(t),l.value.changeType=1,l.value.changePoint=0}finally{s.value=!1}}}});const T=w,k=async()=>{if(p&&await p.value.validate())if(l.value.changePoint<1)v.error("\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 1");else if(m.value<0)v.error("\u53D8\u52A8\u540E\u7684\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 0");else{s.value=!0;try{await W({id:l.value.id,point:l.value.changePoint*l.value.changeType}),v.success(U("common.updateSuccess")),d.value=!1,T("success")}finally{s.value=!1}}},C=()=>{var t;l.value={id:void 0,nickname:void 0,point:0,changePoint:0,changeType:1},(t=p.value)==null||t.resetFields()},m=I(()=>l.value.point+l.value.changePoint*l.value.changeType);return(t,e)=>{const g=S,i=R,f=z,V=K,F=E,q=M,h=N,A=O,B=L;return y(),_(A,{modelValue:a(d),"onUpdate:modelValue":e[7]||(e[7]=n=>b(d)?d.value=n:null),title:"\u4FEE\u6539\u7528\u6237\u79EF\u5206",width:"600"},{footer:u(()=>[o(h,{disabled:a(s),type:"primary",onClick:k},{default:u(()=>e[10]||(e[10]=[r("\u786E \u5B9A")])),_:1},8,["disabled"]),o(h,{onClick:e[6]||(e[6]=n=>d.value=!1)},{default:u(()=>e[11]||(e[11]=[r("\u53D6 \u6D88")])),_:1})]),default:u(()=>[J((y(),_(q,{ref_key:"formRef",ref:p,model:a(l),rules:a(x),"label-width":"100px"},{default:u(()=>[o(i,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:u(()=>[o(g,{modelValue:a(l).id,"onUpdate:modelValue":e[0]||(e[0]=n=>a(l).id=n),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(i,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:u(()=>[o(g,{modelValue:a(l).nickname,"onUpdate:modelValue":e[1]||(e[1]=n=>a(l).nickname=n),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u524D\u79EF\u5206",prop:"point"},{default:u(()=>[o(f,{modelValue:a(l).point,"onUpdate:modelValue":e[2]||(e[2]=n=>a(l).point=n),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u7C7B\u578B",prop:"changeType"},{default:u(()=>[o(F,{modelValue:a(l).changeType,"onUpdate:modelValue":e[3]||(e[3]=n=>a(l).changeType=n)},{default:u(()=>[o(V,{value:1},{default:u(()=>e[8]||(e[8]=[r("\u589E\u52A0")])),_:1}),o(V,{value:-1},{default:u(()=>e[9]||(e[9]=[r("\u51CF\u5C11")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u79EF\u5206",prop:"changePoint"},{default:u(()=>[o(f,{modelValue:a(l).changePoint,"onUpdate:modelValue":e[4]||(e[4]=n=>a(l).changePoint=n),min:0,precision:0,class:"!w-240px"},null,8,["modelValue"])]),_:1}),o(i,{label:"\u53D8\u52A8\u540E\u79EF\u5206"},{default:u(()=>[o(f,{modelValue:a(m),"onUpdate:modelValue":e[5]||(e[5]=n=>b(m)?m.value=n:null),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[B,a(s)]])]),_:1},8,["modelValue"])}}});export{X as _};
