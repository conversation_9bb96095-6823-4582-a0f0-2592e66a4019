import{d as k,O as C,J as m,M as h,A as i,o,w as l,g as e,L as x,a3 as T,a as c,i as I,t as H,G as j,H as u,I as A,K as B}from"./index-CRsFgzy0.js";import G from"./main-DDCad-8G.js";import{d as J}from"./formatTime-DhdtkSIS.js";const K=k({__name:"VoiceTable",props:{list:{},loading:{type:Boolean}},emits:["delete"],setup(f,{emit:g}){const n=f,s=g;return(L,r)=>{const t=x,d=A,p=j,w=B,_=C("hasPermi"),b=h;return m((o(),i(w,{data:n.list,stripe:"",border:"",style:{"margin-top":"10px"}},{default:l(()=>[e(t,{label:"\u7F16\u53F7",align:"center",prop:"mediaId"}),e(t,{label:"\u6587\u4EF6\u540D",align:"center",prop:"name"}),e(t,{label:"\u8BED\u97F3",align:"center"},{default:l(a=>[a.row.url?(o(),i(c(G),{key:0,url:a.row.url},null,8,["url"])):T("",!0)]),_:1}),e(t,{label:"\u4E0A\u4F20\u65F6\u95F4",align:"center",prop:"createTime",formatter:c(J),width:"180"},{default:l(a=>[I("span",null,H(a.row.createTime),1)]),_:1},8,["formatter"]),e(t,{label:"\u64CD\u4F5C",align:"center","class-name":"small-padding fixed-width"},{default:l(a=>[e(p,{type:"primary",link:"",onClick:y=>s("delete",a.row.id)},{default:l(()=>[e(d,{icon:"ep:download"}),r[0]||(r[0]=u("\u4E0B\u8F7D "))]),_:2},1032,["onClick"]),m((o(),i(p,{type:"primary",link:"",onClick:y=>s("delete",a.row.id)},{default:l(()=>[e(d,{icon:"ep:delete"}),r[1]||(r[1]=u("\u5220\u9664 "))]),_:2},1032,["onClick"])),[[_,["mp:material:delete"]]])]),_:1})]),_:1},8,["data"])),[[b,n.loading]])}}});export{K as _};
