import{d as A,b as J,p as M,r as i,c as k,o as c,g as r,a as l,m as R,w as o,J as B,A as b,s as E,v as T,aB as W,F as j,y as D,cF as L,D as q,aC as G,H as f,t as H,P,M as z,G as K}from"./index-CRsFgzy0.js";import{_ as Q}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{C as X}from"./constants-uird_4gU.js";import{g as Y,c as Z,u as $}from"./index-amKeQZTY.js";const aa=A({name:"WalletChannelForm",__name:"WalletChannelForm",emits:["success"],setup(ea,{expose:w,emit:V}){const{t:p}=J(),v=M(),d=i(!1),_=i(""),n=i(!1),e=i({appId:"",code:"",status:void 0,feeRate:0,remark:"",config:{name:"mock-conf"}}),h={status:[{required:!0,message:"\u6E20\u9053\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]},m=i();w({open:async(s,a)=>{d.value=!0,n.value=!0,x(s,a);try{const u=await Y(s,a);u&&u.id&&(e.value=u,e.value.config=JSON.parse(u.config)),_.value=e.value.id?"\u7F16\u8F91\u652F\u4ED8\u6E20\u9053":"\u521B\u5EFA\u652F\u4ED8\u6E20\u9053"}finally{n.value=!1}}});const C=V,S=async()=>{if(m&&await m.value.validate()){n.value=!0;try{const s={...e.value};s.config=JSON.stringify(e.value.config),s.id?(await $(s),v.success(p("common.updateSuccess"))):(await Z(s),v.success(p("common.createSuccess"))),d.value=!1,C("success")}finally{n.value=!1}}},x=(s,a)=>{var u;e.value={appId:s,code:a,status:X.ENABLE,remark:"",feeRate:0,config:{name:"mock-conf"}},(u=m.value)==null||u.resetFields()};return(s,a)=>{const u=G,F=W,y=T,I=P,N=E,g=K,O=Q,U=z;return c(),k("div",null,[r(O,{modelValue:l(d),"onUpdate:modelValue":a[3]||(a[3]=t=>R(d)?d.value=t:null),title:l(_),width:"800px"},{footer:o(()=>[r(g,{disabled:l(n),type:"primary",onClick:S},{default:o(()=>a[4]||(a[4]=[f("\u786E \u5B9A")])),_:1},8,["disabled"]),r(g,{onClick:a[2]||(a[2]=t=>d.value=!1)},{default:o(()=>a[5]||(a[5]=[f("\u53D6 \u6D88")])),_:1})]),default:o(()=>[B((c(),b(N,{ref_key:"formRef",ref:m,model:l(e),rules:h,"label-width":"100px"},{default:o(()=>[r(y,{label:"\u6E20\u9053\u72B6\u6001","label-width":"180px",prop:"status"},{default:o(()=>[r(F,{modelValue:l(e).status,"onUpdate:modelValue":a[0]||(a[0]=t=>l(e).status=t)},{default:o(()=>[(c(!0),k(j,null,D(l(L)(l(q).COMMON_STATUS),t=>(c(),b(u,{key:parseInt(t.value),value:parseInt(t.value)},{default:o(()=>[f(H(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(y,{label:"\u5907\u6CE8","label-width":"180px",prop:"remark"},{default:o(()=>[r(I,{modelValue:l(e).remark,"onUpdate:modelValue":a[1]||(a[1]=t=>l(e).remark=t),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[U,l(n)]])]),_:1},8,["modelValue","title"])])}}});export{aa as _};
