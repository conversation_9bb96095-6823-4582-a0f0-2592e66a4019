import{_}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as c,r as t,A as f,o as v,w as o,g as n,a as l,G as w,H as V,m as x}from"./index-CRsFgzy0.js";import{_ as g}from"./WalletTransactionList.vue_vue_type_script_setup_true_lang-DgOvGUyX.js";const h=c({__name:"WalletForm",setup(k,{expose:d}){const e=t(!1),s=t("");t(!1);const u=t(0);return d({open:async r=>{e.value=!0,s.value="\u94B1\u5305\u4F59\u989D\u660E\u7EC6",u.value=r}}),(r,a)=>{const i=w,p=_;return v(),f(p,{title:l(s),modelValue:l(e),"onUpdate:modelValue":a[1]||(a[1]=m=>x(e)?e.value=m:null),width:"800"},{footer:o(()=>[n(i,{onClick:a[0]||(a[0]=m=>e.value=!1)},{default:o(()=>a[2]||(a[2]=[V("\u53D6 \u6D88")])),_:1})]),default:o(()=>[n(g,{"wallet-id":l(u)},null,8,["wallet-id"])]),_:1},8,["title","modelValue"])}}});export{h as _};
