import{d as T,r as d,f as x,q as z,A as g,o as f,w as i,J as L,g as l,K as S,a,L as h,H as w,t as I,aR as b,M as U}from"./index-CRsFgzy0.js";import{_ as W}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as j}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{d as A}from"./formatTime-DhdtkSIS.js";import{g as H}from"./index-DMxy9cos.js";import{g as J}from"./index-BqI6YY4H.js";const K=T({name:"WalletTransactionList",__name:"WalletTransactionList",props:{walletId:{type:Number,required:!1},userId:{type:Number,required:!1}},setup(_){const s=_,p=d(!0),u=d(0),e=x({pageNo:1,pageSize:10,walletId:null}),m=d([]),c=async()=>{p.value=!0;try{if(s.userId){const r=await J({userId:s.userId});e.walletId=r.id}else e.walletId=s.walletId;const n=await H(e);m.value=n.list,u.value=n.total}finally{p.value=!1}};return z(()=>{c()}),(n,r)=>{const o=h,y=S,v=j,N=W,q=U;return f(),g(N,null,{default:i(()=>[L((f(),g(y,{data:a(m),"show-overflow-tooltip":!0,stripe:!0},{default:i(()=>[l(o,{align:"center",label:"\u7F16\u53F7",prop:"id"}),l(o,{align:"center",label:"\u94B1\u5305\u7F16\u53F7",prop:"walletId"}),l(o,{align:"center",label:"\u5173\u8054\u4E1A\u52A1\u6807\u9898",prop:"title"}),l(o,{align:"center",label:"\u4EA4\u6613\u91D1\u989D",prop:"price"},{default:i(({row:t})=>[w(I(a(b)(t.price))+" \u5143",1)]),_:1}),l(o,{align:"center",label:"\u94B1\u5305\u4F59\u989D",prop:"balance"},{default:i(({row:t})=>[w(I(a(b)(t.balance))+" \u5143",1)]),_:1}),l(o,{formatter:a(A),align:"center",label:"\u4EA4\u6613\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[q,a(p)]]),l(v,{limit:a(e).pageSize,"onUpdate:limit":r[0]||(r[0]=t=>a(e).pageSize=t),page:a(e).pageNo,"onUpdate:page":r[1]||(r[1]=t=>a(e).pageNo=t),total:a(u),onPagination:c},null,8,["limit","page","total"])]),_:1})}}});export{K as _};
