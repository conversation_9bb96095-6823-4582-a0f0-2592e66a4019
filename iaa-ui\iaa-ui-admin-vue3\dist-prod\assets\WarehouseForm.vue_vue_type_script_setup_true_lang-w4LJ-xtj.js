import{d as B,b as E,p as G,r as m,f as N,A as V,o as p,w as r,J as O,s as R,a,g as s,v as T,P as j,aB as D,c as L,F as H,y as J,R as K,D as z,aC as I,H as _,t as Q,an as X,M as Y,G as Z,m as $}from"./index-CRsFgzy0.js";import{_ as ee}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{W as h}from"./index-mM5XVEAg.js";import{C as ae}from"./constants-uird_4gU.js";const le=B({name:"WarehouseForm",__name:"WarehouseForm",emits:["success"],setup(oe,{expose:y,emit:P}){const{t:n}=E(),b=G(),t=m(!1),g=m(""),i=m(!1),k=m(""),o=m({id:void 0,name:void 0,address:void 0,sort:void 0,remark:void 0,principal:void 0,warehousePrice:void 0,truckagePrice:void 0,status:void 0}),U=N({name:[{required:!0,message:"\u4ED3\u5E93\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=m();y({open:async(u,e)=>{if(t.value=!0,g.value=n("action."+u),k.value=u,F(),e){i.value=!0;try{o.value=await h.getWarehouse(e)}finally{i.value=!1}}}});const C=P,W=async()=>{await v.value.validate(),i.value=!0;try{const u=o.value;k.value==="create"?(await h.createWarehouse(u),b.success(n("common.createSuccess"))):(await h.updateWarehouse(u),b.success(n("common.updateSuccess"))),t.value=!1,C("success")}finally{i.value=!1}},F=()=>{var u;o.value={id:void 0,name:void 0,address:void 0,sort:void 0,remark:void 0,principal:void 0,warehousePrice:void 0,truckagePrice:void 0,status:ae.ENABLE},(u=v.value)==null||u.resetFields()};return(u,e)=>{const c=j,d=T,q=I,x=D,f=X,A=R,w=Z,S=ee,M=Y;return p(),V(S,{title:a(g),modelValue:a(t),"onUpdate:modelValue":e[9]||(e[9]=l=>$(t)?t.value=l:null)},{footer:r(()=>[s(w,{onClick:W,type:"primary",disabled:a(i)},{default:r(()=>e[10]||(e[10]=[_("\u786E \u5B9A")])),_:1},8,["disabled"]),s(w,{onClick:e[8]||(e[8]=l=>t.value=!1)},{default:r(()=>e[11]||(e[11]=[_("\u53D6 \u6D88")])),_:1})]),default:r(()=>[O((p(),V(A,{ref_key:"formRef",ref:v,model:a(o),rules:a(U),"label-width":"100px"},{default:r(()=>[s(d,{label:"\u4ED3\u5E93\u540D\u79F0",prop:"name"},{default:r(()=>[s(c,{modelValue:a(o).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(o).name=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u5E93\u540D\u79F0"},null,8,["modelValue"])]),_:1}),s(d,{label:"\u4ED3\u5E93\u5730\u5740",prop:"address"},{default:r(()=>[s(c,{modelValue:a(o).address,"onUpdate:modelValue":e[1]||(e[1]=l=>a(o).address=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u5E93\u5730\u5740"},null,8,["modelValue"])]),_:1}),s(d,{label:"\u4ED3\u5E93\u72B6\u6001",prop:"status"},{default:r(()=>[s(x,{modelValue:a(o).status,"onUpdate:modelValue":e[2]||(e[2]=l=>a(o).status=l)},{default:r(()=>[(p(!0),L(H,null,J(a(K)(a(z).COMMON_STATUS),l=>(p(),V(q,{key:l.value,value:l.value},{default:r(()=>[_(Q(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(d,{label:"\u4ED3\u50A8\u8D39",prop:"warehousePrice"},{default:r(()=>[s(f,{modelValue:a(o).warehousePrice,"onUpdate:modelValue":e[3]||(e[3]=l=>a(o).warehousePrice=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u50A8\u8D39\uFF0C\u5355\u4F4D\uFF1A\u5143/\u5929/KG",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1}),s(d,{label:"\u642C\u8FD0\u8D39",prop:"truckagePrice"},{default:r(()=>[s(f,{modelValue:a(o).truckagePrice,"onUpdate:modelValue":e[4]||(e[4]=l=>a(o).truckagePrice=l),placeholder:"\u8BF7\u8F93\u5165\u642C\u8FD0\u8D39\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1}),s(d,{label:"\u8D1F\u8D23\u4EBA",prop:"principal"},{default:r(()=>[s(c,{modelValue:a(o).principal,"onUpdate:modelValue":e[5]||(e[5]=l=>a(o).principal=l),placeholder:"\u8BF7\u8F93\u5165\u8D1F\u8D23\u4EBA"},null,8,["modelValue"])]),_:1}),s(d,{label:"\u6392\u5E8F",prop:"sort"},{default:r(()=>[s(f,{modelValue:a(o).sort,"onUpdate:modelValue":e[6]||(e[6]=l=>a(o).sort=l),placeholder:"\u8BF7\u8F93\u5165\u6392\u5E8F",precision:0,class:"!w-1/1"},null,8,["modelValue"])]),_:1}),s(d,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[s(c,{type:"textarea",modelValue:a(o).remark,"onUpdate:modelValue":e[7]||(e[7]=l=>a(o).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[M,a(i)]])]),_:1},8,["title","modelValue"])}}});export{le as _};
