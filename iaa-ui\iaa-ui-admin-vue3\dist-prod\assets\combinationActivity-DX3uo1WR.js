import{as as t}from"./index-CRsFgzy0.js";const i=async a=>await t.get({url:"/promotion/combination-activity/page",params:a}),o=async a=>await t.get({url:"/promotion/combination-activity/get?id="+a}),n=a=>t.get({url:`/promotion/combination-activity/list-by-ids?ids=${a}`}),c=async a=>await t.post({url:"/promotion/combination-activity/create",data:a}),s=async a=>await t.put({url:"/promotion/combination-activity/update",data:a}),r=async a=>await t.put({url:"/promotion/combination-activity/close?id="+a}),e=async a=>await t.delete({url:"/promotion/combination-activity/delete?id="+a});export{i as a,r as b,c,e as d,n as e,o as g,s as u};
