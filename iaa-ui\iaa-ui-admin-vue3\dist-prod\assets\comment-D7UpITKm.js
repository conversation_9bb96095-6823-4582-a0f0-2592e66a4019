import{as as t}from"./index-CRsFgzy0.js";const c=async a=>await t.get({url:"/product/comment/page",params:a}),r=async a=>await t.get({url:"/product/comment/get?id="+a}),s=async a=>await t.post({url:"/product/comment/create",data:a}),e=async a=>await t.put({url:"/product/comment/update-visible",data:a}),o=async a=>await t.put({url:"/product/comment/reply",data:a});export{c as a,s as c,r as g,o as r,e as u};
