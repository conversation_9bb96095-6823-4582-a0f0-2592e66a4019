import{as as o}from"./index-CRsFgzy0.js";function u(t){return o.post({url:"/promotion/coupon-template/create",data:t})}function r(t){return o.put({url:"/promotion/coupon-template/update",data:t})}function a(t,e){const n={id:t,status:e};return o.put({url:"/promotion/coupon-template/update-status",data:n})}function p(t){return o.delete({url:"/promotion/coupon-template/delete?id="+t})}function i(t){return o.get({url:"/promotion/coupon-template/get?id="+t})}function s(t){return o.get({url:"/promotion/coupon-template/page",params:t})}function c(t){return o.get({url:`/promotion/coupon-template/list?ids=${t}`})}export{i as a,a as b,u as c,p as d,c as e,s as g,r as u};
