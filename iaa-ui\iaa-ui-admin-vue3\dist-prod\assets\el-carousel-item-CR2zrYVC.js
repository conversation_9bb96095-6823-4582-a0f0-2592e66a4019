import{aW as le,b0 as Ce,dk as ke,cy as Se,r as h,a2 as B,a as e,fr as ne,aK as q,dq as Me,q as oe,bV as xe,aL as Te,aM as Be,cQ as Ee,cj as Ne,bb as Ae,bA as ie,aY as re,d as Q,aZ as se,bI as He,c as P,o as E,A as ue,a3 as W,i as L,w as U,J as K,bu as X,X as _,g as Y,ay as ve,cO as Le,a6 as F,$ as ce,cN as _e,b3 as de,aw as he,F as Ve,y as $e,t as Oe,bv as Pe,f as ze,av as De,b1 as Ge,b7 as Re,b6 as We}from"./index-CRsFgzy0.js";const je=le({initialIndex:{type:Number,default:0},height:{type:String,default:""},trigger:{type:String,values:["hover","click"],default:"hover"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,values:["","none","outside"],default:""},arrow:{type:String,values:["always","hover","never"],default:"hover"},type:{type:String,values:["","card"],default:""},cardScale:{type:Number,default:.83},loop:{type:Boolean,default:!0},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},pauseOnHover:{type:Boolean,default:!0},motionBlur:Boolean}),qe={change:(t,s)=>[t,s].every(Ce)},fe=Symbol("carouselContextKey"),Z="ElCarouselItem",Ke=(t,s,f)=>{const{children:n,addChild:N,removeChild:p}=ke(ie(),Z),M=Se(),o=h(-1),m=h(null),k=h(!1),c=h(),x=h(0),w=h(!0),d=h(!0),I=h(!1),g=B(()=>t.arrow!=="never"&&!e(T)),C=B(()=>n.value.some(a=>a.props.label.toString().length>0)),A=B(()=>t.type==="card"),T=B(()=>t.direction==="vertical"),G=B(()=>t.height!=="auto"?{height:t.height}:{height:`${x.value}px`,overflow:"hidden"}),R=ne(a=>{u(a)},300,{trailing:!0}),V=ne(a=>{(function(r){t.trigger==="hover"&&r!==o.value&&(o.value=r,d.value||(I.value=!0))})(a)},300);function $(){m.value&&(clearInterval(m.value),m.value=null)}function v(){t.interval<=0||!t.autoplay||m.value||(m.value=setInterval(()=>y(),t.interval))}const y=()=>{d.value||(I.value=!0),d.value=!1,o.value<n.value.length-1?o.value=o.value+1:t.loop?o.value=0:I.value=!1};function u(a){if(d.value||(I.value=!0),d.value=!1,Ae(a)){const i=n.value.filter(D=>D.props.name===a);i.length>0&&(a=n.value.indexOf(i[0]))}if(a=Number(a),Number.isNaN(a)||a!==Math.floor(a))return;const r=n.value.length,S=o.value;o.value=a<0?t.loop?r-1:0:a>=r?t.loop?0:r-1:a,S===o.value&&b(S),l()}function b(a){n.value.forEach((r,S)=>{r.translateItem(S,o.value,a)})}function l(){$(),t.pauseOnHover||v()}q(()=>o.value,(a,r)=>{b(r),w.value&&(a%=2,r%=2),r>-1&&s("change",a,r)}),q(()=>t.autoplay,a=>{a?v():$()}),q(()=>t.loop,()=>{u(o.value)}),q(()=>t.interval,()=>{l()});const z=Me();return oe(()=>{q(()=>n.value,()=>{n.value.length>0&&u(t.initialIndex)},{immediate:!0}),z.value=xe(c.value,()=>{b()}),v()}),Te(()=>{$(),c.value&&z.value&&z.value.stop()}),Be(fe,{root:c,isCardType:A,isVertical:T,items:n,loop:t.loop,cardScale:t.cardScale,addItem:N,removeItem:p,setActiveItem:u,setContainerHeight:function(a){t.height==="auto"&&(x.value=a)}}),{root:c,activeIndex:o,arrowDisplay:g,hasLabel:C,hover:k,isCardType:A,isTransitioning:I,items:n,isVertical:T,containerStyle:G,isItemsTwoLength:w,handleButtonEnter:function(a){e(T)||n.value.forEach((r,S)=>{a===function(i,D){var O,H,J,ee;const j=e(n),ae=j.length;if(ae===0||!i.states.inStage)return!1;const me=D+1,ge=D-1,te=ae-1,ye=j[te].states.active,be=j[0].states.active,Ie=(H=(O=j[me])==null?void 0:O.states)==null?void 0:H.active,we=(ee=(J=j[ge])==null?void 0:J.states)==null?void 0:ee.active;return D===te&&be||Ie?"left":!!(D===0&&ye||we)&&"right"}(r,S)&&(r.states.hover=!0)})},handleTransitionEnd:function(){I.value=!1},handleButtonLeave:function(){e(T)||n.value.forEach(a=>{a.states.hover=!1})},handleIndicatorClick:function(a){a!==o.value&&(d.value||(I.value=!0)),o.value=a},handleMouseEnter:function(){k.value=!0,t.pauseOnHover&&$()},handleMouseLeave:function(){k.value=!1,v()},setActiveItem:u,prev:function(){u(o.value-1)},next:function(){u(o.value+1)},PlaceholderItem:function(){var a;const r=(a=M.default)==null?void 0:a.call(M);if(!r)return null;const S=Ee(r).filter(i=>Ne(i)&&i.type.name===Z);return(S==null?void 0:S.length)===2&&t.loop&&!A.value?(w.value=!0,S):(w.value=!1,null)},isTwoLengthShow:a=>!w.value||(o.value<=1?a<=1:a>1),throttledArrowClick:R,throttledIndicatorHover:V}},Xe=Q({name:"ElCarousel"});var Ye=re(Q({...Xe,props:je,emits:qe,setup(t,{expose:s,emit:f}){const n=t,{root:N,activeIndex:p,arrowDisplay:M,hasLabel:o,hover:m,isCardType:k,items:c,isVertical:x,containerStyle:w,handleButtonEnter:d,handleButtonLeave:I,isTransitioning:g,handleIndicatorClick:C,handleMouseEnter:A,handleMouseLeave:T,handleTransitionEnd:G,setActiveItem:R,prev:V,next:$,PlaceholderItem:v,isTwoLengthShow:y,throttledArrowClick:u,throttledIndicatorHover:b}=Ke(n,f),l=se("carousel"),{t:z}=He(),a=B(()=>{const i=[l.b(),l.m(n.direction)];return e(k)&&i.push(l.m("card")),i}),r=B(()=>{const i=[l.e("container")];return n.motionBlur&&e(g)&&c.value.length>1&&i.push(e(x)?`${l.namespace.value}-transitioning-vertical`:`${l.namespace.value}-transitioning`),i}),S=B(()=>{const i=[l.e("indicators"),l.em("indicators",n.direction)];return e(o)&&i.push(l.em("indicators","labels")),n.indicatorPosition==="outside"&&i.push(l.em("indicators","outside")),e(x)&&i.push(l.em("indicators","right")),i});return s({activeIndex:p,setActiveItem:R,prev:V,next:$}),(i,D)=>(E(),P("div",{ref_key:"root",ref:N,class:_(e(a)),onMouseenter:X(e(A),["stop"]),onMouseleave:X(e(T),["stop"])},[e(M)?(E(),ue(ce,{key:0,name:"carousel-arrow-left",persisted:""},{default:U(()=>[K(L("button",{type:"button",class:_([e(l).e("arrow"),e(l).em("arrow","left")]),"aria-label":e(z)("el.carousel.leftArrow"),onMouseenter:O=>e(d)("left"),onMouseleave:e(I),onClick:X(O=>e(u)(e(p)-1),["stop"])},[Y(e(ve),null,{default:U(()=>[Y(e(Le))]),_:1})],42,["aria-label","onMouseenter","onMouseleave","onClick"]),[[F,(i.arrow==="always"||e(m))&&(n.loop||e(p)>0)]])]),_:1})):W("v-if",!0),e(M)?(E(),ue(ce,{key:1,name:"carousel-arrow-right",persisted:""},{default:U(()=>[K(L("button",{type:"button",class:_([e(l).e("arrow"),e(l).em("arrow","right")]),"aria-label":e(z)("el.carousel.rightArrow"),onMouseenter:O=>e(d)("right"),onMouseleave:e(I),onClick:X(O=>e(u)(e(p)+1),["stop"])},[Y(e(ve),null,{default:U(()=>[Y(e(_e))]),_:1})],42,["aria-label","onMouseenter","onMouseleave","onClick"]),[[F,(i.arrow==="always"||e(m))&&(n.loop||e(p)<e(c).length-1)]])]),_:1})):W("v-if",!0),L("div",{class:_(e(r)),style:he(e(w)),onTransitionend:e(G)},[Y(e(v)),de(i.$slots,"default")],46,["onTransitionend"]),i.indicatorPosition!=="none"?(E(),P("ul",{key:2,class:_(e(S))},[(E(!0),P(Ve,null,$e(e(c),(O,H)=>K((E(),P("li",{key:H,class:_([e(l).e("indicator"),e(l).em("indicator",i.direction),e(l).is("active",H===e(p))]),onMouseenter:J=>e(b)(H),onClick:X(J=>e(C)(H),["stop"])},[L("button",{class:_(e(l).e("button")),"aria-label":e(z)("el.carousel.indicator",{index:H+1})},[e(o)?(E(),P("span",{key:0},Oe(O.props.label),1)):W("v-if",!0)],10,["aria-label"])],42,["onMouseenter","onClick"])),[[F,e(y)(H)]])),128))],2)):W("v-if",!0),n.motionBlur?(E(),P("svg",{key:3,xmlns:"http://www.w3.org/2000/svg",version:"1.1",style:{display:"none"}},[L("defs",null,[L("filter",{id:"elCarouselHorizontal"},[L("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"12,0"})]),L("filter",{id:"elCarouselVertical"},[L("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"0,10"})])])])):W("v-if",!0)],42,["onMouseenter","onMouseleave"]))}}),[["__file","carousel.vue"]]);const Fe=le({name:{type:String,default:""},label:{type:[String,Number],default:""}}),Je=t=>{const s=Pe(fe),f=ie(),n=h(),N=h(!1),p=h(0),M=h(1),o=h(!1),m=h(!1),k=h(!1),c=h(!1),{isCardType:x,isVertical:w,cardScale:d}=s,I=(g,C,A)=>{var T;const G=e(x),R=(T=s.items.value.length)!=null?T:Number.NaN,V=g===C;G||Ge(A)||(c.value=V||g===A),!V&&R>2&&s.loop&&(g=function(v,y,u){const b=u-1,l=u/2;return y===0&&v===b?-1:y===b&&v===0?u:v<y-1&&y-v>=l?u+1:v>y+1&&v-y>=l?-2:v}(g,C,R));const $=e(w);o.value=V,G?(k.value=Math.round(Math.abs(g-C))<=1,p.value=function(v,y){var u,b;const l=e(w)?((u=s.root.value)==null?void 0:u.offsetHeight)||0:((b=s.root.value)==null?void 0:b.offsetWidth)||0;return k.value?l*((2-d)*(v-y)+1)/4:v<y?-(1+d)*l/4:(3+d)*l/4}(g,C),M.value=e(o)?1:d):p.value=function(v,y,u){const b=s.root.value;return b?((u?b.offsetHeight:b.offsetWidth)||0)*(v-y):0}(g,C,$),m.value=!0,V&&n.value&&s.setContainerHeight(n.value.offsetHeight)};return oe(()=>{s.addItem({props:t,states:ze({hover:N,translate:p,scale:M,active:o,ready:m,inStage:k,animating:c}),uid:f.uid,translateItem:I})}),De(()=>{s.removeItem(f.uid)}),{carouselItemRef:n,active:o,animating:c,hover:N,inStage:k,isVertical:w,translate:p,isCardType:x,scale:M,ready:m,handleItemClick:function(){if(s&&e(x)){const g=s.items.value.findIndex(({uid:C})=>C===f.uid);s.setActiveItem(g)}}}},Qe=Q({name:Z});var pe=re(Q({...Qe,props:Fe,setup(t){const s=t,f=se("carousel"),{carouselItemRef:n,active:N,animating:p,hover:M,inStage:o,isVertical:m,translate:k,isCardType:c,scale:x,ready:w,handleItemClick:d}=Je(s),I=B(()=>[f.e("item"),f.is("active",N.value),f.is("in-stage",o.value),f.is("hover",M.value),f.is("animating",p.value),{[f.em("item","card")]:c.value,[f.em("item","card-vertical")]:c.value&&m.value}]),g=B(()=>({transform:[`${"translate"+(e(m)?"Y":"X")}(${e(k)}px)`,`scale(${e(x)})`].join(" ")}));return(C,A)=>K((E(),P("div",{ref_key:"carouselItemRef",ref:n,class:_(e(I)),style:he(e(g)),onClick:e(d)},[e(c)?K((E(),P("div",{key:0,class:_(e(f).e("mask"))},null,2)),[[F,!e(N)]]):W("v-if",!0),de(C.$slots,"default")],14,["onClick"])),[[F,e(w)]])}}),[["__file","carousel-item.vue"]]);const Ue=We(Ye,{CarouselItem:pe}),Ze=Re(pe);export{Ze as E,Ue as a};
