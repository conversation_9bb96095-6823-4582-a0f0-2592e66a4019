import{a2 as M,a as o,a$ as at,r as Y,aK as Xe,bA as nt,dq as De,b0 as ne,bS as no,bO as Le,bW as st,bc as Ye,bz as He,cj as Ce,b2 as St,aZ as Ve,co as so,aX as G,aW as Ae,d as Ie,bv as it,dQ as io,bD as co,g as K,aG as ke,bw as uo,by as ho,bB as mo,q as ct,bC as fo,f7 as wo,bd as Tt,bb as Ht,F as po,bF as go,b1 as yo,aM as Ct,b4 as oe,b3 as Ge,cN as xo,ay as It,bk as vo,f8 as bo,f9 as Ro,bV as So,aL as To,b6 as Et}from"./index-CRsFgzy0.js";import{j as Ee,v as Ho,k as Co,I as Mt,S as Ot,u as Io,B as Wt,F as Qe,A as Ze,d as Je,R as Eo,g as Kt,b as <PERSON>,c as Dt,a as Lt,l as At,e as Oo,D as zt,C as ze,E as dt,f as ut,h as Ft}from"./el-virtual-list-AoPW9ghL.js";import{E as Wo}from"./el-empty-CqTDiVWi.js";var Ne=(e=>(e.ASC="asc",e.DESC="desc",e))(Ne||{}),Pe=(e=>(e.CENTER="center",e.RIGHT="right",e))(Pe||{}),ht=(e=>(e.LEFT="left",e.RIGHT="right",e))(ht||{});const mt={asc:"desc",desc:"asc"},$e=Symbol("placeholder"),Ko=(e,{mainTableRef:t,leftTableRef:l,rightTableRef:r,tableInstance:a,ns:n,isScrolling:i})=>{const s=nt(),{emit:f}=s,u=De(!1),d=Y(e.defaultExpandedRowKeys||[]),y=Y(-1),R=De(null),L=Y({}),O=Y({}),c=De({}),N=De({}),V=De({}),$=M(()=>ne(e.estimatedRowHeight)),U=no(()=>{var S,T,P,q;u.value=!0,L.value={...o(L),...o(O)},k(o(R),!1),O.value={},R.value=null,(S=t.value)==null||S.forceUpdate(),(T=l.value)==null||T.forceUpdate(),(P=r.value)==null||P.forceUpdate(),(q=s.proxy)==null||q.$forceUpdate(),u.value=!1},0);function k(S,T=!1){o($)&&[t,l,r].forEach(P=>{const q=o(P);q&&q.resetAfterRowIndex(S,T)})}return{expandedRowKeys:d,lastRenderedRowIndex:y,isDynamic:$,isResetting:u,rowHeights:L,resetAfterIndex:k,onRowExpanded:function({expanded:S,rowData:T,rowIndex:P,rowKey:q}){var J,D;const X=[...o(d)],Q=X.indexOf(q);S?Q===-1&&X.push(q):Q>-1&&X.splice(Q,1),d.value=X,f("update:expandedRowKeys",X),(J=e.onRowExpand)==null||J.call(e,{expanded:S,rowData:T,rowIndex:P,rowKey:q}),(D=e.onExpandedRowsChange)==null||D.call(e,X)},onRowHovered:function({hovered:S,rowKey:T}){i.value||a.vnode.el.querySelectorAll(`[rowkey="${String(T)}"]`).forEach(P=>{S?P.classList.add(n.is("hovered")):P.classList.remove(n.is("hovered"))})},onRowsRendered:function(S){var T;(T=e.onRowsRendered)==null||T.call(e,S),S.rowCacheEnd>o(y)&&(y.value=S.rowCacheEnd)},onRowHeightChange:function({rowKey:S,height:T,rowIndex:P},q){q?q===ht.RIGHT?V.value[S]=T:c.value[S]=T:N.value[S]=T;const J=Math.max(...[c,V,N].map(D=>D.value[S]||0));o(L)[S]!==J&&(function(D,X,Q){const C=o(R);(C===null||C>Q)&&(R.value=Q),O.value[D]=X}(S,J,P),U())}}},Do=(e,t)=>e+t,_e=e=>Le(e)?e.reduce(Do,0):e,Me=(e,t,l={})=>st(e)?e(t):e??l,xe=e=>(["width","maxWidth","minWidth","height"].forEach(t=>{e[t]=Ye(e[t])}),e),jt=e=>Ce(e)?t=>He(e,t):e;function Lo(e){const t=Y(),l=Y(),r=Y(),{columns:a,columnsStyles:n,columnsTotalWidth:i,fixedColumnsOnLeft:s,fixedColumnsOnRight:f,hasFixedColumns:u,mainColumns:d,onColumnSorted:y}=function(z,h,w){const m=M(()=>o(h).map((p,W)=>{var B,re;return{...p,key:(re=(B=p.key)!=null?B:p.dataKey)!=null?re:W}})),H=M(()=>o(m).filter(p=>!p.hidden)),g=M(()=>o(H).filter(p=>p.fixed==="left"||p.fixed===!0)),b=M(()=>o(H).filter(p=>p.fixed==="right")),x=M(()=>o(H).filter(p=>!p.fixed)),I=M(()=>{const p=[];return o(g).forEach(W=>{p.push({...W,placeholderSign:$e})}),o(x).forEach(W=>{p.push(W)}),o(b).forEach(W=>{p.push({...W,placeholderSign:$e})}),p}),v=M(()=>o(g).length||o(b).length),E=M(()=>o(m).reduce((p,W)=>(p[W.key]=((B,re,we)=>{var ee;const te={flexGrow:0,flexShrink:0,...we?{}:{flexGrow:B.flexGrow||0,flexShrink:B.flexShrink||1}};we||(te.flexShrink=1);const ae={...(ee=B.style)!=null?ee:{},...te,flexBasis:"auto",width:B.width};return re||(B.maxWidth&&(ae.maxWidth=B.maxWidth),B.minWidth&&(ae.minWidth=B.minWidth)),ae})(W,o(w),z.fixed),p),{})),F=M(()=>o(H).reduce((p,W)=>p+W.width,0)),A=p=>o(m).find(W=>W.key===p);return{columns:m,columnsStyles:E,columnsTotalWidth:F,fixedColumnsOnLeft:g,fixedColumnsOnRight:b,hasFixedColumns:v,mainColumns:I,normalColumns:x,visibleColumns:H,getColumn:A,getColumnStyle:p=>o(E)[p],updateColumnWidth:(p,W)=>{p.width=W},onColumnSorted:function(p){var W;const{key:B}=p.currentTarget.dataset;if(!B)return;const{sortState:re,sortBy:we}=z;let ee=Ne.ASC;ee=at(re)?mt[re[B]]:mt[we.order],(W=z.onColumnSort)==null||W.call(z,{column:A(B),key:B,order:ee})}}}(e,St(e,"columns"),St(e,"fixed")),{scrollTo:R,scrollToLeft:L,scrollToTop:O,scrollToRow:c,onScroll:N,onVerticalScroll:V,scrollPos:$}=((z,{mainTableRef:h,leftTableRef:w,rightTableRef:m,onMaybeEndReached:H})=>{const g=Y({scrollLeft:0,scrollTop:0});function b(v){var E,F,A;const{scrollTop:p}=v;(E=h.value)==null||E.scrollTo(v),(F=w.value)==null||F.scrollToTop(p),(A=m.value)==null||A.scrollToTop(p)}function x(v){g.value=v,b(v)}function I(v){g.value.scrollTop=v,b(o(g))}return Xe(()=>o(g).scrollTop,(v,E)=>{v>E&&H()}),{scrollPos:g,scrollTo:x,scrollToLeft:function(v){var E,F;g.value.scrollLeft=v,(F=(E=h.value)==null?void 0:E.scrollTo)==null||F.call(E,o(g))},scrollToTop:I,scrollToRow:function(v,E="auto"){var F;(F=h.value)==null||F.scrollToRow(v,E)},onScroll:function(v){var E;x(v),(E=z.onScroll)==null||E.call(z,v)},onVerticalScroll:function({scrollTop:v}){const{scrollTop:E}=o(g);v!==E&&I(v)}}})(e,{mainTableRef:t,leftTableRef:l,rightTableRef:r,onMaybeEndReached:function(){const{onEndReached:z}=e;if(!z)return;const{scrollTop:h}=o($),w=o(me),m=o(se),H=w-(h+m)+e.hScrollbarSize;o(P)>=0&&w===h+o(de)-o(Se)&&z(H)}}),U=Ve("table-v2"),k=nt(),S=De(!1),{expandedRowKeys:T,lastRenderedRowIndex:P,isDynamic:q,isResetting:J,rowHeights:D,resetAfterIndex:X,onRowExpanded:Q,onRowHeightChange:C,onRowHovered:j,onRowsRendered:_}=Ko(e,{mainTableRef:t,leftTableRef:l,rightTableRef:r,tableInstance:k,ns:U,isScrolling:S}),{data:Z,depthMap:le}=((z,{expandedRowKeys:h,lastRenderedRowIndex:w,resetAfterIndex:m})=>{const H=Y({}),g=M(()=>{const x={},{data:I,rowKey:v}=z,E=o(h);if(!E||!E.length)return I;const F=[],A=new Set;E.forEach(W=>A.add(W));let p=I.slice();for(p.forEach(W=>x[W[v]]=0);p.length>0;){const W=p.shift();F.push(W),A.has(W[v])&&Le(W.children)&&W.children.length>0&&(p=[...W.children,...p],W.children.forEach(B=>x[B[v]]=x[W[v]]+1))}return H.value=x,F}),b=M(()=>{const{data:x,expandColumnKey:I}=z;return I?o(g):x});return Xe(b,(x,I)=>{x!==I&&(w.value=-1,m(0,!0))}),{data:b,depthMap:H}})(e,{expandedRowKeys:T,lastRenderedRowIndex:P,resetAfterIndex:X}),me=M(()=>{const{estimatedRowHeight:z,rowHeight:h}=e,w=o(Z);return ne(z)?Object.values(o(D)).reduce((m,H)=>m+H,0):w.length*h}),{bodyWidth:ve,fixedTableHeight:fe,mainTableHeight:de,leftTableWidth:be,rightTableWidth:ye,headerWidth:ue,windowHeight:se,footerHeight:he,emptyStyle:Re,rootStyle:Ke,headerHeight:Se}=((z,{columnsTotalWidth:h,rowsHeight:w,fixedColumnsOnLeft:m,fixedColumnsOnRight:H})=>{const g=M(()=>{const{fixed:ee,width:te,vScrollbarSize:ae}=z,ie=te-ae;return ee?Math.max(Math.round(o(h)),ie):ie}),b=M(()=>o(g)+z.vScrollbarSize),x=M(()=>{const{height:ee=0,maxHeight:te=0,footerHeight:ae,hScrollbarSize:ie}=z;if(te>0){const ot=o(p),Ue=o(w),lt=o(A)+ot+Ue+ie;return Math.min(lt,te-ae)}return ee-ae}),I=M(()=>{const{maxHeight:ee}=z,te=o(x);if(ne(ee)&&ee>0)return te;const ae=o(w)+o(A)+o(p);return Math.min(te,ae)}),v=ee=>ee.width,E=M(()=>_e(o(m).map(v))),F=M(()=>_e(o(H).map(v))),A=M(()=>_e(z.headerHeight)),p=M(()=>{var ee;return(((ee=z.fixedData)==null?void 0:ee.length)||0)*z.rowHeight}),W=M(()=>o(x)-o(A)-o(p)),B=M(()=>{const{style:ee={},height:te,width:ae}=z;return xe({...ee,height:te,width:ae})}),re=M(()=>xe({height:z.footerHeight})),we=M(()=>({top:Ye(o(A)),bottom:Ye(z.footerHeight),width:Ye(z.width)}));return{bodyWidth:g,fixedTableHeight:I,mainTableHeight:x,leftTableWidth:E,rightTableWidth:F,headerWidth:b,windowHeight:W,footerHeight:re,emptyStyle:we,rootStyle:B,headerHeight:A}})(e,{columnsTotalWidth:i,fixedColumnsOnLeft:s,fixedColumnsOnRight:f,rowsHeight:me}),Fe=Y(),je=M(()=>{const z=o(Z).length===0;return Le(e.fixedData)?e.fixedData.length===0&&z:z});return Xe(()=>e.expandedRowKeys,z=>T.value=z,{deep:!0}),{columns:a,containerRef:Fe,mainTableRef:t,leftTableRef:l,rightTableRef:r,isDynamic:q,isResetting:J,isScrolling:S,hasFixedColumns:u,columnsStyles:n,columnsTotalWidth:i,data:Z,expandedRowKeys:T,depthMap:le,fixedColumnsOnLeft:s,fixedColumnsOnRight:f,mainColumns:d,bodyWidth:ve,emptyStyle:Re,rootStyle:Ke,headerWidth:ue,footerHeight:he,mainTableHeight:de,fixedTableHeight:fe,leftTableWidth:be,rightTableWidth:ye,showEmpty:je,getRowHeight:function(z){const{estimatedRowHeight:h,rowHeight:w,rowKey:m}=e;return h?o(D)[o(Z)[z][m]]||h:w},onColumnSorted:y,onRowHovered:j,onRowExpanded:Q,onRowsRendered:_,onRowHeightChange:C,scrollTo:R,scrollToLeft:L,scrollToTop:O,scrollToRow:c,onScroll:N,onVerticalScroll:V}}const ft=Symbol("tableV2"),Vt=String,Be={type:G(Array),required:!0},wt={type:G(Array)},kt={...wt,required:!0},Ao=String,Gt={type:G(Array),default:()=>so([])},Oe={type:Number,required:!0},Nt={type:G([String,Number,Symbol]),default:"id"},Pt={type:G(Object)},We=Ae({class:String,columns:Be,columnsStyles:{type:G(Object),required:!0},depth:Number,expandColumnKey:Ao,estimatedRowHeight:{...Ee.estimatedRowHeight,default:void 0},isScrolling:Boolean,onRowExpand:{type:G(Function)},onRowHover:{type:G(Function)},onRowHeightChange:{type:G(Function)},rowData:{type:G(Object),required:!0},rowEventHandlers:{type:G(Object)},rowIndex:{type:Number,required:!0},rowKey:Nt,style:{type:G(Object)}}),pt={type:Number,required:!0},gt=Ae({class:String,columns:Be,fixedHeaderData:{type:G(Array)},headerData:{type:G(Array),required:!0},headerHeight:{type:G([Number,Array]),default:50},rowWidth:pt,rowHeight:{type:Number,default:50},height:pt,width:pt}),et=Ae({columns:Be,data:kt,fixedData:wt,estimatedRowHeight:We.estimatedRowHeight,width:Oe,height:Oe,headerWidth:Oe,headerHeight:gt.headerHeight,bodyWidth:Oe,rowHeight:Oe,cache:Ho.cache,useIsScrolling:Boolean,scrollbarAlwaysOn:Ee.scrollbarAlwaysOn,scrollbarStartGap:Ee.scrollbarStartGap,scrollbarEndGap:Ee.scrollbarEndGap,class:Vt,style:Pt,containerStyle:Pt,getRowHeight:{type:G(Function),required:!0},rowKey:We.rowKey,onRowsRendered:{type:G(Function)},onScroll:{type:G(Function)}}),zo=Ae({cache:et.cache,estimatedRowHeight:We.estimatedRowHeight,rowKey:Nt,headerClass:{type:G([String,Function])},headerProps:{type:G([Object,Function])},headerCellProps:{type:G([Object,Function])},headerHeight:gt.headerHeight,footerHeight:{type:Number,default:0},rowClass:{type:G([String,Function])},rowProps:{type:G([Object,Function])},rowHeight:{type:Number,default:50},cellProps:{type:G([Object,Function])},columns:Be,data:kt,dataGetter:{type:G(Function)},fixedData:wt,expandColumnKey:We.expandColumnKey,expandedRowKeys:Gt,defaultExpandedRowKeys:Gt,class:Vt,fixed:Boolean,style:{type:G(Object)},width:Oe,height:Oe,maxHeight:Number,useIsScrolling:Boolean,indentSize:{type:Number,default:12},iconSize:{type:Number,default:12},hScrollbarSize:Ee.hScrollbarSize,vScrollbarSize:Ee.vScrollbarSize,scrollbarAlwaysOn:Co.alwaysOn,sortBy:{type:G(Object),default:()=>({})},sortState:{type:G(Object),default:void 0},onColumnSort:{type:G(Function)},onExpandedRowsChange:{type:G(Function)},onEndReached:{type:G(Function)},onRowExpand:We.onRowExpand,onScroll:et.onScroll,onRowsRendered:et.onRowsRendered,rowEventHandlers:We.rowEventHandlers}),Fo=Ie({name:"ElTableV2Header",props:gt,setup(e,{slots:t,expose:l}){const r=Ve("table-v2"),a=it("tableV2GridScrollLeft"),n=Y(),i=M(()=>xe({width:e.width,height:e.height})),s=M(()=>xe({width:e.rowWidth,height:e.height})),f=M(()=>io(o(e.headerHeight))),u=R=>{const L=o(n);ke(()=>{L!=null&&L.scroll&&L.scroll({left:R})})},d=()=>{const R=r.e("fixed-header-row"),{columns:L,fixedHeaderData:O,rowHeight:c}=e;return O==null?void 0:O.map((N,V)=>{var $;const U=xe({height:c,width:"100%"});return($=t.fixed)==null?void 0:$.call(t,{class:R,columns:L,rowData:N,rowIndex:-(V+1),style:U})})},y=()=>{const R=r.e("dynamic-header-row"),{columns:L}=e;return o(f).map((O,c)=>{var N;const V=xe({width:"100%",height:O});return(N=t.dynamic)==null?void 0:N.call(t,{class:R,columns:L,headerIndex:c,style:V})})};return co(()=>{a!=null&&a.value&&u(a.value)}),l({scrollToLeft:u}),()=>{if(!(e.height<=0))return K("div",{ref:n,class:e.class,style:o(i),role:"rowgroup"},[K("div",{style:o(s),class:r.e("header")},[y(),d()])])}}});var jo=Fo;const $t=({name:e,clearCache:t,getColumnPosition:l,getColumnStartIndexForOffset:r,getColumnStopIndexForStartIndex:a,getEstimatedTotalHeight:n,getEstimatedTotalWidth:i,getColumnOffset:s,getRowOffset:f,getRowPosition:u,getRowStartIndexForOffset:d,getRowStopIndexForStartIndex:y,initCache:R,injectToInstance:L,validateProps:O})=>Ie({name:e??"ElVirtualList",props:Ee,emits:[Mt,Ot],setup(c,{emit:N,expose:V,slots:$}){const U=Ve("vl");O(c);const k=nt(),S=Y(R(c,k));L==null||L(k,S);const T=Y(),P=Y(),q=Y(),J=Y(null),D=Y({isScrolling:!1,scrollLeft:ne(c.initScrollLeft)?c.initScrollLeft:0,scrollTop:ne(c.initScrollTop)?c.initScrollTop:0,updateRequested:!1,xAxisScrollDir:Qe,yAxisScrollDir:Qe}),X=Io(),Q=M(()=>Number.parseInt(`${c.height}`,10)),C=M(()=>Number.parseInt(`${c.width}`,10)),j=M(()=>{const{totalColumn:h,totalRow:w,columnCache:m}=c,{isScrolling:H,xAxisScrollDir:g,scrollLeft:b}=o(D);if(h===0||w===0)return[0,0,0,0];const x=r(c,b,o(S)),I=a(c,x,b,o(S)),v=H&&g!==Wt?1:Math.max(1,m),E=H&&g!==Qe?1:Math.max(1,m);return[Math.max(0,x-v),Math.max(0,Math.min(h-1,I+E)),x,I]}),_=M(()=>{const{totalColumn:h,totalRow:w,rowCache:m}=c,{isScrolling:H,yAxisScrollDir:g,scrollTop:b}=o(D);if(h===0||w===0)return[0,0,0,0];const x=d(c,b,o(S)),I=y(c,x,b,o(S)),v=H&&g!==Wt?1:Math.max(1,m),E=H&&g!==Qe?1:Math.max(1,m);return[Math.max(0,x-v),Math.max(0,Math.min(w-1,I+E)),x,I]}),Z=M(()=>n(c,o(S))),le=M(()=>i(c,o(S))),me=M(()=>{var h;return[{position:"relative",overflow:"hidden",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:c.direction,height:ne(c.height)?`${c.height}px`:c.height,width:ne(c.width)?`${c.width}px`:c.width},(h=c.style)!=null?h:{}]}),ve=M(()=>{const h=`${o(le)}px`;return{height:`${o(Z)}px`,pointerEvents:o(D).isScrolling?"none":void 0,width:h}}),fe=()=>{const{totalColumn:h,totalRow:w}=c;if(h>0&&w>0){const[I,v,E,F]=o(j),[A,p,W,B]=o(_);N(Mt,{columnCacheStart:I,columnCacheEnd:v,rowCacheStart:A,rowCacheEnd:p,columnVisibleStart:E,columnVisibleEnd:F,rowVisibleStart:W,rowVisibleEnd:B})}const{scrollLeft:m,scrollTop:H,updateRequested:g,xAxisScrollDir:b,yAxisScrollDir:x}=o(D);N(Ot,{xAxisScrollDir:b,scrollLeft:m,yAxisScrollDir:x,scrollTop:H,updateRequested:g})},de=h=>{const{clientHeight:w,clientWidth:m,scrollHeight:H,scrollLeft:g,scrollTop:b,scrollWidth:x}=h.currentTarget,I=o(D);if(I.scrollTop===b&&I.scrollLeft===g)return;let v=g;if(At(c.direction))switch(Kt()){case Dt:v=-g;break;case Oo:v=x-m-g}D.value={...I,isScrolling:!0,scrollLeft:v,scrollTop:Math.max(0,Math.min(b,H-w)),updateRequested:!0,xAxisScrollDir:Je(I.scrollLeft,v),yAxisScrollDir:Je(I.scrollTop,b)},ke(()=>Re()),Ke(),fe()},be=(h,w)=>{const m=o(Q),H=(Z.value-m)/w*h;se({scrollTop:Math.min(Z.value-m,H)})},ye=(h,w)=>{const m=o(C),H=(le.value-m)/w*h;se({scrollLeft:Math.min(le.value-m,H)})},{onWheel:ue}=(({atXEndEdge:h,atXStartEdge:w,atYEndEdge:m,atYStartEdge:H},g)=>{let b=null,x=0,I=0;const v=(E,F)=>{const A=E<=0&&w.value||E>=0&&h.value,p=F<=0&&H.value||F>=0&&m.value;return A&&p};return{hasReachedEdge:v,onWheel:E=>{uo(b);let F=E.deltaX,A=E.deltaY;Math.abs(F)>Math.abs(A)?A=0:F=0,E.shiftKey&&A!==0&&(F=A,A=0),v(x,I)&&v(x+F,I+A)||(x+=F,I+=A,E.preventDefault(),b=ho(()=>{g(x,I),x=0,I=0}))}}})({atXStartEdge:M(()=>D.value.scrollLeft<=0),atXEndEdge:M(()=>D.value.scrollLeft>=le.value-o(C)),atYStartEdge:M(()=>D.value.scrollTop<=0),atYEndEdge:M(()=>D.value.scrollTop>=Z.value-o(Q))},(h,w)=>{var m,H,g,b;(H=(m=P.value)==null?void 0:m.onMouseUp)==null||H.call(m),(b=(g=q.value)==null?void 0:g.onMouseUp)==null||b.call(g);const x=o(C),I=o(Q);se({scrollLeft:Math.min(D.value.scrollLeft+h,le.value-x),scrollTop:Math.min(D.value.scrollTop+w,Z.value-I)})});mo(T,"wheel",ue,{passive:!1});const se=({scrollLeft:h=D.value.scrollLeft,scrollTop:w=D.value.scrollTop})=>{h=Math.max(h,0),w=Math.max(w,0);const m=o(D);w===m.scrollTop&&h===m.scrollLeft||(D.value={...m,xAxisScrollDir:Je(m.scrollLeft,h),yAxisScrollDir:Je(m.scrollTop,w),scrollLeft:h,scrollTop:w,updateRequested:!0},ke(()=>Re()),Ke(),fe())},he=(h,w)=>{const{columnWidth:m,direction:H,rowHeight:g}=c,b=X.value(t&&m,t&&g,t&&H),x=`${h},${w}`;if(go(b,x))return b[x];{const[,I]=l(c,w,o(S)),v=o(S),E=At(H),[F,A]=u(c,h,v),[p]=l(c,w,v);return b[x]={position:"absolute",left:E?void 0:`${I}px`,right:E?`${I}px`:void 0,top:`${A}px`,height:`${F}px`,width:`${p}px`},b[x]}},Re=()=>{D.value.isScrolling=!1,ke(()=>{X.value(-1,null,null)})};ct(()=>{if(!fo)return;const{initScrollLeft:h,initScrollTop:w}=c,m=o(T);m&&(ne(h)&&(m.scrollLeft=h),ne(w)&&(m.scrollTop=w)),fe()});const Ke=()=>{const{direction:h}=c,{scrollLeft:w,scrollTop:m,updateRequested:H}=o(D),g=o(T);if(H&&g){if(h===Eo)switch(Kt()){case Dt:g.scrollLeft=-w;break;case Mo:g.scrollLeft=w;break;default:{const{clientWidth:b,scrollWidth:x}=g;g.scrollLeft=x-b-w;break}}else g.scrollLeft=Math.max(0,w);g.scrollTop=Math.max(0,m)}},{resetAfterColumnIndex:Se,resetAfterRowIndex:Fe,resetAfter:je}=k.proxy;V({windowRef:T,innerRef:J,getItemStyleCache:X,scrollTo:se,scrollToItem:(h=0,w=0,m=Ze)=>{const H=o(D);w=Math.max(0,Math.min(w,c.totalColumn-1)),h=Math.max(0,Math.min(h,c.totalRow-1));const g=wo(U.namespace.value),b=o(S),x=n(c,b),I=i(c,b);se({scrollLeft:s(c,w,m,H.scrollLeft,b,I>c.width?g:0),scrollTop:f(c,h,m,H.scrollTop,b,x>c.height?g:0)})},states:D,resetAfterColumnIndex:Se,resetAfterRowIndex:Fe,resetAfter:je});const z=()=>{const h=Tt(c.innerElement),w=(()=>{var m;const[H,g]=o(j),[b,x]=o(_),{data:I,totalColumn:v,totalRow:E,useIsScrolling:F,itemKey:A}=c,p=[];if(E>0&&v>0)for(let W=b;W<=x;W++)for(let B=H;B<=g;B++){const re=A({columnIndex:B,data:I,rowIndex:W});p.push(He(po,{key:re},(m=$.default)==null?void 0:m.call($,{columnIndex:B,data:I,isScrolling:F?o(D).isScrolling:void 0,style:he(W,B),rowIndex:W})))}return p})();return[He(h,{style:o(ve),ref:J},Ht(h)?w:{default:()=>w})]};return()=>{const h=Tt(c.containerElement),{horizontalScrollbar:w,verticalScrollbar:m}=(()=>{const{scrollbarAlwaysOn:g,scrollbarStartGap:b,scrollbarEndGap:x,totalColumn:I,totalRow:v}=c,E=o(C),F=o(Q),A=o(le),p=o(Z),{scrollLeft:W,scrollTop:B}=o(D);return{horizontalScrollbar:He(Lt,{ref:P,alwaysOn:g,startGap:b,endGap:x,class:U.e("horizontal"),clientSize:E,layout:"horizontal",onScroll:ye,ratio:100*E/A,scrollFrom:W/(A-E),total:v,visible:!0}),verticalScrollbar:He(Lt,{ref:q,alwaysOn:g,startGap:b,endGap:x,class:U.e("vertical"),clientSize:F,layout:"vertical",onScroll:be,ratio:100*F/p,scrollFrom:B/(p-F),total:I,visible:!0})}})(),H=z();return He("div",{key:0,class:U.e("wrapper"),role:c.role},[He(h,{class:c.className,style:o(me),onScroll:de,ref:T},Ht(h)?H:{default:()=>H}),w,m])}}}),{max:tt,min:Bt,floor:qt}=Math,Vo={column:"columnWidth",row:"rowHeight"},yt={column:"lastVisitedColumnIndex",row:"lastVisitedRowIndex"},ge=(e,t,l,r)=>{const[a,n,i]=[l[r],e[Vo[r]],l[yt[r]]];if(t>i){let s=0;if(i>=0){const f=a[i];s=f.offset+f.size}for(let f=i+1;f<=t;f++){const u=n(f);a[f]={offset:s,size:u},s+=u}l[yt[r]]=t}return a[t]},Ut=(e,t,l,r,a,n)=>{for(;l<=r;){const i=l+qt((r-l)/2),s=ge(e,i,t,n).offset;if(s===a)return i;s<a?l=i+1:r=i-1}return tt(0,l-1)},Xt=(e,t,l,r)=>{const[a,n]=[t[r],t[yt[r]]];return(n>0?a[n].offset:0)>=l?Ut(e,t,0,n,l,r):((i,s,f,u,d)=>{const y=d==="column"?i.totalColumn:i.totalRow;let R=1;for(;f<y&&ge(i,f,s,d).offset<u;)f+=R,R*=2;return Ut(i,s,qt(f/2),Bt(f,y-1),u,d)})(e,t,tt(0,n),l,r)},Yt=({totalRow:e},{estimatedRowHeight:t,lastVisitedRowIndex:l,row:r})=>{let a=0;if(l>=e&&(l=e-1),l>=0){const n=r[l];a=n.offset+n.size}return a+(e-l-1)*t},Qt=({totalColumn:e},{column:t,estimatedColumnWidth:l,lastVisitedColumnIndex:r})=>{let a=0;if(r>e&&(r=e-1),r>=0){const n=t[r];a=n.offset+n.size}return a+(e-r-1)*l},ko={column:Qt,row:Yt},Zt=(e,t,l,r,a,n,i)=>{const[s,f]=[n==="row"?e.height:e.width,ko[n]],u=ge(e,t,a,n),d=f(e,a),y=tt(0,Bt(d-s,u.offset)),R=tt(0,u.offset-s+i+u.size);switch(l===Ft&&(l=r>=R-s&&r<=y+s?Ze:ze),l){case ut:return y;case dt:return R;case ze:return Math.round(R+(y-R)/2);default:return r>=R&&r<=y?r:R>y||r<R?R:y}},Go=$t({name:"ElDynamicSizeGrid",getColumnPosition:(e,t,l)=>{const r=ge(e,t,l,"column");return[r.size,r.offset]},getRowPosition:(e,t,l)=>{const r=ge(e,t,l,"row");return[r.size,r.offset]},getColumnOffset:(e,t,l,r,a,n)=>Zt(e,t,l,r,a,"column",n),getRowOffset:(e,t,l,r,a,n)=>Zt(e,t,l,r,a,"row",n),getColumnStartIndexForOffset:(e,t,l)=>Xt(e,l,t,"column"),getColumnStopIndexForStartIndex:(e,t,l,r)=>{const a=ge(e,t,r,"column"),n=l+e.width;let i=a.offset+a.size,s=t;for(;s<e.totalColumn-1&&i<n;)s++,i+=ge(e,t,r,"column").size;return s},getEstimatedTotalHeight:Yt,getEstimatedTotalWidth:Qt,getRowStartIndexForOffset:(e,t,l)=>Xt(e,l,t,"row"),getRowStopIndexForStartIndex:(e,t,l,r)=>{const{totalRow:a,height:n}=e,i=ge(e,t,r,"row"),s=l+n;let f=i.size+i.offset,u=t;for(;u<a-1&&f<s;)u++,f+=ge(e,u,r,"row").size;return u},injectToInstance:(e,t)=>{const l=({columnIndex:r,rowIndex:a},n)=>{var i,s;n=!!yo(n)||n,ne(r)&&(t.value.lastVisitedColumnIndex=Math.min(t.value.lastVisitedColumnIndex,r-1)),ne(a)&&(t.value.lastVisitedRowIndex=Math.min(t.value.lastVisitedRowIndex,a-1)),(i=e.exposed)==null||i.getItemStyleCache.value(-1,null,null),n&&((s=e.proxy)==null||s.$forceUpdate())};Object.assign(e.proxy,{resetAfterColumnIndex:(r,a)=>{l({columnIndex:r},a)},resetAfterRowIndex:(r,a)=>{l({rowIndex:r},a)},resetAfter:l})},initCache:({estimatedColumnWidth:e=zt,estimatedRowHeight:t=zt})=>({column:{},estimatedColumnWidth:e,estimatedRowHeight:t,lastVisitedColumnIndex:-1,lastVisitedRowIndex:-1,row:{}}),clearCache:!1,validateProps:({columnWidth:e,rowHeight:t})=>{}}),No=$t({name:"ElFixedSizeGrid",getColumnPosition:({columnWidth:e},t)=>[e,t*e],getRowPosition:({rowHeight:e},t)=>[e,t*e],getEstimatedTotalHeight:({totalRow:e,rowHeight:t})=>t*e,getEstimatedTotalWidth:({totalColumn:e,columnWidth:t})=>t*e,getColumnOffset:({totalColumn:e,columnWidth:t,width:l},r,a,n,i,s)=>{l=Number(l);const f=Math.max(0,e*t-l),u=Math.min(f,r*t),d=Math.max(0,r*t-l+s+t);switch(a==="smart"&&(a=n>=d-l&&n<=u+l?Ze:ze),a){case ut:return u;case dt:return d;case ze:{const y=Math.round(d+(u-d)/2);return y<Math.ceil(l/2)?0:y>f+Math.floor(l/2)?f:y}default:return n>=d&&n<=u?n:d>u||n<d?d:u}},getRowOffset:({rowHeight:e,height:t,totalRow:l},r,a,n,i,s)=>{t=Number(t);const f=Math.max(0,l*e-t),u=Math.min(f,r*e),d=Math.max(0,r*e-t+s+e);switch(a===Ft&&(a=n>=d-t&&n<=u+t?Ze:ze),a){case ut:return u;case dt:return d;case ze:{const y=Math.round(d+(u-d)/2);return y<Math.ceil(t/2)?0:y>f+Math.floor(t/2)?f:y}default:return n>=d&&n<=u?n:d>u||n<d?d:u}},getColumnStartIndexForOffset:({columnWidth:e,totalColumn:t},l)=>Math.max(0,Math.min(t-1,Math.floor(l/e))),getColumnStopIndexForStartIndex:({columnWidth:e,totalColumn:t,width:l},r,a)=>{const n=r*e,i=Math.ceil((l+a-n)/e);return Math.max(0,Math.min(t-1,r+i-1))},getRowStartIndexForOffset:({rowHeight:e,totalRow:t},l)=>Math.max(0,Math.min(t-1,Math.floor(l/e))),getRowStopIndexForStartIndex:({rowHeight:e,totalRow:t,height:l},r,a)=>{const n=r*e,i=Math.ceil((l+a-n)/e);return Math.max(0,Math.min(t-1,r+i-1))},initCache:()=>{},clearCache:!0,validateProps:({columnWidth:e,rowHeight:t})=>{}}),Po=Ie({name:"ElTableV2Grid",props:et,setup(e,{slots:t,expose:l}){const{ns:r}=it(ft),{bodyRef:a,fixedRowHeight:n,gridHeight:i,hasHeader:s,headerRef:f,headerHeight:u,totalHeight:d,forceUpdate:y,itemKey:R,onItemRendered:L,resetAfterRowIndex:O,scrollTo:c,scrollToTop:N,scrollToRow:V,scrollLeft:$}=(k=>{const S=Y(),T=Y(),P=Y(0),q=M(()=>{const{data:C,rowHeight:j,estimatedRowHeight:_}=k;if(!_)return C.length*j}),J=M(()=>{const{fixedData:C,rowHeight:j}=k;return((C==null?void 0:C.length)||0)*j}),D=M(()=>_e(k.headerHeight)),X=M(()=>{const{height:C}=k;return Math.max(0,C-o(D)-o(J))}),Q=M(()=>o(D)+o(J)>0);return{bodyRef:T,forceUpdate:function(){var C,j;(C=o(T))==null||C.$forceUpdate(),(j=o(S))==null||j.$forceUpdate()},fixedRowHeight:J,gridHeight:X,hasHeader:Q,headerHeight:D,headerRef:S,totalHeight:q,itemKey:({data:C,rowIndex:j})=>C[j][k.rowKey],onItemRendered:function({rowCacheStart:C,rowCacheEnd:j,rowVisibleStart:_,rowVisibleEnd:Z}){var le;(le=k.onRowsRendered)==null||le.call(k,{rowCacheStart:C,rowCacheEnd:j,rowVisibleStart:_,rowVisibleEnd:Z})},resetAfterRowIndex:function(C,j){var _;(_=T.value)==null||_.resetAfterRowIndex(C,j)},scrollTo:function(C,j){const _=o(S),Z=o(T);at(C)?(_==null||_.scrollToLeft(C.scrollLeft),P.value=C.scrollLeft,Z==null||Z.scrollTo(C)):(_==null||_.scrollToLeft(C),P.value=C,Z==null||Z.scrollTo({scrollLeft:C,scrollTop:j}))},scrollToTop:function(C){var j;(j=o(T))==null||j.scrollTo({scrollTop:C})},scrollToRow:function(C,j){var _;(_=o(T))==null||_.scrollToItem(C,1,j)},scrollLeft:P}})(e);Ct("tableV2GridScrollLeft",$),l({forceUpdate:y,totalHeight:d,scrollTo:c,scrollToTop:N,scrollToRow:V,resetAfterRowIndex:O});const U=()=>e.bodyWidth;return()=>{const{cache:k,columns:S,data:T,fixedData:P,useIsScrolling:q,scrollbarAlwaysOn:J,scrollbarEndGap:D,scrollbarStartGap:X,style:Q,rowHeight:C,bodyWidth:j,estimatedRowHeight:_,headerWidth:Z,height:le,width:me,getRowHeight:ve,onScroll:fe}=e,de=ne(_),be=de?Go:No,ye=o(u);return K("div",{role:"table",class:[r.e("table"),e.class],style:Q},[K(be,{ref:a,data:T,useIsScrolling:q,itemKey:R,columnCache:0,columnWidth:de?U:j,totalColumn:1,totalRow:T.length,rowCache:k,rowHeight:de?ve:C,width:me,height:o(i),class:r.e("body"),role:"rowgroup",scrollbarStartGap:X,scrollbarEndGap:D,scrollbarAlwaysOn:J,onScroll:fe,onItemRendered:L,perfMode:!1},{default:ue=>{var se;const he=T[ue.rowIndex];return(se=t.row)==null?void 0:se.call(t,{...ue,columns:S,rowData:he})}}),o(s)&&K(jo,{ref:f,class:r.e("header-wrapper"),columns:S,headerData:T,headerHeight:e.headerHeight,fixedHeaderData:P,rowWidth:Z,rowHeight:C,width:me,height:Math.min(ye+o(n),le)},{dynamic:t.header,fixed:t.row})])}}});var xt=Po,$o=(e,{slots:t})=>{const{mainTableRef:l,...r}=e;return K(xt,oe({ref:l},r),typeof(a=t)=="function"||Object.prototype.toString.call(a)==="[object Object]"&&!Ce(a)?t:{default:()=>[t]});var a},Bo=(e,{slots:t})=>{if(!e.columns.length)return;const{leftTableRef:l,...r}=e;return K(xt,oe({ref:l},r),typeof(a=t)=="function"||Object.prototype.toString.call(a)==="[object Object]"&&!Ce(a)?t:{default:()=>[t]});var a},qo=(e,{slots:t})=>{if(!e.columns.length)return;const{rightTableRef:l,...r}=e;return K(xt,oe({ref:l},r),typeof(a=t)=="function"||Object.prototype.toString.call(a)==="[object Object]"&&!Ce(a)?t:{default:()=>[t]});var a};const Uo=e=>{const{isScrolling:t}=it(ft),l=Y(!1),r=Y(),a=M(()=>ne(e.estimatedRowHeight)&&e.rowIndex>=0),n=M(()=>{const{rowData:i,rowIndex:s,rowKey:f,onRowHover:u}=e,d=e.rowEventHandlers||{},y={};return Object.entries(d).forEach(([R,L])=>{st(L)&&(y[R]=O=>{L({event:O,rowData:i,rowIndex:s,rowKey:f})})}),u&&[{name:"onMouseleave",hovered:!1},{name:"onMouseenter",hovered:!0}].forEach(({name:R,hovered:L})=>{const O=y[R];y[R]=c=>{u({event:c,hovered:L,rowData:i,rowIndex:s,rowKey:f}),O==null||O(c)}}),y});return ct(()=>{o(a)&&((i=!1)=>{const s=o(r);if(!s)return;const{columns:f,onRowHeightChange:u,rowKey:d,rowIndex:y,style:R}=e,{height:L}=s.getBoundingClientRect();l.value=!0,ke(()=>{if(i||L!==Number.parseInt(R.height)){const O=f[0],c=(O==null?void 0:O.placeholderSign)===$e;u==null||u({rowKey:d,height:L,rowIndex:y},O&&!c&&O.fixed)}})})(!0)}),{isScrolling:t,measurable:a,measured:l,rowRef:r,eventHandlers:n,onExpand:i=>{const{onRowExpand:s,rowData:f,rowIndex:u,rowKey:d}=e;s==null||s({expanded:i,rowData:f,rowIndex:u,rowKey:d})}}},Xo=Ie({name:"ElTableV2TableRow",props:We,setup(e,{expose:t,slots:l,attrs:r}){const{eventHandlers:a,isScrolling:n,measurable:i,measured:s,rowRef:f,onExpand:u}=Uo(e);return t({onExpand:u}),()=>{const{columns:d,columnsStyles:y,expandColumnKey:R,depth:L,rowData:O,rowIndex:c,style:N}=e;let V=d.map(($,U)=>{const k=Le(O.children)&&O.children.length>0&&$.key===R;return l.cell({column:$,columns:d,columnIndex:U,depth:L,style:y[$.key],rowData:O,rowIndex:c,isScrolling:o(n),expandIconProps:k?{rowData:O,rowIndex:c,onExpand:u}:void 0})});if(l.row&&(V=l.row({cells:V.map($=>Le($)&&$.length===1?$[0]:$),style:N,columns:d,depth:L,rowData:O,rowIndex:c,isScrolling:o(n)})),o(i)){const{height:$,...U}=N||{},k=o(s);return K("div",oe({ref:f,class:e.class,style:k?N:U,role:"row"},r,o(a)),[V])}return K("div",oe(r,{ref:f,class:e.class,style:N,role:"row"},o(a)),[V])}}});var Yo=Xo,Qo=(e,{slots:t})=>{const{columns:l,columnsStyles:r,depthMap:a,expandColumnKey:n,expandedRowKeys:i,estimatedRowHeight:s,hasFixedColumns:f,rowData:u,rowIndex:d,style:y,isScrolling:R,rowProps:L,rowClass:O,rowKey:c,rowEventHandlers:N,ns:V,onRowHovered:$,onRowExpanded:U}=e,k=Me(O,{columns:l,rowData:u,rowIndex:d},""),S=Me(L,{columns:l,rowData:u,rowIndex:d}),T=u[c],P=a[T]||0,q=!!n,J=d<0,D=[V.e("row"),k,{[V.e(`row-depth-${P}`)]:q&&d>=0,[V.is("expanded")]:q&&i.includes(T),[V.is("fixed")]:!P&&J,[V.is("customized")]:!!t.row}],X=f?$:void 0,Q={...S,columns:l,columnsStyles:r,class:D,depth:P,expandColumnKey:n,estimatedRowHeight:J?void 0:s,isScrolling:R,rowIndex:d,rowData:u,rowKey:T,rowEventHandlers:N,style:y};return K(Yo,oe(Q,{onRowExpand:U,onMouseenter:j=>{X==null||X({hovered:!0,rowKey:T,event:j,rowData:u,rowIndex:d})},onMouseleave:j=>{X==null||X({hovered:!1,rowKey:T,event:j,rowData:u,rowIndex:d})},rowkey:T}),typeof(C=t)=="function"||Object.prototype.toString.call(C)==="[object Object]"&&!Ce(C)?t:{default:()=>[t]});var C};const vt=(e,{slots:t})=>{var l;const{cellData:r,style:a}=e,n=((l=r==null?void 0:r.toString)==null?void 0:l.call(r))||"",i=Ge(t,"default",e,()=>[n]);return K("div",{class:e.class,title:n,style:a},[i])};vt.displayName="ElTableV2Cell",vt.inheritAttrs=!1;var Zo=vt,Jo=e=>{const{expanded:t,expandable:l,onExpand:r,style:a,size:n}=e,i={onClick:l?()=>r(!t):void 0,class:e.class};return K(It,oe(i,{size:n,style:a}),{default:()=>[K(xo,null,null)]})};const Jt=({columns:e,column:t,columnIndex:l,depth:r,expandIconProps:a,isScrolling:n,rowData:i,rowIndex:s,style:f,expandedRowKeys:u,ns:d,cellProps:y,expandColumnKey:R,indentSize:L,iconSize:O,rowKey:c},{slots:N})=>{const V=xe(f);if(t.placeholderSign===$e)return K("div",{class:d.em("row-cell","placeholder"),style:V},null);const{cellRenderer:$,dataKey:U,dataGetter:k}=t,S=st(k)?k({columns:e,column:t,columnIndex:l,rowData:i,rowIndex:s}):vo(i,U??""),T=Me(y,{cellData:S,columns:e,column:t,columnIndex:l,rowIndex:s,rowData:i}),P={class:d.e("cell-text"),columns:e,column:t,columnIndex:l,cellData:S,isScrolling:n,rowData:i,rowIndex:s},q=jt($),J=q?q(P):Ge(N,"default",P,()=>[K(Zo,P,null)]),D=[d.e("row-cell"),t.class,t.align===Pe.CENTER&&d.is("align-center"),t.align===Pe.RIGHT&&d.is("align-right")],X=s>=0&&R&&t.key===R,Q=s>=0&&u.includes(i[c]);let C;const j=`margin-inline-start: ${r*L}px;`;return X&&(C=at(a)?K(Jo,oe(a,{class:[d.e("expand-icon"),d.is("expanded",Q)],size:O,expanded:Q,style:j,expandable:!0}),null):K("div",{style:[j,`width: ${O}px; height: ${O}px;`].join(" ")},null)),K("div",oe({class:D,style:V},T,{role:"cell"}),[C,J])};Jt.inheritAttrs=!1;var _t=Jt;const _o=Ae({class:String,columns:Be,columnsStyles:{type:G(Object),required:!0},headerIndex:Number,style:{type:G(Object)}}),el=Ie({name:"ElTableV2HeaderRow",props:_o,setup:(e,{slots:t})=>()=>{const{columns:l,columnsStyles:r,headerIndex:a,style:n}=e;let i=l.map((s,f)=>t.cell({columns:l,column:s,columnIndex:f,headerIndex:a,style:r[s.key]}));return t.header&&(i=t.header({cells:i.map(s=>Le(s)&&s.length===1?s[0]:s),columns:l,headerIndex:a})),K("div",{class:e.class,style:n,role:"row"},[i])}});var tl=el,ol=({columns:e,columnsStyles:t,headerIndex:l,style:r,headerClass:a,headerProps:n,ns:i},{slots:s})=>{const f={columns:e,headerIndex:l},u=[i.e("header-row"),Me(a,f,""),{[i.is("customized")]:!!s.header}],d={...Me(n,f),columnsStyles:t,class:u,columns:e,headerIndex:l,style:r};return K(tl,d,typeof(y=s)=="function"||Object.prototype.toString.call(y)==="[object Object]"&&!Ce(y)?s:{default:()=>[s]});var y};const bt=(e,{slots:t})=>Ge(t,"default",e,()=>{var l,r;return[K("div",{class:e.class,title:(l=e.column)==null?void 0:l.title},[(r=e.column)==null?void 0:r.title])]});bt.displayName="ElTableV2HeaderCell",bt.inheritAttrs=!1;var ll=bt,rl=e=>{const{sortOrder:t}=e;return K(It,{size:14,class:e.class},{default:()=>[t===Ne.ASC?K(bo,null,null):K(Ro,null,null)]})},eo=(e,{slots:t})=>{const{column:l,ns:r,style:a,onColumnSorted:n}=e,i=xe(a);if(l.placeholderSign===$e)return K("div",{class:r.em("header-row-cell","placeholder"),style:i},null);const{headerCellRenderer:s,headerClass:f,sortable:u}=l,d={...e,class:r.e("header-cell-text")},y=jt(s),R=y?y(d):Ge(t,"default",d,()=>[K(ll,d,null)]),{sortBy:L,sortState:O,headerCellProps:c}=e;let N,V;if(O){const k=O[l.key];N=!!mt[k],V=N?k:Ne.ASC}else N=l.key===L.key,V=N?L.order:Ne.ASC;const $=[r.e("header-cell"),Me(f,e,""),l.align===Pe.CENTER&&r.is("align-center"),l.align===Pe.RIGHT&&r.is("align-right"),u&&r.is("sortable")],U={...Me(c,e),onClick:l.sortable?n:void 0,class:$,style:i,"data-key":l.key};return K("div",oe(U,{role:"columnheader"}),[R,u&&K(rl,{class:[r.e("sort-icon"),N&&r.is("sorting")],sortOrder:V},null)])};const to=(e,{slots:t})=>{var l;return K("div",{class:e.class,style:e.style},[(l=t.default)==null?void 0:l.call(t)])};to.displayName="ElTableV2Footer";var al=to;const oo=(e,{slots:t})=>{const l=Ge(t,"default",{},()=>[K(Wo,null,null)]);return K("div",{class:e.class,style:e.style},[l])};oo.displayName="ElTableV2Empty";var nl=oo;const lo=(e,{slots:t})=>{var l;return K("div",{class:e.class,style:e.style},[(l=t.default)==null?void 0:l.call(t)])};lo.displayName="ElTableV2Overlay";var sl=lo;function qe(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!Ce(e)}const il=Ie({name:"ElTableV2",props:zo,setup(e,{slots:t,expose:l}){const r=Ve("table-v2"),{columnsStyles:a,fixedColumnsOnLeft:n,fixedColumnsOnRight:i,mainColumns:s,mainTableHeight:f,fixedTableHeight:u,leftTableWidth:d,rightTableWidth:y,data:R,depthMap:L,expandedRowKeys:O,hasFixedColumns:c,mainTableRef:N,leftTableRef:V,rightTableRef:$,isDynamic:U,isResetting:k,isScrolling:S,bodyWidth:T,emptyStyle:P,rootStyle:q,headerWidth:J,footerHeight:D,showEmpty:X,scrollTo:Q,scrollToLeft:C,scrollToTop:j,scrollToRow:_,getRowHeight:Z,onColumnSorted:le,onRowHeightChange:me,onRowHovered:ve,onRowExpanded:fe,onRowsRendered:de,onScroll:be,onVerticalScroll:ye}=Lo(e);return l({scrollTo:Q,scrollToLeft:C,scrollToTop:j,scrollToRow:_}),Ct(ft,{ns:r,isResetting:k,isScrolling:S}),()=>{const{cache:ue,cellProps:se,estimatedRowHeight:he,expandColumnKey:Re,fixedData:Ke,headerHeight:Se,headerClass:Fe,headerProps:je,headerCellProps:z,sortBy:h,sortState:w,rowHeight:m,rowClass:H,rowEventHandlers:g,rowKey:b,rowProps:x,scrollbarAlwaysOn:I,indentSize:v,iconSize:E,useIsScrolling:F,vScrollbarSize:A,width:p}=e,W=o(R),B={cache:ue,class:r.e("main"),columns:o(s),data:W,fixedData:Ke,estimatedRowHeight:he,bodyWidth:o(T)+A,headerHeight:Se,headerWidth:o(J),height:o(f),mainTableRef:N,rowKey:b,rowHeight:m,scrollbarAlwaysOn:I,scrollbarStartGap:2,scrollbarEndGap:A,useIsScrolling:F,width:p,getRowHeight:Z,onRowsRendered:de,onScroll:be},re=o(d),we=o(u),ee={cache:ue,class:r.e("left"),columns:o(n),data:W,estimatedRowHeight:he,leftTableRef:V,rowHeight:m,bodyWidth:re,headerWidth:re,headerHeight:Se,height:we,rowKey:b,scrollbarAlwaysOn:I,scrollbarStartGap:2,scrollbarEndGap:A,useIsScrolling:F,width:re,getRowHeight:Z,onScroll:ye},te=o(y)+A,ae={cache:ue,class:r.e("right"),columns:o(i),data:W,estimatedRowHeight:he,rightTableRef:$,rowHeight:m,bodyWidth:te,headerWidth:te,headerHeight:Se,height:we,rowKey:b,scrollbarAlwaysOn:I,scrollbarStartGap:2,scrollbarEndGap:A,width:te,style:`--${o(r.namespace)}-table-scrollbar-size: ${A}px`,useIsScrolling:F,getRowHeight:Z,onScroll:ye},ie=o(a),ot={ns:r,depthMap:o(L),columnsStyles:ie,expandColumnKey:Re,expandedRowKeys:o(O),estimatedRowHeight:he,hasFixedColumns:o(c),rowProps:x,rowClass:H,rowKey:b,rowEventHandlers:g,onRowHovered:ve,onRowExpanded:fe,onRowHeightChange:me},Ue={cellProps:se,expandColumnKey:Re,indentSize:v,iconSize:E,rowKey:b,expandedRowKeys:o(O),ns:r},lt={ns:r,headerClass:Fe,headerProps:je,columnsStyles:ie},Rt={ns:r,sortBy:h,sortState:w,headerCellProps:z,onColumnSorted:le},pe={row:rt=>K(Qo,oe(rt,ot),{row:t.row,cell:ce=>{let Te;return t.cell?K(_t,oe(ce,Ue,{style:ie[ce.column.key]}),qe(Te=t.cell(ce))?Te:{default:()=>[Te]}):K(_t,oe(ce,Ue,{style:ie[ce.column.key]}),null)}}),header:rt=>K(ol,oe(rt,lt),{header:t.header,cell:ce=>{let Te;return t["header-cell"]?K(eo,oe(ce,Rt,{style:ie[ce.column.key]}),qe(Te=t["header-cell"](ce))?Te:{default:()=>[Te]}):K(eo,oe(ce,Rt,{style:ie[ce.column.key]}),null)}})},ro=[e.class,r.b(),r.e("root"),{[r.is("dynamic")]:o(U)}],ao={class:r.e("footer"),style:o(D)};return K("div",{class:ro,style:o(q)},[K($o,B,qe(pe)?pe:{default:()=>[pe]}),K(Bo,ee,qe(pe)?pe:{default:()=>[pe]}),K(qo,ae,qe(pe)?pe:{default:()=>[pe]}),t.footer&&K(al,ao,{default:t.footer}),o(X)&&K(nl,{class:r.e("empty"),style:o(P)},{default:t.empty}),t.overlay&&K(sl,{class:r.e("overlay")},{default:t.overlay})])}}});var cl=il;const dl=Ae({disableWidth:Boolean,disableHeight:Boolean,onResize:{type:G(Function)}});var ul=Ie({name:"ElAutoResizer",props:dl,setup(e,{slots:t}){const l=Ve("auto-resizer"),{height:r,width:a,sizer:n}=(s=>{const f=Y(),u=Y(0),d=Y(0);let y;return ct(()=>{y=So(f,([R])=>{const{width:L,height:O}=R.contentRect,{paddingLeft:c,paddingRight:N,paddingTop:V,paddingBottom:$}=getComputedStyle(R.target),U=Number.parseInt(c)||0,k=Number.parseInt(N)||0,S=Number.parseInt(V)||0,T=Number.parseInt($)||0;u.value=L-U-k,d.value=O-S-T}).stop}),To(()=>{y==null||y()}),Xe([u,d],([R,L])=>{var O;(O=s.onResize)==null||O.call(s,{width:R,height:L})}),{sizer:f,width:u,height:d}})(e),i={width:"100%",height:"100%"};return()=>{var s;return K("div",{ref:n,class:l.b(),style:i},[(s=t.default)==null?void 0:s.call(t,{height:r.value,width:a.value})])}}});const hl=Et(cl),ml=Et(ul);export{ml as E,ht as F,hl as a};
