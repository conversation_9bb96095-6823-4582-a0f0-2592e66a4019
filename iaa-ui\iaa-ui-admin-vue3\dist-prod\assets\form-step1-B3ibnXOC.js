import{_ as v,r as _,c as f,o as b,i as s,g as e,w as l,bh as m,H as i,G as X,s as M,a5 as P}from"./index-CvERnF9Y.js";const y={class:"upload-container"},x={class:"upload-section"},L={class:"next-button"},T={class:"create-knowledge"},g=v({__name:"form-step1",setup(S){const t=_([]),o=(d,a)=>{},u=d=>(t.value.push(d),!1);return(d,a)=>{const c=m,n=X,r=M,p=P;return b(),f("div",y,[a[4]||(a[4]=s("div",{class:"title"},[s("div",null,"\u9009\u62E9\u6570\u636E\u6E90")],-1)),a[5]||(a[5]=s("div",{class:"resource-btn"},"\u5BFC\u5165\u5DF2\u6709\u6587\u672C",-1)),e(r,null,{default:l(()=>[s("div",x,[a[1]||(a[1]=s("div",{class:"upload-label"},"\u4E0A\u4F20\u6587\u672C\u6587\u4EF6",-1)),e(c,{class:"upload-area",action:"#","file-list":t.value,"on-remove":o,"before-upload":u,"list-type":"text",drag:""},{default:l(()=>a[0]||(a[0]=[s("i",{class:"el-icon-upload"},null,-1),s("div",{class:"el-upload__text"},[i("\u62D6\u62FD\u6587\u4EF6\u81F3\u6B64\uFF0C\u6216\u8005 "),s("em",null,"\u9009\u62E9\u6587\u4EF6")],-1),s("div",{class:"el-upload__tip"}," \u5DF2\u652F\u6301 TXT\u3001MARKDOWN\u3001PDF\u3001HTML\u3001XLSX\u3001XLS\u3001DOCX\u3001CSV\u3001EML\u3001MSG\u3001PPTX\u3001PPT\u3001XML\u3001EPUB\uFF0C\u6BCF\u4E2A\u6587\u4EF6\u4E0D\u8D85\u8FC7 15MB\u3002 ",-1)])),_:1},8,["file-list"])]),s("div",L,[e(n,{type:"primary",disabled:!t.value.length},{default:l(()=>a[2]||(a[2]=[i("\u4E0B\u4E00\u6B65")])),_:1},8,["disabled"])])]),_:1}),s("div",T,[e(p,{type:"primary",underline:""},{default:l(()=>a[3]||(a[3]=[i("\u521B\u5EFA\u4E00\u4E2A\u7A7A\u77E5\u8BC6\u5E93")])),_:1})])])}}},[["__scopeId","data-v-40d8b8c4"]]);export{g as default};
