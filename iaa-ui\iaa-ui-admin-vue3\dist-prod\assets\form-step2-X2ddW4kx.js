import{_ as B,r as u,A as H,o as v,w as d,g as a,h as y,k as E,s as S,v as T,aB as A,aC as K,H as p,x as j,B as L,i as t,ca as z,c as x,F,y as I,t as h,E as R}from"./index-CRsFgzy0.js";const Z={class:"title"},q={class:"left"},D={class:"id"},G={class:"content"},J=B({__name:"form-step2",setup(N){const b=u("\u81EA\u52A8\u5206\u6BB5\u4E0E\u6E05\u6D17"),c=u("\u9AD8\u8D28\u91CF"),V=u("text-embedding-3-large");u(!0);const s=u(3),o=u(.5),g=u([{number:"001",text:"\u540C\u6B65obs\u6A21\u578B...'UAE-large-V1'"},{number:"002",text:"\u540C\u6B65obs\u6A21\u578B...'plip'"},{number:"003",text:"\u540C\u6B65obs\u6A21\u578B...'phoBERT-base-v2'"},{number:"004",text:"\u540C\u6B65obs\u6A21\u578B...'lama3-bb-bnb-4bit'"},{number:"005",text:"\u540C\u6B65obs\u6A21\u578B...'t5-base-split-and-rephrase'"}]);return(O,e)=>{const i=K,f=A,r=T,w=L,C=j,n=z,m=E,U=S,_=y,M=R;return v(),H(M,null,{default:d(()=>[a(_,{span:12},{default:d(()=>[a(m,null,{default:d(()=>[a(U,null,{default:d(()=>[a(r,{label:"\u5206\u6BB5\u8BBE\u7F6E"},{default:d(()=>[a(f,{modelValue:b.value,"onUpdate:modelValue":e[0]||(e[0]=l=>b.value=l)},{default:d(()=>[a(i,{label:"\u81EA\u52A8\u5206\u6BB5\u4E0E\u6E05\u6D17"},{default:d(()=>e[9]||(e[9]=[p("\u81EA\u52A8\u5206\u6BB5\u4E0E\u6E05\u6D17")])),_:1}),a(i,{label:"\u81EA\u5B9A\u4E49"},{default:d(()=>e[10]||(e[10]=[p("\u81EA\u5B9A\u4E49")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"\u7D22\u5F15\u65B9\u5F0F"},{default:d(()=>[a(f,{modelValue:c.value,"onUpdate:modelValue":e[1]||(e[1]=l=>c.value=l)},{default:d(()=>[a(i,{label:"\u9AD8\u8D28\u91CF"},{default:d(()=>e[11]||(e[11]=[p("\u9AD8\u8D28\u91CF")])),_:1}),a(i,{label:"\u7ECF\u6D4E"},{default:d(()=>e[12]||(e[12]=[p("\u7ECF\u6D4E")])),_:1})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"Embedding \u6A21\u578B"},{default:d(()=>[a(C,{modelValue:V.value,"onUpdate:modelValue":e[2]||(e[2]=l=>V.value=l),placeholder:"Select Embedding Model"},{default:d(()=>[a(w,{label:"text-embedding-3-large",value:"text-embedding-3-large"})]),_:1},8,["modelValue"])]),_:1}),a(r,{label:"\u68C0\u7D22\u8BBE\u7F6E"},{default:d(()=>[a(m,{style:{width:"400px"}},{default:d(()=>[e[13]||(e[13]=t("div",{class:"card-header"},[t("span",null,"\u5411\u91CF\u68C0\u7D22")],-1)),a(n,{modelValue:s.value,"onUpdate:modelValue":e[3]||(e[3]=l=>s.value=l),min:1,max:10,label:"Top K"},null,8,["modelValue"]),a(n,{modelValue:o.value,"onUpdate:modelValue":e[4]||(e[4]=l=>o.value=l),min:0,max:1,step:"0.1",label:"Score \u9608\u503C"},null,8,["modelValue"])]),_:1}),a(m,{style:{width:"400px"}},{default:d(()=>[e[14]||(e[14]=t("div",{class:"card-header"},[t("span",null,"\u5168\u6587\u68C0\u7D22")],-1)),a(n,{modelValue:s.value,"onUpdate:modelValue":e[5]||(e[5]=l=>s.value=l),min:1,max:10,label:"Top K"},null,8,["modelValue"]),a(n,{modelValue:o.value,"onUpdate:modelValue":e[6]||(e[6]=l=>o.value=l),min:0,max:1,step:"0.1",label:"Score \u9608\u503C"},null,8,["modelValue"])]),_:1}),a(m,{style:{width:"400px"}},{default:d(()=>[e[15]||(e[15]=t("div",{class:"card-header"},[t("span",null,"\u6DF7\u5408\u68C0\u7D22")],-1)),a(n,{modelValue:s.value,"onUpdate:modelValue":e[7]||(e[7]=l=>s.value=l),min:1,max:10,label:"Top K"},null,8,["modelValue"]),a(n,{modelValue:o.value,"onUpdate:modelValue":e[8]||(e[8]=l=>o.value=l),min:0,max:1,step:"0.1",label:"Score \u9608\u503C"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),a(_,{span:9},{default:d(()=>[a(m,{shadow:"never"},{default:d(()=>[e[18]||(e[18]=t("div",{class:"previews-title"},"\u5206\u6BB5\u9884\u89C8",-1)),(v(!0),x(F,null,I(g.value,(l,k)=>(v(),x("div",{key:k,class:"segment-preview"},[t("div",Z,[t("div",q,[e[16]||(e[16]=t("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[t("path",{d:"M4.74999 1.5L3.24999 10.5M8.74998 1.5L7.24998 10.5M10.25 4H1.75M9.75 8H1.25",stroke:"#98A2B3","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),t("span",D,h(l.number),1)]),e[17]||(e[17]=t("div",{class:"right"},[t("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[t("path",{d:"M4 3.5H8M6 3.5V8.5M3.9 10.5H8.1C8.94008 10.5 9.36012 10.5 9.68099 10.3365C9.96323 10.1927 10.1927 9.96323 10.3365 9.68099C10.5 9.36012 10.5 8.94008 10.5 8.1V3.9C10.5 3.05992 10.5 2.63988 10.3365 2.31901C10.1927 2.03677 9.96323 1.8073 9.68099 1.66349C9.36012 1.5 8.94008 1.5 8.1 1.5H3.9C3.05992 1.5 2.63988 1.5 2.31901 1.66349C2.03677 1.8073 1.8073 2.03677 1.66349 2.31901C1.5 2.63988 1.5 3.05992 1.5 3.9V8.1C1.5 8.94008 1.5 9.36012 1.66349 9.68099C1.8073 9.96323 2.03677 10.1927 2.31901 10.3365C2.63988 10.5 3.05992 10.5 3.9 10.5Z",stroke:"#667085","stroke-linecap":"round","stroke-linejoin":"round"})]),t("span",{class:"char-size"},"7777 \u5B57\u7B26")],-1))]),t("div",G,h(l.text),1)]))),128))]),_:1})]),_:1})]),_:1})}}},[["__scopeId","data-v-71c9a97e"]]);export{J as default};
