import{i,j as e}from"./constants-uird_4gU.js";import{f as o}from"./formatTime-DhdtkSIS.js";import{aN as a}from"./index-CRsFgzy0.js";const n=t=>t.discountType===i.PRICE.type?`\uFFE5${a(t.discountPrice)}`:t.discountType===i.PERCENT.type?`${t.discountPercent}%`:"\u672A\u77E5\u3010"+t.discountType+"\u3011",s=t=>t.takeLimitCount?t.takeLimitCount===-1?"\u65E0\u9886\u53D6\u9650\u5236":`${t.takeLimitCount} \u5F20/\u4EBA`:" ",d=t=>t.validityType===e.DATE.type?`${o(t.validStartTime)} \u81F3 ${o(t.validEndTime)}`:t.validityType===e.TERM.type?`\u9886\u53D6\u540E\u7B2C ${t.fixedStartTerm} - ${t.fixedEndTerm} \u5929\u5185\u53EF\u7528`:"\u672A\u77E5\u3010"+t.validityType+"\u3011",r=t=>t.totalCount===-1?"\u4E0D\u9650\u5236":t.totalCount,u=t=>t.totalCount===-1?"\u4E0D\u9650\u5236":t.totalCount-t.takeCount,m=t=>`\uFFE5${a(t.usePrice)}`;export{r as a,n as d,u as r,s as t,m as u,d as v};
