import{as as e}from"./index-CRsFgzy0.js";const t={getFunnelSummary:s=>e.get({url:"/crm/statistics-funnel/get-funnel-summary",params:s}),getBusinessSummaryByEndStatus:s=>e.get({url:"/crm/statistics-funnel/get-business-summary-by-end-status",params:s}),getBusinessSummaryByDate:s=>e.get({url:"/crm/statistics-funnel/get-business-summary-by-date",params:s}),getBusinessInversionRateSummaryByDate:s=>e.get({url:"/crm/statistics-funnel/get-business-inversion-rate-summary-by-date",params:s}),getBusinessPageByDate:s=>e.get({url:"/crm/statistics-funnel/get-business-page-by-date",params:s})};export{t as S};
