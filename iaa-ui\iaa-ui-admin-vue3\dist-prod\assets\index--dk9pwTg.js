import{_ as L}from"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import{d as V,b as z,p as A,r as i,q as U,aD as G,O as H,c as O,o as C,g as t,w as n,i as f,a as o,G as $,H as g,a3 as K,t as W,aF as q,J as P,m as Q,F as X,eR as Y,eu as Z,cE as ee}from"./index-CRsFgzy0.js";import{_ as ae}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{u as te}from"./useFormCreateDesigner-BA-XNg_f.js";import{H as v}from"./index-p2LMQUWl.js";import{j as ne}from"./java-CqAR0c3a.js";import"./index-BCN8BzfC.js";import"./dict.type-DsokmC1-.js";function oe(r){const s=["true","false","null"],c={scope:"literal",beginKeywords:s.join(" ")};return{name:"JSON",aliases:["jsonc"],keywords:{literal:s},contains:[{className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},{match:/[{}[\],:]/,className:"punctuation",relevance:0},r.QUOTE_STRING_MODE,c,r.C_NUMBER_MODE,r.C_LINE_COMMENT_MODE,r.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}const se={class:"h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-2px)]"},le={key:0,ref:"editor"},ie={class:"hljs"},re=V({name:"InfraBuild",__name:"index",setup(r){const{t:s}=z(),c=A(),w=i({switchType:[],autoActive:!0,useTemplate:!1,formOptions:{form:{labelWidth:"100px"}},fieldReadonly:!1,hiddenDragMenu:!1,hiddenDragBtn:!1,hiddenMenu:[],hiddenItem:[],hiddenItemConfig:{},disabledItemConfig:{},showSaveBtn:!1,showConfig:!0,showBaseForm:!0,showControl:!0,showPropsForm:!0,showEventForm:!0,showValidateForm:!0,showFormConfig:!0,showInputData:!0,showDevice:!0,appendConfigData:[]}),l=i(),u=i(!1),y=i(""),m=i(-1),p=i("");te(l);const h=a=>{u.value=!0,y.value=a},N=()=>{h("\u751F\u6210 JSON"),m.value=0,p.value=l.value.getRule()},S=()=>{h("\u751F\u6210 Options"),m.value=1,p.value=l.value.getOption()},b=()=>{h("\u751F\u6210\u7EC4\u4EF6"),m.value=2,p.value=E()},E=()=>{const a=l.value.getRule(),e=l.value.getOption();return`<template>
    <form-create
      v-model:api="fApi"
      :rule="rule"
      :option="option"
      @submit="onSubmit"
    ></form-create>
  </template>
  <script setup lang=ts>
    const faps = ref(null)
    const rule = ref('')
    const option = ref('')
    const init = () => {
      rule.value = formCreate.parseJson('${Y.toJson(a).replaceAll("\\","\\\\")}')
      option.value = formCreate.parseJson('${JSON.stringify(e)}')
    }
    const onSubmit = (formData) => {
      //todo \u63D0\u4EA4\u8868\u5355
    }
    init()
  <\/script>`},D=a=>{let e="json";return m.value===2&&(e="xml"),ee(a)||(a=JSON.stringify(a,null,2)),v.highlight(a,{language:e,ignoreIllegals:!0}).value||"&nbsp;"};return U(async()=>{v.registerLanguage("xml",ne),v.registerLanguage("json",oe)}),(a,e)=>{const d=$,M=G("fc-designer"),J=ae,k=q,x=L,I=H("dompurify-html");return C(),O(X,null,[t(J,{"body-style":{padding:"0px"},class:"!mb-0"},{default:n(()=>[f("div",se,[t(M,{class:"my-designer",ref_key:"designer",ref:l,config:o(w)},{handle:n(()=>[t(d,{size:"small",type:"primary",plain:"",onClick:N},{default:n(()=>e[2]||(e[2]=[g("\u751F\u6210JSON")])),_:1}),t(d,{size:"small",type:"success",plain:"",onClick:S},{default:n(()=>e[3]||(e[3]=[g("\u751F\u6210Options")])),_:1}),t(d,{size:"small",type:"danger",plain:"",onClick:b},{default:n(()=>e[4]||(e[4]=[g("\u751F\u6210\u7EC4\u4EF6")])),_:1})]),_:1},8,["config"])])]),_:1}),t(x,{modelValue:o(u),"onUpdate:modelValue":e[1]||(e[1]=_=>Q(u)?u.value=_:null),title:o(y),"max-height":"600"},{default:n(()=>[o(u)?(C(),O("div",le,[t(d,{style:{float:"right"},onClick:e[0]||(e[0]=_=>(async F=>{const j=JSON.stringify(F,null,2),{copy:B,copied:R,isSupported:T}=Z({source:j});T?(await B(),o(R)&&c.success(s("common.copySuccess"))):c.error(s("common.copyError"))})(o(p)))},{default:n(()=>[g(W(o(s)("common.copy")),1)]),_:1}),t(k,{height:"580"},{default:n(()=>[f("div",null,[f("pre",null,[P(f("code",ie,null,512),[[I,D(o(p))]])])])]),_:1})],512)):K("",!0)]),_:1},8,["modelValue","title"])],64)}}});export{re as default};
