import{d as C,r as d,aK as P,c as p,o as r,F as R,y as S,aw as s,a3 as c,i as y,g,X as u,t as a,a as m,aR as h,A as B}from"./index-CRsFgzy0.js";import{E as I}from"./el-image-BQpHFDaE.js";import{b as $}from"./spu-BHhhuUrI.js";import{P as A}from"./index-gOhrLNGh.js";const L={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},E={class:"text-12px"},U={class:"absolute bottom-8px right-8px"},_=C({name:"PromotionPoint",__name:"index",props:{property:{}},setup(b){const i=b,n=d([]),x=d([]),f=d([]);P(()=>i.property.activityIds,async()=>{try{const t=i.property.activityIds;Array.isArray(t)&&t.length>0&&(f.value=await A.getPointActivityListByIds(t),n.value=[],x.value=f.value.map(e=>e.spuId),x.value.length>0&&(n.value=await $(x.value)),f.value.forEach(e=>{const l=n.value.find(o=>o.id===e.spuId);l&&(l.pointStock=e.stock,l.pointTotalStock=e.totalStock,l.point=e.point,l.pointPrice=e.price)}))}catch{}},{immediate:!0,deep:!0});const k=t=>{const e=i.property.layoutType==="twoCol"?2:1;return{marginLeft:t%e==0?"0":i.property.space+"px",marginTop:t<e?"0":i.property.space+"px"}},v=d(),T=()=>{let t="100%";return i.property.layoutType==="twoCol"&&(t=(v.value.offsetWidth-i.property.space)/2+"px"),{width:t}};return(t,e)=>{const l=I;return r(),p("div",{ref_key:"containerRef",ref:v,class:u("box-content min-h-30px w-full flex flex-row flex-wrap")},[(r(!0),p(R,null,S(m(n),(o,w)=>(r(),p("div",{key:w,style:s({...k(w),...T(),borderTopLeftRadius:`${t.property.borderRadiusTop}px`,borderTopRightRadius:`${t.property.borderRadiusTop}px`,borderBottomLeftRadius:`${t.property.borderRadiusBottom}px`,borderBottomRightRadius:`${t.property.borderRadiusBottom}px`}),class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white"},[t.property.badge.show?(r(),p("div",L,[g(l,{src:t.property.badge.imgUrl,class:"h-26px w-38px",fit:"cover"},null,8,["src"])])):c("",!0),y("div",{class:u(["h-140px",{"w-full":t.property.layoutType!=="oneColSmallImg","w-140px":t.property.layoutType==="oneColSmallImg"}])},[g(l,{src:o.picUrl,class:"h-full w-full",fit:"cover"},null,8,["src"])],2),y("div",{class:u([" flex flex-col gap-8px p-8px box-border",{"w-full":t.property.layoutType!=="oneColSmallImg","w-[calc(100%-140px-16px)]":t.property.layoutType==="oneColSmallImg"}])},[t.property.fields.name.show?(r(),p("div",{key:0,class:u(["text-14px ",{truncate:t.property.layoutType!=="oneColSmallImg","overflow-ellipsis line-clamp-2":t.property.layoutType==="oneColSmallImg"}]),style:s({color:t.property.fields.name.color})},a(o.name),7)):c("",!0),t.property.fields.introduction.show?(r(),p("div",{key:1,style:s({color:t.property.fields.introduction.color}),class:"truncate text-12px"},a(o.introduction),5)):c("",!0),y("div",null,[t.property.fields.price.show?(r(),p("span",{key:0,style:s({color:t.property.fields.price.color}),class:"text-16px"},a(o.point)+"\u79EF\u5206 "+a(o.pointPrice&&o.pointPrice!==0?`+${m(h)(o.pointPrice)}\u5143`:""),5)):c("",!0),t.property.fields.marketPrice.show&&o.marketPrice?(r(),p("span",{key:1,style:s({color:t.property.fields.marketPrice.color}),class:"ml-4px text-10px line-through"}," \uFFE5"+a(m(h)(o.marketPrice)),5)):c("",!0)]),y("div",E,[t.property.fields.salesCount.show?(r(),p("span",{key:0,style:s({color:t.property.fields.salesCount.color})}," \u5DF2\u5151"+a((o.pointTotalStock||0)-(o.pointStock||0))+"\u4EF6 ",5)):c("",!0),t.property.fields.stock.show?(r(),p("span",{key:1,style:s({color:t.property.fields.stock.color})}," \u5E93\u5B58"+a(o.pointTotalStock||0),5)):c("",!0)])],2),y("div",U,[t.property.btnBuy.type==="text"?(r(),p("span",{key:0,style:s({background:`linear-gradient(to right, ${t.property.btnBuy.bgBeginColor}, ${t.property.btnBuy.bgEndColor}`}),class:"rounded-full p-x-12px p-y-4px text-12px text-white"},a(t.property.btnBuy.text),5)):(r(),B(l,{key:1,src:t.property.btnBuy.imgUrl,class:"h-28px w-28px rounded-full",fit:"cover"},null,8,["src"]))])],4))),128))],512)}}});export{_ as default};
