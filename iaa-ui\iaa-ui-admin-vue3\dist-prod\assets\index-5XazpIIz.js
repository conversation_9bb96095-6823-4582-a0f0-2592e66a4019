import{d as v,r as m,aK as C,c as t,o as r,F as T,y as R,aw as p,a3 as s,i as y,g as w,X as i,t as a,a as d,aR as b,A as B}from"./index-CRsFgzy0.js";import{E as I}from"./el-image-BQpHFDaE.js";import{b as S}from"./spu-BHhhuUrI.js";const $={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},P={class:"text-12px"},L={class:"absolute bottom-8px right-8px"},U=v({name:"ProductCard",__name:"index",props:{property:{}},setup(g){const l=g,u=m([]);C(()=>l.property.spuIds,async()=>{u.value=await S(l.property.spuIds)},{immediate:!0,deep:!0});const h=o=>{const c=l.property.layoutType==="twoCol"?2:1;return{marginLeft:o%c==0?"0":l.property.space+"px",marginTop:o<c?"0":l.property.space+"px"}},x=m(),k=()=>{let o="100%";return l.property.layoutType==="twoCol"&&(o=(x.value.offsetWidth-l.property.space)/2+"px"),{width:o}};return(o,c)=>{const n=I;return r(),t("div",{class:i("box-content min-h-30px w-full flex flex-row flex-wrap"),ref_key:"containerRef",ref:x},[(r(!0),t(T,null,R(d(u),(e,f)=>(r(),t("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:p({...h(f),...k(),borderTopLeftRadius:`${o.property.borderRadiusTop}px`,borderTopRightRadius:`${o.property.borderRadiusTop}px`,borderBottomLeftRadius:`${o.property.borderRadiusBottom}px`,borderBottomRightRadius:`${o.property.borderRadiusBottom}px`}),key:f},[o.property.badge.show?(r(),t("div",$,[w(n,{fit:"cover",src:o.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):s("",!0),y("div",{class:i(["h-140px",{"w-full":o.property.layoutType!=="oneColSmallImg","w-140px":o.property.layoutType==="oneColSmallImg"}])},[w(n,{fit:"cover",class:"h-full w-full",src:e.picUrl},null,8,["src"])],2),y("div",{class:i([" flex flex-col gap-8px p-8px box-border",{"w-full":o.property.layoutType!=="oneColSmallImg","w-[calc(100%-140px-16px)]":o.property.layoutType==="oneColSmallImg"}])},[o.property.fields.name.show?(r(),t("div",{key:0,class:i(["text-14px ",{truncate:o.property.layoutType!=="oneColSmallImg","overflow-ellipsis line-clamp-2":o.property.layoutType==="oneColSmallImg"}]),style:p({color:o.property.fields.name.color})},a(e.name),7)):s("",!0),o.property.fields.introduction.show?(r(),t("div",{key:1,class:"truncate text-12px",style:p({color:o.property.fields.introduction.color})},a(e.introduction),5)):s("",!0),y("div",null,[o.property.fields.price.show?(r(),t("span",{key:0,class:"text-16px",style:p({color:o.property.fields.price.color})}," \uFFE5"+a(d(b)(e.price)),5)):s("",!0),o.property.fields.marketPrice.show&&e.marketPrice?(r(),t("span",{key:1,class:"ml-4px text-10px line-through",style:p({color:o.property.fields.marketPrice.color})},"\uFFE5"+a(d(b)(e.marketPrice)),5)):s("",!0)]),y("div",P,[o.property.fields.salesCount.show?(r(),t("span",{key:0,style:p({color:o.property.fields.salesCount.color})}," \u5DF2\u552E"+a((e.salesCount||0)+(e.virtualSalesCount||0))+"\u4EF6 ",5)):s("",!0),o.property.fields.stock.show?(r(),t("span",{key:1,style:p({color:o.property.fields.stock.color})}," \u5E93\u5B58"+a(e.stock||0),5)):s("",!0)])],2),y("div",L,[o.property.btnBuy.type==="text"?(r(),t("span",{key:0,class:"rounded-full p-x-12px p-y-4px text-12px text-white",style:p({background:`linear-gradient(to right, ${o.property.btnBuy.bgBeginColor}, ${o.property.btnBuy.bgEndColor}`})},a(o.property.btnBuy.text),5)):(r(),B(n,{key:1,class:"h-28px w-28px rounded-full",fit:"cover",src:o.property.btnBuy.imgUrl},null,8,["src"]))])],4))),128))],512)}}});export{U as default};
