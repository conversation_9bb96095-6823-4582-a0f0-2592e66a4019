import{d as J,p as L,b as O,u as Q,r as p,f as j,q as B,O as E,c as W,o as d,g as e,w as l,s as X,a as o,v as Z,P as $,Q as R,C as ee,J as u,G as ae,H as n,I as te,A as f,K as le,L as oe,t as re,M as ie,F as ne}from"./index-CRsFgzy0.js";import{_ as se}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as de}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as me}from"./index-DYfNUK1u.js";import{d as Y}from"./formatTime-DhdtkSIS.js";import{d as ce}from"./download-oWiM5xVU.js";import{d as pe,e as ue,s as fe,f as we}from"./index-DjgTygXx.js";import{g as ge}from"./index-BYKUBRQa.js";import{_ as _e}from"./ImportTable.vue_vue_type_script_setup_true_lang-lzajMpvR.js";import{_ as be}from"./PreviewCode.vue_vue_type_style_index_0_lang-E6gr6Jqk.js";import"./index-CqPfoRkb.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./tree-COGD3qag.js";import"./index-p2LMQUWl.js";import"./java-CqAR0c3a.js";const ye=J({name:"InfraCodegen",__name:"index",setup(he){const _=L(),{t:N}=O(),{push:F}=Q(),C=p(!0),V=p(0),z=p([]),i=j({pageNo:1,pageSize:10,tableName:void 0,tableComment:void 0,createTime:[]}),S=p(),T=p([]),g=async()=>{C.value=!0;try{const k=await pe(i);z.value=k.list,V.value=k.total}finally{C.value=!1}},b=()=>{i.pageNo=1,g()},H=()=>{S.value.resetFields(),b()},U=p(),D=p();return B(async()=>{await g(),T.value=await ge()}),(k,a)=>{const y=me,M=$,h=Z,I=ee,v=te,s=ae,K=X,P=de,m=oe,q=le,A=se,w=E("hasPermi"),G=ie;return d(),W(ne,null,[e(y,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u5355\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/"}),e(y,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u6811\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/tree/"}),e(y,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u4E3B\u5B50\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/master-sub/"}),e(y,{title:"\u5355\u5143\u6D4B\u8BD5",url:"https://doc.iocoder.cn/unit-test/"}),e(P,null,{default:l(()=>[e(K,{ref_key:"queryFormRef",ref:S,inline:!0,model:o(i),class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(h,{label:"\u8868\u540D\u79F0",prop:"tableName"},{default:l(()=>[e(M,{modelValue:o(i).tableName,"onUpdate:modelValue":a[0]||(a[0]=t=>o(i).tableName=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u540D\u79F0",onKeyup:R(b,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"\u8868\u63CF\u8FF0",prop:"tableComment"},{default:l(()=>[e(M,{modelValue:o(i).tableComment,"onUpdate:modelValue":a[1]||(a[1]=t=>o(i).tableComment=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u63CF\u8FF0",onKeyup:R(b,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[e(I,{modelValue:o(i).createTime,"onUpdate:modelValue":a[2]||(a[2]=t=>o(i).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(h,null,{default:l(()=>[e(s,{onClick:b},{default:l(()=>[e(v,{class:"mr-5px",icon:"ep:search"}),a[6]||(a[6]=n(" \u641C\u7D22 "))]),_:1}),e(s,{onClick:H},{default:l(()=>[e(v,{class:"mr-5px",icon:"ep:refresh"}),a[7]||(a[7]=n(" \u91CD\u7F6E "))]),_:1}),u((d(),f(s,{type:"primary",onClick:a[3]||(a[3]=t=>{U.value.open()})},{default:l(()=>[e(v,{class:"mr-5px",icon:"ep:zoom-in"}),a[8]||(a[8]=n(" \u5BFC\u5165 "))]),_:1})),[[w,["infra:codegen:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:l(()=>[u((d(),f(q,{data:o(z)},{default:l(()=>[e(m,{align:"center",label:"\u6570\u636E\u6E90"},{default:l(t=>{var c;return[n(re((c=o(T).find(r=>r.id===t.row.dataSourceConfigId))==null?void 0:c.name),1)]}),_:1}),e(m,{align:"center",label:"\u8868\u540D\u79F0",prop:"tableName",width:"200"}),e(m,{"show-overflow-tooltip":!0,align:"center",label:"\u8868\u63CF\u8FF0",prop:"tableComment",width:"200"}),e(m,{align:"center",label:"\u5B9E\u4F53",prop:"className",width:"200"}),e(m,{formatter:o(Y),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(m,{formatter:o(Y),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(m,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"300px"},{default:l(t=>[u((d(),f(s,{link:"",type:"primary",onClick:c=>{return r=t.row,void D.value.open(r.id);var r}},{default:l(()=>a[9]||(a[9]=[n(" \u9884\u89C8 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:preview"]]]),u((d(),f(s,{link:"",type:"primary",onClick:c=>{return r=t.row.id,void F("/codegen/edit?id="+r);var r}},{default:l(()=>a[10]||(a[10]=[n(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:update"]]]),u((d(),f(s,{link:"",type:"danger",onClick:c=>(async r=>{try{await _.delConfirm(),await ue(r),_.success(N("common.delSuccess")),await g()}catch{}})(t.row.id)},{default:l(()=>a[11]||(a[11]=[n(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:delete"]]]),u((d(),f(s,{link:"",type:"primary",onClick:c=>(async r=>{const x=r.tableName;try{await _.confirm("\u786E\u8BA4\u8981\u5F3A\u5236\u540C\u6B65"+x+"\u8868\u7ED3\u6784\u5417?",N("common.reminder")),await fe(r.id),_.success("\u540C\u6B65\u6210\u529F")}catch{}})(t.row)},{default:l(()=>a[12]||(a[12]=[n(" \u540C\u6B65 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:update"]]]),u((d(),f(s,{link:"",type:"primary",onClick:c=>(async r=>{const x=await we(r.id);ce.zip(x,"codegen-"+r.className+".zip")})(t.row)},{default:l(()=>a[13]||(a[13]=[n(" \u751F\u6210\u4EE3\u7801 ")])),_:2},1032,["onClick"])),[[w,["infra:codegen:download"]]])]),_:1})]),_:1},8,["data"])),[[G,o(C)]]),e(A,{limit:o(i).pageSize,"onUpdate:limit":a[4]||(a[4]=t=>o(i).pageSize=t),page:o(i).pageNo,"onUpdate:page":a[5]||(a[5]=t=>o(i).pageNo=t),total:o(V),onPagination:g},null,8,["limit","page","total"])]),_:1}),e(_e,{ref_key:"importRef",ref:U,onSuccess:g},null,512),e(be,{ref_key:"previewRef",ref:D},null,512)],64)}}});export{ye as default};
