import{d as x,e3 as m,aN as v,g as l,r as c,aK as C,q as D,A as P,o as a,w as Y,i as t,aw as i,a as r,c as d,F as I,y as E,t as $,aF as M}from"./index-CRsFgzy0.js";import{e as R}from"./couponTemplate-Be-ijyut.js";import{i as h,j as _}from"./constants-uird_4gU.js";import{f as k}from"./formatTime-DhdtkSIS.js";const b=x({name:"CouponDiscount",props:{coupon:m()},setup(s){const o=s.coupon;let p=o.discountPercent+"",n=" \u6298";return o.discountType===h.PRICE.type&&(p=v(o.discountPrice),n=" \u5143"),()=>l("div",null,[l("span",{class:"text-20px font-bold"},[p]),l("span",null,[n])])}}),A=x({name:"CouponDiscountDesc",props:{coupon:m()},setup(s){const o=s.coupon,p=o.usePrice>0?`\u6EE1${v(o.usePrice)}\u5143\uFF0C`:"",n=o.discountType===h.PRICE.type?`\u51CF${v(o.discountPrice)}\u5143`:`\u6253${o.discountPercent}\u6298`;return()=>l("div",null,[l("span",null,[p]),l("span",null,[n])])}}),F=x({name:"CouponValidTerm",props:{coupon:m()},setup(s){const o=s.coupon,p=o.validityType===_.DATE.type?`\u6709\u6548\u671F\uFF1A${k(o.validStartTime,"YYYY-MM-DD")} \u81F3 ${k(o.validEndTime,"YYYY-MM-DD")}`:`\u9886\u53D6\u540E\u7B2C ${o.fixedStartTerm} - ${o.fixedEndTerm} \u5929\u5185\u53EF\u7528`;return()=>l("div",null,[p])}}),S={key:0,class:"m-l-16px flex flex-row justify-between p-8px"},q={class:"flex flex-col justify-evenly gap-4px"},z={class:"flex flex-col justify-evenly"},K={key:1,class:"m-l-16px flex flex-row justify-between p-8px"},N={class:"flex flex-col justify-evenly gap-4px"},V={class:"flex flex-col"},W={key:2,class:"flex flex-col items-center justify-around gap-4px p-4px"},B=x({name:"CouponCard",__name:"index",props:{property:{}},setup(s){const o=s,p=c([]);C(()=>o.property.couponIds,async()=>{var e;((e=o.property.couponIds)==null?void 0:e.length)>0&&(p.value=await R(o.property.couponIds))},{immediate:!0,deep:!0});const n=c(375),g=c(),w=c("100%"),y=c(375);return C(()=>[o.property,n,p.value.length],()=>{y.value=(.95*n.value-o.property.space*(o.property.columns-1))/o.property.columns,w.value=y.value*p.value.length+o.property.space*(p.value.length-1)+"px"},{immediate:!0,deep:!0}),D(()=>{var e,f;n.value=((f=(e=g.value)==null?void 0:e.wrapRef)==null?void 0:f.offsetWidth)||375}),(e,f)=>{const T=M;return a(),P(T,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:g},{default:Y(()=>[t("div",{class:"flex flex-row text-12px",style:i({gap:`${e.property.space}px`,width:r(w)})},[(a(!0),d(I,null,E(r(p),(u,j)=>(a(),d("div",{class:"box-content",style:i({background:e.property.bgImg?`url(${e.property.bgImg}) 100% center / 100% 100% no-repeat`:"#fff",width:`${r(y)}px`,color:e.property.textColor}),key:j},[e.property.columns===1?(a(),d("div",S,[t("div",q,[l(r(b),{coupon:u},null,8,["coupon"]),l(r(A),{coupon:u},null,8,["coupon"]),l(r(F),{coupon:u},null,8,["coupon"])]),t("div",z,[t("div",{class:"rounded-20px p-x-8px p-y-2px",style:i({color:e.property.button.color,background:e.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)])])):e.property.columns===2?(a(),d("div",K,[t("div",N,[l(r(b),{coupon:u},null,8,["coupon"]),t("div",null,$(u.name),1)]),t("div",V,[t("div",{class:"h-full w-20px rounded-20px p-x-2px p-y-8px text-center",style:i({color:e.property.button.color,background:e.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)])])):(a(),d("div",W,[l(r(b),{coupon:u},null,8,["coupon"]),t("div",null,$(u.name),1),t("div",{class:"rounded-20px p-x-8px p-y-2px",style:i({color:e.property.button.color,background:e.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)]))],4))),128))],4)]),_:1},512)}}});export{B as default};
