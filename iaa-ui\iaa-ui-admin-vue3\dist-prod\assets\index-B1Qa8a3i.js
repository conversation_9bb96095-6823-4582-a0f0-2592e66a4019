import{d as u,r,q as p,c as d,o as e,g as l,w as f,J as g,a3 as y,a as t,A as h,M as _,F as w}from"./index-CvERnF9Y.js";import{_ as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as b}from"./IFrame.vue_vue_type_script_setup_true_lang-B5-s51Jd.js";import{_ as v}from"./index-CeUx6j9a.js";import{b as S}from"./index-CfXwz0ub.js";const k=u({name:"InfraSwagger",__name:"index",setup(q){const s=r(!0),o=r("http://shouhou.iaa360.com/doc.html");return p(async()=>{try{const a=await S("url.swagger");a&&a.length>0&&(o.value=a)}finally{s.value=!1}}),(a,A)=>{const n=v,c=b,m=x,i=_;return e(),d(w,null,[l(n,{title:"\u63A5\u53E3\u6587\u6863",url:"https://doc.iocoder.cn/api-doc/"}),l(m,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:f(()=>[t(s)?y("",!0):g((e(),h(c,{key:0,src:t(o)},null,8,["src"])),[[i,t(s)]])]),_:1})],64)}}});export{k as default};
