import{d as X,p as Z,b as $,r as i,f as ee,u as le,q as ae,O as te,c as re,o as d,g as e,w as r,s as oe,a as t,v as ne,P as pe,Q as T,x as ie,B as se,J as w,G as ue,H as s,I as me,A as b,l as de,m as ce,n as fe,K as we,L as be,a5 as _e,t as he,D as N,M as ge,F as ye}from"./index-CRsFgzy0.js";import{_ as xe}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as Ce}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ke}from"./index-DYfNUK1u.js";import{d as y}from"./formatTime-DhdtkSIS.js";import{d as Ve}from"./download-oWiM5xVU.js";import{b as Se,e as Ue,f as Te}from"./index-CNAJo8uH.js";import{_ as Ne}from"./ClueForm.vue_vue_type_script_setup_true_lang-6QpSc0c2.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-DLC3Afbg.js";import"./tree-COGD3qag.js";import"./index-D4y5Z4cM.js";const Re=X({name:"CrmClue",__name:"index",setup(Ee){const x=Z(),{t:P}=$(),C=i(!0),R=i(0),E=i([]),n=ee({pageNo:1,pageSize:10,sceneType:"1",name:null,telephone:null,mobile:null,transformStatus:!1}),M=i(),v=i(!1),k=i("1"),c=async()=>{C.value=!0;try{const p=await Se(n);E.value=p.list,R.value=p.total}finally{C.value=!1}},u=()=>{n.pageNo=1,c()},F=()=>{M.value.resetFields(),u()},q=p=>{n.sceneType=p.paneName,u()},{push:A}=le(),K=i(),L=(p,l)=>{K.value.open(p,l)},H=async()=>{try{await x.exportConfirm(),v.value=!0;const p=await Te(n);Ve.excel(p,"\u7EBF\u7D22.xls")}catch{}finally{v.value=!1}};return ae(()=>{c()}),(p,l)=>{const O=ke,V=pe,f=ne,z=se,B=ie,_=me,m=ue,G=oe,D=ve,S=fe,J=de,Q=_e,o=be,U=Ce,Y=we,j=xe,h=te("hasPermi"),W=ge;return d(),re(ye,null,[e(O,{title:"\u3010\u7EBF\u7D22\u3011\u7EBF\u7D22\u7BA1\u7406",url:"https://doc.iocoder.cn/crm/clue/"}),e(O,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),e(D,null,{default:r(()=>[e(G,{class:"-mb-15px",model:t(n),ref_key:"queryFormRef",ref:M,inline:!0,"label-width":"68px"},{default:r(()=>[e(f,{label:"\u7EBF\u7D22\u540D\u79F0",prop:"name"},{default:r(()=>[e(V,{modelValue:t(n).name,"onUpdate:modelValue":l[0]||(l[0]=a=>t(n).name=a),placeholder:"\u8BF7\u8F93\u5165\u7EBF\u7D22\u540D\u79F0",clearable:"",onKeyup:T(u,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(f,{label:"\u8F6C\u5316\u72B6\u6001",prop:"transformStatus"},{default:r(()=>[e(B,{modelValue:t(n).transformStatus,"onUpdate:modelValue":l[1]||(l[1]=a=>t(n).transformStatus=a),class:"!w-240px"},{default:r(()=>[e(z,{value:!1,label:"\u672A\u8F6C\u5316"}),e(z,{value:!0,label:"\u5DF2\u8F6C\u5316"})]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"\u624B\u673A\u53F7",prop:"mobile"},{default:r(()=>[e(V,{modelValue:t(n).mobile,"onUpdate:modelValue":l[2]||(l[2]=a=>t(n).mobile=a),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7",clearable:"",onKeyup:T(u,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(f,{label:"\u7535\u8BDD",prop:"telephone"},{default:r(()=>[e(V,{modelValue:t(n).telephone,"onUpdate:modelValue":l[3]||(l[3]=a=>t(n).telephone=a),placeholder:"\u8BF7\u8F93\u5165\u7535\u8BDD",clearable:"",onKeyup:T(u,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(f,null,{default:r(()=>[e(m,{onClick:u},{default:r(()=>[e(_,{icon:"ep:search",class:"mr-5px"}),l[8]||(l[8]=s(" \u641C\u7D22"))]),_:1}),e(m,{onClick:F},{default:r(()=>[e(_,{icon:"ep:refresh",class:"mr-5px"}),l[9]||(l[9]=s(" \u91CD\u7F6E"))]),_:1}),w((d(),b(m,{type:"primary",onClick:l[4]||(l[4]=a=>L("create"))},{default:r(()=>[e(_,{icon:"ep:plus",class:"mr-5px"}),l[10]||(l[10]=s(" \u65B0\u589E "))]),_:1})),[[h,["crm:clue:create"]]]),w((d(),b(m,{type:"success",plain:"",onClick:H,loading:t(v)},{default:r(()=>[e(_,{icon:"ep:download",class:"mr-5px"}),l[11]||(l[11]=s(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[h,["crm:clue:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(D,null,{default:r(()=>[e(J,{modelValue:t(k),"onUpdate:modelValue":l[5]||(l[5]=a=>ce(k)?k.value=a:null),onTabClick:q},{default:r(()=>[e(S,{label:"\u6211\u8D1F\u8D23\u7684",name:"1"}),e(S,{label:"\u6211\u53C2\u4E0E\u7684",name:"2"}),e(S,{label:"\u4E0B\u5C5E\u8D1F\u8D23\u7684",name:"3"})]),_:1},8,["modelValue"]),w((d(),b(Y,{data:t(E),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[e(o,{label:"\u7EBF\u7D22\u540D\u79F0",align:"center",prop:"name",fixed:"left",width:"160"},{default:r(a=>[e(Q,{underline:!1,type:"primary",onClick:I=>{return g=a.row.id,void A({name:"CrmClueDetail",params:{id:g}});var g}},{default:r(()=>[s(he(a.row.name),1)]),_:2},1032,["onClick"])]),_:1}),e(o,{label:"\u7EBF\u7D22\u6765\u6E90",align:"center",prop:"source",width:"100"},{default:r(a=>[e(U,{type:t(N).CRM_CUSTOMER_SOURCE,value:a.row.source},null,8,["type","value"])]),_:1}),e(o,{label:"\u624B\u673A",align:"center",prop:"mobile",width:"120"}),e(o,{label:"\u7535\u8BDD",align:"center",prop:"telephone",width:"130"}),e(o,{label:"\u90AE\u7BB1",align:"center",prop:"email",width:"180"}),e(o,{label:"\u5730\u5740",align:"center",prop:"detailAddress",width:"180"}),e(o,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:r(a=>[e(U,{type:t(N).CRM_CUSTOMER_INDUSTRY,value:a.row.industryId},null,8,["type","value"])]),_:1}),e(o,{align:"center",label:"\u5BA2\u6237\u7EA7\u522B",prop:"level",width:"135"},{default:r(a=>[e(U,{type:t(N).CRM_CUSTOMER_LEVEL,value:a.row.level},null,8,["type","value"])]),_:1}),e(o,{formatter:t(y),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime",width:"180px"},null,8,["formatter"]),e(o,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),e(o,{label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",align:"center",prop:"contactLastTime",formatter:t(y),width:"180px"},null,8,["formatter"]),e(o,{align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u8BB0\u5F55",prop:"contactLastContent",width:"200"}),e(o,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"100px"}),e(o,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100"}),e(o,{label:"\u66F4\u65B0\u65F6\u95F4",align:"center",prop:"updateTime",formatter:t(y),width:"180px"},null,8,["formatter"]),e(o,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(y),width:"180px"},null,8,["formatter"]),e(o,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"}),e(o,{label:"\u64CD\u4F5C",align:"center","min-width":"110",fixed:"right"},{default:r(a=>[w((d(),b(m,{link:"",type:"primary",onClick:I=>L("update",a.row.id)},{default:r(()=>l[12]||(l[12]=[s(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[h,["crm:clue:update"]]]),w((d(),b(m,{link:"",type:"danger",onClick:I=>(async g=>{try{await x.delConfirm(),await Ue(g),x.success(P("common.delSuccess")),await c()}catch{}})(a.row.id)},{default:r(()=>l[13]||(l[13]=[s(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[h,["crm:clue:delete"]]])]),_:1})]),_:1},8,["data"])),[[W,t(C)]]),e(j,{total:t(R),page:t(n).pageNo,"onUpdate:page":l[6]||(l[6]=a=>t(n).pageNo=a),limit:t(n).pageSize,"onUpdate:limit":l[7]||(l[7]=a=>t(n).pageSize=a),onPagination:c},null,8,["total","page","limit"])]),_:1}),e(Ne,{ref_key:"formRef",ref:K,onSuccess:c},null,512)],64)}}});export{Re as default};
