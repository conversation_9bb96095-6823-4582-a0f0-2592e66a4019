import{d as K,p as L,b as Q,r as d,f as j,a2 as W,q as X,O as Y,c as M,o as s,g as a,w as l,s as Z,a as i,v as $,x as aa,F as O,y as ea,R as ta,D as T,A as u,B as la,J as f,G as ia,H as n,I as oa,K as ra,L as pa,t as sa,M as na}from"./index-CRsFgzy0.js";import{_ as ma}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as ca}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as ua}from"./el-image-BQpHFDaE.js";import{_ as da}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as fa}from"./index-DYfNUK1u.js";import{d as wa}from"./formatTime-DhdtkSIS.js";import{_ as ya}from"./PointActivityForm.vue_vue_type_script_setup_true_lang-BWMTpF8o.js";import{f as _a}from"./formatter-D3GpDdeL.js";import{P as k}from"./index-gOhrLNGh.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./Form-BF4H89jq.js";import"./el-virtual-list-AoPW9ghL.js";import"./el-tree-select-BijZG_HG.js";import"./el-time-select-DBwK3NDO.js";import"./InputPassword-CO9Ecx-K.js";import"./tsxHelper-DqnKTMG3.js";import"./SpuSelect.vue_vue_type_script_setup_true_lang-tSVtv9n7.js";import"./index-CQgbu5O7.js";import"./SkuList.vue_vue_type_script_setup_true_lang-ChNgeWhW.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";import"./spu-BHhhuUrI.js";import"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-DGosLJYC.js";import"./formRules-V2Qetfkc.js";import"./useCrudSchemas-CNYomGr4.js";const va=K({name:"PointActivity",__name:"index",setup(ga){const w=L(),{t:z}=Q(),_=d(!0),h=d(0),b=d([]),r=j({pageNo:1,pageSize:10,name:null,status:null}),x=d(),F=W(()=>p=>(p.totalStock||0)-(p.stock||0)),m=async()=>{_.value=!0;try{const p=await k.getPointActivityPage(r);b.value=p.list,h.value=p.total}finally{_.value=!1}},C=()=>{r.pageNo=1,m()},R=()=>{x.value.resetFields(),C()},S=d(),P=(p,e)=>{S.value.open(p,e)};return X(async()=>{await m()}),(p,e)=>{const V=fa,q=la,I=aa,N=$,v=oa,c=ia,B=Z,A=da,o=pa,D=ua,E=ca,G=ra,H=ma,y=Y("hasPermi"),J=na;return s(),M(O,null,[a(V,{title:"\u3010\u8425\u9500\u3011\u79EF\u5206\u5546\u57CE\u6D3B\u52A8",url:"https://doc.iocoder.cn/mall/promotion-point/"}),a(A,null,{default:l(()=>[a(B,{ref_key:"queryFormRef",ref:x,inline:!0,model:i(r),class:"-mb-15px","label-width":"68px"},{default:l(()=>[a(N,{label:"\u6D3B\u52A8\u72B6\u6001",prop:"status"},{default:l(()=>[a(I,{modelValue:i(r).status,"onUpdate:modelValue":e[0]||(e[0]=t=>i(r).status=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u72B6\u6001"},{default:l(()=>[(s(!0),M(O,null,ea(i(ta)(i(T).COMMON_STATUS),t=>(s(),u(q,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(N,null,{default:l(()=>[a(c,{onClick:C},{default:l(()=>[a(v,{class:"mr-5px",icon:"ep:search"}),e[4]||(e[4]=n(" \u641C\u7D22 "))]),_:1}),a(c,{onClick:R},{default:l(()=>[a(v,{class:"mr-5px",icon:"ep:refresh"}),e[5]||(e[5]=n(" \u91CD\u7F6E "))]),_:1}),f((s(),u(c,{plain:"",type:"primary",onClick:e[1]||(e[1]=t=>P("create"))},{default:l(()=>[a(v,{class:"mr-5px",icon:"ep:plus"}),e[6]||(e[6]=n(" \u65B0\u589E "))]),_:1})),[[y,["promotion:point-activity:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(A,null,{default:l(()=>[f((s(),u(G,{data:i(b),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[a(o,{label:"\u6D3B\u52A8\u7F16\u53F7","min-width":"80",prop:"id"}),a(o,{label:"\u5546\u54C1\u56FE\u7247","min-width":"80",prop:"spuName"},{default:l(t=>[a(D,{"preview-src-list":[t.row.picUrl],src:t.row.picUrl,class:"h-40px w-40px","preview-teleported":""},null,8,["preview-src-list","src"])]),_:1}),a(o,{label:"\u5546\u54C1\u6807\u9898","min-width":"300",prop:"spuName"}),a(o,{formatter:i(_a),label:"\u539F\u4EF7","min-width":"100",prop:"marketPrice"},null,8,["formatter"]),a(o,{label:"\u539F\u4EF7","min-width":"100",prop:"marketPrice"}),a(o,{align:"center",label:"\u6D3B\u52A8\u72B6\u6001","min-width":"100",prop:"status"},{default:l(t=>[a(E,{type:i(T).COMMON_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),a(o,{align:"center",label:"\u5E93\u5B58","min-width":"80",prop:"stock"}),a(o,{align:"center",label:"\u603B\u5E93\u5B58","min-width":"80",prop:"totalStock"}),a(o,{align:"center",label:"\u5DF2\u5151\u6362\u6570\u91CF","min-width":"100",prop:"redeemedQuantity"},{default:l(({row:t})=>[n(sa(i(F)(t)),1)]),_:1}),a(o,{formatter:i(wa),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(o,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"150px"},{default:l(t=>[f((s(),u(c,{link:"",type:"primary",onClick:U=>P("update",t.row.id)},{default:l(()=>e[7]||(e[7]=[n(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["promotion:point-activity:update"]]]),t.row.status===0?f((s(),u(c,{key:0,link:"",type:"danger",onClick:U=>(async g=>{try{await w.confirm("\u786E\u8BA4\u5173\u95ED\u8BE5\u79EF\u5206\u5546\u57CE\u6D3B\u52A8\u5417\uFF1F"),await k.closePointActivity(g),w.success("\u5173\u95ED\u6210\u529F"),await m()}catch{}})(t.row.id)},{default:l(()=>e[8]||(e[8]=[n(" \u5173\u95ED ")])),_:2},1032,["onClick"])),[[y,["promotion:point-activity:close"]]]):f((s(),u(c,{key:1,link:"",type:"danger",onClick:U=>(async g=>{try{await w.delConfirm(),await k.deletePointActivity(g),w.success(z("common.delSuccess")),await m()}catch{}})(t.row.id)},{default:l(()=>e[9]||(e[9]=[n(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["promotion:point-activity:delete"]]])]),_:1})]),_:1},8,["data"])),[[J,i(_)]]),a(H,{limit:i(r).pageSize,"onUpdate:limit":e[2]||(e[2]=t=>i(r).pageSize=t),page:i(r).pageNo,"onUpdate:page":e[3]||(e[3]=t=>i(r).pageNo=t),total:i(h),onPagination:m},null,8,["limit","page","total"])]),_:1}),a(ya,{ref_key:"formRef",ref:S,onSuccess:m},null,512)],64)}}});export{va as default};
