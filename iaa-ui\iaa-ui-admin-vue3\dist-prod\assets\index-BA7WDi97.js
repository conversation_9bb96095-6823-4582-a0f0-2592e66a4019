import{d as Q,p as j,b as W,r as p,f as Z,q as $,O as ee,c as D,o as n,g as e,w as t,s as ae,a as o,v as le,P as te,Q as oe,x as re,F as E,y as se,R as ne,D as Y,A as u,B as ie,C as de,J as f,G as pe,H as m,I as ue,K as me,L as ce,l as fe,n as _e,M as xe}from"./index-CRsFgzy0.js";import{_ as we}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as ye}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as ge}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as be}from"./index-DYfNUK1u.js";import{d as M}from"./formatTime-DhdtkSIS.js";import{d as ve}from"./download-oWiM5xVU.js";import{d as he,e as Se,f as ke}from"./index-CN3eSHy6.js";import{_ as Ce}from"./Demo03StudentForm.vue_vue_type_script_setup_true_lang-CCOXBWg2.js";import{_ as Ve}from"./Demo03CourseList.vue_vue_type_script_setup_true_lang-BLfoR3de.js";import{_ as Te}from"./Demo03GradeList.vue_vue_type_script_setup_true_lang-C5GXFS8k.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./Demo03CourseForm.vue_vue_type_script_setup_true_lang-DRXbVM-9.js";import"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-DUlJR2Hp.js";const Ue=Q({name:"Demo03Student",__name:"index",setup(De){const y=j(),{t:R}=W(),g=p(!0),h=p([]),S=p(0),r=Z({pageNo:1,pageSize:10,name:null,sex:null,description:null,createTime:[]}),k=p(),b=p(!1),c=async()=>{g.value=!0;try{const i=await he(r);h.value=i.list,S.value=i.total}finally{g.value=!1}},v=()=>{r.pageNo=1,c()},H=()=>{k.value.resetFields(),v()},C=p(),V=(i,a)=>{C.value.open(i,a)},N=async()=>{try{await y.exportConfirm(),b.value=!0;const i=await ke(r);ve.excel(i,"\u5B66\u751F.xls")}catch{}finally{b.value=!1}};return $(()=>{c()}),(i,a)=>{const P=be,z=te,_=le,F=ie,q=re,G=de,x=ue,d=pe,K=ae,T=ge,U=_e,L=fe,s=ce,O=ye,X=me,A=we,w=ee("hasPermi"),B=xe;return n(),D(E,null,[e(P,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u4E3B\u5B50\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/master-sub/"}),e(T,null,{default:t(()=>[e(K,{class:"-mb-15px",model:o(r),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:t(()=>[e(_,{label:"\u540D\u5B57",prop:"name"},{default:t(()=>[e(z,{modelValue:o(r).name,"onUpdate:modelValue":a[0]||(a[0]=l=>o(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57",clearable:"",onKeyup:oe(v,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,{label:"\u6027\u522B",prop:"sex"},{default:t(()=>[e(q,{modelValue:o(r).sex,"onUpdate:modelValue":a[1]||(a[1]=l=>o(r).sex=l),placeholder:"\u8BF7\u9009\u62E9\u6027\u522B",clearable:"",class:"!w-240px"},{default:t(()=>[(n(!0),D(E,null,se(o(ne)(o(Y).SYSTEM_USER_SEX),l=>(n(),u(F,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(G,{modelValue:o(r).createTime,"onUpdate:modelValue":a[2]||(a[2]=l=>o(r).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(_,null,{default:t(()=>[e(d,{onClick:v},{default:t(()=>[e(x,{icon:"ep:search",class:"mr-5px"}),a[6]||(a[6]=m(" \u641C\u7D22"))]),_:1}),e(d,{onClick:H},{default:t(()=>[e(x,{icon:"ep:refresh",class:"mr-5px"}),a[7]||(a[7]=m(" \u91CD\u7F6E"))]),_:1}),f((n(),u(d,{type:"primary",plain:"",onClick:a[3]||(a[3]=l=>V("create"))},{default:t(()=>[e(x,{icon:"ep:plus",class:"mr-5px"}),a[8]||(a[8]=m(" \u65B0\u589E "))]),_:1})),[[w,["infra:demo03-student:create"]]]),f((n(),u(d,{type:"success",plain:"",onClick:N,loading:o(b)},{default:t(()=>[e(x,{icon:"ep:download",class:"mr-5px"}),a[9]||(a[9]=m(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[w,["infra:demo03-student:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(T,null,{default:t(()=>[f((n(),u(X,{data:o(h),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(s,{type:"expand"},{default:t(l=>[e(L,{"model-value":"demo03Course"},{default:t(()=>[e(U,{label:"\u5B66\u751F\u8BFE\u7A0B",name:"demo03Course"},{default:t(()=>[e(Ve,{"student-id":l.row.id},null,8,["student-id"])]),_:2},1024),e(U,{label:"\u5B66\u751F\u73ED\u7EA7",name:"demo03Grade"},{default:t(()=>[e(Te,{"student-id":l.row.id},null,8,["student-id"])]),_:2},1024)]),_:2},1024)]),_:1}),e(s,{label:"\u7F16\u53F7",align:"center",prop:"id"}),e(s,{label:"\u540D\u5B57",align:"center",prop:"name"}),e(s,{label:"\u6027\u522B",align:"center",prop:"sex"},{default:t(l=>[e(O,{type:o(Y).SYSTEM_USER_SEX,value:l.row.sex},null,8,["type","value"])]),_:1}),e(s,{label:"\u51FA\u751F\u65E5\u671F",align:"center",prop:"birthday",formatter:o(M),width:"180px"},null,8,["formatter"]),e(s,{label:"\u7B80\u4ECB",align:"center",prop:"description"}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:o(M),width:"180px"},null,8,["formatter"]),e(s,{label:"\u64CD\u4F5C",align:"center"},{default:t(l=>[f((n(),u(d,{link:"",type:"primary",onClick:I=>V("update",l.row.id)},{default:t(()=>a[10]||(a[10]=[m(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["infra:demo03-student:update"]]]),f((n(),u(d,{link:"",type:"danger",onClick:I=>(async J=>{try{await y.delConfirm(),await Se(J),y.success(R("common.delSuccess")),await c()}catch{}})(l.row.id)},{default:t(()=>a[11]||(a[11]=[m(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[B,o(g)]]),e(A,{total:o(S),page:o(r).pageNo,"onUpdate:page":a[4]||(a[4]=l=>o(r).pageNo=l),limit:o(r).pageSize,"onUpdate:limit":a[5]||(a[5]=l=>o(r).pageSize=l),onPagination:c},null,8,["total","page","limit"])]),_:1}),e(Ce,{ref_key:"formRef",ref:C,onSuccess:c},null,512)],64)}}});export{Ue as default};
