import{d as K,p as R,b as D,r as _,f as G,u as J,q as L,O as N,c as Q,o as n,g as a,w as l,s as j,a as t,v as z,P as B,Q as E,J as p,G as W,H as i,I as X,A as c,K as Y,L as Z,i as $,D as aa,a3 as ea,M as la,F as ra}from"./index-CRsFgzy0.js";import{_ as ta}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as oa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as sa}from"./index-DYfNUK1u.js";import{h as na}from"./tree-COGD3qag.js";import{d as ia}from"./formatTime-DhdtkSIS.js";import{g as pa,d as ca}from"./category--cl9fhwU.js";import{_ as da}from"./CategoryForm.vue_vue_type_script_setup_true_lang-Bu7tb817.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./constants-uird_4gU.js";const ua=["src"],ma=K({name:"ProductCategory",__name:"index",setup(fa){const g=R(),{t:F}=D(),w=_(!0),b=_([]),d=G({name:void 0}),C=_(),u=async()=>{w.value=!0;try{const m=await pa(d);b.value=na(m,"id","parentId")}finally{w.value=!1}},h=()=>{u()},I=()=>{C.value.resetFields(),h()},v=_(),x=(m,e)=>{v.value.open(m,e)},P=J();return L(()=>{u()}),(m,e)=>{const M=sa,O=B,S=z,k=X,o=W,T=j,U=oa,s=Z,V=ta,A=Y,f=N("hasPermi"),H=la;return n(),Q(ra,null,[a(M,{title:"\u3010\u5546\u54C1\u3011\u5546\u54C1\u5206\u7C7B",url:"https://doc.iocoder.cn/mall/product-category/"}),a(U,null,{default:l(()=>[a(T,{class:"-mb-15px",model:t(d),ref_key:"queryFormRef",ref:C,inline:!0,"label-width":"68px"},{default:l(()=>[a(S,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name"},{default:l(()=>[a(O,{modelValue:t(d).name,"onUpdate:modelValue":e[0]||(e[0]=r=>t(d).name=r),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",clearable:"",onKeyup:E(h,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(S,null,{default:l(()=>[a(o,{onClick:h},{default:l(()=>[a(k,{icon:"ep:search",class:"mr-5px"}),e[2]||(e[2]=i(" \u641C\u7D22"))]),_:1}),a(o,{onClick:I},{default:l(()=>[a(k,{icon:"ep:refresh",class:"mr-5px"}),e[3]||(e[3]=i(" \u91CD\u7F6E"))]),_:1}),p((n(),c(o,{type:"primary",plain:"",onClick:e[1]||(e[1]=r=>x("create"))},{default:l(()=>[a(k,{icon:"ep:plus",class:"mr-5px"}),e[4]||(e[4]=i(" \u65B0\u589E "))]),_:1})),[[f,["product:category:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(U,null,{default:l(()=>[p((n(),c(A,{data:t(b),"row-key":"id","default-expand-all":""},{default:l(()=>[a(s,{label:"\u540D\u79F0","min-width":"240",prop:"name",sortable:""}),a(s,{label:"\u5206\u7C7B\u56FE\u6807",align:"center","min-width":"80",prop:"picUrl"},{default:l(r=>[$("img",{src:r.row.picUrl,alt:"\u79FB\u52A8\u7AEF\u5206\u7C7B\u56FE",class:"h-36px"},null,8,ua)]),_:1}),a(s,{label:"\u6392\u5E8F",align:"center","min-width":"150",prop:"sort"}),a(s,{label:"\u72B6\u6001",align:"center","min-width":"150",prop:"status"},{default:l(r=>[a(V,{type:t(aa).COMMON_STATUS,value:r.row.status},null,8,["type","value"])]),_:1}),a(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:t(ia)},null,8,["formatter"]),a(s,{label:"\u64CD\u4F5C",align:"center","min-width":"180"},{default:l(r=>[p((n(),c(o,{link:"",type:"primary",onClick:q=>x("update",r.row.id)},{default:l(()=>e[5]||(e[5]=[i(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[f,["product:category:update"]]]),r.row.parentId>0?p((n(),c(o,{key:0,link:"",type:"primary",onClick:q=>{return y=r.row.id,void P.push({name:"ProductSpu",query:{categoryId:y}});var y}},{default:l(()=>e[6]||(e[6]=[i(" \u67E5\u770B\u5546\u54C1 ")])),_:2},1032,["onClick"])),[[f,["product:spu:query"]]]):ea("",!0),p((n(),c(o,{link:"",type:"danger",onClick:q=>(async y=>{try{await g.delConfirm(),await ca(y),g.success(F("common.delSuccess")),await u()}catch{}})(r.row.id)},{default:l(()=>e[7]||(e[7]=[i(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[f,["product:category:delete"]]])]),_:1})]),_:1},8,["data"])),[[H,t(w)]])]),_:1}),a(da,{ref_key:"formRef",ref:v,onSuccess:u},null,512)],64)}}});export{ma as default};
