import{d as j,p as W,b as X,r as i,f as Y,q as Z,O as $,c as O,o as s,g as a,w as o,s as ee,a as l,v as ae,P as le,Q as te,x as oe,F as T,y as re,R as se,D as M,A as d,B as pe,J as y,G as ne,H as u,I as ie,a3 as ue,K as ce,L as de,M as me,aG as fe}from"./index-CvERnF9Y.js";import{_ as ye}from"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import{_ as ge}from"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as ve}from"./index-CeUx6j9a.js";import{d as xe}from"./formatTime-CmW2_KRq.js";import{h as we}from"./tree-COGD3qag.js";import{d as Ce}from"./download-oWiM5xVU.js";import{P as h}from"./index-CtELDy76.js";import{_ as be}from"./ProductCategoryForm.vue_vue_type_script_setup_true_lang-ITMNaoMD.js";import"./index-DHM6tdge.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./el-tree-select-CD6tMKW2.js";import"./constants-uird_4gU.js";const ke=j({name:"ErpProductCategory",__name:"index",setup(he){const _=W(),{t:z}=X(),v=i(!0),P=i([]),r=Y({name:void 0,status:void 0}),S=i(),x=i(!1),m=async()=>{v.value=!0;try{const p=await h.getProductCategoryList(r);P.value=we(p,"id","parentId")}finally{v.value=!1}},w=()=>{r.pageNo=1,m()},A=()=>{S.value.resetFields(),w()},N=i(),U=(p,e)=>{N.value.open(p,e)},F=async()=>{try{await _.exportConfirm(),x.value=!0;const p=await h.exportProductCategory(r);Ce.excel(p,"\u4EA7\u54C1\u5206\u7C7B.xls")}catch{}finally{x.value=!1}},C=i(!0),b=i(!0),I=async()=>{b.value=!1,C.value=!C.value,await fe(),b.value=!0};return Z(()=>{m()}),(p,e)=>{const R=ve,q=le,k=ae,D=pe,G=oe,f=ie,n=ne,K=ee,V=_e,c=de,L=ge,B=ce,E=ye,g=$("hasPermi"),H=me;return s(),O(T,null,[a(R,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u4FE1\u606F\u3001\u5206\u7C7B\u3001\u5355\u4F4D",url:"https://doc.iocoder.cn/erp/product/"}),a(V,null,{default:o(()=>[a(K,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:S,inline:!0,"label-width":"68px"},{default:o(()=>[a(k,{label:"\u5206\u7C7B\u540D\u79F0",prop:"name"},{default:o(()=>[a(q,{modelValue:l(r).name,"onUpdate:modelValue":e[0]||(e[0]=t=>l(r).name=t),placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B\u540D\u79F0",clearable:"",onKeyup:te(w,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(k,{label:"\u5F00\u542F\u72B6\u6001",prop:"status"},{default:o(()=>[a(G,{modelValue:l(r).status,"onUpdate:modelValue":e[1]||(e[1]=t=>l(r).status=t),placeholder:"\u8BF7\u9009\u62E9\u5F00\u542F\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(s(!0),O(T,null,re(l(se)(l(M).COMMON_STATUS),t=>(s(),d(D,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(k,null,{default:o(()=>[a(n,{onClick:w},{default:o(()=>[a(f,{icon:"ep:search",class:"mr-5px"}),e[5]||(e[5]=u(" \u641C\u7D22"))]),_:1}),a(n,{onClick:A},{default:o(()=>[a(f,{icon:"ep:refresh",class:"mr-5px"}),e[6]||(e[6]=u(" \u91CD\u7F6E"))]),_:1}),y((s(),d(n,{type:"primary",plain:"",onClick:e[2]||(e[2]=t=>U("create"))},{default:o(()=>[a(f,{icon:"ep:plus",class:"mr-5px"}),e[7]||(e[7]=u(" \u65B0\u589E "))]),_:1})),[[g,["erp:product-category:create"]]]),y((s(),d(n,{type:"success",plain:"",onClick:F,loading:l(x)},{default:o(()=>[a(f,{icon:"ep:download",class:"mr-5px"}),e[8]||(e[8]=u(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[g,["erp:product-category:export"]]]),a(n,{type:"danger",plain:"",onClick:I},{default:o(()=>[a(f,{icon:"ep:sort",class:"mr-5px"}),e[9]||(e[9]=u(" \u5C55\u5F00/\u6298\u53E0 "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(V,null,{default:o(()=>[l(b)?y((s(),d(B,{key:0,data:l(P),stripe:!0,"show-overflow-tooltip":!0,"row-key":"id","default-expand-all":l(C)},{default:o(()=>[a(c,{label:"\u7F16\u7801",align:"center",prop:"code"}),a(c,{label:"\u540D\u79F0",align:"center",prop:"name"}),a(c,{label:"\u6392\u5E8F",align:"center",prop:"sort"}),a(c,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:o(t=>[a(L,{type:l(M).COMMON_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),a(c,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(xe),width:"180px"},null,8,["formatter"]),a(c,{label:"\u64CD\u4F5C",align:"center"},{default:o(t=>[y((s(),d(n,{link:"",type:"primary",onClick:J=>U("update",t.row.id)},{default:o(()=>e[10]||(e[10]=[u(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[g,["erp:product-category:update"]]]),y((s(),d(n,{link:"",type:"danger",onClick:J=>(async Q=>{try{await _.delConfirm(),await h.deleteProductCategory(Q),_.success(z("common.delSuccess")),await m()}catch{}})(t.row.id)},{default:o(()=>e[11]||(e[11]=[u(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["erp:product-category:delete"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[H,l(v)]]):ue("",!0),a(E,{total:p.total,page:l(r).pageNo,"onUpdate:page":e[3]||(e[3]=t=>l(r).pageNo=t),limit:l(r).pageSize,"onUpdate:limit":e[4]||(e[4]=t=>l(r).pageSize=t),onPagination:m},null,8,["total","page","limit"])]),_:1}),a(be,{ref_key:"formRef",ref:N,onSuccess:m},null,512)],64)}}});export{ke as default};
