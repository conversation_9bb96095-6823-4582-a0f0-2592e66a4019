import{d as I,p as T,b as j,r as d,q,O as D,c as F,o as s,g as a,w as t,J as i,A as o,G,H as g,I as L,K as N,a as _,L as P,D as H,M as J,F as K}from"./index-CRsFgzy0.js";import{_ as R}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as U}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as z}from"./index-DYfNUK1u.js";import{_ as B,g as E,d as Q}from"./SignInConfigForm.vue_vue_type_script_setup_true_lang-DTOLaP0b.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./constants-uird_4gU.js";const V=I({name:"SignInConfig",__name:"index",setup(W){const y=T(),{t:S}=j(),p=d(!0),k=d([]),c=async()=>{p.value=!0;try{const r=await E();k.value=r}finally{p.value=!1}},w=d(),C=(r,e)=>{w.value.open(r,e)};return q(()=>{c()}),(r,e)=>{const x=z,O=L,u=G,b=U,l=P,h=R,M=N,m=D("hasPermi"),A=J;return s(),F(K,null,[a(x,{title:"\u4F1A\u5458\u7B49\u7EA7\u3001\u79EF\u5206\u3001\u7B7E\u5230",url:"https://doc.iocoder.cn/member/level/"}),a(b,null,{default:t(()=>[i((s(),o(u,{type:"primary",plain:"",onClick:e[0]||(e[0]=n=>C("create"))},{default:t(()=>[a(O,{icon:"ep:plus",class:"mr-5px"}),e[1]||(e[1]=g(" \u65B0\u589E "))]),_:1})),[[m,["point:sign-in-config:create"]]])]),_:1}),a(b,null,{default:t(()=>[i((s(),o(M,{data:_(k)},{default:t(()=>[a(l,{label:"\u7B7E\u5230\u5929\u6570",align:"center",prop:"day",formatter:(n,v,f)=>["\u7B2C",f,"\u5929"].join(" ")},null,8,["formatter"]),a(l,{label:"\u5956\u52B1\u79EF\u5206",align:"center",prop:"point"}),a(l,{label:"\u5956\u52B1\u7ECF\u9A8C",align:"center",prop:"experience"}),a(l,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:t(n=>[a(h,{type:_(H).COMMON_STATUS,value:n.row.status},null,8,["type","value"])]),_:1}),a(l,{label:"\u64CD\u4F5C",align:"center"},{default:t(n=>[i((s(),o(u,{link:"",type:"primary",onClick:v=>C("update",n.row.id)},{default:t(()=>e[2]||(e[2]=[g(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[m,["point:sign-in-config:update"]]]),i((s(),o(u,{link:"",type:"danger",onClick:v=>(async f=>{try{await y.delConfirm(),await Q(f),y.success(S("common.delSuccess")),await c()}catch{}})(n.row.id)},{default:t(()=>e[3]||(e[3]=[g(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[m,["point:sign-in-config:delete"]]])]),_:1})]),_:1},8,["data"])),[[A,_(p)]])]),_:1}),a(B,{ref_key:"formRef",ref:w,onSuccess:c},null,512)],64)}}});export{V as default};
