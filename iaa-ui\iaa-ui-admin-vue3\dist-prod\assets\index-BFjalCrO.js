import{d as W,p as j,b as X,r as d,f as Y,u as Z,bE as $,q as ee,O as ae,c as D,o as n,g as e,w as r,s as le,a as t,v as te,P as re,Q as oe,x as ie,F as O,y as ne,R as pe,D as x,A as m,B as se,J as f,G as ue,H as s,I as ce,K as de,L as me,a5 as fe,t as _e,e9 as we,M as ge}from"./index-CRsFgzy0.js";import{_ as ye}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as be}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as he}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ve}from"./index-DYfNUK1u.js";import{d as z}from"./formatTime-DhdtkSIS.js";import{d as Ce}from"./download-oWiM5xVU.js";import{b as xe,d as ke,e as Ue}from"./index-CwPVot3P.js";import{_ as Re}from"./ProductForm.vue_vue_type_script_setup_true_lang-CmSrS0Jr.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-C5r8YBwc.js";import"./tree-COGD3qag.js";import"./index-D4y5Z4cM.js";const Te=W({name:"CrmProduct",__name:"index",setup(Se){const y=j(),{t:A}=X(),b=d(!0),k=d(0),U=d([]),o=Y({pageNo:1,pageSize:10,name:void 0,status:void 0}),R=d(),h=d(!1),u=async()=>{b.value=!0;try{const p=await xe(o);U.value=p.list,k.value=p.total}finally{b.value=!1}},v=()=>{o.pageNo=1,u()},F=()=>{R.value.resetFields(),v()},T=d(),S=(p,a)=>{T.value.open(p,a)},{currentRoute:Ne,push:I}=Z(),M=async()=>{try{await y.exportConfirm(),h.value=!0;const p=await Ue(o);Ce.excel(p,"\u4EA7\u54C1.xls")}catch{}finally{h.value=!1}};return $(()=>{u()}),ee(()=>{u()}),(p,a)=>{const q=ve,E=re,C=te,H=se,K=ie,_=ce,c=ue,B=le,N=he,G=fe,i=me,P=be,J=de,L=ye,w=ae("hasPermi"),Q=ge;return n(),D(O,null,[e(q,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u7BA1\u7406\u3001\u4EA7\u54C1\u5206\u7C7B",url:"https://doc.iocoder.cn/crm/product/"}),e(N,null,{default:r(()=>[e(B,{class:"-mb-15px",model:t(o),ref_key:"queryFormRef",ref:R,inline:!0,"label-width":"68px"},{default:r(()=>[e(C,{label:"\u4EA7\u54C1\u540D\u79F0",prop:"name"},{default:r(()=>[e(E,{modelValue:t(o).name,"onUpdate:modelValue":a[0]||(a[0]=l=>t(o).name=l),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u540D\u79F0",clearable:"",onKeyup:oe(v,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(C,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[e(K,{modelValue:t(o).status,"onUpdate:modelValue":a[1]||(a[1]=l=>t(o).status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(n(!0),D(O,null,ne(t(pe)(t(x).CRM_PRODUCT_STATUS),l=>(n(),m(H,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(C,null,{default:r(()=>[e(c,{onClick:v},{default:r(()=>[e(_,{icon:"ep:search",class:"mr-5px"}),a[5]||(a[5]=s(" \u641C\u7D22 "))]),_:1}),e(c,{onClick:F},{default:r(()=>[e(_,{icon:"ep:refresh",class:"mr-5px"}),a[6]||(a[6]=s(" \u91CD\u7F6E "))]),_:1}),f((n(),m(c,{type:"primary",onClick:a[2]||(a[2]=l=>S("create"))},{default:r(()=>[e(_,{icon:"ep:plus",class:"mr-5px"}),a[7]||(a[7]=s(" \u65B0\u589E "))]),_:1})),[[w,["crm:product:create"]]]),f((n(),m(c,{type:"success",plain:"",onClick:M,loading:t(h)},{default:r(()=>[e(_,{icon:"ep:download",class:"mr-5px"}),a[8]||(a[8]=s(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[w,["crm:product:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(N,null,{default:r(()=>[f((n(),m(J,{data:t(U),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[e(i,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"name",width:"160"},{default:r(l=>[e(G,{underline:!1,type:"primary",onClick:V=>{return g=l.row.id,void I({name:"CrmProductDetail",params:{id:g}});var g}},{default:r(()=>[s(_e(l.row.name),1)]),_:2},1032,["onClick"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u7C7B\u578B",align:"center",prop:"categoryName",width:"160"}),e(i,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unit"},{default:r(l=>[e(P,{type:t(x).CRM_PRODUCT_UNIT,value:l.row.unit},null,8,["type","value"])]),_:1}),e(i,{label:"\u4EA7\u54C1\u7F16\u7801",align:"center",prop:"no"}),e(i,{label:"\u4EF7\u683C\uFF08\u5143\uFF09",align:"center",prop:"price",formatter:t(we),width:"100"},null,8,["formatter"]),e(i,{label:"\u4EA7\u54C1\u63CF\u8FF0",align:"center",prop:"description",width:"150"}),e(i,{label:"\u4E0A\u67B6\u72B6\u6001",align:"center",prop:"status",width:"120"},{default:r(l=>[e(P,{type:t(x).CRM_PRODUCT_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(i,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"ownerUserName",width:"120"}),e(i,{label:"\u66F4\u65B0\u65F6\u95F4",align:"center",prop:"updateTime",formatter:t(z),width:"180px"},null,8,["formatter"]),e(i,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName",width:"120"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(z),width:"180px"},null,8,["formatter"]),e(i,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"160"},{default:r(l=>[f((n(),m(c,{link:"",type:"primary",onClick:V=>S("update",l.row.id)},{default:r(()=>a[9]||(a[9]=[s(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["crm:product:update"]]]),f((n(),m(c,{link:"",type:"danger",onClick:V=>(async g=>{try{await y.delConfirm(),await ke(g),y.success(A("common.delSuccess")),await u()}catch{}})(l.row.id)},{default:r(()=>a[10]||(a[10]=[s(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["crm:product:delete"]]])]),_:1})]),_:1},8,["data"])),[[Q,t(b)]]),e(L,{total:t(k),page:t(o).pageNo,"onUpdate:page":a[3]||(a[3]=l=>t(o).pageNo=l),limit:t(o).pageSize,"onUpdate:limit":a[4]||(a[4]=l=>t(o).pageSize=l),onPagination:u},null,8,["total","page","limit"])]),_:1}),e(Re,{ref_key:"formRef",ref:T,onSuccess:u},null,512)],64)}}});export{Te as default};
