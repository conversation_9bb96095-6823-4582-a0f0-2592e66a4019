import{d as K,p as L,b as G,r as _,f as H,q as J,O,c as A,o as r,g as e,w as t,s as Q,a as n,v as j,P as B,Q as D,J as m,G as E,H as p,I as T,A as f,K as W,L as X,t as Y,a3 as Z,M as $,F as aa}from"./index-CvERnF9Y.js";import{_ as ea}from"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import{_ as la}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{a as ta,d as na,b as oa,e as ra}from"./index-CpH6tZ7i.js";import{_ as pa}from"./AccountForm.vue_vue_type_script_setup_true_lang-CrHSHjzj.js";import"./index-DHM6tdge.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";const ca=["src"],ia=K({name:"MpAccount",__name:"index",setup(sa){const u=L(),{t:S}=G(),w=_(!0),h=_(0),x=_([]),o=H({pageNo:1,pageSize:10,name:null,account:null,appId:null}),I=_(),d=async()=>{w.value=!0;try{const y=await ta(o);x.value=y.list,h.value=y.total}finally{w.value=!1}},k=()=>{o.pageNo=1,d()},N=()=>{I.value.resetFields(),k()},U=_(),q=(y,a)=>{U.value.open(y,a)};return J(()=>{d()}),(y,a)=>{const z=B,v=j,C=T,c=E,F=Q,P=la,i=X,M=W,R=ea,g=O("hasPermi"),V=$;return r(),A(aa,null,[e(P,null,{default:t(()=>[e(F,{class:"-mb-15px",model:n(o),ref_key:"queryFormRef",ref:I,inline:!0,"label-width":"68px"},{default:t(()=>[e(v,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[e(z,{modelValue:n(o).name,"onUpdate:modelValue":a[0]||(a[0]=l=>n(o).name=l),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:D(k,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(v,null,{default:t(()=>[e(c,{onClick:k},{default:t(()=>[e(C,{icon:"ep:search",class:"mr-5px"}),a[4]||(a[4]=p("\u641C\u7D22"))]),_:1}),e(c,{onClick:N},{default:t(()=>[e(C,{icon:"ep:refresh",class:"mr-5px"}),a[5]||(a[5]=p("\u91CD\u7F6E"))]),_:1}),m((r(),f(c,{type:"primary",onClick:a[1]||(a[1]=l=>q("create"))},{default:t(()=>[e(C,{icon:"ep:plus",class:"mr-5px"}),a[6]||(a[6]=p(" \u65B0\u589E "))]),_:1})),[[g,["mp:account:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:t(()=>[m((r(),f(M,{data:n(x)},{default:t(()=>[e(i,{label:"\u540D\u79F0",align:"center",prop:"name"}),e(i,{label:"\u5FAE\u4FE1\u53F7",align:"center",prop:"account",width:"180"}),e(i,{label:"appId",align:"center",prop:"appId",width:"180"}),e(i,{label:"\u670D\u52A1\u5668\u5730\u5740(URL)",align:"center",prop:"appId",width:"360"},{default:t(l=>[p(Y("http://\u670D\u52A1\u7AEF\u5730\u5740/admin-api/mp/open/"+l.row.appId),1)]),_:1}),e(i,{label:"\u4E8C\u7EF4\u7801",align:"center",prop:"qrCodeUrl"},{default:t(l=>[l.row.qrCodeUrl?(r(),A("img",{key:0,src:l.row.qrCodeUrl,alt:"\u4E8C\u7EF4\u7801",style:{display:"inline-block",height:"100px"}},null,8,ca)):Z("",!0),m((r(),f(c,{link:"",type:"primary",onClick:b=>(async s=>{try{await u.confirm('\u662F\u5426\u786E\u8BA4\u751F\u6210\u516C\u4F17\u53F7\u8D26\u53F7\u7F16\u53F7\u4E3A"'+s.name+'"\u7684\u4E8C\u7EF4\u7801?'),await oa(s.id),u.success("\u751F\u6210\u4E8C\u7EF4\u7801\u6210\u529F"),await d()}catch{}})(l.row)},{default:t(()=>a[7]||(a[7]=[p(" \u751F\u6210\u4E8C\u7EF4\u7801 ")])),_:2},1032,["onClick"])),[[g,["mp:account:qr-code"]]])]),_:1}),e(i,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),e(i,{label:"\u64CD\u4F5C",align:"center"},{default:t(l=>[m((r(),f(c,{link:"",type:"primary",onClick:b=>q("update",l.row.id)},{default:t(()=>a[8]||(a[8]=[p(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[g,["mp:account:update"]]]),m((r(),f(c,{link:"",type:"danger",onClick:b=>(async s=>{try{await u.delConfirm(),await na(s),u.success(S("common.delSuccess")),await d()}catch{}})(l.row.id)},{default:t(()=>a[9]||(a[9]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["mp:account:delete"]]]),m((r(),f(c,{link:"",type:"danger",onClick:b=>(async s=>{try{await u.confirm('\u662F\u5426\u786E\u8BA4\u6E05\u7A7A\u751F\u6210\u516C\u4F17\u53F7\u8D26\u53F7\u7F16\u53F7\u4E3A"'+s.name+'"\u7684 API \u914D\u989D?'),await ra(s.id),u.success("\u6E05\u7A7A API \u914D\u989D\u6210\u529F")}catch{}})(l.row)},{default:t(()=>a[10]||(a[10]=[p(" \u6E05\u7A7A API \u914D\u989D ")])),_:2},1032,["onClick"])),[[g,["mp:account:clear-quota"]]])]),_:1})]),_:1},8,["data"])),[[V,n(w)]]),e(R,{total:n(h),page:n(o).pageNo,"onUpdate:page":a[2]||(a[2]=l=>n(o).pageNo=l),limit:n(o).pageSize,"onUpdate:limit":a[3]||(a[3]=l=>n(o).pageSize=l),onPagination:d},null,8,["total","page","limit"])]),_:1}),e(pa,{ref_key:"formRef",ref:U,onSuccess:d},null,512)],64)}}});export{ia as default};
