import{_ as f}from"./app-nav-bar-mp-QvSN8lzY.js";import h from"./index-MVA5HPyZ.js";import{d as w,a2 as l,c as t,o as p,i as v,a3 as b,F as x,y as _,aw as c,A as k,t as C,a as o,_ as I}from"./index-CRsFgzy0.js";const M={class:"h-full w-full flex items-center"},R={key:0},A=["src"],B={key:0,src:f,alt:"",class:"h-30px w-86px"},F=I(w({name:"NavigationBar",__name:"index",props:{property:{}},setup(y){const e=y,n=l(()=>({background:e.property.bgType==="img"&&e.property.bgImg?`url(${e.property.bgImg}) no-repeat top center / 100% 100%`:e.property.bgColor})),d=l(()=>{var r;return(r=e.property._local)!=null&&r.previewMp?e.property.mpCells:e.property.otherCells}),s=l(()=>{var r;return(r=e.property._local)!=null&&r.previewMp?209/6:285/8}),u=r=>({width:r.width*s.value+10*(r.width-1)+"px",left:r.left*s.value+10*(r.left+1)+"px",position:"absolute"}),m=l(()=>r=>({height:30,showScan:!1,placeholder:r.placeholder,borderRadius:r.borderRadius}));return(r,N)=>{var i;return p(),t("div",{class:"navigation-bar",style:c(o(n))},[v("div",M,[(p(!0),t(x,null,_(o(d),(a,g)=>(p(),t("div",{key:g,style:c(u(a))},[a.type==="text"?(p(),t("span",R,C(a.text),1)):a.type==="image"?(p(),t("img",{key:1,src:a.imgUrl,alt:"",class:"h-full w-full"},null,8,A)):(p(),k(h,{key:2,property:o(m)(a)},null,8,["property"]))],4))),128))]),(i=r.property._local)!=null&&i.previewMp?(p(),t("img",B)):b("",!0)],4)}}}),[["__scopeId","data-v-c302c726"]]);export{F as default};
