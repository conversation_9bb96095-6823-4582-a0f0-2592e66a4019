import{as as t}from"./index-CvERnF9Y.js";const o=async a=>await t.get({url:"/promotion/article/page",params:a}),r=async a=>await t.get({url:"/promotion/article/get?id="+a}),e=async a=>await t.post({url:"/promotion/article/create",data:a}),i=async a=>await t.put({url:"/promotion/article/update",data:a}),s=async a=>await t.delete({url:"/promotion/article/delete?id="+a});export{o as a,e as c,s as d,r as g,i as u};
