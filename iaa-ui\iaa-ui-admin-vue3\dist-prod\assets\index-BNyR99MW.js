import{d as D,p as J,r as g,f as L,q as X,O as z,c as B,o as C,g as c,i as O,w as n,h as K,a as e,eZ as f,aR as d,E as M,J as N,A as Q,G as U,H as V,I as W,k as Y,F as $,ck as ee,_ as ae}from"./index-CvERnF9Y.js";import{E as te}from"./el-skeleton-item-CIGs_6QW.js";import{_ as re}from"./Echart.vue_vue_type_script_setup_true_lang-D9HJSbBT.js";import{_ as le}from"./index.vue_vue_type_script_setup_true_lang-PqRfzyQf.js";import{_ as oe}from"./index-CeUx6j9a.js";import{c as ie,d as ce,e as se,f as ne}from"./trade-ChL0DcQe.js";import{_ as b}from"./TradeStatisticValue.vue_vue_type_script_setup_true_lang-BvanbXTt.js";import{_ as y}from"./index.vue_vue_type_script_setup_true_lang-CZoePPkw.js";import{d as ue}from"./download-oWiM5xVU.js";import{C as de}from"./CardTitle-CdidxETN.js";import{n as me,f as pe}from"./formatTime-CmW2_KRq.js";import"./echarts-BcS7Kngw.js";import"./CountTo.vue_vue_type_script_setup_true_lang-DkUXM3W0.js";const fe={class:"flex flex-col"},ve={class:"flex flex-row items-center justify-between"},Pe=ae(D({name:"TradeStatistics",__name:"index",setup(ye){const S=J(),_=g(!0),w=g(!1),u=g(),s=g(),x=g(),h=L({dataset:{dimensions:["date","turnoverPrice","orderPayPrice","rechargePrice","expensePrice"],source:[]},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50},series:[{name:"\u8425\u4E1A\u989D",type:"line",smooth:!0},{name:"\u5546\u54C1\u652F\u4ED8\u91D1\u989D",type:"line",smooth:!0},{name:"\u5145\u503C\u91D1\u989D",type:"line",smooth:!0},{name:"\u652F\u51FA\u91D1\u989D",type:"line",smooth:!0}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u4EA4\u6613\u72B6\u51B5"}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",boundaryGap:!1,axisTick:{show:!1}},yAxis:{axisTick:{show:!1}}}),A=async()=>{_.value=!0;const m=x.value.times;me(m[0],m[1])&&(m[0]=pe(ee(m[0]).subtract(1,"d"))),await Promise.all([R(),I()]),_.value=!1},R=async()=>{const m=x.value.times;s.value=await ce({times:m})},I=async()=>{const m=x.value.times,P=await se({times:m});for(let v of P)v.turnoverPrice=d(v.turnoverPrice),v.orderPayPrice=d(v.orderPayPrice),v.rechargePrice=d(v.rechargePrice),v.expensePrice=d(v.expensePrice);h.dataset&&h.dataset.source&&(h.dataset.source=P)},T=async()=>{try{await S.exportConfirm(),w.value=!0;const m=x.value.times,P=await ne({times:m});ue.excel(P,"\u4EA4\u6613\u72B6\u51B5.xls")}catch{}finally{w.value=!1}};return X(async()=>{await(async()=>{u.value=await ie()})()}),(m,P)=>{const v=oe,p=K,k=M,j=W,E=U,F=le,G=re,H=te,Z=Y,q=z("hasPermi");return C(),B($,null,[c(v,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),O("div",fe,[c(k,{gutter:16,class:"summary"},{default:n(()=>[c(p,{sm:6,xs:12},{default:n(()=>{var a,t,r,l,o,i;return[c(b,{tooltip:"\u6628\u65E5\u8BA2\u5355\u6570\u91CF",title:"\u6628\u65E5\u8BA2\u5355\u6570\u91CF",value:((t=(a=e(u))==null?void 0:a.value)==null?void 0:t.yesterdayOrderCount)||0,percent:e(f)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.yesterdayOrderCount,(i=(o=e(u))==null?void 0:o.reference)==null?void 0:i.yesterdayOrderCount)},null,8,["value","percent"])]}),_:1}),c(p,{sm:6,xs:12},{default:n(()=>{var a,t,r,l,o,i;return[c(b,{tooltip:"\u672C\u6708\u8BA2\u5355\u6570\u91CF",title:"\u672C\u6708\u8BA2\u5355\u6570\u91CF",value:((t=(a=e(u))==null?void 0:a.value)==null?void 0:t.monthOrderCount)||0,percent:e(f)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.monthOrderCount,(i=(o=e(u))==null?void 0:o.reference)==null?void 0:i.monthOrderCount)},null,8,["value","percent"])]}),_:1}),c(p,{sm:6,xs:12},{default:n(()=>{var a,t,r,l,o,i;return[c(b,{tooltip:"\u6628\u65E5\u652F\u4ED8\u91D1\u989D",title:"\u6628\u65E5\u652F\u4ED8\u91D1\u989D",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(u))==null?void 0:a.value)==null?void 0:t.yesterdayPayPrice)||0),percent:e(f)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.yesterdayPayPrice,(i=(o=e(u))==null?void 0:o.reference)==null?void 0:i.yesterdayPayPrice)},null,8,["value","percent"])]}),_:1}),c(p,{sm:6,xs:12},{default:n(()=>{var a,t,r,l,o,i;return[c(b,{tooltip:"\u672C\u6708\u652F\u4ED8\u91D1\u989D",title:"\u672C\u6708\u652F\u4ED8\u91D1\u989D",prefix:"\uFFE5",":decimals":2,value:e(d)(((t=(a=e(u))==null?void 0:a.value)==null?void 0:t.monthPayPrice)||0),percent:e(f)((l=(r=e(u))==null?void 0:r.value)==null?void 0:l.monthPayPrice,(i=(o=e(u))==null?void 0:o.reference)==null?void 0:i.monthPayPrice)},null,8,["value","percent"])]}),_:1})]),_:1}),c(Z,{shadow:"never"},{header:n(()=>[O("div",ve,[c(e(de),{title:"\u4EA4\u6613\u72B6\u51B5"}),c(F,{ref_key:"shortcutDateRangePicker",ref:x,onChange:A},{default:n(()=>[N((C(),Q(E,{class:"ml-4",onClick:T,loading:e(w)},{default:n(()=>[c(j,{icon:"ep:download",class:"mr-1"}),P[0]||(P[0]=V("\u5BFC\u51FA "))]),_:1},8,["loading"])),[[q,["statistics:trade:export"]]])]),_:1},512)])]),default:n(()=>[c(k,{gutter:16},{default:n(()=>[c(p,{md:6,sm:12,xs:24},{default:n(()=>{var a,t,r,l,o,i;return[c(y,{title:"\u8425\u4E1A\u989D",tooltip:"\u5546\u54C1\u652F\u4ED8\u91D1\u989D\u3001\u5145\u503C\u91D1\u989D",icon:"fa-solid:yen-sign","icon-color":"bg-blue-100","icon-bg-color":"text-blue-500",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(s))==null?void 0:a.value)==null?void 0:t.turnoverPrice)||0),percent:e(f)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.turnoverPrice,(i=(o=e(s))==null?void 0:o.reference)==null?void 0:i.turnoverPrice)},null,8,["value","percent"])]}),_:1}),c(p,{md:6,sm:12,xs:24},{default:n(()=>{var a,t,r,l,o,i;return[c(y,{title:"\u5546\u54C1\u652F\u4ED8\u91D1\u989D",tooltip:"\u7528\u6237\u8D2D\u4E70\u5546\u54C1\u7684\u5B9E\u9645\u652F\u4ED8\u91D1\u989D\uFF0C\u5305\u62EC\u5FAE\u4FE1\u652F\u4ED8\u3001\u4F59\u989D\u652F\u4ED8\u3001\u652F\u4ED8\u5B9D\u652F\u4ED8\u3001\u7EBF\u4E0B\u652F\u4ED8\u91D1\u989D\uFF08\u62FC\u56E2\u5546\u54C1\u5728\u6210\u56E2\u4E4B\u540E\u8BA1\u5165\uFF0C\u7EBF\u4E0B\u652F\u4ED8\u8BA2\u5355\u5728\u540E\u53F0\u786E\u8BA4\u652F\u4ED8\u540E\u8BA1\u5165\uFF09",icon:"fa-solid:shopping-cart","icon-color":"bg-purple-100","icon-bg-color":"text-purple-500",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(s))==null?void 0:a.value)==null?void 0:t.orderPayPrice)||0),percent:e(f)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.orderPayPrice,(i=(o=e(s))==null?void 0:o.reference)==null?void 0:i.orderPayPrice)},null,8,["value","percent"])]}),_:1}),c(p,{md:6,sm:12,xs:24},{default:n(()=>{var a,t,r,l,o,i;return[c(y,{title:"\u5145\u503C\u91D1\u989D",tooltip:"\u7528\u6237\u6210\u529F\u5145\u503C\u7684\u91D1\u989D",icon:"fa-solid:money-check-alt","icon-color":"bg-yellow-100","icon-bg-color":"text-yellow-500",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(s))==null?void 0:a.value)==null?void 0:t.rechargePrice)||0),percent:e(f)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.rechargePrice,(i=(o=e(s))==null?void 0:o.reference)==null?void 0:i.rechargePrice)},null,8,["value","percent"])]}),_:1}),c(p,{md:6,sm:12,xs:24},{default:n(()=>{var a,t,r,l,o,i;return[c(y,{title:"\u652F\u51FA\u91D1\u989D",tooltip:"\u4F59\u989D\u652F\u4ED8\u91D1\u989D\u3001\u652F\u4ED8\u4F63\u91D1\u91D1\u989D\u3001\u5546\u54C1\u9000\u6B3E\u91D1\u989D",icon:"ep:warning-filled","icon-color":"bg-green-100","icon-bg-color":"text-green-500",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(s))==null?void 0:a.value)==null?void 0:t.expensePrice)||0),percent:e(f)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.expensePrice,(i=(o=e(s))==null?void 0:o.reference)==null?void 0:i.expensePrice)},null,8,["value","percent"])]}),_:1}),c(p,{md:6,sm:12,xs:24},{default:n(()=>{var a,t,r,l,o,i;return[c(y,{title:"\u4F59\u989D\u652F\u4ED8\u91D1\u989D",tooltip:"\u7528\u6237\u4E0B\u5355\u65F6\u4F7F\u7528\u4F59\u989D\u5B9E\u9645\u652F\u4ED8\u7684\u91D1\u989D",icon:"fa-solid:wallet","icon-color":"bg-cyan-100","icon-bg-color":"text-cyan-500",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(s))==null?void 0:a.value)==null?void 0:t.walletPayPrice)||0),percent:e(f)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.walletPayPrice,(i=(o=e(s))==null?void 0:o.reference)==null?void 0:i.walletPayPrice)},null,8,["value","percent"])]}),_:1}),c(p,{md:6,sm:12,xs:24},{default:n(()=>{var a,t,r,l,o,i;return[c(y,{title:"\u652F\u4ED8\u4F63\u91D1\u91D1\u989D",tooltip:"\u540E\u53F0\u7ED9\u63A8\u5E7F\u5458\u652F\u4ED8\u7684\u63A8\u5E7F\u4F63\u91D1\uFF0C\u4EE5\u5B9E\u9645\u652F\u4ED8\u4E3A\u51C6",icon:"fa-solid:award","icon-color":"bg-yellow-100","icon-bg-color":"text-yellow-500",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(s))==null?void 0:a.value)==null?void 0:t.brokerageSettlementPrice)||0),percent:e(f)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.brokerageSettlementPrice,(i=(o=e(s))==null?void 0:o.reference)==null?void 0:i.brokerageSettlementPrice)},null,8,["value","percent"])]}),_:1}),c(p,{md:6,sm:12,xs:24},{default:n(()=>{var a,t,r,l,o,i;return[c(y,{title:"\u5546\u54C1\u9000\u6B3E\u91D1\u989D",tooltip:"\u7528\u6237\u6210\u529F\u9000\u6B3E\u7684\u5546\u54C1\u91D1\u989D",icon:"fa-solid:times-circle","icon-color":"bg-blue-100","icon-bg-color":"text-blue-500",prefix:"\uFFE5",decimals:2,value:e(d)(((t=(a=e(s))==null?void 0:a.value)==null?void 0:t.afterSaleRefundPrice)||0),percent:e(f)((l=(r=e(s))==null?void 0:r.value)==null?void 0:l.afterSaleRefundPrice,(i=(o=e(s))==null?void 0:o.reference)==null?void 0:i.afterSaleRefundPrice)},null,8,["value","percent"])]}),_:1})]),_:1}),c(H,{loading:e(_),animated:""},{default:n(()=>[c(G,{height:500,options:e(h)},null,8,["options"])]),_:1},8,["loading"])]),_:1})])],64)}}}),[["__scopeId","data-v-b0bf5ffb"]]);export{Pe as default};
