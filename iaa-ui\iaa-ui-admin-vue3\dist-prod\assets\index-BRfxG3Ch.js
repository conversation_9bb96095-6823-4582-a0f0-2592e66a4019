import{d as W,p as X,b as Z,r as m,f as ee,q as ae,O as le,c as C,o as s,g as e,w as t,s as te,a as i,v as ie,P as oe,Q as re,x as se,F as M,y as O,R as pe,D as z,A as c,B as ne,J as _,G as me,H as n,I as ce,K as ue,L as de,aE as fe,t as b,M as we,aR as _e}from"./index-CvERnF9Y.js";import{_ as ke}from"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import{_ as ye}from"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import{E as he}from"./el-image-DTDUrxnp.js";import{_ as be}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as ve}from"./index-CeUx6j9a.js";import{f as A,d as ge}from"./formatTime-CmW2_KRq.js";import{a as xe,b as Se,d as Ce}from"./seckillActivity-CtWT_KxB.js";import{S as Me}from"./seckillConfig-z4DVgq-x.js";import{_ as Te}from"./SeckillActivityForm.vue_vue_type_script_setup_true_lang-y-ZsLR4z.js";import{f as Ye}from"./formatter-UUK_ohaG.js";import"./index-DHM6tdge.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./Form-CtCwm2Jr.js";import"./el-virtual-list-Dr8vLDdU.js";import"./el-tree-select-CD6tMKW2.js";import"./el-time-select-BhR0Pja8.js";import"./InputPassword-BrncDU0y.js";import"./tsxHelper-hGg2Sis1.js";import"./SpuSelect.vue_vue_type_script_setup_true_lang-rLwAfuNU.js";import"./index-CbwqS0qc.js";import"./SkuList.vue_vue_type_script_setup_true_lang-DiERrdvE.js";import"./tree-COGD3qag.js";import"./category-Cruo05cH.js";import"./spu-D1cu9Tn-.js";import"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-CftpH64G.js";import"./formRules-D4R_42fv.js";import"./useCrudSchemas-BOoWixRT.js";const Ne=W({name:"SeckillActivity",__name:"index",setup(Ue){const k=X(),{t:R}=Z(),v=m(!0),T=m(0),Y=m([]),r=ee({pageNo:1,pageSize:10,name:null,status:null}),N=m();m(!1);const u=async()=>{v.value=!0;try{const p=await xe(r);Y.value=p.list,T.value=p.total}finally{v.value=!1}},g=()=>{r.pageNo=1,u()},$=()=>{N.value.resetFields(),g()},U=m(),P=(p,a)=>{U.value.open(p,a)},V=m([]),F=p=>{const a=V.value.find(f=>f.id===p);return a!=null?`${a.name}[${a.startTime} ~ ${a.endTime}]`:""},I=p=>{const a=Math.min(...p.map(f=>f.seckillPrice));return`\uFFE5${_e(a)}`};return ae(async()=>{await u(),V.value=await Me.getSimpleSeckillConfigList()}),(p,a)=>{const f=ve,q=oe,x=ie,B=ne,E=se,S=ce,d=me,H=te,D=be,o=de,K=fe,L=he,G=ye,J=ue,Q=ke,y=le("hasPermi"),j=we;return s(),C(M,null,[e(f,{title:"\u3010\u8425\u9500\u3011\u79D2\u6740\u6D3B\u52A8",url:"https://doc.iocoder.cn/mall/promotion-seckill/"}),e(D,null,{default:t(()=>[e(H,{class:"-mb-15px",model:i(r),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:t(()=>[e(x,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name"},{default:t(()=>[e(q,{modelValue:i(r).name,"onUpdate:modelValue":a[0]||(a[0]=l=>i(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u6D3B\u52A8\u540D\u79F0",clearable:"",onKeyup:re(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(x,{label:"\u6D3B\u52A8\u72B6\u6001",prop:"status"},{default:t(()=>[e(E,{modelValue:i(r).status,"onUpdate:modelValue":a[1]||(a[1]=l=>i(r).status=l),placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),C(M,null,O(i(pe)(i(z).COMMON_STATUS),l=>(s(),c(B,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(x,null,{default:t(()=>[e(d,{onClick:g},{default:t(()=>[e(S,{icon:"ep:search",class:"mr-5px"}),a[5]||(a[5]=n(" \u641C\u7D22"))]),_:1}),e(d,{onClick:$},{default:t(()=>[e(S,{icon:"ep:refresh",class:"mr-5px"}),a[6]||(a[6]=n(" \u91CD\u7F6E"))]),_:1}),_((s(),c(d,{type:"primary",plain:"",onClick:a[2]||(a[2]=l=>P("create"))},{default:t(()=>[e(S,{icon:"ep:plus",class:"mr-5px"}),a[7]||(a[7]=n(" \u65B0\u589E "))]),_:1})),[[y,["promotion:seckill-activity:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(D,null,{default:t(()=>[_((s(),c(J,{data:i(Y),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(o,{label:"\u6D3B\u52A8\u7F16\u53F7",prop:"id","min-width":"80"}),e(o,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name","min-width":"140"}),e(o,{label:"\u79D2\u6740\u65F6\u6BB5",prop:"configIds",width:"220px","show-overflow-tooltip":!1},{default:t(l=>[(s(!0),C(M,null,O(l.row.configIds,(h,w)=>(s(),c(K,{key:w,class:"mr-5px"},{default:t(()=>[n(b(F(h)),1)]),_:2},1024))),128))]),_:1}),e(o,{label:"\u6D3B\u52A8\u65F6\u95F4","min-width":"210"},{default:t(l=>[n(b(i(A)(l.row.startTime,"YYYY-MM-DD"))+" ~ "+b(i(A)(l.row.endTime,"YYYY-MM-DD")),1)]),_:1}),e(o,{label:"\u5546\u54C1\u56FE\u7247",prop:"spuName","min-width":"80"},{default:t(l=>[e(L,{src:l.row.picUrl,class:"h-40px w-40px","preview-src-list":[l.row.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),e(o,{label:"\u5546\u54C1\u6807\u9898",prop:"spuName","min-width":"300"}),e(o,{label:"\u539F\u4EF7",prop:"marketPrice","min-width":"100",formatter:i(Ye)},null,8,["formatter"]),e(o,{label:"\u539F\u4EF7",prop:"marketPrice","min-width":"100"}),e(o,{label:"\u79D2\u6740\u4EF7",prop:"seckillPrice","min-width":"100"},{default:t(l=>[n(b(I(l.row.products)),1)]),_:1}),e(o,{label:"\u6D3B\u52A8\u72B6\u6001",align:"center",prop:"status","min-width":"100"},{default:t(l=>[e(G,{type:i(z).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(o,{label:"\u5E93\u5B58",align:"center",prop:"stock","min-width":"80"}),e(o,{label:"\u603B\u5E93\u5B58",align:"center",prop:"totalStock","min-width":"80"}),e(o,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:i(ge),width:"180px"},null,8,["formatter"]),e(o,{label:"\u64CD\u4F5C",align:"center",width:"150px",fixed:"right"},{default:t(l=>[_((s(),c(d,{link:"",type:"primary",onClick:h=>P("update",l.row.id)},{default:t(()=>a[8]||(a[8]=[n(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["promotion:seckill-activity:update"]]]),l.row.status===0?_((s(),c(d,{key:0,link:"",type:"danger",onClick:h=>(async w=>{try{await k.confirm("\u786E\u8BA4\u5173\u95ED\u8BE5\u79D2\u6740\u6D3B\u52A8\u5417\uFF1F"),await Se(w),k.success("\u5173\u95ED\u6210\u529F"),await u()}catch{}})(l.row.id)},{default:t(()=>a[9]||(a[9]=[n(" \u5173\u95ED ")])),_:2},1032,["onClick"])),[[y,["promotion:seckill-activity:close"]]]):_((s(),c(d,{key:1,link:"",type:"danger",onClick:h=>(async w=>{try{await k.delConfirm(),await Ce(w),k.success(R("common.delSuccess")),await u()}catch{}})(l.row.id)},{default:t(()=>a[10]||(a[10]=[n(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["promotion:seckill-activity:delete"]]])]),_:1})]),_:1},8,["data"])),[[j,i(v)]]),e(Q,{total:i(T),page:i(r).pageNo,"onUpdate:page":a[3]||(a[3]=l=>i(r).pageNo=l),limit:i(r).pageSize,"onUpdate:limit":a[4]||(a[4]=l=>i(r).pageSize=l),onPagination:u},null,8,["total","page","limit"])]),_:1}),e(Te,{ref_key:"formRef",ref:U,onSuccess:u},null,512)],64)}}});export{Ne as default};
