import{d as p,c as o,o as t,i,aw as e}from"./index-CRsFgzy0.js";const s=p({name:"Divider",__name:"index",props:{property:{}},setup:l=>(r,a)=>(t(),o("div",{class:"flex items-center",style:e({height:r.property.height+"px"})},[i("div",{class:"w-full",style:e({borderTopStyle:r.property.borderType,borderTopColor:r.property.lineColor,borderTopWidth:`${r.property.lineWidth}px`,margin:r.property.paddingType==="none"?"0":"0px 16px"})},null,4)],4))});export{s as default};
