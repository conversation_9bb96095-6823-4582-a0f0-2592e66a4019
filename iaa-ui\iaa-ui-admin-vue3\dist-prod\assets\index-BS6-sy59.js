import{d as m,r as x,c as e,o as a,g as r,I as g,a3 as f,w as s,F as v,y as h,A as b,t as o,a as k}from"./index-CRsFgzy0.js";import{E as _,a as w}from"./el-carousel-item-CR2zrYVC.js";import{E as j}from"./el-image-BQpHFDaE.js";const C={key:0,class:"h-250px flex items-center justify-center bg-gray-3"},E={key:1,class:"relative"},A={key:0,class:"absolute bottom-10px right-10px rounded-xl bg-black p-x-8px p-y-2px text-10px text-white opacity-40"},F=m({name:"Carousel",__name:"index",props:{property:{}},setup(I){const p=x(0),i=t=>{p.value=t+1};return(t,U)=>{const l=g,n=j,y=_,c=w;return t.property.items.length===0?(a(),e("div",C,[r(l,{icon:"tdesign:image",class:"text-gray-8 text-120px!"})])):(a(),e("div",E,[r(c,{height:"174px",type:t.property.type==="card"?"card":"",autoplay:t.property.autoplay,interval:1e3*t.property.interval,"indicator-position":t.property.indicator==="number"?"none":void 0,onChange:i},{default:s(()=>[(a(!0),e(v,null,h(t.property.items,(u,d)=>(a(),b(y,{key:d},{default:s(()=>[r(n,{class:"h-full w-full",src:u.imgUrl},null,8,["src"])]),_:2},1024))),128))]),_:1},8,["type","autoplay","interval","indicator-position"]),t.property.indicator==="number"?(a(),e("div",A,o(k(p))+" / "+o(t.property.items.length),1)):f("",!0)]))}}});export{F as default};
