import{d as x,c as i,o,i as e,g as t,w as n,I as r}from"./index-CRsFgzy0.js";import{E as l}from"./el-avatar-Nl9DW69B.js";const f={class:"flex flex-col"},c={class:"flex items-center justify-between p-x-18px p-y-24px"},d={class:"flex flex-1 items-center gap-16px"},m=x({name:"UserCard",__name:"index",props:{property:{}},setup:u=>(y,s)=>{const p=r,a=l;return o(),i("div",f,[e("div",c,[e("div",d,[t(a,{size:60},{default:n(()=>[t(p,{icon:"ep:avatar",size:60})]),_:1}),s[0]||(s[0]=e("span",{class:"text-18px font-bold"},"\u828B\u9053\u6E90\u7801",-1))]),t(p,{icon:"tdesign:qrcode",size:20})]),s[1]||(s[1]=e("div",{class:"flex items-center justify-between justify-between bg-white p-x-20px p-y-8px text-12px"},[e("span",{class:"color-#ff690d"},"\u70B9\u51FB\u7ED1\u5B9A\u624B\u673A\u53F7"),e("span",{class:"rounded-26px bg-#ff6100 p-x-8px p-y-5px color-white"},"\u53BB\u7ED1\u5B9A")],-1))])}});export{m as default};
