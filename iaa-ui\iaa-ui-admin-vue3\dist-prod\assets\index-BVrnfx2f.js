import{as as e}from"./index-CRsFgzy0.js";const r=a=>e.get({url:"/infra/config/page",params:a}),i=a=>e.get({url:"/infra/config/get?id="+a}),t=a=>e.get({url:"/infra/config/get-value-by-key?key="+a}),n=a=>e.post({url:"/infra/config/create",data:a}),f=a=>e.put({url:"/infra/config/update",data:a}),o=a=>e.delete({url:"/infra/config/delete?id="+a}),g=a=>e.download({url:"/infra/config/export",params:a});export{r as a,t as b,n as c,o as d,g as e,i as g,f as u};
