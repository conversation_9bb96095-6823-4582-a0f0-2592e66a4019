import{d as L,p as O,b as Q,r as d,f as j,q as W,O as Z,c as U,o as s,g as e,w as o,s as $,a as l,v as ee,P as ae,Q as le,x as te,F as D,y as oe,R as re,D as E,A as u,B as se,C as ne,J as f,G as ie,H as m,I as pe,K as de,L as ue,M as me}from"./index-CRsFgzy0.js";import{_ as ce}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as fe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as xe}from"./index-DYfNUK1u.js";import{d as R}from"./formatTime-DhdtkSIS.js";import{d as ye}from"./download-oWiM5xVU.js";import{d as ge,e as we,f as be}from"./index-CAxVHOSV.js";import{_ as ve}from"./Demo03StudentForm.vue_vue_type_script_setup_true_lang-8Gi5VxGn.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./Demo03CourseForm.vue_vue_type_script_setup_true_lang-DNMjTWDS.js";import"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-CVdy109u.js";const he=L({name:"Demo03Student",__name:"index",setup(Se){const g=O(),{t:Y}=Q(),w=d(!0),h=d([]),S=d(0),r=j({pageNo:1,pageSize:10,name:null,sex:null,description:null,createTime:[]}),k=d(),b=d(!1),c=async()=>{w.value=!0;try{const n=await ge(r);h.value=n.list,S.value=n.total}finally{w.value=!1}},v=()=>{r.pageNo=1,c()},M=()=>{k.value.resetFields(),v()},C=d(),V=(n,a)=>{C.value.open(n,a)},z=async()=>{try{await g.exportConfirm(),b.value=!0;const n=await be(r);ye.excel(n,"\u5B66\u751F.xls")}catch{}finally{b.value=!1}};return W(()=>{c()}),(n,a)=>{const H=xe,N=ae,_=ee,F=se,P=te,q=ne,x=pe,p=ie,G=$,T=_e,i=ue,K=fe,X=de,A=ce,y=Z("hasPermi"),B=me;return s(),U(D,null,[e(H,{title:"\u4EE3\u7801\u751F\u6210\uFF08\u4E3B\u5B50\u8868\uFF09",url:"https://doc.iocoder.cn/new-feature/master-sub/"}),e(T,null,{default:o(()=>[e(G,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:o(()=>[e(_,{label:"\u540D\u5B57",prop:"name"},{default:o(()=>[e(N,{modelValue:l(r).name,"onUpdate:modelValue":a[0]||(a[0]=t=>l(r).name=t),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57",clearable:"",onKeyup:le(v,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,{label:"\u6027\u522B",prop:"sex"},{default:o(()=>[e(P,{modelValue:l(r).sex,"onUpdate:modelValue":a[1]||(a[1]=t=>l(r).sex=t),placeholder:"\u8BF7\u9009\u62E9\u6027\u522B",clearable:"",class:"!w-240px"},{default:o(()=>[(s(!0),U(D,null,oe(l(re)(l(E).SYSTEM_USER_SEX),t=>(s(),u(F,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:o(()=>[e(q,{modelValue:l(r).createTime,"onUpdate:modelValue":a[2]||(a[2]=t=>l(r).createTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(_,null,{default:o(()=>[e(p,{onClick:v},{default:o(()=>[e(x,{icon:"ep:search",class:"mr-5px"}),a[6]||(a[6]=m(" \u641C\u7D22"))]),_:1}),e(p,{onClick:M},{default:o(()=>[e(x,{icon:"ep:refresh",class:"mr-5px"}),a[7]||(a[7]=m(" \u91CD\u7F6E"))]),_:1}),f((s(),u(p,{type:"primary",plain:"",onClick:a[3]||(a[3]=t=>V("create"))},{default:o(()=>[e(x,{icon:"ep:plus",class:"mr-5px"}),a[8]||(a[8]=m(" \u65B0\u589E "))]),_:1})),[[y,["infra:demo03-student:create"]]]),f((s(),u(p,{type:"success",plain:"",onClick:z,loading:l(b)},{default:o(()=>[e(x,{icon:"ep:download",class:"mr-5px"}),a[9]||(a[9]=m(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[y,["infra:demo03-student:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(T,null,{default:o(()=>[f((s(),u(X,{data:l(h),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[e(i,{label:"\u7F16\u53F7",align:"center",prop:"id"}),e(i,{label:"\u540D\u5B57",align:"center",prop:"name"}),e(i,{label:"\u6027\u522B",align:"center",prop:"sex"},{default:o(t=>[e(K,{type:l(E).SYSTEM_USER_SEX,value:t.row.sex},null,8,["type","value"])]),_:1}),e(i,{label:"\u51FA\u751F\u65E5\u671F",align:"center",prop:"birthday",formatter:l(R),width:"180px"},null,8,["formatter"]),e(i,{label:"\u7B80\u4ECB",align:"center",prop:"description"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(R),width:"180px"},null,8,["formatter"]),e(i,{label:"\u64CD\u4F5C",align:"center"},{default:o(t=>[f((s(),u(p,{link:"",type:"primary",onClick:I=>V("update",t.row.id)},{default:o(()=>a[10]||(a[10]=[m(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:update"]]]),f((s(),u(p,{link:"",type:"danger",onClick:I=>(async J=>{try{await g.delConfirm(),await we(J),g.success(Y("common.delSuccess")),await c()}catch{}})(t.row.id)},{default:o(()=>a[11]||(a[11]=[m(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[B,l(w)]]),e(A,{total:l(S),page:l(r).pageNo,"onUpdate:page":a[4]||(a[4]=t=>l(r).pageNo=t),limit:l(r).pageSize,"onUpdate:limit":a[5]||(a[5]=t=>l(r).pageSize=t),onPagination:c},null,8,["total","page","limit"])]),_:1}),e(ve,{ref_key:"formRef",ref:C,onSuccess:c},null,512)],64)}}});export{he as default};
