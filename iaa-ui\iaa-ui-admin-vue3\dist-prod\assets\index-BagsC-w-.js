import{d as L,p as Q,b as W,r as d,f as $,q as j,O as X,c as T,o as p,g as e,w as l,s as Z,a as o,v as ee,P as ae,Q as te,x as le,F as V,y as oe,R as ie,D,A as u,B as re,J as f,G as pe,H as s,I as se,K as ne,L as me,t as x,M as ce,aR as ue}from"./index-CRsFgzy0.js";import{_ as de}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as fe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as we}from"./el-image-BQpHFDaE.js";import{_ as be}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as _e}from"./index-DYfNUK1u.js";import{f as O,d as ye}from"./formatTime-DhdtkSIS.js";import{a as he,b as ve,d as ge}from"./combinationActivity-DX3uo1WR.js";import{_ as ke}from"./CombinationActivityForm.vue_vue_type_script_setup_true_lang-DJ7k20tr.js";import{f as xe}from"./formatter-D3GpDdeL.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./Form-BF4H89jq.js";import"./el-virtual-list-AoPW9ghL.js";import"./el-tree-select-BijZG_HG.js";import"./el-time-select-DBwK3NDO.js";import"./InputPassword-CO9Ecx-K.js";import"./tsxHelper-DqnKTMG3.js";import"./formRules-V2Qetfkc.js";import"./useCrudSchemas-CNYomGr4.js";import"./tree-COGD3qag.js";import"./SpuSelect.vue_vue_type_script_setup_true_lang-tSVtv9n7.js";import"./index-CQgbu5O7.js";import"./SkuList.vue_vue_type_script_setup_true_lang-ChNgeWhW.js";import"./category--cl9fhwU.js";import"./spu-BHhhuUrI.js";import"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-DGosLJYC.js";const Ce=L({name:"PromotionBargainActivity",__name:"index",setup(Me){const w=Q(),{t:A}=W(),_=d(!0),C=d(0),M=d([]),r=$({pageNo:1,pageSize:10,name:null,status:null}),S=d(),m=async()=>{_.value=!0;try{const n=await he(r);M.value=n.list,C.value=n.total}finally{_.value=!1}},y=()=>{r.pageNo=1,m()},R=()=>{S.value.resetFields(),y()},U=d(),N=(n,t)=>{U.value.open(n,t)},z=n=>{const t=Math.min(...n.map(h=>h.combinationPrice));return`\uFFE5${ue(t)}`};return j(async()=>{await m()}),(n,t)=>{const h=_e,F=ae,v=ee,q=re,B=le,g=se,c=pe,H=Z,Y=be,i=me,K=we,E=fe,G=ne,I=de,b=X("hasPermi"),J=ce;return p(),T(V,null,[e(h,{title:"\u3010\u8425\u9500\u3011\u62FC\u56E2\u6D3B\u52A8",url:"https://doc.iocoder.cn/mall/promotion-combination/"}),e(Y,null,{default:l(()=>[e(H,{ref_key:"queryFormRef",ref:S,inline:!0,model:o(r),class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(v,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name"},{default:l(()=>[e(F,{modelValue:o(r).name,"onUpdate:modelValue":t[0]||(t[0]=a=>o(r).name=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u6D3B\u52A8\u540D\u79F0",onKeyup:te(y,["enter"])},null,8,["modelValue"])]),_:1}),e(v,{label:"\u6D3B\u52A8\u72B6\u6001",prop:"status"},{default:l(()=>[e(B,{modelValue:o(r).status,"onUpdate:modelValue":t[1]||(t[1]=a=>o(r).status=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u72B6\u6001"},{default:l(()=>[(p(!0),T(V,null,oe(o(ie)(o(D).COMMON_STATUS),a=>(p(),u(q,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:l(()=>[e(c,{onClick:y},{default:l(()=>[e(g,{class:"mr-5px",icon:"ep:search"}),t[5]||(t[5]=s(" \u641C\u7D22 "))]),_:1}),e(c,{onClick:R},{default:l(()=>[e(g,{class:"mr-5px",icon:"ep:refresh"}),t[6]||(t[6]=s(" \u91CD\u7F6E "))]),_:1}),f((p(),u(c,{plain:"",type:"primary",onClick:t[2]||(t[2]=a=>N("create"))},{default:l(()=>[e(g,{class:"mr-5px",icon:"ep:plus"}),t[7]||(t[7]=s(" \u65B0\u589E "))]),_:1})),[[b,["promotion:combination-activity:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(Y,null,{default:l(()=>[f((p(),u(G,{data:o(M),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[e(i,{label:"\u6D3B\u52A8\u7F16\u53F7","min-width":"80",prop:"id"}),e(i,{label:"\u6D3B\u52A8\u540D\u79F0","min-width":"140",prop:"name"}),e(i,{label:"\u6D3B\u52A8\u65F6\u95F4","min-width":"210"},{default:l(a=>[s(x(o(O)(a.row.startTime,"YYYY-MM-DD"))+" ~ "+x(o(O)(a.row.endTime,"YYYY-MM-DD")),1)]),_:1}),e(i,{label:"\u5546\u54C1\u56FE\u7247","min-width":"80",prop:"spuName"},{default:l(a=>[e(K,{"preview-src-list":[a.row.picUrl],src:a.row.picUrl,class:"h-40px w-40px","preview-teleported":""},null,8,["preview-src-list","src"])]),_:1}),e(i,{label:"\u5546\u54C1\u6807\u9898","min-width":"300",prop:"spuName"}),e(i,{formatter:o(xe),label:"\u539F\u4EF7","min-width":"100",prop:"marketPrice"},null,8,["formatter"]),e(i,{label:"\u62FC\u56E2\u4EF7","min-width":"100",prop:"seckillPrice"},{default:l(a=>[s(x(z(a.row.products)),1)]),_:1}),e(i,{label:"\u5F00\u56E2\u7EC4\u6570","min-width":"100",prop:"groupCount"}),e(i,{label:"\u6210\u56E2\u7EC4\u6570","min-width":"100",prop:"groupSuccessCount"}),e(i,{label:"\u8D2D\u4E70\u6B21\u6570","min-width":"100",prop:"recordCount"}),e(i,{align:"center",label:"\u6D3B\u52A8\u72B6\u6001","min-width":"100",prop:"status"},{default:l(a=>[e(E,{type:o(D).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(i,{formatter:o(ye),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(i,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"150px"},{default:l(a=>[f((p(),u(c,{link:"",type:"primary",onClick:P=>N("update",a.row.id)},{default:l(()=>t[8]||(t[8]=[s(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[b,["promotion:combination-activity:update"]]]),a.row.status===0?f((p(),u(c,{key:0,link:"",type:"danger",onClick:P=>(async k=>{try{await w.confirm("\u786E\u8BA4\u5173\u95ED\u8BE5\u62FC\u56E2\u6D3B\u52A8\u5417\uFF1F"),await ve(k),w.success("\u5173\u95ED\u6210\u529F"),await m()}catch{}})(a.row.id)},{default:l(()=>t[9]||(t[9]=[s(" \u5173\u95ED ")])),_:2},1032,["onClick"])),[[b,["promotion:combination-activity:close"]]]):f((p(),u(c,{key:1,link:"",type:"danger",onClick:P=>(async k=>{try{await w.delConfirm(),await ge(k),w.success(A("common.delSuccess")),await m()}catch{}})(a.row.id)},{default:l(()=>t[10]||(t[10]=[s(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[b,["promotion:combination-activity:delete"]]])]),_:1})]),_:1},8,["data"])),[[J,o(_)]]),e(I,{limit:o(r).pageSize,"onUpdate:limit":t[3]||(t[3]=a=>o(r).pageSize=a),page:o(r).pageNo,"onUpdate:page":t[4]||(t[4]=a=>o(r).pageNo=a),total:o(C),onPagination:m},null,8,["limit","page","total"])]),_:1}),e(ke,{ref_key:"formRef",ref:U,onSuccess:m},null,512)],64)}}});export{Ce as default};
