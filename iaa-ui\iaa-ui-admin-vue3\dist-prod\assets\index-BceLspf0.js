import{as as a}from"./index-CRsFgzy0.js";const t={getSaleOrderPage:async e=>await a.get({url:"/erp/sale-order/page",params:e}),getSaleOrder:async e=>await a.get({url:"/erp/sale-order/get?id="+e}),createSaleOrder:async e=>await a.post({url:"/erp/sale-order/create",data:e}),updateSaleOrder:async e=>await a.put({url:"/erp/sale-order/update",data:e}),updateSaleOrderStatus:async(e,r)=>await a.put({url:"/erp/sale-order/update-status",params:{id:e,status:r}}),deleteSaleOrder:async e=>await a.delete({url:"/erp/sale-order/delete",params:{ids:e.join(",")}}),exportSaleOrder:async e=>await a.download({url:"/erp/sale-order/export-excel",params:e})};export{t as S};
