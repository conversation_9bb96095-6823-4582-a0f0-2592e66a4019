import{d as i,c as e,o as t,i as o,F as n,y as b,g as l,w as d,I as u,aw as p,t as m,_ as g}from"./index-CRsFgzy0.js";import{E as f}from"./el-image-BQpHFDaE.js";const v={class:"tab-bar"},_={class:"h-full w-full flex items-center justify-center"},k=i({name:"TabBar",__name:"index",props:{property:{}},setup:I=>(r,w)=>{const c=u,y=f;return t(),e("div",v,[o("div",{class:"tab-bar-bg",style:p({background:r.property.style.bgType==="color"?r.property.style.bgColor:`url(${r.property.style.bgImg})`,backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"})},[(t(!0),e(n,null,b(r.property.items,(a,s)=>(t(),e("div",{key:s,class:"tab-bar-item"},[l(y,{src:s===0?a.activeIconUrl:a.iconUrl},{error:d(()=>[o("div",_,[l(c,{icon:"ep:picture"})])]),_:2},1032,["src"]),o("span",{style:p({color:s===0?r.property.style.activeColor:r.property.style.color})},m(a.text),5)]))),128))],4)])}}),x=g(k,[["__scopeId","data-v-26ac6ad9"]]);export{x as default};
