import{as as a}from"./index-CvERnF9Y.js";const o={getStockPage:async t=>await a.get({url:"/erp/stock/page",params:t}),getStock:async t=>await a.get({url:"/erp/stock/get?id="+t}),getStock2:async(t,e)=>await a.get({url:"/erp/stock/get",params:{productId:t,warehouseId:e}}),getStockCount:async t=>await a.get({url:"/erp/stock/get-count",params:{productId:t}}),exportStock:async t=>await a.download({url:"/erp/stock/export-excel",params:t})};export{o as S};
