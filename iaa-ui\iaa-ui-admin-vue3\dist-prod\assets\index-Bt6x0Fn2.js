import{d as g,p as k,r,eW as y,eX as S,aK as C,q as M,aL as L,A,o as F,w as R,g as m,a as p,aq as w,f0 as x}from"./index-CvERnF9Y.js";import I from"./KeFuConversationList-8tE4uZ_0.js";import K from"./KeFuMessageList-CEHWMCrH.js";import N from"./MemberInfo-B0gqXrRP.js";import{u as T,W as l}from"./kefu-IVg7zuYK.js";import"./el-avatar-BLtz-X9T.js";import"./emoji-Co-48FgL.js";import"./formatTime-CmW2_KRq.js";import"./el-empty-Dr-80WPm.js";import"./el-image-DTDUrxnp.js";import"./EmojiSelectPopover.vue_vue_type_script_setup_true_lang-Dv9jBPEw.js";import"./PictureSelectUpload.vue_vue_type_script_setup_true_lang-CPyzjA4G.js";import"./picture-CTjip5lJ.js";import"./ProductItem-DXz4x8Nt.js";import"./OrderItem-DdEWxTw-.js";import"./constants-uird_4gU.js";import"./relativeTime-1RWAMv6O.js";import"./ProductBrowsingHistory.vue_vue_type_script_setup_true_lang-CmEGY6E-.js";import"./concat-CwbTxRkn.js";import"./OrderBrowsingHistory.vue_vue_type_script_setup_true_lang-BCKSUHFy.js";import"./index-BuF2kS0D.js";import"./CardTitle-CdidxETN.js";import"./UserBasicInfo-AyK4ay6L.js";import"./el-descriptions-item-imVgRiUQ.js";import"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import"./color-CIFUYK2M.js";import"./Descriptions.vue_vue_type_style_index_0_scoped_74d4336e_lang-GmfNoJhv.js";import"./el-collapse-transition-l0sNRNKZ.js";import"./DescriptionsItemLabel-BArHQO-q.js";import"./UserAccountInfo-DB_19eu4.js";import"./index-C_i5BwF_.js";import"./index-CX5iXL4F.js";const b=g({name:"KeFu",__name:"index",setup(q){const c=k(),a=T(),v=r("http://shouhou.iaa360.com/infra/ws".replace("http","ws")+"?token="+y()),{data:d,close:h,open:_}=S(v.value,{autoReconnect:!0,heartbeat:!0});C(()=>d.value,t=>{var o;if(t)try{if(t==="pong")return;const e=JSON.parse(t),s=e.type;if(!s)return void c.error("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B\uFF1A"+t);if(s===l.KEFU_MESSAGE_TYPE){const f=JSON.parse(e.content);return a.updateConversation(f.conversationId),void((o=i.value)==null?void 0:o.refreshMessageList(f))}s===l.KEFU_MESSAGE_ADMIN_READ&&a.updateConversationStatus(x(e.content))}catch{}},{immediate:!1});const i=r(),n=r(),E=t=>{var o,e;(o=i.value)==null||o.getNewMessageList(t),(e=n.value)==null||e.initHistory(t)},u=r();return M(()=>{a.setConversationList().then(()=>{var t;(t=u.value)==null||t.calculationLastMessageTime()}),_()}),L(()=>{h()}),(t,o)=>{const e=w;return F(),A(e,{class:"kefu-layout"},{default:R(()=>[m(p(I),{ref_key:"keFuConversationRef",ref:u,onChange:E},null,512),m(p(K),{ref_key:"keFuChatBoxRef",ref:i},null,512),m(p(N),{ref_key:"memberInfoRef",ref:n},null,512)]),_:1})}}});export{b as default};
