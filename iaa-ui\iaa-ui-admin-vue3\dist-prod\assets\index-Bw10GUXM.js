import{d as o,c as p,o as e,A as l,aw as t,_ as a}from"./index-CRsFgzy0.js";import{E as y}from"./el-image-BQpHFDaE.js";const u=["src","poster","autoplay"],c=a(o({name:"VideoPlayer",__name:"index",props:{property:{}},setup:d=>(r,i)=>{const s=y;return e(),p("div",{class:"w-full",style:t({height:`${r.property.style.height}px`})},[r.property.posterUrl?(e(),l(s,{key:0,class:"w-full w-full",src:r.property.posterUrl},null,8,["src"])):(e(),p("video",{key:1,class:"w-full w-full",src:r.property.videoUrl,poster:r.property.posterUrl,autoplay:r.property.autoplay,controls:""},null,8,u))],4)}}),[["__scopeId","data-v-04e61d53"]]);export{c as default};
