import{as as g,d as j,p as B,b as F,r as y,f as G,q as H,O as M,c as p,o as i,g as l,w as o,J as E,A as V,s as R,a as s,k as S,a3 as x,v as I,aB as T,aC as W,H as u,an as K,i as L,G as N,M as Q,F as X}from"./index-CRsFgzy0.js";import{_ as Y}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Z}from"./index-DYfNUK1u.js";import{C as $}from"./CardTitle-DCrrQp54.js";const ee={class:"flex items-center justify-between"},ae={key:0},le={key:0},se=j({name:"CrmCustomerPoolConfig",__name:"index",setup(oe){const D=B(),{t:w}=F(),d=y(!1),a=y({enabled:!1,contactExpireDays:void 0,dealExpireDays:void 0,notifyEnabled:!1,notifyDays:void 0}),h=G({enabled:[{required:!0,message:"\u662F\u5426\u542F\u7528\u5BA2\u6237\u516C\u6D77\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),c=y(),v=async()=>{try{d.value=!0;const n=await(async()=>await g.get({url:"/crm/customer-pool-config/get"}))();if(n===null)return;a.value=n}finally{d.value=!1}},C=async()=>{if(c&&await c.value.validate()){d.value=!0;try{const n=a.value;await(async e=>await g.put({url:"/crm/customer-pool-config/save",data:e}))(n),D.success(w("common.updateSuccess")),await v(),d.value=!1}finally{d.value=!1}}},k=()=>{a.value.enabled||(a.value.contactExpireDays=void 0,a.value.dealExpireDays=void 0,a.value.notifyEnabled=!1,a.value.notifyDays=void 0)},U=()=>{a.value.notifyEnabled||(a.value.notifyDays=void 0)};return H(()=>{v()}),(n,e)=>{const b=Z,z=N,r=W,_=T,m=I,f=K,O=S,q=R,A=Y,J=M("hasPermi"),P=Q;return i(),p(X,null,[l(b,{title:"\u3010\u5BA2\u6237\u3011\u5BA2\u6237\u7BA1\u7406\u3001\u516C\u6D77\u5BA2\u6237",url:"https://doc.iocoder.cn/crm/customer/"}),l(b,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),l(A,null,{default:o(()=>[E((i(),V(q,{ref_key:"formRef",ref:c,model:s(a),rules:s(h),"label-width":"160px"},{default:o(()=>[l(O,{shadow:"never"},{header:o(()=>[L("div",ee,[l(s($),{title:"\u5BA2\u6237\u516C\u6D77\u89C4\u5219\u8BBE\u7F6E"}),E((i(),V(z,{type:"primary",onClick:C},{default:o(()=>e[5]||(e[5]=[u(" \u4FDD\u5B58 ")])),_:1})),[[J,["crm:customer-pool-config:update"]]])])]),default:o(()=>[l(m,{label:"\u5BA2\u6237\u516C\u6D77\u89C4\u5219\u8BBE\u7F6E",prop:"enabled"},{default:o(()=>[l(_,{modelValue:s(a).enabled,"onUpdate:modelValue":e[0]||(e[0]=t=>s(a).enabled=t),onChange:k,class:"ml-4"},{default:o(()=>[l(r,{value:!1,size:"large"},{default:o(()=>e[6]||(e[6]=[u("\u4E0D\u542F\u7528")])),_:1}),l(r,{value:!0,size:"large"},{default:o(()=>e[7]||(e[7]=[u("\u542F\u7528")])),_:1})]),_:1},8,["modelValue"])]),_:1}),s(a).enabled?(i(),p("div",ae,[l(m,null,{default:o(()=>[l(f,{class:"mr-2",modelValue:s(a).contactExpireDays,"onUpdate:modelValue":e[1]||(e[1]=t=>s(a).contactExpireDays=t)},null,8,["modelValue"]),e[8]||(e[8]=u(" \u5929\u4E0D\u8DDF\u8FDB\u6216 ")),l(f,{class:"mx-2",modelValue:s(a).dealExpireDays,"onUpdate:modelValue":e[2]||(e[2]=t=>s(a).dealExpireDays=t)},null,8,["modelValue"]),e[9]||(e[9]=u(" \u5929\u672A\u6210\u4EA4 "))]),_:1}),l(m,{label:"\u63D0\u524D\u63D0\u9192\u8BBE\u7F6E",prop:"notifyEnabled"},{default:o(()=>[l(_,{modelValue:s(a).notifyEnabled,"onUpdate:modelValue":e[3]||(e[3]=t=>s(a).notifyEnabled=t),onChange:U,class:"ml-4"},{default:o(()=>[l(r,{value:!1,size:"large"},{default:o(()=>e[10]||(e[10]=[u("\u4E0D\u63D0\u9192")])),_:1}),l(r,{value:!0,size:"large"},{default:o(()=>e[11]||(e[11]=[u("\u63D0\u9192")])),_:1})]),_:1},8,["modelValue"])]),_:1}),s(a).notifyEnabled?(i(),p("div",le,[l(m,null,{default:o(()=>[e[12]||(e[12]=u(" \u63D0\u524D ")),l(f,{class:"mx-2",modelValue:s(a).notifyDays,"onUpdate:modelValue":e[4]||(e[4]=t=>s(a).notifyDays=t)},null,8,["modelValue"]),e[13]||(e[13]=u(" \u5929\u63D0\u9192 "))]),_:1})])):x("",!0)])):x("",!0)]),_:1})]),_:1},8,["model","rules"])),[[P,s(d)]])]),_:1})],64)}}});export{se as default};
