import{_ as r}from"./index-DYfNUK1u.js";import{_ as i}from"./ProductSummary.vue_vue_type_script_setup_true_lang-D4_d9SKG.js";import{_ as m}from"./ProductRank.vue_vue_type_script_setup_true_lang-ColWcgOw.js";import{d as p,c as s,o as a,g as t,F as c}from"./index-CRsFgzy0.js";import"./el-skeleton-item-CZ5buDOR.js";import"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import"./echarts-BcS7Kngw.js";import"./index.vue_vue_type_script_setup_true_lang-CWLo4KQn.js";import"./formatTime-DhdtkSIS.js";import"./product-D_J6xZ01.js";import"./index.vue_vue_type_script_setup_true_lang-J7g70ndY.js";import"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";import"./download-oWiM5xVU.js";import"./CardTitle-DCrrQp54.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./el-image-BQpHFDaE.js";import"./formatter-D3GpDdeL.js";const e=p({name:"ProductStatistics",__name:"index",setup:l=>(n,u)=>{const o=r;return a(),s(c,null,[t(o,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),t(i),t(m,{class:"mt-16px"})],64)}});export{e as default};
