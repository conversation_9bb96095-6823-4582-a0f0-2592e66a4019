import{d as u,r as e,q as m,c as p,o as n,g as t,w as f,J as y,a3 as h,a as r,A as _,M as x,F as b}from"./index-CRsFgzy0.js";import{_ as g}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as v}from"./IFrame.vue_vue_type_script_setup_true_lang-CgNW9vNM.js";import{_ as w}from"./index-DYfNUK1u.js";import{b as M}from"./index-BVrnfx2f.js";const k=u({name:"InfraDruid",__name:"index",setup(q){const s=e(!0),o=e("http://shouhou.iaa360.com/druid/index.html");return m(async()=>{try{const a=await M("url.druid");a&&a.length>0&&(o.value=a)}finally{s.value=!1}}),(a,A)=>{const i=w,d=v,l=g,c=x;return n(),p(b,null,[t(i,{title:"\u6570\u636E\u5E93 MyBatis",url:"https://doc.iocoder.cn/mybatis/"}),t(i,{title:"\u591A\u6570\u636E\u6E90\uFF08\u8BFB\u5199\u5206\u79BB\uFF09",url:"https://doc.iocoder.cn/dynamic-datasource/"}),t(l,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:f(()=>[r(s)?h("",!0):y((n(),_(d,{key:0,src:r(o)},null,8,["src"])),[[c,r(s)]])]),_:1})],64)}}});export{k as default};
