import{d as E,f as G,e as H,r as m,a2 as j,aK as J,q as L,c as h,o as v,g as e,w as t,s as M,a as r,v as O,C as Q,x as T,F as x,y as W,A as N,B as X,G as Z,H as g,I as $,l as ee,m as ae,n as le,h as re}from"./index-CRsFgzy0.js";import{_ as te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{E as oe}from"./el-tree-select-BijZG_HG.js";import{g as se}from"./index-C0yL_L5C.js";import{g as me}from"./index-D4y5Z4cM.js";import{f as i,e as q,g as R}from"./formatTime-DhdtkSIS.js";import{h as Y,d as ue}from"./tree-COGD3qag.js";import{_ as de}from"./ContractCountPerformance.vue_vue_type_script_setup_true_lang-Ht4ajm1E.js";import{_ as ne}from"./ContractPricePerformance.vue_vue_type_script_setup_true_lang-CKYbkgU-.js";import{_ as pe}from"./ReceivablePricePerformance.vue_vue_type_script_setup_true_lang-0oquWc31.js";import"./el-skeleton-item-CZ5buDOR.js";import"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import"./echarts-BcS7Kngw.js";import"./performance-C6IgBWCd.js";const ce=E({name:"CrmStatisticsCustomer",__name:"index",setup(fe){const l=G({deptId:H().getUser.deptId,userId:void 0,times:[i(q(new Date(new Date().getFullYear(),0,1))),i(R(new Date(new Date().getFullYear(),11,31)))]}),C=m(),b=m([]),P=m([]),F=j(()=>l.deptId?P.value.filter(n=>n.deptId===l.deptId):[]),d=m("ContractCountPerformance"),w=m(),I=m(),k=m(),_=()=>{var a,p,s,c,u,f;const n=parseInt(l.times[0]);switch(l.times[0]=i(q(new Date(n,0,1))),l.times[1]=i(R(new Date(n,11,31))),d.value){case"ContractCountPerformance":(p=(a=w.value)==null?void 0:a.loadData)==null||p.call(a);break;case"ContractPricePerformance":(c=(s=I.value)==null?void 0:s.loadData)==null||c.call(s);break;case"ReceivablePricePerformance":(f=(u=k.value)==null?void 0:u.loadData)==null||f.call(u)}};J(d,()=>{_()});const U=()=>{C.value.resetFields(),_()};return L(async()=>{b.value=Y(await se()),P.value=Y(await me())}),(n,a)=>{const p=Q,s=O,c=oe,u=X,f=T,D=$,V=Z,z=M,K=te,y=le,S=ee,A=re;return v(),h(x,null,[e(K,null,{default:t(()=>[e(z,{class:"-mb-15px",model:r(l),ref_key:"queryFormRef",ref:C,inline:!0,"label-width":"68px"},{default:t(()=>[e(s,{label:"\u9009\u62E9\u5E74\u4EFD",prop:"orderDate"},{default:t(()=>[e(p,{modelValue:r(l).times[0],"onUpdate:modelValue":a[0]||(a[0]=o=>r(l).times[0]=o),class:"!w-240px",type:"year","value-format":"YYYY","default-time":[new Date().getFullYear()]},null,8,["modelValue","default-time"])]),_:1}),e(s,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:t(()=>[e(c,{modelValue:r(l).deptId,"onUpdate:modelValue":a[1]||(a[1]=o=>r(l).deptId=o),class:"!w-240px",data:r(b),props:r(ue),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",onChange:a[2]||(a[2]=o=>r(l).userId=void 0)},null,8,["modelValue","data","props"])]),_:1}),e(s,{label:"\u5458\u5DE5",prop:"userId"},{default:t(()=>[e(f,{modelValue:r(l).userId,"onUpdate:modelValue":a[3]||(a[3]=o=>r(l).userId=o),class:"!w-240px",placeholder:"\u5458\u5DE5",clearable:""},{default:t(()=>[(v(!0),h(x,null,W(r(F),(o,B)=>(v(),N(u,{label:o.nickname,value:o.id,key:B},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,null,{default:t(()=>[e(V,{onClick:_},{default:t(()=>[e(D,{icon:"ep:search",class:"mr-5px"}),a[5]||(a[5]=g(" \u641C\u7D22 "))]),_:1}),e(V,{onClick:U},{default:t(()=>[e(D,{icon:"ep:refresh",class:"mr-5px"}),a[6]||(a[6]=g(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(A,null,{default:t(()=>[e(S,{modelValue:r(d),"onUpdate:modelValue":a[4]||(a[4]=o=>ae(d)?d.value=o:null)},{default:t(()=>[e(y,{label:"\u5458\u5DE5\u5408\u540C\u6570\u91CF\u7EDF\u8BA1",name:"ContractCountPerformance",lazy:""},{default:t(()=>[e(de,{"query-params":r(l),ref_key:"ContractCountPerformanceRef",ref:w},null,8,["query-params"])]),_:1}),e(y,{label:"\u5458\u5DE5\u5408\u540C\u91D1\u989D\u7EDF\u8BA1",name:"ContractPricePerformance",lazy:""},{default:t(()=>[e(ne,{"query-params":r(l),ref_key:"ContractPricePerformanceRef",ref:I},null,8,["query-params"])]),_:1}),e(y,{label:"\u5458\u5DE5\u56DE\u6B3E\u91D1\u989D\u7EDF\u8BA1",name:"ReceivablePricePerformance",lazy:""},{default:t(()=>[e(pe,{"query-params":r(l),ref_key:"ReceivablePricePerformanceRef",ref:k},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}});export{ce as default};
