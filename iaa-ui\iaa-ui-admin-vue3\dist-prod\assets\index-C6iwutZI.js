import{d as J,p as Q,b as j,r as n,f as E,q as W,O as X,c as k,o as i,g as e,w as t,s as Z,a as o,v as $,P as ee,Q as O,x as le,F as N,y as ae,R as te,D as R,A as m,B as oe,C as re,J as f,G as se,H as p,I as ie,K as pe,L as de,a3 as ne,t as Y,M as me}from"./index-CRsFgzy0.js";import{_ as ue}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as ce}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as fe}from"./index-DYfNUK1u.js";import{d as _e,e as ye}from"./index-CPqNYl_u.js";import{_ as we}from"./PickUpStoreForm.vue_vue_type_script_setup_true_lang-CC8YbZQW.js";import{_ as he}from"./DeliveryPickUpStoreBindForm.vue_vue_type_script_setup_true_lang-CXsEYanZ.js";import{d as ve}from"./formatTime-DhdtkSIS.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./IFrame.vue_vue_type_script_setup_true_lang-CgNW9vNM.js";import"./el-time-select-DBwK3NDO.js";import"./constants-uird_4gU.js";import"./tree-COGD3qag.js";import"./index-DLC3Afbg.js";import"./index-D4cXf1oy.js";import"./StoreStaffTableSelect.vue_vue_type_script_setup_true_lang-CRG1D8kL.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./index-D4y5Z4cM.js";import"./DeptTree.vue_vue_type_script_setup_true_lang-Cj24ZQp6.js";import"./index-C0yL_L5C.js";const be=["src"],ke=J({__name:"index",setup(ge){const g=Q(),{t:B}=j(),D=n(0),v=n(!0),x=n([]),r=E({pageNo:1,pageSize:10,status:void 0,phone:void 0,name:void 0,createTime:[]}),C=n(),V=n(),T=(u,l)=>{V.value.open(u,l)},M=n(),_=async()=>{v.value=!0;try{const u=await ye(r);x.value=u.list,D.value=u.total}finally{v.value=!1}},y=()=>{r.pageNo=1,_()},F=()=>{C.value.resetFields(),y()};return W(()=>{_()}),(u,l)=>{const H=fe,S=ee,c=$,K=oe,q=le,L=re,b=ie,d=se,P=Z,U=ce,s=de,z=ue,G=pe,w=X("hasPermi"),I=me;return i(),k(N,null,[e(H,{title:"\u3010\u4EA4\u6613\u3011\u5FEB\u9012\u53D1\u8D27",url:"https://doc.iocoder.cn/mall/trade-delivery-express/"}),e(U,null,{default:t(()=>[e(P,{ref_key:"queryFormRef",ref:C,inline:!0,model:o(r),class:"-mb-15px"},{default:t(()=>[e(c,{label:"\u95E8\u5E97\u624B\u673A",prop:"phone"},{default:t(()=>[e(S,{modelValue:o(r).phone,"onUpdate:modelValue":l[0]||(l[0]=a=>o(r).phone=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u95E8\u5E97\u624B\u673A",onKeyup:O(y,["enter"])},null,8,["modelValue"])]),_:1}),e(c,{label:"\u95E8\u5E97\u540D\u79F0",prop:"name"},{default:t(()=>[e(S,{modelValue:o(r).name,"onUpdate:modelValue":l[1]||(l[1]=a=>o(r).name=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u95E8\u5E97\u540D\u79F0",onKeyup:O(y,["enter"])},null,8,["modelValue"])]),_:1}),e(c,{label:"\u95E8\u5E97\u72B6\u6001",prop:"status"},{default:t(()=>[e(q,{modelValue:o(r).status,"onUpdate:modelValue":l[2]||(l[2]=a=>o(r).status=a),class:"!w-240px",clearable:"",placeholder:"\u95E8\u5E97\u72B6\u6001"},{default:t(()=>[(i(!0),k(N,null,ae(o(te)(o(R).COMMON_STATUS),a=>(i(),m(K,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(L,{modelValue:o(r).createTime,"onUpdate:modelValue":l[3]||(l[3]=a=>o(r).createTime=a),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"datetimerange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),e(c,null,{default:t(()=>[e(d,{onClick:y},{default:t(()=>[e(b,{class:"mr-5px",icon:"ep:search"}),l[5]||(l[5]=p(" \u641C\u7D22 "))]),_:1}),e(d,{onClick:F},{default:t(()=>[e(b,{class:"mr-5px",icon:"ep:refresh"}),l[6]||(l[6]=p(" \u91CD\u7F6E "))]),_:1}),f((i(),m(d,{plain:"",type:"primary",onClick:l[4]||(l[4]=a=>T("create"))},{default:t(()=>[e(b,{class:"mr-5px",icon:"ep:plus"}),l[7]||(l[7]=p(" \u65B0\u589E "))]),_:1})),[[w,["trade:delivery:pick-up-store:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(U,null,{default:t(()=>[f((i(),m(G,{data:o(x)},{default:t(()=>[e(s,{label:"\u7F16\u53F7","min-width":"80",prop:"id"}),e(s,{label:"\u95E8\u5E97 logo","min-width":"100",prop:"logo"},{default:t(a=>[a.row.logo?(i(),k("img",{key:0,src:a.row.logo,alt:"\u95E8\u5E97 logo",class:"h-50px"},null,8,be)):ne("",!0)]),_:1}),e(s,{label:"\u95E8\u5E97\u540D\u79F0","min-width":"150",prop:"name"}),e(s,{label:"\u95E8\u5E97\u624B\u673A","min-width":"100",prop:"phone"}),e(s,{label:"\u5730\u5740","min-width":"100",prop:"detailAddress"}),e(s,{label:"\u8425\u4E1A\u65F6\u95F4","min-width":"180"},{default:t(a=>[p(Y(a.row.openingTime)+" ~ "+Y(a.row.closingTime),1)]),_:1}),e(s,{align:"center",label:"\u5F00\u542F\u72B6\u6001","min-width":"100",prop:"status"},{default:t(a=>[e(z,{type:o(R).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(s,{formatter:o(ve),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(s,{align:"center",label:"\u64CD\u4F5C","min-width":"110"},{default:t(a=>[f((i(),m(d,{link:"",type:"primary",onClick:A=>T("update",a.row.id)},{default:t(()=>l[8]||(l[8]=[p(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["trade:delivery:pick-up-store:update"]]]),f((i(),m(d,{link:"",type:"primary",onClick:A=>{return h=a.row.id,void M.value.open(h);var h}},{default:t(()=>l[9]||(l[9]=[p(" \u7ED1\u5B9A\u5E97\u5458 ")])),_:2},1032,["onClick"])),[[w,["trade:delivery:pick-up-store:update"]]]),f((i(),m(d,{link:"",type:"danger",onClick:A=>(async h=>{try{await g.delConfirm(),await _e(h),g.success(B("common.delSuccess")),await _()}catch{}})(a.row.id)},{default:t(()=>l[10]||(l[10]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["trade:delivery:pick-up-store:delete"]]])]),_:1})]),_:1},8,["data"])),[[I,o(v)]])]),_:1}),e(we,{ref_key:"formRef",ref:V,onSuccess:_},null,512),e(he,{ref_key:"formBindRef",ref:M},null,512)],64)}}});export{ke as default};
