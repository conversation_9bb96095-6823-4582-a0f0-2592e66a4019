import{d as i,c as s,o as t,F as d,y as u,aw as o,a3 as r,A as y,i as x,t as a}from"./index-CRsFgzy0.js";import{E as b}from"./el-image-BQpHFDaE.js";const m={class:"flex flex-row flex-wrap"},f=i({name:"MenuGrid",__name:"index",props:{property:{}},setup:g=>(p,h)=>{const n=b;return t(),s("div",m,[(t(!0),s(d,null,u(p.property.list,(e,c)=>{var l;return t(),s("div",{key:c,class:"relative flex flex-col items-center p-b-14px p-t-20px",style:o({width:1/p.property.column*100+"%"})},[(l=e.badge)!=null&&l.show?(t(),s("span",{key:0,class:"absolute left-50% top-10px z-1 h-20px rounded-50% p-x-6px text-center text-12px leading-20px",style:o({color:e.badge.textColor,backgroundColor:e.badge.bgColor})},a(e.badge.text),5)):r("",!0),e.iconUrl?(t(),y(n,{key:1,class:"h-28px w-28px",src:e.iconUrl},null,8,["src"])):r("",!0),x("span",{class:"m-t-8px h-16px text-12px leading-16px",style:o({color:e.titleColor})},a(e.title),5),x("span",{class:"m-t-6px h-12px text-10px leading-12px",style:o({color:e.subtitleColor})},a(e.subtitle),5)],4)}),128))])}});export{f as default};
