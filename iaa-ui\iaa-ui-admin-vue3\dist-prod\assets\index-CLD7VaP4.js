import{d as K,u as R,p as j,b as A,r as m,f as G,q as J,O as L,c as O,o as u,g as a,w as l,s as Q,a as t,v as B,P as E,Q as W,C as X,J as f,G as Z,H as i,I as $,A as y,K as ee,L as ae,M as le,F as te}from"./index-CRsFgzy0.js";import{_ as re}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as oe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as pe}from"./index-DYfNUK1u.js";import{d as ie}from"./formatTime-DhdtkSIS.js";import{j as ne,k as se}from"./property-CzC0z5Eb.js";import{_ as de}from"./PropertyForm.vue_vue_type_script_setup_true_lang-pqiY3S0j.js";import"./index-CqPfoRkb.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";const me=K({name:"ProductProperty",__name:"index",setup(ue){const{push:T}=R(),v=j(),{t:D}=A(),_=m(!0),b=m(0),C=m([]),o=G({pageNo:1,pageSize:10,name:void 0,createTime:[]}),x=m(),n=async()=>{_.value=!0;try{const s=await ne(o);C.value=s.list,b.value=s.total}finally{_.value=!1}},g=()=>{o.pageNo=1,n()},F=()=>{x.value.resetFields(),g()},P=m(),V=(s,e)=>{P.value.open(s,e)};return J(()=>{n()}),(s,e)=>{const U=pe,Y=E,w=B,z=X,k=$,p=Z,H=Q,N=oe,d=ae,M=ee,q=re,h=L("hasPermi"),I=le;return u(),O(te,null,[a(U,{title:"\u3010\u5546\u54C1\u3011\u5546\u54C1\u5C5E\u6027",url:"https://doc.iocoder.cn/mall/product-property/"}),a(N,null,{default:l(()=>[a(H,{ref_key:"queryFormRef",ref:x,inline:!0,model:t(o),class:"-mb-15px","label-width":"68px"},{default:l(()=>[a(w,{label:"\u540D\u79F0",prop:"name"},{default:l(()=>[a(Y,{modelValue:t(o).name,"onUpdate:modelValue":e[0]||(e[0]=r=>t(o).name=r),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",onKeyup:W(g,["enter"])},null,8,["modelValue"])]),_:1}),a(w,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[a(z,{modelValue:t(o).createTime,"onUpdate:modelValue":e[1]||(e[1]=r=>t(o).createTime=r),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(w,null,{default:l(()=>[a(p,{onClick:g},{default:l(()=>[a(k,{class:"mr-5px",icon:"ep:search"}),e[5]||(e[5]=i(" \u641C\u7D22 "))]),_:1}),a(p,{onClick:F},{default:l(()=>[a(k,{class:"mr-5px",icon:"ep:refresh"}),e[6]||(e[6]=i(" \u91CD\u7F6E "))]),_:1}),f((u(),y(p,{plain:"",type:"primary",onClick:e[2]||(e[2]=r=>V("create"))},{default:l(()=>[a(k,{class:"mr-5px",icon:"ep:plus"}),e[7]||(e[7]=i(" \u65B0\u589E "))]),_:1})),[[h,["product:property:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(N,null,{default:l(()=>[f((u(),y(M,{data:t(C)},{default:l(()=>[a(d,{align:"center",label:"\u7F16\u53F7","min-width":"60",prop:"id"}),a(d,{align:"center",label:"\u5C5E\u6027\u540D\u79F0",prop:"name","min-width":"150"}),a(d,{"show-overflow-tooltip":!0,align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(d,{formatter:t(ie),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(d,{align:"center",label:"\u64CD\u4F5C"},{default:l(r=>[f((u(),y(p,{link:"",type:"primary",onClick:S=>V("update",r.row.id)},{default:l(()=>e[8]||(e[8]=[i(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[h,["product:property:update"]]]),a(p,{link:"",type:"primary",onClick:S=>{return c=r.row.id,void T({name:"ProductPropertyValue",params:{propertyId:c}});var c}},{default:l(()=>e[9]||(e[9]=[i("\u5C5E\u6027\u503C")])),_:2},1032,["onClick"]),f((u(),y(p,{link:"",type:"danger",onClick:S=>(async c=>{try{await v.delConfirm(),await se(c),v.success(D("common.delSuccess")),await n()}catch{}})(r.row.id)},{default:l(()=>e[10]||(e[10]=[i(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[h,["product:property:delete"]]])]),_:1})]),_:1},8,["data"])),[[I,t(_)]]),a(q,{limit:t(o).pageSize,"onUpdate:limit":e[3]||(e[3]=r=>t(o).pageSize=r),page:t(o).pageNo,"onUpdate:page":e[4]||(e[4]=r=>t(o).pageNo=r),total:t(b),onPagination:n},null,8,["limit","page","total"])]),_:1}),a(de,{ref_key:"formRef",ref:P,onSuccess:n},null,512)],64)}}});export{me as default};
