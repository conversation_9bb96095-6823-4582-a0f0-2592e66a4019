import{d as m,a2 as n,c as p,o,F as g,y as f,aw as s,g as l,w as b,i as d,I as R,a as w}from"./index-CRsFgzy0.js";import{E as v}from"./el-image-BQpHFDaE.js";const $={class:"image-slot"},t=93.75,B=m({name:"MagicCube",__name:"index",props:{property:{}},setup(c){const a=c,h=n(()=>{let r=0;return a.property.list.length>0&&(r=Math.max(...a.property.list.map(i=>i.bottom))),r+1});return(r,i)=>{const u=R,y=v;return o(),p("div",{class:"relative",style:s({height:w(h)*t+"px",width:"375px"})},[(o(!0),p(g,null,f(r.property.list,(e,x)=>(o(),p("div",{key:x,class:"absolute",style:s({width:e.width*t-2*r.property.space+"px",height:e.height*t-2*r.property.space+"px",margin:`${r.property.space}px`,top:e.top*t+"px",left:e.left*t+"px"})},[l(y,{class:"h-full w-full",fit:"cover",src:e.imgUrl,style:s({borderTopLeftRadius:`${r.property.borderRadiusTop}px`,borderTopRightRadius:`${r.property.borderRadiusTop}px`,borderBottomLeftRadius:`${r.property.borderRadiusBottom}px`,borderBottomRightRadius:`${r.property.borderRadiusBottom}px`})},{error:b(()=>[d("div",$,[d("div",{class:"flex items-center justify-center",style:s({width:e.width*t+"px",height:e.height*t+"px"})},[l(u,{icon:"ep-picture",color:"gray",size:t})],4)])]),_:2},1032,["src","style"])],4))),128))],4)}}});export{B as default};
