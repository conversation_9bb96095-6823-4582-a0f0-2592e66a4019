import{_ as r}from"./index-CeUx6j9a.js";import{_ as i}from"./ProductSummary.vue_vue_type_script_setup_true_lang-CMlCZ2Ac.js";import{_ as m}from"./ProductRank.vue_vue_type_script_setup_true_lang-pWrcsonJ.js";import{d as p,c as s,o as a,g as t,F as c}from"./index-CvERnF9Y.js";import"./el-skeleton-item-CIGs_6QW.js";import"./Echart.vue_vue_type_script_setup_true_lang-D9HJSbBT.js";import"./echarts-BcS7Kngw.js";import"./index.vue_vue_type_script_setup_true_lang-PqRfzyQf.js";import"./formatTime-CmW2_KRq.js";import"./product-BdZZ9kA0.js";import"./index.vue_vue_type_script_setup_true_lang-CZoePPkw.js";import"./CountTo.vue_vue_type_script_setup_true_lang-DkUXM3W0.js";import"./download-oWiM5xVU.js";import"./CardTitle-CdidxETN.js";import"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import"./index-DHM6tdge.js";import"./el-image-DTDUrxnp.js";import"./formatter-UUK_ohaG.js";const e=p({name:"ProductStatistics",__name:"index",setup:l=>(n,u)=>{const o=r;return a(),s(c,null,[t(o,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),t(i),t(m,{class:"mt-16px"})],64)}});export{e as default};
