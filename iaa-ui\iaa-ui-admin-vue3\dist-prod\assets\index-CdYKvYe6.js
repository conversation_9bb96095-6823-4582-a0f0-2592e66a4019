import{as as I,d as X,p as Z,r as c,f as $,q as ee,O as ae,c as N,o as s,g as a,w as t,s as le,a as r,v as re,P as te,Q as G,x as oe,F as R,y as L,R as M,D as x,A as n,B as pe,C as se,J as f,G as ie,H as m,I as ue,K as ne,L as de,a3 as z,M as ce}from"./index-CRsFgzy0.js";import{_ as me}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as fe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ye}from"./index-DYfNUK1u.js";import{d as ge}from"./formatTime-DhdtkSIS.js";import{d as we}from"./download-oWiM5xVU.js";import{_ as be}from"./ApiErrorLogDetail.vue_vue_type_script_setup_true_lang-BU3JeF7a.js";import{I as _}from"./constants-uird_4gU.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./el-descriptions-item-lelixL8M.js";const ve=X({name:"InfraApiErrorLog",__name:"index",setup(xe){const S=Z(),h=c(!0),V=c(0),C=c([]),o=$({pageNo:1,pageSize:10,userId:null,userType:null,applicationName:null,requestUrl:null,processStatus:null,exceptionTime:[]}),U=c(),T=c(!1),y=async()=>{h.value=!0;try{const e=await(i=o,I.get({url:"/infra/api-error-log/page",params:i}));C.value=e.list,V.value=e.total}finally{h.value=!1}var i},g=()=>{o.pageNo=1,y()},H=()=>{U.value.resetFields(),g()},E=c(),O=async(i,e)=>{try{const w=e===_.DONE?"\u5DF2\u5904\u7406":"\u5DF2\u5FFD\u7565";await S.confirm("\u786E\u8BA4\u6807\u8BB0\u4E3A"+w+"?"),await((b,u)=>I.put({url:"/infra/api-error-log/update-status?id="+b+"&processStatus="+u}))(i,e),await S.success(w),await y()}catch{}},K=async()=>{try{await S.exportConfirm(),T.value=!0;const e=await(i=o,I.download({url:"/infra/api-error-log/export-excel",params:i}));we.excel(e,"\u5F02\u5E38\u65E5\u5FD7.xls")}catch{}finally{T.value=!1}var i};return ee(()=>{y()}),(i,e)=>{const w=ye,b=te,u=re,A=pe,P=oe,B=se,k=ue,d=ie,J=le,Y=_e,p=de,D=fe,Q=ne,j=me,v=ae("hasPermi"),W=ce;return s(),N(R,null,[a(w,{title:"\u7CFB\u7EDF\u65E5\u5FD7",url:"https://doc.iocoder.cn/system-log/"}),a(Y,null,{default:t(()=>[a(J,{class:"-mb-15px",model:r(o),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:t(()=>[a(u,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:t(()=>[a(b,{modelValue:r(o).userId,"onUpdate:modelValue":e[0]||(e[0]=l=>r(o).userId=l),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",clearable:"",onKeyup:G(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(u,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:t(()=>[a(P,{modelValue:r(o).userType,"onUpdate:modelValue":e[1]||(e[1]=l=>r(o).userType=l),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),N(R,null,L(r(M)(r(x).USER_TYPE),l=>(s(),n(A,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"\u5E94\u7528\u540D",prop:"applicationName"},{default:t(()=>[a(b,{modelValue:r(o).applicationName,"onUpdate:modelValue":e[2]||(e[2]=l=>r(o).applicationName=l),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D",clearable:"",onKeyup:G(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(u,{label:"\u5F02\u5E38\u65F6\u95F4",prop:"exceptionTime"},{default:t(()=>[a(B,{modelValue:r(o).exceptionTime,"onUpdate:modelValue":e[3]||(e[3]=l=>r(o).exceptionTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(u,{label:"\u5904\u7406\u72B6\u6001",prop:"processStatus"},{default:t(()=>[a(P,{modelValue:r(o).processStatus,"onUpdate:modelValue":e[4]||(e[4]=l=>r(o).processStatus=l),placeholder:"\u8BF7\u9009\u62E9\u5904\u7406\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),N(R,null,L(r(M)(r(x).INFRA_API_ERROR_LOG_PROCESS_STATUS),l=>(s(),n(A,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,null,{default:t(()=>[a(d,{onClick:g},{default:t(()=>[a(k,{icon:"ep:search",class:"mr-5px"}),e[7]||(e[7]=m(" \u641C\u7D22"))]),_:1}),a(d,{onClick:H},{default:t(()=>[a(k,{icon:"ep:refresh",class:"mr-5px"}),e[8]||(e[8]=m(" \u91CD\u7F6E"))]),_:1}),f((s(),n(d,{type:"success",plain:"",onClick:K,loading:r(T)},{default:t(()=>[a(k,{icon:"ep:download",class:"mr-5px"}),e[9]||(e[9]=m(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[v,["infra:api-error-log:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(Y,null,{default:t(()=>[f((s(),n(Q,{data:r(C)},{default:t(()=>[a(p,{label:"\u65E5\u5FD7\u7F16\u53F7",align:"center",prop:"id"}),a(p,{label:"\u7528\u6237\u7F16\u53F7",align:"center",prop:"userId"}),a(p,{label:"\u7528\u6237\u7C7B\u578B",align:"center",prop:"userType"},{default:t(l=>[a(D,{type:r(x).USER_TYPE,value:l.row.userType},null,8,["type","value"])]),_:1}),a(p,{label:"\u5E94\u7528\u540D",align:"center",prop:"applicationName",width:"200"}),a(p,{label:"\u8BF7\u6C42\u65B9\u6CD5",align:"center",prop:"requestMethod",width:"80"}),a(p,{label:"\u8BF7\u6C42\u5730\u5740",align:"center",prop:"requestUrl",width:"180"}),a(p,{label:"\u5F02\u5E38\u53D1\u751F\u65F6\u95F4",align:"center",prop:"exceptionTime",width:"180",formatter:r(ge)},null,8,["formatter"]),a(p,{label:"\u5F02\u5E38\u540D",align:"center",prop:"exceptionName",width:"180"}),a(p,{label:"\u5904\u7406\u72B6\u6001",align:"center",prop:"processStatus"},{default:t(l=>[a(D,{type:r(x).INFRA_API_ERROR_LOG_PROCESS_STATUS,value:l.row.processStatus},null,8,["type","value"])]),_:1}),a(p,{label:"\u64CD\u4F5C",align:"center",width:"200"},{default:t(l=>[f((s(),n(d,{link:"",type:"primary",onClick:q=>{return F=l.row,void E.value.open(F);var F}},{default:t(()=>e[10]||(e[10]=[m(" \u8BE6\u7EC6 ")])),_:2},1032,["onClick"])),[[v,["infra:api-error-log:query"]]]),l.row.processStatus===r(_).INIT?f((s(),n(d,{key:0,link:"",type:"primary",onClick:q=>O(l.row.id,r(_).DONE)},{default:t(()=>e[11]||(e[11]=[m(" \u5DF2\u5904\u7406 ")])),_:2},1032,["onClick"])),[[v,["infra:api-error-log:update-status"]]]):z("",!0),l.row.processStatus===r(_).INIT?f((s(),n(d,{key:1,link:"",type:"primary",onClick:q=>O(l.row.id,r(_).IGNORE)},{default:t(()=>e[12]||(e[12]=[m(" \u5DF2\u5FFD\u7565 ")])),_:2},1032,["onClick"])),[[v,["infra:api-error-log:update-status"]]]):z("",!0)]),_:1})]),_:1},8,["data"])),[[W,r(h)]]),a(j,{total:r(V),page:r(o).pageNo,"onUpdate:page":e[5]||(e[5]=l=>r(o).pageNo=l),limit:r(o).pageSize,"onUpdate:limit":e[6]||(e[6]=l=>r(o).pageSize=l),onPagination:y},null,8,["total","page","limit"])]),_:1}),a(be,{ref_key:"detailRef",ref:E},null,512)],64)}}});export{ve as default};
