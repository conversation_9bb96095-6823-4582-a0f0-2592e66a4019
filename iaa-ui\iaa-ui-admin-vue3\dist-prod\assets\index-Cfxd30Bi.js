import{d as a,c as o,o as r,i as s,J as l,a3 as e,aw as p,t as i,a6 as n,A as c,I as d,_ as g}from"./index-CRsFgzy0.js";const m={key:0},x=g(a({name:"TitleBar",__name:"index",props:{property:{}},setup:u=>(t,b)=>{const y=d;return r(),o("div",{style:p({background:t.property.style.bgType==="color"?t.property.style.bgColor:`url(${t.property.style.bgImg})`,backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"}),class:"title-bar"},[s("div",null,[t.property.title?(r(),o("div",{key:0,style:p({fontSize:`${t.property.titleSize}px`,fontWeight:t.property.titleWeight,color:t.property.titleColor,textAlign:t.property.textAlign})},i(t.property.title),5)):e("",!0),t.property.description?(r(),o("div",{key:1,style:p({fontSize:`${t.property.descriptionSize}px`,fontWeight:t.property.descriptionWeight,color:t.property.descriptionColor,textAlign:t.property.textAlign}),class:"m-t-8px"},i(t.property.description),5)):e("",!0)]),l(s("div",{style:p({color:t.property.descriptionColor}),class:"more"},[t.property.more.type!=="icon"?(r(),o("span",m,i(t.property.more.text),1)):e("",!0),t.property.more.type!=="text"?(r(),c(y,{key:1,icon:"ep:arrow-right"})):e("",!0)],4),[[n,t.property.more.show]])],4)}}),[["__scopeId","data-v-93399e84"]]);export{x as default};
