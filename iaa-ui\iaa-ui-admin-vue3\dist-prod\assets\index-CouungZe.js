import{d as X,g as a,I as d,D as U,r as o,a0 as Y,G as i,F as R,fa as Z,b as $,p as ee,f as ae,q as le,O as te,c as V,o as y,w as s,s as se,a as t,v as ne,P as oe,Q as ie,x as re,y as ue,R as de,A as M,B as me,J as ce,H as r,m as pe,fb as N}from"./index-CRsFgzy0.js";import"./el-empty-CqTDiVWi.js";import"./el-virtual-list-AoPW9ghL.js";import{F as b,E as ye,a as fe}from"./el-table-v2-DMzSXqeI.js";import{_ as he}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ke}from"./index-DYfNUK1u.js";import{h as ve}from"./tree-COGD3qag.js";import{b as we,d as _e,u as xe}from"./index-De4pDBF3.js";import{_ as Ce}from"./MenuForm.vue_vue_type_script_setup_true_lang-Yj-d6xHM.js";import{_ as Re}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{c as f}from"./permission-aU39l5nQ.js";import{C as A}from"./constants-uird_4gU.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./Tooltip.vue_vue_type_script_setup_true_lang-CQxPX9Oy.js";import"./index-CqPfoRkb.js";import"./el-tree-select-BijZG_HG.js";import"./color-CIFUYK2M.js";const be=X({name:"SystemMenu",__name:"index",setup(Se){const S=[{key:"name",title:"\u83DC\u5355\u540D\u79F0",dataKey:"name",width:250,fixed:b.LEFT},{key:"icon",title:"\u56FE\u6807",dataKey:"icon",width:100,align:"center",cellRenderer:({cellData:l})=>a(d,{icon:l},null)},{key:"sort",title:"\u6392\u5E8F",dataKey:"sort",width:60},{key:"permission",title:"\u6743\u9650\u6807\u8BC6",dataKey:"permission",width:300},{key:"component",title:"\u7EC4\u4EF6\u8DEF\u5F84",dataKey:"component",width:500},{key:"componentName",title:"\u7EC4\u4EF6\u540D\u79F0",dataKey:"componentName",width:200},{key:"status",title:"\u72B6\u6001",dataKey:"status",width:60,fixed:b.RIGHT,cellRenderer:({rowData:l})=>f(["system:menu:update"])?a(Y,{modelValue:l.status,"onUpdate:modelValue":e=>l.status=e,"active-value":A.ENABLE,"inactive-value":A.DISABLE,loading:x[l.id],class:"ml-4px",onChange:e=>G(l,e)},null):a(Re,{type:U.COMMON_STATUS,value:l.status},null)},{key:"operations",title:"\u64CD\u4F5C",align:"center",width:160,fixed:b.RIGHT,cellRenderer:({rowData:l})=>{const e=[];return f(["system:menu:update"])&&e.push(a(i,{key:"edit",link:!0,type:"primary",onClick:()=>_("update",l.id)},{default:()=>[r("\u4FEE\u6539")]})),f(["system:menu:create"])&&e.push(a(i,{key:"create",link:!0,type:"primary",onClick:()=>_("create",void 0,l.id)},{default:()=>[r("\u65B0\u589E")]})),f(["system:menu:delete"])&&e.push(a(i,{key:"delete",link:!0,type:"danger",onClick:()=>B(l.id)},{default:()=>[r("\u5220\u9664")]})),e.length===0?null:a(R,null,[e])}}],{wsCache:g}=Z(),{t:D}=$(),h=ee(),E=o(!0),k=o([]),u=ae({name:void 0,status:void 0}),K=o(),v=o(!1);o(!0);const m=o([]),c=async()=>{E.value=!0;try{const l=await we(u);k.value=ve(l)}finally{E.value=!1}},w=()=>{c()},F=()=>{K.value.resetFields(),w()},O=o(),_=(l,e,p)=>{O.value.open(l,e,p)},I=()=>{v.value?m.value=[]:m.value=k.value.map(l=>l.id),v.value=!v.value},L=async()=>{try{await h.confirm("\u5373\u5C06\u66F4\u65B0\u7F13\u5B58\u5237\u65B0\u6D4F\u89C8\u5668\uFF01","\u5237\u65B0\u83DC\u5355\u7F13\u5B58"),g.delete(N.USER),g.delete(N.ROLE_ROUTERS),location.reload()}catch{}},B=async l=>{try{await h.delConfirm(),await _e(l),h.success(D("common.delSuccess")),await c()}catch{}},x=o({}),G=async(l,e)=>{x.value[l.id]=!0;try{l.status=e,await xe(l)}finally{x.value[l.id]=!1}};return le(()=>{c()}),(l,e)=>{const p=ke,H=oe,C=ne,P=me,q=re,J=se,T=he,Q=fe,j=ye,z=te("hasPermi");return y(),V(R,null,[a(p,{title:"\u529F\u80FD\u6743\u9650",url:"https://doc.iocoder.cn/resource-permission"}),a(p,{title:"\u83DC\u5355\u8DEF\u7531",url:"https://doc.iocoder.cn/vue3/route/"}),a(T,null,{default:s(()=>[a(J,{ref_key:"queryFormRef",ref:K,inline:!0,model:t(u),class:"-mb-15px","label-width":"68px"},{default:s(()=>[a(C,{label:"\u83DC\u5355\u540D\u79F0",prop:"name"},{default:s(()=>[a(H,{modelValue:t(u).name,"onUpdate:modelValue":e[0]||(e[0]=n=>t(u).name=n),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u83DC\u5355\u540D\u79F0",onKeyup:ie(w,["enter"])},null,8,["modelValue"])]),_:1}),a(C,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[a(q,{modelValue:t(u).status,"onUpdate:modelValue":e[1]||(e[1]=n=>t(u).status=n),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u83DC\u5355\u72B6\u6001"},{default:s(()=>[(y(!0),V(R,null,ue(t(de)(t(U).COMMON_STATUS),n=>(y(),M(P,{key:n.value,label:n.label,value:n.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(C,null,{default:s(()=>[a(t(i),{onClick:w},{default:s(()=>[a(t(d),{class:"mr-5px",icon:"ep:search"}),e[4]||(e[4]=r(" \u641C\u7D22 "))]),_:1}),a(t(i),{onClick:F},{default:s(()=>[a(t(d),{class:"mr-5px",icon:"ep:refresh"}),e[5]||(e[5]=r(" \u91CD\u7F6E "))]),_:1}),ce((y(),M(t(i),{plain:"",type:"primary",onClick:e[2]||(e[2]=n=>_("create"))},{default:s(()=>[a(t(d),{class:"mr-5px",icon:"ep:plus"}),e[6]||(e[6]=r(" \u65B0\u589E "))]),_:1})),[[z,["system:menu:create"]]]),a(t(i),{plain:"",type:"danger",onClick:I},{default:s(()=>[a(t(d),{class:"mr-5px",icon:"ep:sort"}),e[7]||(e[7]=r(" \u5C55\u5F00/\u6298\u53E0 "))]),_:1}),a(t(i),{plain:"",onClick:L},{default:s(()=>[a(t(d),{class:"mr-5px",icon:"ep:refresh"}),e[8]||(e[8]=r(" \u5237\u65B0\u83DC\u5355\u7F13\u5B58 "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(T,null,{default:s(()=>[a(j,null,{default:s(({width:n})=>[a(Q,{"expanded-row-keys":t(m),"onUpdate:expandedRowKeys":e[3]||(e[3]=W=>pe(m)?m.value=W:null),columns:S,data:t(k),"expand-column-key":S[0].key,height:1e3,width:n,fixed:"","row-key":"id"},null,8,["expanded-row-keys","data","expand-column-key","width"])]),_:1})]),_:1}),a(Ce,{ref_key:"formRef",ref:O,onSuccess:c},null,512)],64)}}});export{be as default};
