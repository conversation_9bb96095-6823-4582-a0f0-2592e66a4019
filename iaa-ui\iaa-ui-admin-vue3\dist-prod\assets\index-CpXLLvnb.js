import{as as e}from"./index-CvERnF9Y.js";const u={getSaleOutPage:async a=>await e.get({url:"/erp/sale-out/page",params:a}),getSaleOut:async a=>await e.get({url:"/erp/sale-out/get?id="+a}),createSaleOut:async a=>await e.post({url:"/erp/sale-out/create",data:a}),updateSaleOut:async a=>await e.put({url:"/erp/sale-out/update",data:a}),updateSaleOutStatus:async(a,t)=>await e.put({url:"/erp/sale-out/update-status",params:{id:a,status:t}}),deleteSaleOut:async a=>await e.delete({url:"/erp/sale-out/delete",params:{ids:a.join(",")}}),exportSaleOut:async a=>await e.download({url:"/erp/sale-out/export-excel",params:a})};export{u as S};
