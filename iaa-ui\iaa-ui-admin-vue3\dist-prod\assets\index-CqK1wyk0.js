import{d as v,r as k,c as s,o as a,i as x,a3 as r,g as o,a as c,F as u,y as _,w as d,I as g,aw as b,t as h,X as f,G as w,_ as C}from"./index-CvERnF9Y.js";import{E as z}from"./el-image-DTDUrxnp.js";const A=["onClick"],F={class:"h-full w-full flex items-center justify-center"},I=v({name:"FloatingActionButton",__name:"index",props:{property:{}},setup(B){const e=k(!1),i=()=>{e.value=!e.value};return(t,E)=>{const p=g,y=z,m=w;return a(),s(u,null,[x("div",{class:f(["absolute bottom-32px right-[calc(50%-375px/2+32px)] flex z-12 gap-12px items-center",{"flex-row":t.property.direction==="horizontal","flex-col":t.property.direction==="vertical"}])},[c(e)?(a(!0),s(u,{key:0},_(t.property.list,(l,n)=>(a(),s("div",{key:n,class:"flex flex-col items-center",onClick:G=>t.handleActive(n)},[o(y,{src:l.imgUrl,fit:"contain",class:"h-27px w-27px"},{error:d(()=>[x("div",F,[o(p,{icon:"ep:picture",color:l.textColor},null,8,["color"])])]),_:2},1032,["src"]),t.property.showText?(a(),s("span",{key:0,class:"mt-4px text-12px",style:b({color:l.textColor})},h(l.text),5)):r("",!0)],8,A))),128)):r("",!0),o(m,{type:"primary",size:"large",circle:"",onClick:i},{default:d(()=>[o(p,{icon:"ep:plus",class:f(["fab-icon",{active:c(e)}])},null,8,["class"])]),_:1})],2),c(e)?(a(),s("div",{key:0,class:"modal-bg",onClick:i})):r("",!0)],64)}}}),j=C(I,[["__scopeId","data-v-53b08e1d"]]);export{j as default};
