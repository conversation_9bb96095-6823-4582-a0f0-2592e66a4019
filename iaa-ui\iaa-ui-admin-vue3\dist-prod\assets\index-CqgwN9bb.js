import{d as re,p as le,r as p,f as te,q as oe,O as ne,c as de,o as s,g as e,w as l,s as ie,a as r,v as pe,P as se,Q as me,x as ue,B as ce,C as be,J as V,G as fe,H as m,I as ge,A as b,K as ke,L as we,a0 as _e,cY as he,cZ as xe,a3 as y,c_ as ye,M as Ue,F as ve}from"./index-CvERnF9Y.js";import{_ as Ce}from"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import{E as Ve}from"./el-avatar-BLtz-X9T.js";import{_ as Ee}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as Te}from"./index-CeUx6j9a.js";import{d as O}from"./formatTime-CmW2_KRq.js";import{g as Be,b as Fe,d as Ie}from"./index-D5Gkk3nd.js";import{c as f}from"./permission-bCGjWiuI.js";import{f as U}from"./formatter-UUK_ohaG.js";import{_ as qe}from"./BrokerageUserUpdateForm.vue_vue_type_script_setup_true_lang-CSBdr4nq.js";import{_ as De}from"./BrokerageUserListDialog.vue_vue_type_script_setup_true_lang-CvA-ozdQ.js";import{_ as Pe}from"./BrokerageOrderListDialog.vue_vue_type_script_setup_true_lang-DB7kb_1i.js";import{_ as Se}from"./BrokerageUserCreateForm.vue_vue_type_script_setup_true_lang-RgnTb5Co.js";import"./index-DHM6tdge.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./el-descriptions-item-imVgRiUQ.js";import"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import"./color-CIFUYK2M.js";import"./index-DEQpndjB.js";import"./constants-uird_4gU.js";import"./index-C_i5BwF_.js";const Ne=re({name:"TradeBrokerageUser",__name:"index",setup(Oe){const g=le(),v=p(!0),E=p(0),T=p([]),d=te({pageNo:1,pageSize:10,bindUserId:null,brokerageEnabled:!0,createTime:[]}),B=p(),u=async()=>{v.value=!0;try{const o=await Be(d);T.value=o.list,E.value=o.total}finally{v.value=!1}},C=()=>{d.pageNo=1,u()},R=()=>{B.value.resetFields(),C()},F=p(),Y=o=>{F.value.open(o)},I=p(),z=o=>{I.value.open(o)},q=p(),H=o=>{q.value.open(o)},D=p(),K=()=>{var o;(o=D.value)==null||o.open()},M=async o=>{try{await g.confirm(`\u786E\u8BA4\u8981\u6E05\u9664"${o.nickname}"\u7684\u4E0A\u7EA7\u63A8\u5E7F\u4EBA\u5417\uFF1F`),await Fe({id:o.id}),g.success("\u6E05\u9664\u6210\u529F"),await u()}catch{}};return oe(()=>{u()}),(o,a)=>{const $=Te,A=se,k=pe,P=ce,G=ue,J=be,w=ge,_=fe,L=ie,S=Ee,n=we,Q=Ve,Z=_e,h=ye,j=xe,W=he,X=ke,ee=Ce,N=ne("hasPermi"),ae=Ue;return s(),de(ve,null,[e($,{title:"\u3010\u4EA4\u6613\u3011\u5206\u9500\u8FD4\u4F63",url:"https://doc.iocoder.cn/mall/trade-brokerage/"}),e(S,null,{default:l(()=>[e(L,{ref_key:"queryFormRef",ref:B,inline:!0,model:r(d),class:"-mb-15px","label-width":"85px"},{default:l(()=>[e(k,{label:"\u63A8\u5E7F\u5458\u7F16\u53F7",prop:"bindUserId"},{default:l(()=>[e(A,{modelValue:r(d).bindUserId,"onUpdate:modelValue":a[0]||(a[0]=t=>r(d).bindUserId=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u63A8\u5E7F\u5458\u7F16\u53F7",onKeyup:me(C,["enter"])},null,8,["modelValue"])]),_:1}),e(k,{label:"\u63A8\u5E7F\u8D44\u683C",prop:"brokerageEnabled"},{default:l(()=>[e(G,{modelValue:r(d).brokerageEnabled,"onUpdate:modelValue":a[1]||(a[1]=t=>r(d).brokerageEnabled=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u63A8\u5E7F\u8D44\u683C"},{default:l(()=>[e(P,{value:!0,label:"\u6709"}),e(P,{value:!1,label:"\u65E0"})]),_:1},8,["modelValue"])]),_:1}),e(k,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[e(J,{modelValue:r(d).createTime,"onUpdate:modelValue":a[2]||(a[2]=t=>r(d).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(k,null,{default:l(()=>[e(_,{onClick:C},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:search"}),a[5]||(a[5]=m(" \u641C\u7D22 "))]),_:1}),e(_,{onClick:R},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:refresh"}),a[6]||(a[6]=m(" \u91CD\u7F6E "))]),_:1}),V((s(),b(_,{plain:"",type:"primary",onClick:K},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:plus"}),a[7]||(a[7]=m(" \u65B0\u589E "))]),_:1})),[[N,["trade:brokerage-user:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(S,null,{default:l(()=>[V((s(),b(X,{data:r(T),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[e(n,{align:"center",label:"\u7528\u6237\u7F16\u53F7","min-width":"80px",prop:"id"}),e(n,{align:"center",label:"\u5934\u50CF",prop:"avatar",width:"70px"},{default:l(t=>[e(Q,{src:t.row.avatar},null,8,["src"])]),_:1}),e(n,{align:"center",label:"\u6635\u79F0","min-width":"80px",prop:"nickname"}),e(n,{align:"center",label:"\u63A8\u5E7F\u4EBA\u6570",prop:"brokerageUserCount",width:"80px"}),e(n,{align:"center",label:"\u63A8\u5E7F\u8BA2\u5355\u6570\u91CF","min-width":"110px",prop:"brokerageOrderCount"}),e(n,{formatter:r(U),align:"center",label:"\u63A8\u5E7F\u8BA2\u5355\u91D1\u989D","min-width":"110px",prop:"brokerageOrderPrice"},null,8,["formatter"]),e(n,{formatter:r(U),align:"center",label:"\u5DF2\u63D0\u73B0\u91D1\u989D","min-width":"100px",prop:"withdrawPrice"},null,8,["formatter"]),e(n,{align:"center",label:"\u5DF2\u63D0\u73B0\u6B21\u6570","min-width":"100px",prop:"withdrawCount"}),e(n,{formatter:r(U),align:"center",label:"\u672A\u63D0\u73B0\u91D1\u989D","min-width":"100px",prop:"price"},null,8,["formatter"]),e(n,{formatter:r(U),align:"center",label:"\u51BB\u7ED3\u4E2D\u4F63\u91D1","min-width":"100px",prop:"frozenPrice"},null,8,["formatter"]),e(n,{align:"center",label:"\u63A8\u5E7F\u8D44\u683C","min-width":"80px",prop:"brokerageEnabled"},{default:l(t=>[e(Z,{modelValue:t.row.brokerageEnabled,"onUpdate:modelValue":x=>t.row.brokerageEnabled=x,disabled:!r(f)(["trade:brokerage-user:update-bind-user"]),"active-text":"\u6709","inactive-text":"\u65E0","inline-prompt":"",onChange:x=>(async i=>{try{const c=i.brokerageEnabled?"\u5F00\u901A":"\u5173\u95ED";await g.confirm(`\u786E\u8BA4\u8981${c}"${i.nickname}"\u7684\u63A8\u5E7F\u8D44\u683C\u5417\uFF1F`),await Ie({id:i.id,enabled:i.brokerageEnabled}),g.success(c+"\u6210\u529F"),await u()}catch{i.brokerageEnabled=!i.brokerageEnabled}})(t.row)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),_:1}),e(n,{formatter:r(O),align:"center",label:"\u6210\u4E3A\u63A8\u5E7F\u5458\u65F6\u95F4",prop:"brokerageTime",width:"180px"},null,8,["formatter"]),e(n,{align:"center",label:"\u4E0A\u7EA7\u63A8\u5E7F\u5458\u7F16\u53F7",prop:"bindUserId",width:"150px"}),e(n,{formatter:r(O),align:"center",label:"\u63A8\u5E7F\u5458\u7ED1\u5B9A\u65F6\u95F4",prop:"bindUserTime",width:"180px"},null,8,["formatter"]),e(n,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"150px"},{default:l(t=>[V((s(),b(W,{onCommand:x=>((i,c)=>{switch(i){case"openBrokerageUserTable":Y(c.id);break;case"openBrokerageOrderTable":z(c.id);break;case"openUpdateBindUserForm":H(c);break;case"handleClearBindUser":M(c)}})(x,t.row)},{dropdown:l(()=>[e(j,null,{default:l(()=>[r(f)(["trade:brokerage-user:user-query"])?(s(),b(h,{key:0,command:"openBrokerageUserTable"},{default:l(()=>a[9]||(a[9]=[m(" \u63A8\u5E7F\u4EBA ")])),_:1})):y("",!0),r(f)(["trade:brokerage-user:order-query"])?(s(),b(h,{key:1,command:"openBrokerageOrderTable"},{default:l(()=>a[10]||(a[10]=[m(" \u63A8\u5E7F\u8BA2\u5355 ")])),_:1})):y("",!0),r(f)(["trade:brokerage-user:update-bind-user"])?(s(),b(h,{key:2,command:"openUpdateBindUserForm"},{default:l(()=>a[11]||(a[11]=[m(" \u4FEE\u6539\u4E0A\u7EA7\u63A8\u5E7F\u4EBA ")])),_:1})):y("",!0),t.row.bindUserId&&r(f)(["trade:brokerage-user:clear-bind-user"])?(s(),b(h,{key:3,command:"handleClearBindUser"},{default:l(()=>a[12]||(a[12]=[m(" \u6E05\u9664\u4E0A\u7EA7\u63A8\u5E7F\u4EBA ")])),_:1})):y("",!0)]),_:2},1024)]),default:l(()=>[e(_,{link:"",type:"primary"},{default:l(()=>[e(w,{icon:"ep:d-arrow-right"}),a[8]||(a[8]=m(" \u66F4\u591A "))]),_:1})]),_:2},1032,["onCommand"])),[[N,["trade:brokerage-user:user-query","trade:brokerage-user:order-query","trade:brokerage-user:update-bind-user","trade:brokerage-user:clear-bind-user"]]])]),_:1})]),_:1},8,["data"])),[[ae,r(v)]]),e(ee,{limit:r(d).pageSize,"onUpdate:limit":a[3]||(a[3]=t=>r(d).pageSize=t),page:r(d).pageNo,"onUpdate:page":a[4]||(a[4]=t=>r(d).pageNo=t),total:r(E),onPagination:u},null,8,["limit","page","total"])]),_:1}),e(qe,{ref_key:"updateFormRef",ref:q,onSuccess:u},null,512),e(De,{ref_key:"listDialogRef",ref:F},null,512),e(Pe,{ref_key:"orderDialogRef",ref:I},null,512),e(Se,{ref_key:"createFormRef",ref:D,onSuccess:u},null,512)],64)}}});export{Ne as default};
