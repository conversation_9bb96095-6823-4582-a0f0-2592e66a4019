import{d as ra,p as da,N as pa,b as ca,u as ma,r as _,bE as fa,q as ya,O as _a,c as L,o as i,g as a,w as e,s as wa,a as n,v as ba,P as ga,Q as va,cb as ka,C as ha,J as v,G as Ca,H as d,I as xa,A as c,l as Va,F as D,y as Ea,n as Ia,K as Sa,L as Ua,E as Ta,h as Pa,i as w,t as k,aR as N,bi as La,a0 as Da,aE as Na,M as Aa,aV as Ya,_ as qa}from"./index-CRsFgzy0.js";import{_ as Ba}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{E as Ma}from"./el-image-BQpHFDaE.js";import{_ as Ra}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as $a}from"./index-DYfNUK1u.js";import{d as za}from"./formatTime-DhdtkSIS.js";import{h as Fa,d as Ha,t as Ka}from"./tree-COGD3qag.js";import{g as C}from"./constants-uird_4gU.js";import{d as Ja}from"./download-oWiM5xVU.js";import{d as Oa,a as ja,e as J,f as Ga,h as Qa}from"./spu-BHhhuUrI.js";import{g as Xa}from"./category--cl9fhwU.js";import"./index-CqPfoRkb.js";const Za={class:"flex"},Wa={class:"ml-4 overflow-hidden"},ae=qa(ra({name:"ProductSpu",__name:"index",setup(ee){const b=da(),A=pa(),{t:O}=ca(),{push:Y}=ma(),S=_(!1),U=_(!1),q=_(0),B=_([]),M=_([{name:"\u51FA\u552E\u4E2D",type:0,count:0},{name:"\u4ED3\u5E93\u4E2D",type:1,count:0},{name:"\u5DF2\u552E\u7F44",type:2,count:0},{name:"\u8B66\u6212\u5E93\u5B58",type:3,count:0},{name:"\u56DE\u6536\u7AD9",type:4,count:0}]),s=_({pageNo:1,pageSize:10,tabType:0,name:"",categoryId:void 0,createTime:void 0}),R=_(),y=async()=>{S.value=!0;try{const o=await Oa(s.value);B.value=o.list,q.value=o.total}finally{S.value=!1}},j=o=>{s.value.tabType=o.paneName,y()},x=async()=>{const o=await ja();for(let l in o)M.value[Number(l)].count=o[l]},$=async(o,l)=>{try{const V=l===C.RECYCLE.status?"\u52A0\u5165\u5230\u56DE\u6536\u7AD9":"\u6062\u590D\u5230\u4ED3\u5E93";await b.confirm(`\u786E\u8BA4\u8981"${o.name}"${V}\u5417\uFF1F`),await J({id:o.id,status:l}),b.success(V+"\u6210\u529F"),await x(),await y()}catch{}},T=()=>{y()},G=()=>{R.value.resetFields(),T()},z=o=>{Y(typeof o!="number"?{name:"ProductSpuAdd"}:{name:"ProductSpuEdit",params:{id:o}})},Q=async()=>{try{await b.exportConfirm(),U.value=!0;const o=await Qa(s);Ja.excel(o,"\u5546\u54C1\u5217\u8868.xls")}catch{}finally{U.value=!1}},P=_();return fa(()=>{y()}),ya(async()=>{A.query.categoryId&&(s.value.categoryId=Number(A.query.categoryId)),await x(),await y();const o=await Xa({});P.value=Fa(o,"id","parentId")}),(o,l)=>{const V=$a,X=ga,m=ba,Z=ka,W=ha,E=xa,f=Ca,F=wa,H=Ra,aa=Ia,ea=Va,g=Pa,I=Ta,p=Ua,ta=Ma,la=La,na=Da,sa=Na,oa=Sa,ua=Ba,h=_a("hasPermi"),ia=Aa;return i(),L(D,null,[a(V,{title:"\u3010\u5546\u54C1\u3011\u5546\u54C1 SPU \u4E0E SKU",url:"https://doc.iocoder.cn/mall/product-spu-sku/"}),a(H,null,{default:e(()=>[a(F,{ref_key:"queryFormRef",ref:R,inline:!0,model:n(s),class:"-mb-15px","label-width":"68px"},{default:e(()=>[a(m,{label:"\u5546\u54C1\u540D\u79F0",prop:"name"},{default:e(()=>[a(X,{modelValue:n(s).name,"onUpdate:modelValue":l[0]||(l[0]=t=>n(s).name=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",onKeyup:va(T,["enter"])},null,8,["modelValue"])]),_:1}),a(m,{label:"\u5546\u54C1\u5206\u7C7B",prop:"categoryId"},{default:e(()=>[a(Z,{modelValue:n(s).categoryId,"onUpdate:modelValue":l[1]||(l[1]=t=>n(s).categoryId=t),options:n(P),props:n(Ha),class:"w-1/1",clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","options","props"])]),_:1}),a(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:e(()=>[a(W,{modelValue:n(s).createTime,"onUpdate:modelValue":l[2]||(l[2]=t=>n(s).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(m,null,{default:e(()=>[a(f,{onClick:T},{default:e(()=>[a(E,{class:"mr-5px",icon:"ep:search"}),l[7]||(l[7]=d(" \u641C\u7D22 "))]),_:1}),a(f,{onClick:G},{default:e(()=>[a(E,{class:"mr-5px",icon:"ep:refresh"}),l[8]||(l[8]=d(" \u91CD\u7F6E "))]),_:1}),v((i(),c(f,{plain:"",type:"primary",onClick:l[3]||(l[3]=t=>z(void 0))},{default:e(()=>[a(E,{class:"mr-5px",icon:"ep:plus"}),l[9]||(l[9]=d(" \u65B0\u589E "))]),_:1})),[[h,["product:spu:create"]]]),v((i(),c(f,{loading:n(U),plain:"",type:"success",onClick:Q},{default:e(()=>[a(E,{class:"mr-5px",icon:"ep:download"}),l[10]||(l[10]=d(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[h,["product:spu:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(H,null,{default:e(()=>[a(ea,{modelValue:n(s).tabType,"onUpdate:modelValue":l[4]||(l[4]=t=>n(s).tabType=t),onTabClick:j},{default:e(()=>[(i(!0),L(D,null,Ea(n(M),t=>(i(),c(aa,{key:t.type,label:t.name+"("+t.count+")",name:t.type},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),v((i(),c(oa,{data:n(B)},{default:e(()=>[a(p,{type:"expand"},{default:e(({row:t})=>[a(F,{class:"spu-table-expand","label-position":"left"},{default:e(()=>[a(I,null,{default:e(()=>[a(g,{span:24},{default:e(()=>[a(I,null,{default:e(()=>[a(g,{span:8},{default:e(()=>[a(m,{label:"\u5546\u54C1\u5206\u7C7B:"},{default:e(()=>{return[w("span",null,k((r=t.categoryId,Ka(P.value,r))),1)];var r}),_:2},1024)]),_:2},1024),a(g,{span:8},{default:e(()=>[a(m,{label:"\u5E02\u573A\u4EF7:"},{default:e(()=>[w("span",null,k(n(N)(t.marketPrice)),1)]),_:2},1024)]),_:2},1024),a(g,{span:8},{default:e(()=>[a(m,{label:"\u6210\u672C\u4EF7:"},{default:e(()=>[w("span",null,k(n(N)(t.costPrice)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024),a(I,null,{default:e(()=>[a(g,{span:24},{default:e(()=>[a(I,null,{default:e(()=>[a(g,{span:8},{default:e(()=>[a(m,{label:"\u6D4F\u89C8\u91CF:"},{default:e(()=>[w("span",null,k(t.browseCount),1)]),_:2},1024)]),_:2},1024),a(g,{span:8},{default:e(()=>[a(m,{label:"\u865A\u62DF\u9500\u91CF:"},{default:e(()=>[w("span",null,k(t.virtualSalesCount),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:1}),a(p,{label:"\u5546\u54C1\u7F16\u53F7","min-width":"140",prop:"id"}),a(p,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"300"},{default:e(({row:t})=>[w("div",Za,[a(ta,{fit:"cover",src:t.picUrl,class:"flex-none w-50px h-50px",onClick:r=>{return u=t.picUrl,void Ya({urlList:[u]});var u}},null,8,["src","onClick"]),w("div",Wa,[a(la,{effect:"dark",content:t.name,placement:"top"},{default:e(()=>[w("div",null,k(t.name),1)]),_:2},1032,["content"])])])]),_:1}),a(p,{align:"center",label:"\u4EF7\u683C","min-width":"160",prop:"price"},{default:e(({row:t})=>[d(" \xA5 "+k(n(N)(t.price)),1)]),_:1}),a(p,{align:"center",label:"\u9500\u91CF","min-width":"90",prop:"salesCount"}),a(p,{align:"center",label:"\u5E93\u5B58","min-width":"90",prop:"stock"}),a(p,{align:"center",label:"\u6392\u5E8F","min-width":"70",prop:"sort"}),a(p,{align:"center",label:"\u9500\u552E\u72B6\u6001","min-width":"80"},{default:e(({row:t})=>[t.status>=0?(i(),c(na,{key:0,modelValue:t.status,"onUpdate:modelValue":r=>t.status=r,"active-value":1,"inactive-value":0,"active-text":"\u4E0A\u67B6","inactive-text":"\u4E0B\u67B6","inline-prompt":"",onChange:r=>(async u=>{try{const K=u.status?"\u4E0A\u67B6":"\u4E0B\u67B6";await b.confirm(`\u786E\u8BA4\u8981${K}"${u.name}"\u5417\uFF1F`),await J({id:u.id,status:u.status}),b.success(K+"\u6210\u529F"),await x(),await y()}catch{u.status=u.status===C.DISABLE.status?C.ENABLE.status:C.DISABLE.status}})(t)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(i(),c(sa,{key:1,type:"info"},{default:e(()=>l[11]||(l[11]=[d("\u56DE\u6536\u7AD9")])),_:1}))]),_:1}),a(p,{formatter:n(za),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(p,{align:"center",fixed:"right",label:"\u64CD\u4F5C","min-width":"200"},{default:e(({row:t})=>[a(f,{link:"",type:"primary",onClick:r=>{return u=t.id,void Y({name:"ProductSpuDetail",params:{id:u}});var u}},{default:e(()=>l[12]||(l[12]=[d(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"]),v((i(),c(f,{link:"",type:"primary",onClick:r=>z(t.id)},{default:e(()=>l[13]||(l[13]=[d(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])),[[h,["product:spu:update"]]]),n(s).tabType===4?(i(),L(D,{key:0},[v((i(),c(f,{link:"",type:"danger",onClick:r=>(async u=>{try{await b.delConfirm(),await Ga(u),b.success(O("common.delSuccess")),await x(),await y()}catch{}})(t.id)},{default:e(()=>l[14]||(l[14]=[d(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[h,["product:spu:delete"]]]),v((i(),c(f,{link:"",type:"primary",onClick:r=>$(t,n(C).DISABLE.status)},{default:e(()=>l[15]||(l[15]=[d(" \u6062\u590D ")])),_:2},1032,["onClick"])),[[h,["product:spu:update"]]])],64)):v((i(),c(f,{key:1,link:"",type:"danger",onClick:r=>$(t,n(C).RECYCLE.status)},{default:e(()=>l[16]||(l[16]=[d(" \u56DE\u6536 ")])),_:2},1032,["onClick"])),[[h,["product:spu:update"]]])]),_:1})]),_:1},8,["data"])),[[ia,n(S)]]),a(ua,{limit:n(s).pageSize,"onUpdate:limit":l[5]||(l[5]=t=>n(s).pageSize=t),page:n(s).pageNo,"onUpdate:page":l[6]||(l[6]=t=>n(s).pageNo=t),total:n(q),onPagination:y},null,8,["limit","page","total"])]),_:1})],64)}}}),[["__scopeId","data-v-cf012bf5"]]);export{ae as default};
