import{d as G,p as H,b as J,r as c,f as M,u as Q,q as Y,O as j,c as B,o as m,g as e,w as t,s as W,a as o,v as X,P as Z,Q as V,J as y,G as $,H as p,I as ee,A as _,K as ae,L as le,a5 as te,t as oe,D,M as re,F as ie}from"./index-CRsFgzy0.js";import{_ as pe}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as de}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{d as ne}from"./formatTime-DhdtkSIS.js";import{P as F}from"./index-RX3kpD3r.js";import{_ as ue}from"./ProductForm.vue_vue_type_script_setup_true_lang-K5uyFEqh.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";const ce=G({name:"IoTProduct",__name:"index",setup(me){const P=H(),{t:O}=J(),v=c(!0),k=c([]),C=c(0),r=M({pageNo:1,pageSize:10,name:void 0,createTime:[],productKey:void 0,protocolId:void 0,categoryId:void 0,description:void 0,validateType:void 0,status:void 0,deviceType:void 0,netType:void 0,protocolType:void 0,dataFormat:void 0}),h=c(),d=async()=>{v.value=!0;try{const s=await F.getProductPage(r);k.value=s.list,C.value=s.total}finally{v.value=!1}},f=()=>{r.pageNo=1,d()},N=()=>{h.value.resetFields(),f()},x=c(),{push:R}=Q(),K=s=>{R({name:"IoTProductDetail",params:{id:s}})};return Y(()=>{d()}),(s,a)=>{const I=Z,g=X,w=ee,n=$,q=W,S=se,z=te,i=le,U=de,E=ae,A=pe,T=j("hasPermi"),L=re;return m(),B(ie,null,[e(S,null,{default:t(()=>[e(q,{class:"-mb-15px",model:o(r),ref_key:"queryFormRef",ref:h,inline:!0,"label-width":"68px"},{default:t(()=>[e(g,{label:"\u4EA7\u54C1\u540D\u79F0",prop:"name"},{default:t(()=>[e(I,{modelValue:o(r).name,"onUpdate:modelValue":a[0]||(a[0]=l=>o(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u540D\u79F0",clearable:"",onKeyup:V(f,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(g,{label:"ProductKey",prop:"productKey"},{default:t(()=>[e(I,{modelValue:o(r).productKey,"onUpdate:modelValue":a[1]||(a[1]=l=>o(r).productKey=l),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u6807\u8BC6",clearable:"",onKeyup:V(f,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(g,null,{default:t(()=>[e(n,{onClick:f},{default:t(()=>[e(w,{icon:"ep:search",class:"mr-5px"}),a[5]||(a[5]=p(" \u641C\u7D22"))]),_:1}),e(n,{onClick:N},{default:t(()=>[e(w,{icon:"ep:refresh",class:"mr-5px"}),a[6]||(a[6]=p(" \u91CD\u7F6E"))]),_:1}),y((m(),_(n,{type:"primary",plain:"",onClick:a[2]||(a[2]=l=>{return u="create",void x.value.open(u,b);var u,b})},{default:t(()=>[e(w,{icon:"ep:plus",class:"mr-5px"}),a[7]||(a[7]=p(" \u65B0\u589E "))]),_:1})),[[T,["iot:product:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(S,null,{default:t(()=>[y((m(),_(E,{data:o(k),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(i,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"name"},{default:t(l=>[e(z,{onClick:u=>K(l.row.id)},{default:t(()=>[p(oe(l.row.name),1)]),_:2},1032,["onClick"])]),_:1}),e(i,{label:"ProductKey",align:"center",prop:"productKey"}),e(i,{label:"\u8BBE\u5907\u7C7B\u578B",align:"center",prop:"deviceType"},{default:t(l=>[e(U,{type:o(D).IOT_PRODUCT_DEVICE_TYPE,value:l.row.deviceType},null,8,["type","value"])]),_:1}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:o(ne),width:"180px"},null,8,["formatter"]),e(i,{label:"\u4EA7\u54C1\u72B6\u6001",align:"center",prop:"status"},{default:t(l=>[e(U,{type:o(D).IOT_PRODUCT_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(i,{label:"\u64CD\u4F5C",align:"center"},{default:t(l=>[y((m(),_(n,{link:"",type:"primary",onClick:u=>K(l.row.id)},{default:t(()=>a[8]||(a[8]=[p(" \u67E5\u770B ")])),_:2},1032,["onClick"])),[[T,["iot:product:query"]]]),y((m(),_(n,{link:"",type:"danger",onClick:u=>(async b=>{try{await P.delConfirm(),await F.deleteProduct(b),P.success(O("common.delSuccess")),await d()}catch{}})(l.row.id),disabled:l.row.status===1},{default:t(()=>a[9]||(a[9]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick","disabled"])),[[T,["iot:product:delete"]]])]),_:1})]),_:1},8,["data"])),[[L,o(v)]]),e(A,{total:o(C),page:o(r).pageNo,"onUpdate:page":a[3]||(a[3]=l=>o(r).pageNo=l),limit:o(r).pageSize,"onUpdate:limit":a[4]||(a[4]=l=>o(r).pageSize=l),onPagination:d},null,8,["total","page","limit"])]),_:1}),e(ue,{ref_key:"formRef",ref:x,onSuccess:d},null,512)],64)}}});export{ce as default};
