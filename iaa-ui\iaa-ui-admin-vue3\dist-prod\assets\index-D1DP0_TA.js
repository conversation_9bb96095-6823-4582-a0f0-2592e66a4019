import{d as F,f as E,e as M,r as s,aK as T,q as G,c as K,o as j,g as a,w as e,s as A,a as l,v as B,C as J,G as L,H as U,I as N,l as O,m as Q,n as W,h as X,F as Z}from"./index-CRsFgzy0.js";import{_ as $}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{E as aa}from"./el-tree-select-BijZG_HG.js";import{_ as ea}from"./ContractPriceRank.vue_vue_type_script_setup_true_lang-B4-jbTPC.js";import{_ as la}from"./ReceivablePriceRank.vue_vue_type_script_setup_true_lang-CfuSUnT3.js";import{_ as ra}from"./ContractCountRank.vue_vue_type_script_setup_true_lang-BXdDGCcg.js";import{_ as ta}from"./ProductSalesRank.vue_vue_type_script_setup_true_lang-DJSBhZ4k.js";import{_ as oa}from"./CustomerCountRank.vue_vue_type_script_setup_true_lang-A1fBR6r5.js";import{_ as sa}from"./ContactCountRank.vue_vue_type_script_setup_true_lang-Bpj2f3jN.js";import{_ as na}from"./FollowCountRank.vue_vue_type_script_setup_true_lang-DDp3NXpt.js";import{_ as ua}from"./FollowCustomerCountRank.vue_vue_type_script_setup_true_lang-Bc5Uniaq.js";import{h as ma,d as fa}from"./tree-COGD3qag.js";import{g as ca}from"./index-C0yL_L5C.js";import{f as Y,e as da,g as pa,h as ia}from"./formatTime-DhdtkSIS.js";import"./el-skeleton-item-CZ5buDOR.js";import"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import"./echarts-BcS7Kngw.js";import"./rank-DcFXIK-C.js";const ka=F({name:"CrmStatisticsRank",__name:"index",setup(ya){const r=E({deptId:M().getUser.deptId,times:[Y(da(new Date(new Date().getTime()-6048e5))),Y(pa(new Date(new Date().getTime()-864e5)))]}),q=s(),w=s([]),i=s("contractPriceRank"),v=s(),D=s(),h=s(),V=s(),x=s(),z=s(),g=s(),I=s(),C=()=>{var k,t,m,f,c,y,u,_,d,o,p,R,n,P,b,S;switch(i.value){case"contractPriceRank":(t=(k=v.value)==null?void 0:k.loadData)==null||t.call(k);break;case"receivablePriceRank":(f=(m=D.value)==null?void 0:m.loadData)==null||f.call(m);break;case"contractCountRank":(y=(c=h.value)==null?void 0:c.loadData)==null||y.call(c);break;case"productSalesRank":(_=(u=V.value)==null?void 0:u.loadData)==null||_.call(u);break;case"customerCountRank":(o=(d=x.value)==null?void 0:d.loadData)==null||o.call(d);break;case"contactCountRank":(R=(p=z.value)==null?void 0:p.loadData)==null||R.call(p);break;case"followCountRank":(P=(n=g.value)==null?void 0:n.loadData)==null||P.call(n);break;case"followCustomerCountRank":(S=(b=I.value)==null?void 0:b.loadData)==null||S.call(b)}};T(i,()=>{C()});const H=()=>{q.value.resetFields(),C()};return G(async()=>{w.value=ma(await ca())}),(k,t)=>{const m=J,f=B,c=aa,y=N,u=L,_=A,d=$,o=W,p=O,R=X;return j(),K(Z,null,[a(d,null,{default:e(()=>[a(_,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:q,inline:!0,"label-width":"68px"},{default:e(()=>[a(f,{label:"\u65F6\u95F4\u8303\u56F4",prop:"orderDate"},{default:e(()=>[a(m,{modelValue:l(r).times,"onUpdate:modelValue":t[0]||(t[0]=n=>l(r).times=n),shortcuts:l(ia),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")]},null,8,["modelValue","shortcuts","default-time"])]),_:1}),a(f,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:e(()=>[a(c,{modelValue:l(r).deptId,"onUpdate:modelValue":t[1]||(t[1]=n=>l(r).deptId=n),data:l(w),props:l(fa),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",class:"!w-240px"},null,8,["modelValue","data","props"])]),_:1}),a(f,null,{default:e(()=>[a(u,{onClick:C},{default:e(()=>[a(y,{icon:"ep:search",class:"mr-5px"}),t[3]||(t[3]=U(" \u641C\u7D22"))]),_:1}),a(u,{onClick:H},{default:e(()=>[a(y,{icon:"ep:refresh",class:"mr-5px"}),t[4]||(t[4]=U(" \u91CD\u7F6E"))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(R,null,{default:e(()=>[a(p,{modelValue:l(i),"onUpdate:modelValue":t[2]||(t[2]=n=>Q(i)?i.value=n:null)},{default:e(()=>[a(o,{label:"\u5408\u540C\u91D1\u989D\u6392\u884C",name:"contractPriceRank",lazy:""},{default:e(()=>[a(ea,{"query-params":l(r),ref_key:"contractPriceRankRef",ref:v},null,8,["query-params"])]),_:1}),a(o,{label:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C",name:"receivablePriceRank",lazy:""},{default:e(()=>[a(la,{"query-params":l(r),ref_key:"receivablePriceRankRef",ref:D},null,8,["query-params"])]),_:1}),a(o,{label:"\u7B7E\u7EA6\u5408\u540C\u6392\u884C",name:"contractCountRank",lazy:""},{default:e(()=>[a(ra,{"query-params":l(r),ref_key:"contractCountRankRef",ref:h},null,8,["query-params"])]),_:1}),a(o,{label:"\u4EA7\u54C1\u9500\u91CF\u6392\u884C",name:"productSalesRank",lazy:""},{default:e(()=>[a(ta,{"query-params":l(r),ref_key:"productSalesRankRef",ref:V},null,8,["query-params"])]),_:1}),a(o,{label:"\u65B0\u589E\u5BA2\u6237\u6570\u6392\u884C",name:"customerCountRank",lazy:""},{default:e(()=>[a(oa,{"query-params":l(r),ref_key:"customerCountRankRef",ref:x},null,8,["query-params"])]),_:1}),a(o,{label:"\u65B0\u589E\u8054\u7CFB\u4EBA\u6570\u6392\u884C",name:"contactCountRank",lazy:""},{default:e(()=>[a(sa,{"query-params":l(r),ref_key:"contactCountRankRef",ref:z},null,8,["query-params"])]),_:1}),a(o,{label:"\u8DDF\u8FDB\u6B21\u6570\u6392\u884C",name:"followCountRank",lazy:""},{default:e(()=>[a(na,{"query-params":l(r),ref_key:"followCountRankRef",ref:g},null,8,["query-params"])]),_:1}),a(o,{label:"\u8DDF\u8FDB\u5BA2\u6237\u6570\u6392\u884C",name:"followCustomerCountRank",lazy:""},{default:e(()=>[a(ua,{"query-params":l(r),ref_key:"followCustomerCountRankRef",ref:I},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}});export{ka as default};
