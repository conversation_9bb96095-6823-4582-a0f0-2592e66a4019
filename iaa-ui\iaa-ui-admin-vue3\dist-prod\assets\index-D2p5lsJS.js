import{d as X,p as z,b as B,r as n,f as I,q as J,O as L,c as M,o,g as e,w as l,s as Q,a as t,v as j,P as W,Q as Y,x as Z,F as R,y as $,R as ee,D as S,A as m,B as ae,J as f,G as le,H as u,I as re,K as te,L as se,M as oe}from"./index-CRsFgzy0.js";import{_ as pe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as de}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ie}from"./index-DYfNUK1u.js";import{d as ne}from"./formatTime-DhdtkSIS.js";import{b as me,d as ue}from"./index-wRqO_lBC.js";import{_ as ce}from"./ExpressTemplateForm.vue_vue_type_script_setup_true_lang-YOECsB6W.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-DLC3Afbg.js";import"./tree-COGD3qag.js";const fe=X({name:"DeliveryExpressTemplate",__name:"index",setup(_e){const w=z(),{t:V}=B(),A=n(0),_=n(!0),x=n([]),s=I({pageNo:1,pageSize:10,name:"",chargeMode:void 0}),g=n(),c=async()=>{_.value=!0;try{const d=await me(s);x.value=d.list,A.value=d.total}finally{_.value=!1}},y=()=>{s.pageNo=1,c()},D=()=>{g.value.resetFields(),y()},k=n(),C=(d,a)=>{k.value.open(d,a)};return J(()=>{c()}),(d,a)=>{const F=ie,P=W,h=j,G=ae,H=Z,v=re,i=le,N=Q,E=de,p=se,O=pe,q=te,b=L("hasPermi"),K=oe;return o(),M(R,null,[e(F,{title:"\u3010\u4EA4\u6613\u3011\u5FEB\u9012\u53D1\u8D27",url:"https://doc.iocoder.cn/mall/trade-delivery-express/"}),e(E,null,{default:l(()=>[e(N,{class:"-mb-15px",model:t(s),ref_key:"queryFormRef",ref:g,inline:!0,"label-width":"100px"},{default:l(()=>[e(h,{label:"\u6A21\u677F\u540D\u79F0",prop:"name"},{default:l(()=>[e(P,{modelValue:t(s).name,"onUpdate:modelValue":a[0]||(a[0]=r=>t(s).name=r),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u540D\u79F0",clearable:"",onKeyup:Y(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(h,{label:"\u8BA1\u8D39\u65B9\u5F0F",prop:"chargeMode"},{default:l(()=>[e(H,{modelValue:t(s).chargeMode,"onUpdate:modelValue":a[1]||(a[1]=r=>t(s).chargeMode=r),placeholder:"\u8BA1\u8D39\u65B9\u5F0F",clearable:"",class:"!w-240px"},{default:l(()=>[(o(!0),M(R,null,$(t(ee)(t(S).EXPRESS_CHARGE_MODE),r=>(o(),m(G,{key:r.value,label:r.label,value:r.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:l(()=>[e(i,{onClick:y},{default:l(()=>[e(v,{icon:"ep:search",class:"mr-5px"}),a[3]||(a[3]=u(" \u641C\u7D22"))]),_:1}),e(i,{onClick:D},{default:l(()=>[e(v,{icon:"ep:refresh",class:"mr-5px"}),a[4]||(a[4]=u(" \u91CD\u7F6E"))]),_:1}),f((o(),m(i,{type:"primary",plain:"",onClick:a[2]||(a[2]=r=>C("create"))},{default:l(()=>[e(v,{icon:"ep:plus",class:"mr-5px"}),a[5]||(a[5]=u(" \u65B0\u589E "))]),_:1})),[[b,["trade:delivery:express-template:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(E,null,{default:l(()=>[f((o(),m(q,{data:t(x)},{default:l(()=>[e(p,{label:"\u7F16\u53F7","min-width":"60",prop:"id"}),e(p,{label:"\u6A21\u677F\u540D\u79F0","min-width":"100",prop:"name"}),e(p,{label:"\u8BA1\u8D39\u65B9\u5F0F",prop:"chargeMode","min-width":"100",align:"center"},{default:l(r=>[e(O,{type:t(S).EXPRESS_CHARGE_MODE,value:r.row.chargeMode},null,8,["type","value"])]),_:1}),e(p,{label:"\u6392\u5E8F","min-width":"100",prop:"sort"}),e(p,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:t(ne)},null,8,["formatter"]),e(p,{label:"\u64CD\u4F5C",align:"center"},{default:l(r=>[f((o(),m(i,{link:"",type:"primary",onClick:T=>C("update",r.row.id)},{default:l(()=>a[6]||(a[6]=[u(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[b,["trade:delivery:express-template:update"]]]),f((o(),m(i,{link:"",type:"danger",onClick:T=>(async U=>{try{await w.delConfirm(),await ue(U),w.success(V("common.delSuccess")),await c()}catch{}})(r.row.id)},{default:l(()=>a[7]||(a[7]=[u(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[b,["trade:delivery:express-template:delete"]]])]),_:1})]),_:1},8,["data"])),[[K,t(_)]])]),_:1}),e(ce,{ref_key:"formRef",ref:k,onSuccess:c},null,512)],64)}}});export{fe as default};
