import{d as T,r as u,aK as R,c as e,o as r,F as B,y as I,aw as l,a3 as i,i as c,g as w,X as d,t as y,a as m,aR as h,A as S}from"./index-CRsFgzy0.js";import{E as P}from"./el-image-BQpHFDaE.js";import{b as $}from"./spu-BHhhuUrI.js";import{e as A}from"./combinationActivity-DX3uo1WR.js";const E={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},L={class:"text-12px"},U={class:"absolute bottom-8px right-8px"},_=T({name:"PromotionCombination",__name:"index",props:{property:{}},setup(g){const s=g,n=u([]),x=u([]),f=u([]);R(()=>s.property.activityIds,async()=>{try{const o=s.property.activityIds;Array.isArray(o)&&o.length>0&&(f.value=await A(o),n.value=[],x.value=f.value.map(p=>p.spuId).filter(p=>typeof p=="number"),x.value.length>0&&(n.value=await $(x.value)),f.value.forEach(p=>{const a=n.value.find(t=>t.id===p.spuId);a&&(a.price=Math.min(p.combinationPrice||1/0,a.price||1/0))}))}catch{}},{immediate:!0,deep:!0});const k=o=>{const p=s.property.layoutType==="twoCol"?2:1;return{marginLeft:o%p==0?"0":s.property.space+"px",marginTop:o<p?"0":s.property.space+"px"}},b=u(),C=()=>{let o="100%";return s.property.layoutType==="twoCol"&&(o=(b.value.offsetWidth-s.property.space)/2+"px"),{width:o}};return(o,p)=>{const a=P;return r(),e("div",{class:d("box-content min-h-30px w-full flex flex-row flex-wrap"),ref_key:"containerRef",ref:b},[(r(!0),e(B,null,I(m(n),(t,v)=>(r(),e("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:l({...k(v),...C(),borderTopLeftRadius:`${o.property.borderRadiusTop}px`,borderTopRightRadius:`${o.property.borderRadiusTop}px`,borderBottomLeftRadius:`${o.property.borderRadiusBottom}px`,borderBottomRightRadius:`${o.property.borderRadiusBottom}px`}),key:v},[o.property.badge.show?(r(),e("div",E,[w(a,{fit:"cover",src:o.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):i("",!0),c("div",{class:d(["h-140px",{"w-full":o.property.layoutType!=="oneColSmallImg","w-140px":o.property.layoutType==="oneColSmallImg"}])},[w(a,{fit:"cover",class:"h-full w-full",src:t.picUrl},null,8,["src"])],2),c("div",{class:d([" flex flex-col gap-8px p-8px box-border",{"w-full":o.property.layoutType!=="oneColSmallImg","w-[calc(100%-140px-16px)]":o.property.layoutType==="oneColSmallImg"}])},[o.property.fields.name.show?(r(),e("div",{key:0,class:d(["text-14px ",{truncate:o.property.layoutType!=="oneColSmallImg","overflow-ellipsis line-clamp-2":o.property.layoutType==="oneColSmallImg"}]),style:l({color:o.property.fields.name.color})},y(t.name),7)):i("",!0),o.property.fields.introduction.show?(r(),e("div",{key:1,class:"truncate text-12px",style:l({color:o.property.fields.introduction.color})},y(t.introduction),5)):i("",!0),c("div",null,[o.property.fields.price.show?(r(),e("span",{key:0,class:"text-16px",style:l({color:o.property.fields.price.color})}," \uFFE5"+y(m(h)(t.price||1/0)),5)):i("",!0),o.property.fields.marketPrice.show&&t.marketPrice?(r(),e("span",{key:1,class:"ml-4px text-10px line-through",style:l({color:o.property.fields.marketPrice.color})},"\uFFE5"+y(m(h)(t.marketPrice)),5)):i("",!0)]),c("div",L,[o.property.fields.salesCount.show?(r(),e("span",{key:0,style:l({color:o.property.fields.salesCount.color})}," \u5DF2\u552E"+y((t.salesCount||0)+(t.virtualSalesCount||0))+"\u4EF6 ",5)):i("",!0),o.property.fields.stock.show?(r(),e("span",{key:1,style:l({color:o.property.fields.stock.color})}," \u5E93\u5B58"+y(t.stock||0),5)):i("",!0)])],2),c("div",U,[o.property.btnBuy.type==="text"?(r(),e("span",{key:0,class:"rounded-full p-x-12px p-y-4px text-12px text-white",style:l({background:`linear-gradient(to right, ${o.property.btnBuy.bgBeginColor}, ${o.property.btnBuy.bgEndColor}`})},y(o.property.btnBuy.text),5)):(r(),S(a,{key:1,class:"h-28px w-28px rounded-full",fit:"cover",src:o.property.btnBuy.imgUrl},null,8,["src"]))])],4))),128))],512)}}});export{_ as default};
