import{d as H,p as J,b as Q,r as s,f as j,q as B,O as W,c as X,o as c,g as e,w as t,s as Y,a as l,v as Z,P as $,Q as ee,J as u,G as ae,H as d,I as le,A as f,K as te,L as re,e9 as v,D as oe,M as pe,F as ne}from"./index-CRsFgzy0.js";import{_ as se}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as ie}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as ce}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{E as de}from"./el-tree-select-BijZG_HG.js";import{_ as me}from"./index-DYfNUK1u.js";import{d as ue}from"./formatTime-DhdtkSIS.js";import{d as fe}from"./download-oWiM5xVU.js";import{P}from"./index-v67yau6-.js";import{P as ge}from"./index-DjmZXe-0.js";import{_ as ye}from"./ProductForm.vue_vue_type_script_setup_true_lang-DCKxQYK0.js";import{h as _e,d as we}from"./tree-COGD3qag.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-CrB20wZB.js";import"./constants-uird_4gU.js";const be=H({name:"ErpProduct",__name:"index",setup(xe){const _=J(),{t:z}=Q(),w=s(!0),k=s([]),C=s(0),o=j({pageNo:1,pageSize:10,name:void 0,categoryId:void 0}),S=s(),b=s(!1),N=s([]),m=async()=>{w.value=!0;try{const n=await P.getProductPage(o);k.value=n.list,C.value=n.total}finally{w.value=!1}},x=()=>{o.pageNo=1,m()},F=()=>{S.value.resetFields(),x()},I=s(),V=(n,a)=>{I.value.open(n,a)},O=async()=>{try{await _.exportConfirm(),b.value=!0;const n=await P.exportProduct(o);fe.excel(n,"\u4EA7\u54C1.xls")}catch{}finally{b.value=!1}};return B(async()=>{await m();const n=await ge.getProductCategorySimpleList();N.value=_e(n,"id","parentId")}),(n,a)=>{const L=me,M=$,h=Z,T=de,g=le,i=ae,q=Y,U=ce,p=re,A=ie,E=te,K=se,y=W("hasPermi"),R=pe;return c(),X(ne,null,[e(L,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u4FE1\u606F\u3001\u5206\u7C7B\u3001\u5355\u4F4D",url:"https://doc.iocoder.cn/erp/product/"}),e(U,null,{default:t(()=>[e(q,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:S,inline:!0,"label-width":"68px"},{default:t(()=>[e(h,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[e(M,{modelValue:l(o).name,"onUpdate:modelValue":a[0]||(a[0]=r=>l(o).name=r),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:ee(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(h,{label:"\u5206\u7C7B",prop:"categoryId"},{default:t(()=>[e(T,{modelValue:l(o).categoryId,"onUpdate:modelValue":a[1]||(a[1]=r=>l(o).categoryId=r),data:l(N),props:l(we),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B",class:"!w-240px"},null,8,["modelValue","data","props"])]),_:1}),e(h,null,{default:t(()=>[e(i,{onClick:x},{default:t(()=>[e(g,{icon:"ep:search",class:"mr-5px"}),a[5]||(a[5]=d(" \u641C\u7D22"))]),_:1}),e(i,{onClick:F},{default:t(()=>[e(g,{icon:"ep:refresh",class:"mr-5px"}),a[6]||(a[6]=d(" \u91CD\u7F6E"))]),_:1}),u((c(),f(i,{type:"primary",plain:"",onClick:a[2]||(a[2]=r=>V("create"))},{default:t(()=>[e(g,{icon:"ep:plus",class:"mr-5px"}),a[7]||(a[7]=d(" \u65B0\u589E "))]),_:1})),[[y,["erp:product:create"]]]),u((c(),f(i,{type:"success",plain:"",onClick:O,loading:l(b)},{default:t(()=>[e(g,{icon:"ep:download",class:"mr-5px"}),a[8]||(a[8]=d(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[y,["erp:product:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(U,null,{default:t(()=>[u((c(),f(E,{data:l(k),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(p,{label:"\u6761\u7801",align:"center",prop:"barCode"}),e(p,{label:"\u540D\u79F0",align:"center",prop:"name"}),e(p,{label:"\u89C4\u683C",align:"center",prop:"standard"}),e(p,{label:"\u5206\u7C7B",align:"center",prop:"categoryName"}),e(p,{label:"\u5355\u4F4D",align:"center",prop:"unitName"}),e(p,{label:"\u91C7\u8D2D\u4EF7\u683C",align:"center",prop:"purchasePrice",formatter:l(v)},null,8,["formatter"]),e(p,{label:"\u9500\u552E\u4EF7\u683C",align:"center",prop:"salePrice",formatter:l(v)},null,8,["formatter"]),e(p,{label:"\u6700\u4F4E\u4EF7\u683C",align:"center",prop:"minPrice",formatter:l(v)},null,8,["formatter"]),e(p,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:t(r=>[e(A,{type:l(oe).COMMON_STATUS,value:r.row.status},null,8,["type","value"])]),_:1}),e(p,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(ue),width:"180px"},null,8,["formatter"]),e(p,{label:"\u64CD\u4F5C",align:"center",width:"110"},{default:t(r=>[u((c(),f(i,{link:"",type:"primary",onClick:D=>V("update",r.row.id)},{default:t(()=>a[9]||(a[9]=[d(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["erp:product:update"]]]),u((c(),f(i,{link:"",type:"danger",onClick:D=>(async G=>{try{await _.delConfirm(),await P.deleteProduct(G),_.success(z("common.delSuccess")),await m()}catch{}})(r.row.id)},{default:t(()=>a[10]||(a[10]=[d(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["erp:product:delete"]]])]),_:1})]),_:1},8,["data"])),[[R,l(w)]]),e(K,{total:l(C),page:l(o).pageNo,"onUpdate:page":a[3]||(a[3]=r=>l(o).pageNo=r),limit:l(o).pageSize,"onUpdate:limit":a[4]||(a[4]=r=>l(o).pageSize=r),onPagination:m},null,8,["total","page","limit"])]),_:1}),e(ye,{ref_key:"formRef",ref:I,onSuccess:m},null,512)],64)}}});export{be as default};
