import{d as c,ah as d,fA as f,A as _,o as g,P as v,b4 as b,a as s,m as t,w as h,g as q,c7 as x,_ as A}from"./index-CRsFgzy0.js";import{P as I}from"./color-CIFUYK2M.js";const P=A(c({name:"InputWithColor",__name:"index",props:{modelValue:d.string.def("").isRequired,color:d.string.def("").isRequired},emits:["update:modelValue","update:color"],setup(u,{emit:r}){const n=u,m=r,{modelValue:a,color:o}=f(n,m);return(i,e)=>{const p=x,V=v;return g(),_(V,b({modelValue:s(a),"onUpdate:modelValue":e[1]||(e[1]=l=>t(a)?a.value=l:null)},i.$attrs),{append:h(()=>[q(p,{modelValue:s(o),"onUpdate:modelValue":e[0]||(e[0]=l=>t(o)?o.value=l:null),predefine:s(I)},null,8,["modelValue","predefine"])]),_:1},16,["modelValue"])}}}),[["__scopeId","data-v-c319fbe4"]]);export{P as _};
