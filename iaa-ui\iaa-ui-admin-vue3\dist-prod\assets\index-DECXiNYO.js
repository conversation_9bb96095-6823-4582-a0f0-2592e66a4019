import{d as q,p as A,b as G,r as _,f as H,O as J,c as K,o as n,g as t,w as o,s as L,a as l,v as O,J as p,A as d,G as j,H as y,I as D,K as V,L as W,M as Y,F as B}from"./index-CRsFgzy0.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as Q}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as X}from"./index-DYfNUK1u.js";import{d as Z}from"./formatTime-DhdtkSIS.js";import{a as $,d as aa,s as ta}from"./index-C6FPxIVQ.js";import{_ as ea}from"./TagForm.vue_vue_type_script_setup_true_lang-C5-rAGxg.js";import{_ as la}from"./main.vue_vue_type_script_setup_true_lang-_ZtuPp57.js";import"./index-CqPfoRkb.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-vwqkkhoL.js";import"./tagsView-BnrVTrUo.js";const oa=q({name:"MpTag",__name:"index",setup(sa){const m=A(),{t:N}=G(),b=_(!0),w=_(0),h=_([]),e=H({pageNo:1,pageSize:10,accountId:-1}),k=_(null),S=s=>{e.accountId=s,e.pageNo=1,r()},r=async()=>{try{b.value=!0;const s=await $(e);h.value=s.list,w.value=s.total}finally{b.value=!1}},C=(s,a)=>{var u;(u=k.value)==null||u.open(s,e.accountId,a)},z=async()=>{try{await m.confirm("\u662F\u5426\u786E\u8BA4\u540C\u6B65\u6807\u7B7E\uFF1F"),await ta(e.accountId),m.success("\u540C\u6B65\u6807\u7B7E\u6210\u529F"),await r()}catch{}};return(s,a)=>{const u=X,I=O,v=D,f=j,F=L,x=Q,c=W,M=V,P=E,g=J("hasPermi"),R=Y;return n(),K(B,null,[t(u,{title:"\u516C\u4F17\u53F7\u6807\u7B7E",url:"https://doc.iocoder.cn/mp/tag/"}),t(x,null,{default:o(()=>[t(F,{class:"-mb-15px",model:l(e),ref:"queryFormRef",inline:!0,"label-width":"68px"},{default:o(()=>[t(I,{label:"\u516C\u4F17\u53F7",prop:"accountId"},{default:o(()=>[t(l(la),{onChange:S})]),_:1}),t(I,null,{default:o(()=>[p((n(),d(f,{type:"primary",plain:"",onClick:a[0]||(a[0]=i=>C("create")),disabled:l(e).accountId===0},{default:o(()=>[t(v,{icon:"ep:plus",class:"mr-5px"}),a[3]||(a[3]=y(" \u65B0\u589E "))]),_:1},8,["disabled"])),[[g,["mp:tag:create"]]]),p((n(),d(f,{type:"success",plain:"",onClick:z,disabled:l(e).accountId===0},{default:o(()=>[t(v,{icon:"ep:refresh",class:"mr-5px"}),a[4]||(a[4]=y(" \u540C\u6B65 "))]),_:1},8,["disabled"])),[[g,["mp:tag:sync"]]])]),_:1})]),_:1},8,["model"])]),_:1}),t(x,null,{default:o(()=>[p((n(),d(M,{data:l(h)},{default:o(()=>[t(c,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(c,{label:"\u6807\u7B7E\u540D\u79F0",align:"center",prop:"name"}),t(c,{label:"\u7C89\u4E1D\u6570",align:"center",prop:"count"}),t(c,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:l(Z)},null,8,["formatter"]),t(c,{label:"\u64CD\u4F5C",align:"center"},{default:o(i=>[p((n(),d(f,{link:"",type:"primary",onClick:T=>C("update",i.row.id)},{default:o(()=>a[5]||(a[5]=[y(" \u4FEE\u6539 ")])),_:2},1032,["onClick"])),[[g,["mp:tag:update"]]]),p((n(),d(f,{link:"",type:"danger",onClick:T=>(async U=>{try{await m.delConfirm(),await aa(U),m.success(N("common.delSuccess")),await r()}catch{}})(i.row.id)},{default:o(()=>a[6]||(a[6]=[y(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["mp:tag:delete"]]])]),_:1})]),_:1},8,["data"])),[[R,l(b)]]),t(P,{total:l(w),page:l(e).pageNo,"onUpdate:page":a[1]||(a[1]=i=>l(e).pageNo=i),limit:l(e).pageSize,"onUpdate:limit":a[2]||(a[2]=i=>l(e).pageSize=i),onPagination:r},null,8,["total","page","limit"])]),_:1}),t(ea,{ref_key:"formRef",ref:k,onSuccess:r},null,512)],64)}}});export{oa as default};
