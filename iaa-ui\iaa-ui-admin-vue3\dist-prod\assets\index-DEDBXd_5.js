import{as as D,d as J,p as j,b as X,r as i,f as $,bE as ee,q as ae,O as le,c as _,o as s,g as e,w as r,s as te,a as l,v as oe,x as re,F as g,y as V,A as u,B as pe,R as se,D as U,P as ie,Q as de,C as ue,J as k,G as ne,H as w,I as ce,K as me,L as fe,eQ as Y,M as be}from"./index-CRsFgzy0.js";import{_ as _e}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as ge}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as we}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ve}from"./index-DYfNUK1u.js";import{d as ye}from"./formatTime-DhdtkSIS.js";import{d as he}from"./download-oWiM5xVU.js";import{P as xe}from"./index-v67yau6-.js";import{W as Te}from"./index-mM5XVEAg.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";const Ve=async c=>await D.get({url:"/erp/stock-record/page",params:c}),ke=async c=>await D.download({url:"/erp/stock-record/export-excel",params:c}),ze=J({name:"ErpStockRecord",__name:"index",setup(c){const K=j(),{t:Ne}=X(),v=i(!0),z=i([]),N=i(0),o=$({pageNo:1,pageSize:10,productId:void 0,warehouseId:void 0,bizType:void 0,bizNo:void 0,createTime:[]}),C=i(),y=i(!1),I=i([]),P=i([]),m=async()=>{v.value=!0;try{const n=await Ve(o);z.value=n.list,N.value=n.total}finally{v.value=!1}},h=()=>{o.pageNo=1,m()},L=()=>{C.value.resetFields(),h()},O=i(),B=async()=>{try{await K.exportConfirm(),y.value=!0;const n=await ke(o);he.excel(n,"\u4EA7\u54C1\u5E93\u5B58\u660E\u7EC6.xls")}catch{}finally{y.value=!1}};return ee(()=>{m()}),ae(async()=>{await m(),I.value=await xe.getProductSimpleList(),P.value=await Te.getWarehouseSimpleList()}),(n,t)=>{const F=ve,x=pe,T=re,d=oe,H=ie,M=ue,f=ce,b=ne,q=te,R=we,p=fe,Q=ge,W=me,Z=_e,E=le("hasPermi"),A=be;return s(),_(g,null,[e(F,{title:"\u3010\u5E93\u5B58\u3011\u4EA7\u54C1\u5E93\u5B58\u3001\u5E93\u5B58\u660E\u7EC6",url:"https://doc.iocoder.cn/erp/stock/"}),e(R,null,{default:r(()=>[e(q,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:C,inline:!0,"label-width":"68px"},{default:r(()=>[e(d,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[e(T,{modelValue:l(o).productId,"onUpdate:modelValue":t[0]||(t[0]=a=>l(o).productId=a),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(s(!0),_(g,null,V(l(I),a=>(s(),u(x,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[e(T,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[1]||(t[1]=a=>l(o).warehouseId=a),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(s(!0),_(g,null,V(l(P),a=>(s(),u(x,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u7C7B\u578B",prop:"bizType"},{default:r(()=>[e(T,{modelValue:l(o).bizType,"onUpdate:modelValue":t[2]||(t[2]=a=>l(o).bizType=a),placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:r(()=>[(s(!0),_(g,null,V(l(se)(l(U).ERP_STOCK_RECORD_BIZ_TYPE),a=>(s(),u(x,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u4E1A\u52A1\u5355\u53F7",prop:"bizNo"},{default:r(()=>[e(H,{modelValue:l(o).bizNo,"onUpdate:modelValue":t[3]||(t[3]=a=>l(o).bizNo=a),placeholder:"\u8BF7\u8F93\u5165\u4E1A\u52A1\u5355\u53F7",clearable:"",onKeyup:de(h,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(M,{modelValue:l(o).createTime,"onUpdate:modelValue":t[4]||(t[4]=a=>l(o).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(d,null,{default:r(()=>[e(b,{onClick:h},{default:r(()=>[e(f,{icon:"ep:search",class:"mr-5px"}),t[8]||(t[8]=w(" \u641C\u7D22"))]),_:1}),e(b,{onClick:L},{default:r(()=>[e(f,{icon:"ep:refresh",class:"mr-5px"}),t[9]||(t[9]=w(" \u91CD\u7F6E"))]),_:1}),k((s(),u(b,{type:"primary",plain:"",onClick:t[5]||(t[5]=a=>{return S="create",void O.value.open(S,G);var S,G})},{default:r(()=>[e(f,{icon:"ep:plus",class:"mr-5px"}),t[10]||(t[10]=w(" \u65B0\u589E "))]),_:1})),[[E,["erp:stock-record:create"]]]),k((s(),u(b,{type:"success",plain:"",onClick:B,loading:l(y)},{default:r(()=>[e(f,{icon:"ep:download",class:"mr-5px"}),t[11]||(t[11]=w(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[E,["erp:stock-record:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(R,null,{default:r(()=>[k((s(),u(W,{data:l(z),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[e(p,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName"}),e(p,{label:"\u4EA7\u54C1\u5206\u7C7B",align:"center",prop:"categoryName"}),e(p,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unitName"}),e(p,{label:"\u4ED3\u5E93\u7F16\u53F7",align:"center",prop:"warehouseName"}),e(p,{label:"\u7C7B\u578B",align:"center",prop:"bizType","min-width":"100"},{default:r(a=>[e(Q,{type:l(U).ERP_STOCK_RECORD_BIZ_TYPE,value:a.row.bizType},null,8,["type","value"])]),_:1}),e(p,{label:"\u51FA\u5165\u5E93\u5355\u53F7",align:"center",prop:"bizNo",width:"200"}),e(p,{label:"\u51FA\u5165\u5E93\u65E5\u671F",align:"center",prop:"createTime",formatter:l(ye),width:"180px"},null,8,["formatter"]),e(p,{label:"\u51FA\u5165\u5E93\u6570\u91CF",align:"center",prop:"count",formatter:l(Y)},null,8,["formatter"]),e(p,{label:"\u5E93\u5B58\u91CF",align:"center",prop:"totalCount",formatter:l(Y)},null,8,["formatter"]),e(p,{label:"\u64CD\u4F5C\u4EBA",align:"center",prop:"creatorName"})]),_:1},8,["data"])),[[A,l(v)]]),e(Z,{total:l(N),page:l(o).pageNo,"onUpdate:page":t[6]||(t[6]=a=>l(o).pageNo=a),limit:l(o).pageSize,"onUpdate:limit":t[7]||(t[7]=a=>l(o).pageSize=a),onPagination:m},null,8,["total","page","limit"])]),_:1})],64)}}});export{ze as default};
