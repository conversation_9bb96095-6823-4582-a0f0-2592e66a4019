import{as as w,d as k,p as G,r as A,f as g,c as j,o as q,g as e,w as t,s as O,v as R,a as h,C as H,m as S,E as z,h as B,k as J,i as d,F as K}from"./index-CRsFgzy0.js";import{_ as L}from"./Echart.vue_vue_type_script_setup_true_lang-CrQApbEd.js";import{_ as N}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{e as P,g as Q,o as D,f as i,p as W}from"./formatTime-DhdtkSIS.js";import{_ as X}from"./main.vue_vue_type_script_setup_true_lang-_ZtuPp57.js";import"./echarts-BcS7Kngw.js";import"./index-vwqkkhoL.js";import"./tagsView-BnrVTrUo.js";const Z=k({name:"MpStatistics",__name:"index",setup($){const _=G(),s=A([P(new Date(new Date().getTime()-6048e5)),Q(new Date(new Date().getTime()-864e5))]),m=A(-1),u=A([]),p=g({color:["#67C23A","#E5323E"],legend:{data:["\u65B0\u589E\u7528\u6237","\u53D6\u6D88\u5173\u6CE8\u7684\u7528\u6237"]},tooltip:{},xAxis:{data:[]},yAxis:{minInterval:1},series:[{name:"\u65B0\u589E\u7528\u6237",type:"bar",label:{show:!0},barGap:0,data:[]},{name:"\u53D6\u6D88\u5173\u6CE8\u7684\u7528\u6237",type:"bar",label:{show:!0},data:[]}]}),x=g({legend:{data:["\u7D2F\u8BA1\u7528\u6237\u91CF"]},xAxis:{type:"category",data:[]},yAxis:{minInterval:1},series:[{name:"\u7D2F\u8BA1\u7528\u6237\u91CF",data:[],type:"line",smooth:!0,label:{show:!0}}]}),c=g({color:["#67C23A","#E5323E"],legend:{data:["\u7528\u6237\u53D1\u9001\u4EBA\u6570","\u7528\u6237\u53D1\u9001\u6761\u6570"]},tooltip:{},xAxis:{data:[]},yAxis:{minInterval:1},series:[{name:"\u7528\u6237\u53D1\u9001\u4EBA\u6570",type:"line",smooth:!0,label:{show:!0},data:[]},{name:"\u7528\u6237\u53D1\u9001\u6761\u6570",type:"line",smooth:!0,label:{show:!0},data:[]}]}),o=g({color:["#67C23A","#E5323E","#E6A23C","#409EFF"],legend:{data:["\u88AB\u52A8\u56DE\u590D\u7528\u6237\u6D88\u606F\u7684\u6B21\u6570","\u5931\u8D25\u6B21\u6570","\u6700\u5927\u8017\u65F6","\u603B\u8017\u65F6"]},tooltip:{},xAxis:{data:[]},yAxis:{},series:[{name:"\u88AB\u52A8\u56DE\u590D\u7528\u6237\u6D88\u606F\u7684\u6B21\u6570",type:"bar",label:{show:!0},barGap:0,data:[]},{name:"\u5931\u8D25\u6B21\u6570",type:"bar",label:{show:!0},data:[]},{name:"\u6700\u5927\u8017\u65F6",type:"bar",label:{show:!0},data:[]},{name:"\u603B\u8017\u65F6",type:"bar",label:{show:!0},data:[]}]}),I=l=>{m.value=l,E()},E=()=>{if(!m)return _.error("\u672A\u9009\u4E2D\u516C\u4F17\u53F7\uFF0C\u65E0\u6CD5\u7EDF\u8BA1\u6570\u636E"),!1;if(D(s.value[0],s.value[1])>=7)return _.error("\u65F6\u95F4\u95F4\u9694 7 \u5929\u4EE5\u5185\uFF0C\u8BF7\u91CD\u65B0\u9009\u62E9"),!1;u.value=[];const l=D(s.value[0],s.value[1]);for(let a=0;a<=l;a++)u.value.push(i(W(s.value[0],864e5*a),"YYYY-MM-DD"));Y(),M(),U(),F()},Y=async()=>{p.xAxis.data=[],p.series[0].data=[],p.series[1].data=[];try{const a=await(l={accountId:m.value,date:[i(s.value[0]),i(s.value[1])]},w.get({url:"/mp/statistics/user-summary",params:l}));p.xAxis.data=u.value,u.value.forEach((r,n)=>{a.forEach(v=>{i(new Date(v.refDate),"YYYY-MM-DD").indexOf(r)!==-1&&(p.series[0].data[n]=v.newUser,p.series[1].data[n]=v.cancelUser)})})}catch{}var l},M=async()=>{x.xAxis.data=[],x.series[0].data=[];try{const a=await(l={accountId:m.value,date:[i(s.value[0]),i(s.value[1])]},w.get({url:"/mp/statistics/user-cumulate",params:l}));x.xAxis.data=u.value,a.forEach((r,n)=>{x.series[0].data[n]=r.cumulateUser})}catch{}var l},U=async()=>{c.xAxis.data=[],c.series[0].data=[],c.series[1].data=[];try{const a=await(l={accountId:m.value,date:[i(s.value[0]),i(s.value[1])]},w.get({url:"/mp/statistics/upstream-message",params:l}));c.xAxis.data=u.value,a.forEach((r,n)=>{c.series[0].data[n]=r.messageUser,c.series[1].data[n]=r.messageCount})}catch{}var l},F=async()=>{o.xAxis.data=[],o.series[0].data=[],o.series[1].data=[],o.series[2].data=[],o.series[3].data=[];try{const a=await(l={accountId:m.value,date:[i(s.value[0]),i(s.value[1])]},w.get({url:"/mp/statistics/interface-summary",params:l}));o.xAxis.data=u.value,a.forEach((r,n)=>{o.series[0].data[n]=r.callbackCount,o.series[1].data[n]=r.failCount,o.series[2].data[n]=r.maxTimeCost,o.series[3].data[n]=r.totalTimeCost})}catch{}var l};return(l,a)=>{const r=R,n=H,v=O,C=N,f=L,b=J,y=B,T=z;return q(),j(K,null,[e(C,null,{default:t(()=>[e(v,{class:"-mb-15px",ref:"queryForm",inline:!0,"label-width":"68px"},{default:t(()=>[e(r,{label:"\u516C\u4F17\u53F7",prop:"accountId"},{default:t(()=>[e(h(X),{onChange:I})]),_:1}),e(r,{label:"\u65F6\u95F4\u8303\u56F4",prop:"dateRange"},{default:t(()=>[e(n,{modelValue:h(s),"onUpdate:modelValue":a[0]||(a[0]=V=>S(s)?s.value=V:null),type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],onChange:E,class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1})]),_:1},512)]),_:1}),e(C,null,{default:t(()=>[e(T,null,{default:t(()=>[e(y,{span:12,class:"card-box"},{default:t(()=>[e(b,null,{header:t(()=>a[1]||(a[1]=[d("div",null,[d("span",null,"\u7528\u6237\u589E\u51CF\u6570\u636E")],-1)])),default:t(()=>[e(f,{options:h(p),height:420},null,8,["options"])]),_:1})]),_:1}),e(y,{span:12,class:"card-box"},{default:t(()=>[e(b,null,{header:t(()=>a[2]||(a[2]=[d("div",null,[d("span",null,"\u7D2F\u8BA1\u7528\u6237\u6570\u636E")],-1)])),default:t(()=>[e(f,{options:h(x),height:420},null,8,["options"])]),_:1})]),_:1}),e(y,{span:12,class:"card-box"},{default:t(()=>[e(b,null,{header:t(()=>a[3]||(a[3]=[d("div",null,[d("span",null,"\u6D88\u606F\u6982\u51B5\u6570\u636E")],-1)])),default:t(()=>[e(f,{options:h(c),height:420},null,8,["options"])]),_:1})]),_:1}),e(y,{span:12,class:"card-box"},{default:t(()=>[e(b,null,{header:t(()=>a[4]||(a[4]=[d("div",null,[d("span",null,"\u63A5\u53E3\u5206\u6790\u6570\u636E")],-1)])),default:t(()=>[e(f,{options:h(o),height:420},null,8,["options"])]),_:1})]),_:1})]),_:1})]),_:1})],64)}}});export{Z as default};
