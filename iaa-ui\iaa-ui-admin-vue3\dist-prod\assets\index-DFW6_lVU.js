import{d as u,r as d,c as e,o,y as x,aw as m,a as f,g as s,i as a,w as b,I as g,t as h,F as y}from"./index-CRsFgzy0.js";import{E as w}from"./el-image-BQpHFDaE.js";const v=["onClick"],k={class:"h-full w-full flex items-center justify-center"},_={class:"absolute right-1 top-1 text-12px"},C=u({name:"Popover",__name:"index",props:{property:{}},setup(I){const t=d(0);return(l,j)=>{const i=g,p=w;return o(!0),e(y,null,x(l.property.list,(n,r)=>(o(),e("div",{key:r,class:"absolute bottom-50% right-50% h-454px w-292px border-1px border-gray border-rounded-4px border-solid bg-white p-1px",style:m({zIndex:100+r+(f(t)===r?100:0),marginRight:-146-20*r+"px",marginBottom:-227-20*r+"px"}),onClick:z=>(c=>{t.value=c})(r)},[s(p,{src:n.imgUrl,fit:"contain",class:"h-full w-full"},{error:b(()=>[a("div",k,[s(i,{icon:"ep:picture"})])]),_:2},1032,["src"]),a("div",_,h(r+1),1)],12,v))),128)}}});export{C as default};
