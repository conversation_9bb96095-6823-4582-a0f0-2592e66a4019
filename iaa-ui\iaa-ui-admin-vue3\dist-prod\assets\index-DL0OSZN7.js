import{d as de,p as me,r as u,f as ve,q as fe,O as ye,c as w,o as i,g as e,w as r,s as ge,v as be,C as we,x as _e,F as N,y as D,A as m,B as he,J as f,P as xe,a as s,a6 as ke,G as Ue,H as y,I as Pe,h as Se,M as Ie,aR as Z,E as Ne,K as Ve,L as Ce,i as Y,t as _,aE as Re,aN as Te,D as $,e as qe,_ as De}from"./index-CRsFgzy0.js";import{_ as Ee}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as Fe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as Oe}from"./el-image-BQpHFDaE.js";import{_ as Ae}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Ye}from"./index-DYfNUK1u.js";import{f as ze,e as Be}from"./index-CtK0H6z7.js";import{a as Le}from"./index-CPqNYl_u.js";import{f as Me}from"./formatter-D3GpDdeL.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-J7g70ndY.js";import{d as He}from"./formatTime-DhdtkSIS.js";import{D as je}from"./constants-uird_4gU.js";import{_ as Ge}from"./OrderPickUpForm.vue_vue_type_script_setup_true_lang-CX8rsNDZ.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-9DEJGG6p.js";import"./el-timeline-item-D5y24tPs.js";import"./el-descriptions-item-lelixL8M.js";import"./OrderUpdateRemarkForm.vue_vue_type_script_setup_true_lang-R_l8lC29.js";import"./OrderDeliveryForm.vue_vue_type_script_setup_true_lang-4wL4yP3z.js";import"./index-Co_X9Ri-.js";import"./OrderUpdateAddressForm.vue_vue_type_script_setup_true_lang-CIYOctbj.js";import"./el-tree-select-BijZG_HG.js";import"./index-DLC3Afbg.js";import"./tree-COGD3qag.js";import"./OrderUpdatePriceForm.vue_vue_type_script_setup_true_lang-Sy1NdiT6.js";import"./tagsView-BnrVTrUo.js";const Je={class:"mr-10px"},Ke={class:"flex flex-col flex-wrap gap-1"},Ze=De(de({name:"PickUpOrder",__name:"index",setup($e){const h=me(),g=u(""),Q=u([]),V=u(""),v=u(!0),z=u(2),B=u([]),L=u(),M={pageNo:1,pageSize:10,createTime:void 0,deliveryType:je.PICK_UP.type,pickUpStoreId:-1},t=u({...M}),x=ve({queryParam:"no"}),k=u(),C=u(!1),U=u(!0),H=u([{value:"no",label:"\u8BA2\u5355\u53F7"},{value:"userId",label:"\u7528\u6237UID"},{value:"userNickname",label:"\u7528\u6237\u6635\u79F0"},{value:"userMobile",label:"\u7528\u6237\u7535\u8BDD"}]),W=o=>{var l;(l=H.value.filter(n=>n.value!==o))==null||l.forEach(n=>{t.value.hasOwnProperty(n.value)&&delete t.value[n.value]})},R=async()=>{v.value=!0;try{k.value=await ze(s(t));const o=await Be(s(t));B.value=o.list,z.value=o.total}finally{v.value=!1}},F=async()=>{t.value.pageNo=1,await R()},X=()=>{var o;(o=L.value)==null||o.resetFields(),t.value={...M},d.value.length>0&&(t.value.pickUpStoreId=d.value[0].id),F()},d=u([]),O=u(),ee=()=>{O.value.open()},ae=async()=>{try{if(!("serial"in navigator)||navigator.serial==null||typeof navigator.serial!="object"||!("requestPort"in navigator.serial))return void h.error("\u6D4F\u89C8\u5668\u4E0D\u652F\u6301\u626B\u7801\u67AA\u8FDE\u63A5\uFF0C\u8BF7\u66F4\u6362\u6D4F\u89C8\u5668\u91CD\u8BD5");g.value=await navigator.serial.requestPort(),Q.value=await navigator.serial.getPorts(),await g.value.open({baudRate:9600,dataBits:8,stopBits:2}),h.success("\u6210\u529F\u8FDE\u63A5\u626B\u7801\u67AA"),C.value=!0,le()}catch{}},le=async()=>{V.value=g.value.readable.getReader();let o="";for(;;){const{value:l,done:n}=await V.value.read();if(n){V.value.releaseLock();break}const T=new TextDecoder().decode(l);if(o=`${o}${T}`,T.includes("\r")){let b=o.replace("\r","");o="",O.value.open(b)}}},re=async()=>{g.value!==""?(await V.value.cancel(),await g.value.close(),g.value="",h.success("\u5DF2\u6210\u529F\u65AD\u5F00\u626B\u7801\u67AA\u8FDE\u63A5"),C.value=!1):h.warning("\u8BF7\u5148\u8FDE\u63A5\u6216\u6253\u5F00\u626B\u7801\u67AA")};return fe(async()=>{if(await(async()=>{d.value=await Le();const o=qe().getUser.id;d.value=d.value.filter(l=>{var n;return(n=l.verifyUserIds)==null?void 0:n.includes(o)})})(),d.value.length===0)return h.error("\u5F53\u524D\u767B\u5F55\u4EBA\u6CA1\u7ED1\u5B9A\u4EFB\u4F55\u81EA\u63D0\u70B9"),v.value=!1,void(U.value=!0);t.value.pickUpStoreId=d.value[0].id,U.value=!1,await R()}),(o,l)=>{const n=Ye,T=we,b=be,j=he,G=_e,te=xe,A=Pe,P=Ue,oe=ge,J=Ae,q=Se,ie=Ne,p=Ce,ue=Oe,se=Re,K=Fe,ne=Ve,pe=Ee,ce=ye("hasPermi"),S=Ie;return i(),w(N,null,[e(n,{title:"\u3010\u4EA4\u6613\u3011\u4EA4\u6613\u8BA2\u5355",url:"https://doc.iocoder.cn/mall/trade-order/"}),e(n,{title:"\u3010\u4EA4\u6613\u3011\u8D2D\u7269\u8F66",url:"https://doc.iocoder.cn/mall/trade-cart/"}),e(J,null,{default:r(()=>[e(oe,{ref_key:"queryFormRef",ref:L,inline:!0,model:t.value,class:"-mb-15px","label-width":"68px"},{default:r(()=>[e(b,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(T,{modelValue:t.value.createTime,"onUpdate:modelValue":l[0]||(l[0]=a=>t.value.createTime=a),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(b,{label:"\u81EA\u63D0\u95E8\u5E97",prop:"pickUpStoreId"},{default:r(()=>[e(G,{modelValue:t.value.pickUpStoreId,"onUpdate:modelValue":l[1]||(l[1]=a=>t.value.pickUpStoreId=a),class:"!w-280px",placeholder:"\u5168\u90E8",onChange:F},{default:r(()=>[(i(!0),w(N,null,D(d.value,a=>(i(),m(j,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(b,{label:"\u805A\u5408\u641C\u7D22"},{default:r(()=>[f(e(te,{modelValue:t.value[s(x).queryParam],"onUpdate:modelValue":l[3]||(l[3]=a=>t.value[s(x).queryParam]=a),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165",type:s(x).queryParam==="userId"?"number":"text"},{prepend:r(()=>[e(G,{modelValue:s(x).queryParam,"onUpdate:modelValue":l[2]||(l[2]=a=>s(x).queryParam=a),class:"!w-110px",placeholder:"\u5168\u90E8",onChange:W},{default:r(()=>[(i(!0),w(N,null,D(H.value,a=>(i(),m(j,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","type"]),[[ke,!0]])]),_:1}),e(b,null,{default:r(()=>[e(P,{onClick:F},{default:r(()=>[e(A,{class:"mr-5px",icon:"ep:search"}),l[6]||(l[6]=y(" \u641C\u7D22 "))]),_:1}),e(P,{onClick:X},{default:r(()=>[e(A,{class:"mr-5px",icon:"ep:refresh"}),l[7]||(l[7]=y(" \u91CD\u7F6E "))]),_:1}),f((i(),m(P,{onClick:ee,type:"success",plain:"",disabled:U.value},{default:r(()=>[e(A,{class:"mr-5px",icon:"ep:check"}),l[8]||(l[8]=y(" \u6838\u9500 "))]),_:1},8,["disabled"])),[[ce,["trade:order:pick-up"]]]),e(P,{type:"primary",onClick:ae,disabled:C.value||U.value},{default:r(()=>l[9]||(l[9]=[y(" \u8FDE\u63A5\u626B\u63CF\u67AA ")])),_:1},8,["disabled"]),e(P,{type:"danger",onClick:re,disabled:!C.value||U.value},{default:r(()=>l[10]||(l[10]=[y(" \u65AD\u5F00\u626B\u63CF\u67AA ")])),_:1},8,["disabled"])]),_:1})]),_:1},8,["model"])]),_:1}),e(ie,{gutter:16,class:"summary"},{default:r(()=>[f((i(),m(q,{sm:6,xs:12},{default:r(()=>{var a;return[e(E,{title:"\u8BA2\u5355\u6570\u91CF",icon:"icon-park-outline:transaction-order","icon-color":"bg-blue-100","icon-bg-color":"text-blue-500",value:((a=k.value)==null?void 0:a.orderCount)||0},null,8,["value"])]}),_:1})),[[S,v.value]]),f((i(),m(q,{sm:6,xs:12},{default:r(()=>{var a;return[e(E,{title:"\u8BA2\u5355\u91D1\u989D",icon:"streamline:money-cash-file-dollar-common-money-currency-cash-file","icon-color":"bg-purple-100","icon-bg-color":"text-purple-500",prefix:"\uFFE5",decimals:2,value:s(Z)(((a=k.value)==null?void 0:a.orderPayPrice)||0)},null,8,["value"])]}),_:1})),[[S,v.value]]),f((i(),m(q,{sm:6,xs:12},{default:r(()=>{var a;return[e(E,{title:"\u9000\u6B3E\u5355\u6570",icon:"heroicons:receipt-refund","icon-color":"bg-yellow-100","icon-bg-color":"text-yellow-500",value:((a=k.value)==null?void 0:a.afterSaleCount)||0},null,8,["value"])]}),_:1})),[[S,v.value]]),f((i(),m(q,{sm:6,xs:12},{default:r(()=>{var a;return[e(E,{title:"\u9000\u6B3E\u91D1\u989D",icon:"ri:refund-2-line","icon-color":"bg-green-100","icon-bg-color":"text-green-500",prefix:"\uFFE5",decimals:2,value:s(Z)(((a=k.value)==null?void 0:a.afterSalePrice)||0)},null,8,["value"])]}),_:1})),[[S,v.value]])]),_:1}),e(J,null,{default:r(()=>[f((i(),m(ne,{data:B.value},{default:r(()=>[e(p,{label:"\u8BA2\u5355\u53F7",align:"center",prop:"no","min-width":"180"}),e(p,{label:"\u7528\u6237\u4FE1\u606F",align:"center",prop:"user.nickname","min-width":"80"}),e(p,{label:"\u63A8\u8350\u4EBA\u4FE1\u606F",align:"center",prop:"brokerageUser.nickname","min-width":"100"}),e(p,{label:"\u5546\u54C1\u4FE1\u606F",align:"center",prop:"spuName","min-width":"300"},{default:r(({row:a})=>[(i(!0),w(N,null,D(a.items,c=>(i(),w("div",{class:"flex items-center",key:c.id},[e(ue,{src:c.picUrl,class:"mr-10px h-30px w-30px flex-shrink-0","preview-src-list":[c.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"]),Y("span",Je,_(c.spuName),1),Y("div",Ke,[(i(!0),w(N,null,D(c.properties,I=>(i(),m(se,{key:I.propertyId,class:"mr-10px"},{default:r(()=>[y(_(I.propertyName)+": "+_(I.valueName),1)]),_:2},1024))),128)),Y("span",null,_(s(Te)(c.price))+" \u5143 x "+_(c.count),1)])]))),128))]),_:1}),e(p,{label:"\u5B9E\u4ED8\u91D1\u989D(\u5143)",align:"center",prop:"payPrice","min-width":"110",formatter:s(Me)},null,8,["formatter"]),e(p,{label:"\u6838\u9500\u5458",align:"center",prop:"storeStaffName","min-width":"70"}),e(p,{label:"\u6838\u9500\u95E8\u5E97",align:"center",prop:"pickUpStoreId","min-width":"80"},{default:r(({row:a})=>{var c;return[y(_((c=d.value.find(I=>I.id===a.pickUpStoreId))==null?void 0:c.name),1)]}),_:1}),e(p,{label:"\u652F\u4ED8\u72B6\u6001",align:"center",prop:"payStatus","min-width":"80"},{default:r(({row:a})=>[e(K,{type:s($).INFRA_BOOLEAN_STRING,value:a.payStatus||!1},null,8,["type","value"])]),_:1}),e(p,{align:"center",label:"\u8BA2\u5355\u72B6\u6001",prop:"status",width:"120"},{default:r(({row:a})=>[e(K,{type:s($).TRADE_ORDER_STATUS,value:a.status},null,8,["type","value"])]),_:1}),e(p,{label:"\u4E0B\u5355\u65F6\u95F4",align:"center",prop:"createTime","min-width":"170",formatter:s(He)},null,8,["formatter"])]),_:1},8,["data"])),[[S,v.value]]),e(pe,{limit:t.value.pageSize,"onUpdate:limit":l[4]||(l[4]=a=>t.value.pageSize=a),page:t.value.pageNo,"onUpdate:page":l[5]||(l[5]=a=>t.value.pageNo=a),total:z.value,onPagination:R},null,8,["limit","page","total"])]),_:1}),e(Ge,{ref_key:"pickUpForm",ref:O,onSuccess:R},null,512)],64)}}}),[["__scopeId","data-v-37c33c0d"]]);export{Ze as default};
