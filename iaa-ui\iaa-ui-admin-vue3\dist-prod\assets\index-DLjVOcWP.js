import{d as ee,p as le,b as ae,r as v,f as F,u as te,q as oe,O as ie,c as w,o as d,g as a,w as o,s as de,a as t,v as pe,x as ue,F as g,y as D,A as r,B as re,P as se,Q as K,R as L,D as k,J as _,G as ne,H as s,I as ce,K as me,L as ve,a5 as fe,t as Y,M as _e}from"./index-CRsFgzy0.js";import{_ as ye}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as be}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as we}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{d as ge}from"./formatTime-DhdtkSIS.js";import{D as B}from"./index-DZ5Yf9eO.js";import{_ as ke}from"./DeviceForm.vue_vue_type_script_setup_true_lang-DTYhyMSk.js";import{P as G}from"./index-RX3kpD3r.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";const Te=ee({name:"IoTDevice",__name:"index",setup(Ve){const h=le(),{t:H}=ae(),T=v(!0),x=v([]),N=v(0),i=F({pageNo:1,pageSize:10,deviceName:void 0,productId:void 0,deviceType:void 0,nickname:void 0,status:void 0}),P=v(),E=F({}),f=async()=>{T.value=!0;try{const u=await B.getDevicePage(i);x.value=u.list,N.value=u.total;const l=[...new Set(u.list.map(p=>p.productId))];(await Promise.all(l.map(p=>G.getProduct(p)))).forEach(p=>{E[p.id]=p.name})}finally{T.value=!1}},y=()=>{i.pageNo=1,f()},J=()=>{P.value.resetFields(),y()},S=v(),U=(u,l)=>{S.value.open(u,l)},{push:M}=te(),O=u=>{M({name:"IoTDeviceDetail",params:{id:u}})},R=v();return oe(()=>{f(),(async()=>R.value=await G.getSimpleProductList())()}),(u,l)=>{const p=re,V=ue,c=pe,z=se,C=ce,m=ne,Q=de,A=we,$=fe,n=ve,q=be,j=me,W=ye,b=ie("hasPermi"),X=_e;return d(),w(g,null,[a(A,null,{default:o(()=>[a(Q,{class:"-mb-15px",model:t(i),ref_key:"queryFormRef",ref:P,inline:!0,"label-width":"68px"},{default:o(()=>[a(c,{label:"\u4EA7\u54C1",prop:"productId"},{default:o(()=>[a(V,{modelValue:t(i).productId,"onUpdate:modelValue":l[0]||(l[0]=e=>t(i).productId=e),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",clearable:"",class:"!w-240px"},{default:o(()=>[(d(!0),w(g,null,D(t(R),e=>(d(),r(p,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"DeviceName",prop:"deviceName"},{default:o(()=>[a(z,{modelValue:t(i).deviceName,"onUpdate:modelValue":l[1]||(l[1]=e=>t(i).deviceName=e),placeholder:"\u8BF7\u8F93\u5165 DeviceName",clearable:"",onKeyup:K(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(c,{label:"\u5907\u6CE8\u540D\u79F0",prop:"nickname"},{default:o(()=>[a(z,{modelValue:t(i).nickname,"onUpdate:modelValue":l[2]||(l[2]=e=>t(i).nickname=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8\u540D\u79F0",clearable:"",onKeyup:K(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(c,{label:"\u8BBE\u5907\u7C7B\u578B",prop:"deviceType"},{default:o(()=>[a(V,{modelValue:t(i).deviceType,"onUpdate:modelValue":l[3]||(l[3]=e=>t(i).deviceType=e),placeholder:"\u8BF7\u9009\u62E9\u8BBE\u5907\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:o(()=>[(d(!0),w(g,null,D(t(L)(t(k).IOT_PRODUCT_DEVICE_TYPE),e=>(d(),r(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"\u8BBE\u5907\u72B6\u6001",prop:"status"},{default:o(()=>[a(V,{modelValue:t(i).status,"onUpdate:modelValue":l[4]||(l[4]=e=>t(i).status=e),placeholder:"\u8BF7\u9009\u62E9\u8BBE\u5907\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(d(!0),w(g,null,D(t(L)(t(k).IOT_DEVICE_STATUS),e=>(d(),r(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(c,null,{default:o(()=>[a(m,{onClick:y},{default:o(()=>[a(C,{icon:"ep:search",class:"mr-5px"}),l[8]||(l[8]=s(" \u641C\u7D22 "))]),_:1}),a(m,{onClick:J},{default:o(()=>[a(C,{icon:"ep:refresh",class:"mr-5px"}),l[9]||(l[9]=s(" \u91CD\u7F6E "))]),_:1}),_((d(),r(m,{type:"primary",plain:"",onClick:l[5]||(l[5]=e=>U("create"))},{default:o(()=>[a(C,{icon:"ep:plus",class:"mr-5px"}),l[10]||(l[10]=s(" \u65B0\u589E "))]),_:1})),[[b,["iot:device:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(A,null,{default:o(()=>[_((d(),r(j,{data:t(x),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[a(n,{label:"DeviceName",align:"center",prop:"deviceName"},{default:o(e=>[a($,{onClick:I=>O(e.row.id)},{default:o(()=>[s(Y(e.row.deviceName),1)]),_:2},1032,["onClick"])]),_:1}),a(n,{label:"\u5907\u6CE8\u540D\u79F0",align:"center",prop:"nickname"}),a(n,{label:"\u8BBE\u5907\u6240\u5C5E\u4EA7\u54C1",align:"center",prop:"productId"},{default:o(e=>[s(Y(t(E)[e.row.productId]),1)]),_:1}),a(n,{label:"\u8BBE\u5907\u7C7B\u578B",align:"center",prop:"deviceType"},{default:o(e=>[a(q,{type:t(k).IOT_PRODUCT_DEVICE_TYPE,value:e.row.deviceType},null,8,["type","value"])]),_:1}),a(n,{label:"\u8BBE\u5907\u72B6\u6001",align:"center",prop:"status"},{default:o(e=>[a(q,{type:t(k).IOT_DEVICE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u6700\u540E\u4E0A\u7EBF\u65F6\u95F4",align:"center",prop:"lastOnlineTime",formatter:t(ge),width:"180px"},null,8,["formatter"]),a(n,{label:"\u64CD\u4F5C",align:"center","min-width":"120px"},{default:o(e=>[_((d(),r(m,{link:"",type:"primary",onClick:I=>O(e.row.id)},{default:o(()=>l[11]||(l[11]=[s(" \u67E5\u770B ")])),_:2},1032,["onClick"])),[[b,["iot:product:query"]]]),_((d(),r(m,{link:"",type:"primary",onClick:I=>U("update",e.row.id)},{default:o(()=>l[12]||(l[12]=[s(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[b,["iot:device:update"]]]),_((d(),r(m,{link:"",type:"danger",onClick:I=>(async Z=>{try{await h.delConfirm(),await B.deleteDevice(Z),h.success(H("common.delSuccess")),await f()}catch{}})(e.row.id)},{default:o(()=>l[13]||(l[13]=[s(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[b,["iot:device:delete"]]])]),_:1})]),_:1},8,["data"])),[[X,t(T)]]),a(W,{total:t(N),page:t(i).pageNo,"onUpdate:page":l[6]||(l[6]=e=>t(i).pageNo=e),limit:t(i).pageSize,"onUpdate:limit":l[7]||(l[7]=e=>t(i).pageSize=e),onPagination:f},null,8,["total","page","limit"])]),_:1}),a(ke,{ref_key:"formRef",ref:S,onSuccess:f},null,512)],64)}}});export{Te as default};
