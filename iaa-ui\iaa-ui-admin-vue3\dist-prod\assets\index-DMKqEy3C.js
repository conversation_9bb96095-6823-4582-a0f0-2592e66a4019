import{d as K,p as L,b as B,r as _,f as J,u as Q,q as $,O as j,c as b,o as p,g as a,w as t,s as W,a as r,v as X,P as Z,Q as ee,C as ae,J as u,G as le,H as m,I as te,A as d,K as re,L as ie,F as C,y as oe,D as pe,a3 as se,M as ne}from"./index-CRsFgzy0.js";import{_ as me}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as de}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as ce}from"./el-image-BQpHFDaE.js";import{_ as ue}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as fe}from"./index-DYfNUK1u.js";import{d as V}from"./formatTime-DhdtkSIS.js";import{d as ye,e as we,f as _e}from"./template-CGghypgj.js";import{_ as ge}from"./DiyTemplateForm.vue_vue_type_script_setup_true_lang-BPVpFpO8.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";const ke=K({name:"DiyTemplate",__name:"index",setup(ve){const g=L(),{t:z}=B(),k=_(!0),T=_(0),D=_([]),i=J({pageNo:1,pageSize:10,name:null,createTime:[]}),N=_(),c=async()=>{k.value=!0;try{const f=await ye(i);D.value=f.list,T.value=f.total}finally{k.value=!1}},v=()=>{i.pageNo=1,c()},F=()=>{N.value.resetFields(),v()},U=_(),P=(f,e)=>{U.value.open(f,e)},{push:M}=Q();return $(()=>{c()}),(f,e)=>{const R=fe,Y=Z,h=X,A=ae,x=te,n=le,H=W,S=ue,s=ie,I=ce,O=de,q=re,E=me,y=j("hasPermi"),G=ne;return p(),b(C,null,[a(R,{title:"\u3010\u8425\u9500\u3011\u5546\u57CE\u88C5\u4FEE",url:"https://doc.iocoder.cn/mall/diy/"}),a(S,null,{default:t(()=>[a(H,{class:"-mb-15px",model:r(i),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:t(()=>[a(h,{label:"\u6A21\u677F\u540D\u79F0",prop:"name"},{default:t(()=>[a(Y,{modelValue:r(i).name,"onUpdate:modelValue":e[0]||(e[0]=l=>r(i).name=l),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u540D\u79F0",clearable:"",onKeyup:ee(v,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(h,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(A,{modelValue:r(i).createTime,"onUpdate:modelValue":e[1]||(e[1]=l=>r(i).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(h,null,{default:t(()=>[a(n,{onClick:v},{default:t(()=>[a(x,{icon:"ep:search",class:"mr-5px"}),e[5]||(e[5]=m(" \u641C\u7D22"))]),_:1}),a(n,{onClick:F},{default:t(()=>[a(x,{icon:"ep:refresh",class:"mr-5px"}),e[6]||(e[6]=m(" \u91CD\u7F6E"))]),_:1}),u((p(),d(n,{type:"primary",plain:"",onClick:e[2]||(e[2]=l=>P("create"))},{default:t(()=>[a(x,{icon:"ep:plus",class:"mr-5px"}),e[7]||(e[7]=m(" \u65B0\u589E "))]),_:1})),[[y,["promotion:diy-template:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(S,null,{default:t(()=>[u((p(),d(q,{data:r(D),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[a(s,{label:"\u7F16\u53F7",align:"center",prop:"id"}),a(s,{label:"\u9884\u89C8\u56FE",align:"center",prop:"previewPicUrls"},{default:t(l=>[(p(!0),b(C,null,oe(l.row.previewPicUrls,(w,o)=>(p(),d(I,{class:"h-40px max-w-40px",key:o,src:w,"preview-src-list":l.row.previewPicUrls,"initial-index":o,"preview-teleported":""},null,8,["src","preview-src-list","initial-index"]))),128))]),_:1}),a(s,{label:"\u6A21\u677F\u540D\u79F0",align:"center",prop:"name"}),a(s,{label:"\u662F\u5426\u4F7F\u7528",align:"center",prop:"used"},{default:t(l=>[a(O,{type:r(pe).INFRA_BOOLEAN_STRING,value:l.row.used},null,8,["type","value"])]),_:1}),a(s,{label:"\u4F7F\u7528\u65F6\u95F4",align:"center",prop:"usedTime",formatter:r(V),width:"180px"},null,8,["formatter"]),a(s,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),a(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:r(V),width:"180px"},null,8,["formatter"]),a(s,{label:"\u64CD\u4F5C",align:"center",width:"200"},{default:t(l=>[u((p(),d(n,{link:"",type:"primary",onClick:w=>{return o=l.row.id,void M({name:"DiyTemplateDecorate",params:{id:o}});var o}},{default:t(()=>e[8]||(e[8]=[m(" \u88C5\u4FEE ")])),_:2},1032,["onClick"])),[[y,["promotion:diy-template:update"]]]),u((p(),d(n,{link:"",type:"primary",onClick:w=>P("update",l.row.id)},{default:t(()=>e[9]||(e[9]=[m(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[y,["promotion:diy-template:update"]]]),l.row.used?se("",!0):(p(),b(C,{key:0},[u((p(),d(n,{link:"",type:"primary",onClick:w=>(async o=>{try{await g.confirm(`\u662F\u5426\u4F7F\u7528\u6A21\u677F\u201C${o.name}\u201D?`),await _e(o.id),g.success("\u4F7F\u7528\u6210\u529F"),await c()}catch{}})(l.row)},{default:t(()=>e[10]||(e[10]=[m(" \u4F7F\u7528 ")])),_:2},1032,["onClick"])),[[y,["promotion:diy-template:use"]]]),u((p(),d(n,{link:"",type:"danger",onClick:w=>(async o=>{try{await g.delConfirm(),await we(o),g.success(z("common.delSuccess")),await c()}catch{}})(l.row.id)},{default:t(()=>e[11]||(e[11]=[m(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[y,["promotion:diy-template:delete"]]])],64))]),_:1})]),_:1},8,["data"])),[[G,r(k)]]),a(E,{total:r(T),page:r(i).pageNo,"onUpdate:page":e[3]||(e[3]=l=>r(i).pageNo=l),limit:r(i).pageSize,"onUpdate:limit":e[4]||(e[4]=l=>r(i).pageSize=l),onPagination:c},null,8,["total","page","limit"])]),_:1}),a(ge,{ref_key:"formRef",ref:U,onSuccess:c},null,512)],64)}}});export{ke as default};
