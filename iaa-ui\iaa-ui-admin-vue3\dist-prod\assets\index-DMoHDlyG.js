import{as as a}from"./index-CRsFgzy0.js";const l=async e=>await a.get({url:"/member/level/list",params:e}),t=async e=>await a.get({url:"/member/level/get?id="+e}),s=async()=>await a.get({url:"/member/level/list-all-simple"}),r=async e=>await a.post({url:"/member/level/create",data:e}),m=async e=>await a.put({url:"/member/level/update",data:e}),i=async e=>await a.delete({url:"/member/level/delete?id="+e});export{s as a,l as b,r as c,i as d,t as g,m as u};
