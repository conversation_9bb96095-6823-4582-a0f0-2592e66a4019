import{as as z,d as G,p as L,b as J,r as n,f as K,q as j,O as Q,c as T,o as p,g as a,w as r,s as W,a as t,v as X,x as Z,F as O,y as $,R as aa,D,A as d,B as ea,C as la,J as f,G as ta,H as s,I as ra,K as oa,L as ia,t as pa,M as na}from"./index-CRsFgzy0.js";import{_ as sa}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as ma}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as ua}from"./el-image-BQpHFDaE.js";import{_ as da}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ca}from"./index-DYfNUK1u.js";import{d as N}from"./formatTime-DhdtkSIS.js";import{f as A}from"./formatter-D3GpDdeL.js";import{_ as fa}from"./BargainRecordListDialog.vue_vue_type_script_setup_true_lang-5cuZ9Y75.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./el-avatar-Nl9DW69B.js";const ga=G({name:"PromotionBargainRecord",__name:"index",setup(_a){L();const{t:wa}=J(),g=n(!0),v=n(0),h=n([]),i=K({pageNo:1,pageSize:10,status:null,createTime:[]}),y=n(),M=n(!1),_=async()=>{g.value=!0;try{const m=await(async e=>await z.get({url:"/promotion/bargain-record/page",params:e}))(i);h.value=m.list,v.value=m.total}finally{g.value=!1}},x=()=>{i.pageNo=1,_()},P=()=>{y.value.resetFields(),x()},k=n();return j(()=>{_()}),(m,e)=>{const S=ca,I=ea,U=Z,w=X,V=la,c=ra,u=ta,H=W,C=da,o=ia,B=ua,E=ma,F=oa,Y=sa,b=Q("hasPermi"),q=na;return p(),T(O,null,[a(S,{title:"\u3010\u8425\u9500\u3011\u780D\u4EF7\u6D3B\u52A8",url:"https://doc.iocoder.cn/mall/promotion-bargain/"}),a(C,null,{default:r(()=>[a(H,{class:"-mb-15px",model:t(i),ref_key:"queryFormRef",ref:y,inline:!0,"label-width":"68px"},{default:r(()=>[a(w,{label:"\u780D\u4EF7\u72B6\u6001",prop:"status"},{default:r(()=>[a(U,{modelValue:t(i).status,"onUpdate:modelValue":e[0]||(e[0]=l=>t(i).status=l),placeholder:"\u8BF7\u9009\u62E9\u780D\u4EF7\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(p(!0),T(O,null,$(t(aa)(t(D).PROMOTION_BARGAIN_RECORD_STATUS),l=>(p(),d(I,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(w,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[a(V,{modelValue:t(i).createTime,"onUpdate:modelValue":e[1]||(e[1]=l=>t(i).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(w,null,{default:r(()=>[a(u,{onClick:x},{default:r(()=>[a(c,{icon:"ep:search",class:"mr-5px"}),e[5]||(e[5]=s(" \u641C\u7D22"))]),_:1}),a(u,{onClick:P},{default:r(()=>[a(c,{icon:"ep:refresh",class:"mr-5px"}),e[6]||(e[6]=s(" \u91CD\u7F6E"))]),_:1}),f((p(),d(u,{type:"primary",plain:"",onClick:e[2]||(e[2]=l=>m.openForm("create"))},{default:r(()=>[a(c,{icon:"ep:plus",class:"mr-5px"}),e[7]||(e[7]=s(" \u65B0\u589E "))]),_:1})),[[b,["promotion:bargain-record:create"]]]),f((p(),d(u,{type:"success",plain:"",onClick:m.handleExport,loading:t(M)},{default:r(()=>[a(c,{icon:"ep:download",class:"mr-5px"}),e[8]||(e[8]=s(" \u5BFC\u51FA "))]),_:1},8,["onClick","loading"])),[[b,["promotion:bargain-record:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(C,null,{default:r(()=>[f((p(),d(F,{data:t(h),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[a(o,{label:"\u7F16\u53F7","min-width":"50",prop:"id"}),a(o,{label:"\u53D1\u8D77\u7528\u6237","min-width":"120"},{default:r(l=>[a(B,{src:l.row.avatar,class:"h-20px w-20px","preview-src-list":[l.row.avatar],"preview-teleported":""},null,8,["src","preview-src-list"]),s(" "+pa(l.row.nickname),1)]),_:1}),a(o,{label:"\u53D1\u8D77\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(N),width:"180px"},null,8,["formatter"]),a(o,{label:"\u780D\u4EF7\u6D3B\u52A8","min-width":"150",prop:"activity.name"}),a(o,{label:"\u6700\u4F4E\u4EF7","min-width":"100",prop:"activity.bargainMinPrice",formatter:t(A)},null,8,["formatter"]),a(o,{label:"\u5F53\u524D\u4EF7","min-width":"100",prop:"bargainPrice",formatter:t(A)},null,8,["formatter"]),a(o,{label:"\u603B\u780D\u4EF7\u6B21\u6570","min-width":"100",prop:"activity.helpMaxCount"}),a(o,{label:"\u5269\u4F59\u780D\u4EF7\u6B21\u6570","min-width":"100",prop:"helpCount"}),a(o,{label:"\u780D\u4EF7\u72B6\u6001",align:"center",prop:"status"},{default:r(l=>[a(E,{type:t(D).PROMOTION_BARGAIN_RECORD_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),a(o,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",formatter:t(N),width:"180px"},null,8,["formatter"]),a(o,{label:"\u8BA2\u5355\u7F16\u53F7",align:"center",prop:"orderId"}),a(o,{label:"\u64CD\u4F5C",align:"center"},{default:r(l=>[f((p(),d(u,{link:"",type:"primary",onClick:ba=>{return R=l.row.id,void k.value.open(R);var R}},{default:r(()=>e[9]||(e[9]=[s(" \u52A9\u529B ")])),_:2},1032,["onClick"])),[[b,["promotion:bargain-help:query"]]])]),_:1})]),_:1},8,["data"])),[[q,t(g)]]),a(Y,{total:t(v),page:t(i).pageNo,"onUpdate:page":e[3]||(e[3]=l=>t(i).pageNo=l),limit:t(i).pageSize,"onUpdate:limit":e[4]||(e[4]=l=>t(i).pageSize=l),onPagination:_},null,8,["total","page","limit"])]),_:1}),a(fa,{ref_key:"recordListDialogRef",ref:k},null,512)],64)}}});export{ga as default};
