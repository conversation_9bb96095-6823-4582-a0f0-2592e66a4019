import{as as e}from"./index-CRsFgzy0.js";const t={getPurchaseOrderPage:async r=>await e.get({url:"/erp/purchase-order/page",params:r}),getPurchaseOrder:async r=>await e.get({url:"/erp/purchase-order/get?id="+r}),createPurchaseOrder:async r=>await e.post({url:"/erp/purchase-order/create",data:r}),updatePurchaseOrder:async r=>await e.put({url:"/erp/purchase-order/update",data:r}),updatePurchaseOrderStatus:async(r,a)=>await e.put({url:"/erp/purchase-order/update-status",params:{id:r,status:a}}),deletePurchaseOrder:async r=>await e.delete({url:"/erp/purchase-order/delete",params:{ids:r.join(",")}}),exportPurchaseOrder:async r=>await e.download({url:"/erp/purchase-order/export-excel",params:r})};export{t as P};
