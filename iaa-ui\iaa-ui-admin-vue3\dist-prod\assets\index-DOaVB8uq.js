import{d as j,u as X,r as y,f as Z,q as $,cF as g,D as m,c,o as s,g as l,w as r,s as ee,a,v as le,P as ae,Q as S,x as te,B as re,F as f,y as w,A as _,C as ue,G as oe,H as T,I as se,J as ne,l as pe,n as de,K as ie,L as me,t as b,i as E,aE as ce,aR as fe,M as _e,aP as be,aV as ve}from"./index-CRsFgzy0.js";import{_ as ye}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as we}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as Te}from"./el-image-BQpHFDaE.js";import{_ as Ae}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Ve}from"./index-DYfNUK1u.js";import{e as he}from"./index-qMk5NKfc.js";import{f as ge}from"./formatTime-DhdtkSIS.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";const Ee={class:"flex items-center"},xe={class:"mr-10px"},Ne=j({name:"TradeAfterSale",__name:"index",setup(ke){const{push:U}=X(),x=y(!0),R=y(0),D=y([]),F=y([{label:"\u5168\u90E8",value:"0"}]),C=y(),u=Z({pageNo:1,pageSize:10,no:null,status:"0",orderNo:null,spuName:null,createTime:[],way:null,type:null}),A=async()=>{x.value=!0;try{const o=be(u);o.status==="0"&&delete o.status;const t=await he(o);D.value=t.list,R.value=t.total}finally{x.value=!1}},v=async()=>{u.pageNo=1,await A()},z=()=>{var o;(o=C.value)==null||o.resetFields(),v()},K=async o=>{u.status=o.paneName,await A()};return $(async()=>{await A();for(const o of g(m.TRADE_AFTER_SALE_STATUS))F.value.push({label:o.label,value:o.value})}),(o,t)=>{const H=Ve,N=ae,p=le,V=re,k=te,I=ue,L=se,h=oe,M=ee,P=Ae,q=de,O=pe,n=me,W=Te,B=ce,Y=we,G=ie,J=ye,Q=_e;return s(),c(f,null,[l(H,{title:"\u3010\u4EA4\u6613\u3011\u552E\u540E\u9000\u6B3E",url:"https://doc.iocoder.cn/mall/trade-aftersale/"}),l(P,null,{default:r(()=>[l(M,{ref_key:"queryFormRef",ref:C,inline:!0,model:a(u),"label-width":"68px"},{default:r(()=>[l(p,{label:"\u5546\u54C1\u540D\u79F0",prop:"spuName"},{default:r(()=>[l(N,{modelValue:a(u).spuName,"onUpdate:modelValue":t[0]||(t[0]=e=>a(u).spuName=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1 SPU \u540D\u79F0",onKeyup:S(v,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u9000\u6B3E\u7F16\u53F7",prop:"no"},{default:r(()=>[l(N,{modelValue:a(u).no,"onUpdate:modelValue":t[1]||(t[1]=e=>a(u).no=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u9000\u6B3E\u7F16\u53F7",onKeyup:S(v,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u8BA2\u5355\u7F16\u53F7",prop:"orderNo"},{default:r(()=>[l(N,{modelValue:a(u).orderNo,"onUpdate:modelValue":t[2]||(t[2]=e=>a(u).orderNo=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u7F16\u53F7",onKeyup:S(v,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u72B6\u6001",prop:"status"},{default:r(()=>[l(k,{modelValue:a(u).status,"onUpdate:modelValue":t[3]||(t[3]=e=>a(u).status=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u72B6\u6001"},{default:r(()=>[l(V,{label:"\u5168\u90E8",value:"0"}),(s(!0),c(f,null,w(a(g)(a(m).TRADE_AFTER_SALE_STATUS),e=>(s(),_(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u65B9\u5F0F",prop:"way"},{default:r(()=>[l(k,{modelValue:a(u).way,"onUpdate:modelValue":t[4]||(t[4]=e=>a(u).way=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u65B9\u5F0F"},{default:r(()=>[(s(!0),c(f,null,w(a(g)(a(m).TRADE_AFTER_SALE_WAY),e=>(s(),_(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u7C7B\u578B",prop:"type"},{default:r(()=>[l(k,{modelValue:a(u).type,"onUpdate:modelValue":t[5]||(t[5]=e=>a(u).type=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u7C7B\u578B"},{default:r(()=>[(s(!0),c(f,null,w(a(g)(a(m).TRADE_AFTER_SALE_TYPE),e=>(s(),_(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[l(I,{modelValue:a(u).createTime,"onUpdate:modelValue":t[6]||(t[6]=e=>a(u).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(p,null,{default:r(()=>[l(h,{onClick:v},{default:r(()=>[l(L,{class:"mr-5px",icon:"ep:search"}),t[10]||(t[10]=T(" \u641C\u7D22 "))]),_:1}),l(h,{onClick:z},{default:r(()=>[l(L,{class:"mr-5px",icon:"ep:refresh"}),t[11]||(t[11]=T(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(P,null,{default:r(()=>[l(O,{modelValue:a(u).status,"onUpdate:modelValue":t[7]||(t[7]=e=>a(u).status=e),onTabClick:K},{default:r(()=>[(s(!0),c(f,null,w(a(F),e=>(s(),_(q,{key:e.label,label:e.label,name:e.value},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),ne((s(),_(G,{data:a(D)},{default:r(()=>[l(n,{align:"center",label:"\u9000\u6B3E\u7F16\u53F7","min-width":"200",prop:"no"}),l(n,{align:"center",label:"\u8BA2\u5355\u7F16\u53F7","min-width":"200",prop:"orderNo"},{default:r(({row:e})=>[l(h,{link:"",type:"primary",onClick:i=>{return d=e.orderId,void U({name:"TradeOrderDetail",params:{id:d}});var d}},{default:r(()=>[T(b(e.orderNo),1)]),_:2},1032,["onClick"])]),_:1}),l(n,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"600",prop:"spuName"},{default:r(({row:e})=>[E("div",Ee,[l(W,{src:e.picUrl,class:"mr-10px h-30px w-30px",onClick:i=>{return d=e.picUrl,void ve({urlList:[d]});var d}},null,8,["src","onClick"]),E("span",xe,b(e.spuName),1),(s(!0),c(f,null,w(e.properties,i=>(s(),_(B,{key:i.propertyId,class:"mr-10px"},{default:r(()=>[T(b(i.propertyName)+": "+b(i.valueName),1)]),_:2},1024))),128))])]),_:1}),l(n,{align:"center",label:"\u8BA2\u5355\u91D1\u989D","min-width":"120",prop:"refundPrice"},{default:r(e=>[E("span",null,b(a(fe)(e.row.refundPrice))+" \u5143",1)]),_:1}),l(n,{align:"center",label:"\u4E70\u5BB6",prop:"user.nickname"}),l(n,{align:"center",label:"\u7533\u8BF7\u65F6\u95F4",prop:"createTime",width:"180"},{default:r(e=>[E("span",null,b(a(ge)(e.row.createTime)),1)]),_:1}),l(n,{align:"center",label:"\u552E\u540E\u72B6\u6001",width:"100"},{default:r(e=>[l(Y,{type:a(m).TRADE_AFTER_SALE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),l(n,{align:"center",label:"\u552E\u540E\u65B9\u5F0F"},{default:r(e=>[l(Y,{type:a(m).TRADE_AFTER_SALE_WAY,value:e.row.way},null,8,["type","value"])]),_:1}),l(n,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"160"},{default:r(({row:e})=>[l(h,{link:"",type:"primary",onClick:i=>{return d=e.id,void U({name:"TradeAfterSaleDetail",params:{id:d}});var d}},{default:r(()=>t[12]||(t[12]=[T("\u5904\u7406\u9000\u6B3E")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Q,a(x)]]),l(J,{limit:a(u).pageSize,"onUpdate:limit":t[8]||(t[8]=e=>a(u).pageSize=e),page:a(u).pageNo,"onUpdate:page":t[9]||(t[9]=e=>a(u).pageNo=e),total:a(R),onPagination:A},null,8,["limit","page","total"])]),_:1})],64)}}});export{Ne as default};
