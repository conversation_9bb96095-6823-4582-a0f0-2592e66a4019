import{d as G,ah as _,e5 as J,r as k,aK as H,c as v,o as d,i as K,F as V,y as M,aw as R,X as L,g as X,I as N,a as $,a3 as O,H as P,t as Q,_ as T}from"./index-CRsFgzy0.js";const B=(i,x)=>{const[t,a]=[i.x,x.x].sort(),[l,r]=[i.y,x.y].sort(),p=a+1,f=r+1;return{left:t,right:p,top:l,bottom:f,height:f-l,width:p-t}},U={class:"relative"},W={class:"cube-table"},Y=["onClick","onMouseenter"],Z=["onClick"],ee=["onClick"],te=T(G({name:"MagicCubeEditor",__name:"index",props:{modelValue:J().isRequired,rows:_.number.def(4),cols:_.number.def(4),cubeSize:_.number.def(75)},emits:["update:modelValue","hotAreaSelected"],setup(i,{emit:x}){const t=i,a=k([]);H(()=>[t.rows,t.cols],()=>{if(a.value=[],t.rows&&t.cols)for(let o=0;o<t.rows;o++){a.value[o]=[];for(let e=0;e<t.cols;e++)a.value[o].push({x:e,y:o,active:!1})}},{immediate:!0});const l=k([]);H(()=>t.modelValue,()=>l.value=t.modelValue||[],{immediate:!0});const r=k(),p=()=>!!r.value,f=x,A=()=>f("update:modelValue",l),I=k(0),q=(o,e)=>{I.value=e,f("hotAreaSelected",o,e)};function y(){j((o,e,s)=>{s.active&&(s.active=!1)}),r.value=void 0}const j=o=>{for(let e=0;e<a.value.length;e++)for(let s=0;s<a.value[e].length;s++)o(e,s,a.value[e][s])};return(o,e)=>{const s=N;return d(),v("div",U,[K("table",W,[K("tbody",null,[(d(!0),v(V,null,M($(a),(u,n)=>(d(),v("tr",{key:n},[(d(!0),v(V,null,M(u,(S,b)=>(d(),v("td",{key:b,class:L(["cube",{active:S.active}]),style:R({width:`${i.cubeSize}px`,height:`${i.cubeSize}px`}),onClick:D=>((C,z)=>{const m=a.value[C][z];if(!p())return r.value=m,void(r.value.active=!0);l.value.push(B(r.value,m)),y();let c=l.value.length-1;q(l.value[c],c),A()})(n,b),onMouseenter:D=>((C,z)=>{if(!p())return;const m=B(r.value,a.value[C][z]);for(const E of l.value)if(h=m,(c=E).left<h.left+h.width&&c.left+c.width>h.left&&c.top<h.top+h.height&&c.height+c.top>h.top)return void y();var c,h;j((E,ae,F)=>{var g,w;F.active=(g=m,(w=F).x>=g.left&&w.x<g.right&&w.y>=g.top&&w.y<g.bottom)})})(n,b)},[X(s,{icon:"ep-plus"})],46,Y))),128))]))),128))]),(d(!0),v(V,null,M($(l),(u,n)=>(d(),v("div",{key:n,class:"hot-area",style:R({top:i.cubeSize*u.top+"px",left:i.cubeSize*u.left+"px",height:i.cubeSize*u.height+"px",width:i.cubeSize*u.width+"px"}),onClick:S=>q(u,n),onMouseover:y},[$(I)===n?(d(),v("div",{key:0,class:"btn-delete",onClick:S=>(b=>{l.value.splice(b,1),y(),A()})(n)},[X(s,{icon:"ep:circle-close-filled"})],8,ee)):O("",!0),P(" "+Q(`${u.width}\xD7${u.height}`),1)],44,Z))),128))])])}}}),[["__scopeId","data-v-bfb0102d"]]);export{te as _};
