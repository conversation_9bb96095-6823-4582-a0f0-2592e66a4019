import{d as U,r as l,aK as $,A as x,o as t,w as g,c as r,F as v,y as m,a as s,i as w,aw as c,a3 as y,X as z,t as b}from"./index-CvERnF9Y.js";import{E as A,a as F}from"./el-carousel-item-McMyAnqb.js";import{E as H}from"./el-image-DTDUrxnp.js";const K={class:"flex flex-row flex-wrap"},M=U({name:"MenuSwiper",__name:"index",props:{property:{}},setup(k){const a=k,p=l([]),d=l(0),i=l(0),h=l("");return $(()=>a.property,()=>{h.value=1/a.property.column*100+"%",i.value=32+(a.property.layout==="iconText"?62:42),d.value=a.property.row*i.value;const n=a.property.row*a.property.column;p.value=[];let o=[];for(const u of a.property.list)o.length===n&&(o=[]),o.length===0&&p.value.push(o),o.push(u)},{immediate:!0,deep:!0}),(n,o)=>{const u=H,C=A,_=F;return t(),x(_,{height:`${s(d)}px`,autoplay:!1,arrow:"hover","indicator-position":"outside"},{default:g(()=>[(t(!0),r(v,null,m(s(p),(j,E)=>(t(),x(C,{key:E},{default:g(()=>[w("div",K,[(t(!0),r(v,null,m(j,(e,T)=>{var f;return t(),r("div",{key:T,class:"relative flex flex-col items-center justify-center",style:c({width:s(h),height:`${s(i)}px`})},[w("div",{class:z(["relative","h-42px w-42px"])},[(f=e.badge)!=null&&f.show?(t(),r("span",{key:0,class:"absolute right--10px top--10px z-1 h-20px rounded-10px p-x-6px text-center text-12px leading-20px",style:c({color:e.badge.textColor,backgroundColor:e.badge.bgColor})},b(e.badge.text),5)):y("",!0),e.iconUrl?(t(),x(u,{key:1,src:e.iconUrl,class:"h-full w-full"},null,8,["src"])):y("",!0)],2),n.property.layout==="iconText"?(t(),r("span",{key:0,class:"text-12px",style:c({color:e.titleColor,height:"20px",lineHeight:"20px"})},b(e.title),5)):y("",!0)],4)}),128))])]),_:2},1024))),128))]),_:1},8,["height"])}}});export{M as default};
