import{as as t}from"./index-CRsFgzy0.js";const s=async a=>await t.get({url:"/system/mail-account/page",params:a}),e=async a=>await t.get({url:"/system/mail-account/get?id="+a}),c=async a=>await t.post({url:"/system/mail-account/create",data:a}),i=async a=>await t.put({url:"/system/mail-account/update",data:a}),l=async a=>await t.delete({url:"/system/mail-account/delete?id="+a}),m=async()=>t.get({url:"/system/mail-account/simple-list"});export{s as a,m as b,c,l as d,e as g,i as u};
