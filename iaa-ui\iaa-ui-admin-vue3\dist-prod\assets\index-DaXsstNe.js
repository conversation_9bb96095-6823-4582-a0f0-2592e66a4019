import{as as _,d as k,b as F,p as J,r,a2 as j,f as q,q as A,c as H,o as b,g as l,w as a,J as v,A as I,s as R,a as d,v as O,P as S,a6 as B,l as K,n as L,a0 as N,dv as Q,H as i,an as W,m as X,G as Y,M as Z,F as $}from"./index-CRsFgzy0.js";import{_ as ee}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as le}from"./index-DYfNUK1u.js";const ae=k({name:"MemberConfig",__name:"index",setup(te){const{t:y}=F(),T=J(),V=r(!1),c=r(!1),t=r({id:void 0,pointTradeDeductEnable:!0,pointTradeDeductUnitPrice:0,pointTradeDeductMaxPrice:0,pointTradeGivePoint:0}),p=j({get:()=>(t.value.pointTradeDeductUnitPrice/100).toFixed(2),set:o=>{t.value.pointTradeDeductUnitPrice=Math.round(100*o)}}),P=q({}),m=r(),D=async()=>{if(m&&await m.value.validate()){c.value=!0;try{const o=t.value;await(async e=>await _.put({url:"/member/config/save",data:e}))(o),T.success(y("common.updateSuccess")),V.value=!1}finally{c.value=!1}}},w=async()=>{try{const o=await(async()=>await _.get({url:"/member/config/get"}))();if(o===null)return;t.value=o}finally{}};return A(()=>{w()}),(o,e)=>{const U=le,x=S,n=O,g=N,s=Q,f=W,h=L,M=K,E=Y,G=R,z=ee,C=Z;return b(),H($,null,[l(U,{title:"\u4F1A\u5458\u624B\u518C\uFF08\u529F\u80FD\u5F00\u542F\uFF09",url:"https://doc.iocoder.cn/member/build/"}),l(z,null,{default:a(()=>[v((b(),I(G,{ref_key:"formRef",ref:m,model:d(t),rules:d(P),"label-width":"120px"},{default:a(()=>[v(l(n,{label:"hideId"},{default:a(()=>[l(x,{modelValue:d(t).id,"onUpdate:modelValue":e[0]||(e[0]=u=>d(t).id=u)},null,8,["modelValue"])]),_:1},512),[[B,!1]]),l(M,null,{default:a(()=>[l(h,{label:"\u79EF\u5206"},{default:a(()=>[l(n,{label:"\u79EF\u5206\u62B5\u6263",prop:"pointTradeDeductEnable"},{default:a(()=>[l(g,{modelValue:d(t).pointTradeDeductEnable,"onUpdate:modelValue":e[1]||(e[1]=u=>d(t).pointTradeDeductEnable=u),style:{"user-select":"none"}},null,8,["modelValue"]),l(s,{class:"w-full",size:"small",type:"info"},{default:a(()=>e[5]||(e[5]=[i("\u4E0B\u5355\u79EF\u5206\u662F\u5426\u62B5\u7528\u8BA2\u5355\u91D1\u989D")])),_:1})]),_:1}),l(n,{label:"\u79EF\u5206\u62B5\u6263",prop:"pointTradeDeductUnitPrice"},{default:a(()=>[l(f,{modelValue:d(p),"onUpdate:modelValue":e[2]||(e[2]=u=>X(p)?p.value=u:null),placeholder:"\u8BF7\u8F93\u5165\u79EF\u5206\u62B5\u6263\u91D1\u989D",precision:2},null,8,["modelValue"]),l(s,{class:"w-full",size:"small",type:"info"},{default:a(()=>e[6]||(e[6]=[i(" \u79EF\u5206\u62B5\u7528\u6BD4\u4F8B(1 \u79EF\u5206\u62B5\u591A\u5C11\u91D1\u989D)\uFF0C\u5355\u4F4D\uFF1A\u5143 ")])),_:1})]),_:1}),l(n,{label:"\u79EF\u5206\u62B5\u6263\u6700\u5927\u503C",prop:"pointTradeDeductMaxPrice"},{default:a(()=>[l(f,{modelValue:d(t).pointTradeDeductMaxPrice,"onUpdate:modelValue":e[3]||(e[3]=u=>d(t).pointTradeDeductMaxPrice=u),placeholder:"\u8BF7\u8F93\u5165\u79EF\u5206\u62B5\u6263\u6700\u5927\u503C"},null,8,["modelValue"]),l(s,{class:"w-full",size:"small",type:"info"},{default:a(()=>e[7]||(e[7]=[i(" \u5355\u6B21\u4E0B\u5355\u79EF\u5206\u4F7F\u7528\u4E0A\u9650\uFF0C0 \u4E0D\u9650\u5236 ")])),_:1})]),_:1}),l(n,{label:"1 \u5143\u8D60\u9001\u591A\u5C11\u5206",prop:"pointTradeGivePoint"},{default:a(()=>[l(f,{modelValue:d(t).pointTradeGivePoint,"onUpdate:modelValue":e[4]||(e[4]=u=>d(t).pointTradeGivePoint=u),placeholder:"\u8BF7\u8F93\u5165 1 \u5143\u8D60\u9001\u591A\u5C11\u79EF\u5206"},null,8,["modelValue"]),l(s,{class:"w-full",size:"small",type:"info"},{default:a(()=>e[8]||(e[8]=[i(" \u4E0B\u5355\u652F\u4ED8\u91D1\u989D\u6309\u6BD4\u4F8B\u8D60\u9001\u79EF\u5206\uFF08\u5B9E\u9645\u652F\u4ED8 1 \u5143\u8D60\u9001\u591A\u5C11\u79EF\u5206\uFF09 ")])),_:1})]),_:1})]),_:1})]),_:1}),l(n,null,{default:a(()=>[l(E,{type:"primary",onClick:D},{default:a(()=>e[9]||(e[9]=[i("\u4FDD\u5B58")])),_:1})]),_:1})]),_:1},8,["model","rules"])),[[C,d(c)]])]),_:1})],64)}}});export{ae as default};
