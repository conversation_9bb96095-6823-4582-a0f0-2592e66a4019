import{d as t,c as p,A as o,o as r,g as c,I as m,_ as i}from"./index-CRsFgzy0.js";import{E as n}from"./el-image-BQpHFDaE.js";const l={key:0,class:"h-50px flex items-center justify-center bg-gray-3"},x=i(t({name:"ImageBar",__name:"index",props:{property:{}},setup:y=>(e,d)=>{const s=m,a=n;return e.property.imgUrl?(r(),o(a,{key:1,class:"min-h-30px",src:e.property.imgUrl},null,8,["src"])):(r(),p("div",l,[c(s,{icon:"ep:picture",class:"text-gray-8 text-30px!"})]))}}),[["__scopeId","data-v-357d8f5e"]]);export{x as default};
