import{as as y,d as j,p as B,r as u,f as L,O as Q,c as W,o as F,g as l,w as s,s as X,a as t,v as Y,l as $,m as h,E as ee,h as ae,J as le,a3 as te,A as se,G as oe,H as c,I as re,n as pe,cz as ue,F as ie}from"./index-CvERnF9Y.js";import{_ as ne}from"./index-CeUx6j9a.js";import{_ as de}from"./ReplyForm.vue_vue_type_script_setup_true_lang-BUmj95d7.js";import{R as k}from"./TabNews-CP5s1A4W.js";import"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import"./el-image-DTDUrxnp.js";import"./main-DruMbIs0.js";import"./BenzAMRRecorder-dBvNaVvQ.js";import"./main-UpKW1XRT.js";import"./main.vue_vue_type_script_setup_true_lang-CZEznXEY.js";import"./main-C8INObly.js";import"./useUpload-B3hHcA3v.js";import"./TabImage-tbhtv2TU.js";import"./TabVoice-DyDV1SeR.js";import"./TabVideo-Fa0HcEzu.js";import"./main-BJb7Su3Y.js";import{_ as me}from"./main.vue_vue_type_script_setup_true_lang-BtApKOBq.js";import{_ as A}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as ce}from"./ReplyTable.vue_vue_type_script_setup_true_lang-Dyzm38p3.js";import{M as v}from"./types-CAO1T7C7.js";import"./index-DHM6tdge.js";import"./index-CvUTvZT7.js";import"./index-CH6UXmCe.js";import"./formatTime-CmW2_KRq.js";import"./TabText.vue_vue_type_script_setup_true_lang-CoFunBW6.js";import"./TabMusic.vue_vue_type_script_setup_true_lang-SoVwpl49.js";import"./index-CpH6tZ7i.js";import"./tagsView-BBlo6m3V.js";import"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import"./color-CIFUYK2M.js";import"./main-ukaNzr4E.js";const ve=j({name:"MpAutoReply",__name:"index",setup(ye){const f=B(),q=u(-1),p=u(v.Keyword),I=u(!0),D=u(0),U=u([]),w=u(null),i=L({pageNo:1,pageSize:10,accountId:q}),b=u(!1),n=u(!1),r=u({}),o=u({type:k.Text,accountId:-1}),H=a=>{q.value=a,o.value.accountId=a,i.pageNo=1,M()},M=async()=>{I.value=!0;try{const e=await(a={...i,type:p.value},y.get({url:"/mp/auto-reply/page",params:a}));U.value=e.list,D.value=e.total}finally{I.value=!1}var a},K=a=>{p.value=a,i.pageNo=1,M()},R=()=>{T(),o.value={type:k.Text,accountId:i.accountId},b.value=!0,n.value=!0},N=async a=>{T();const e=await(m=>y.get({url:"/mp/auto-reply/get?id="+m}))(a);r.value={...e},delete r.value.responseMessageType,delete r.value.responseContent,delete r.value.responseMediaId,delete r.value.responseMediaUrl,delete r.value.responseDescription,delete r.value.responseArticles,o.value={type:e.responseMessageType,accountId:i.accountId,content:e.responseContent,mediaId:e.responseMediaId,url:e.responseMediaUrl,title:e.responseTitle,description:e.responseDescription,thumbMediaId:e.responseThumbMediaId,thumbMediaUrl:e.responseThumbMediaUrl,articles:e.responseArticles,musicUrl:e.responseMusicUrl,hqMusicUrl:e.responseHqMusicUrl},b.value=!1,n.value=!0},O=async a=>{await f.confirm("\u662F\u5426\u786E\u8BA4\u5220\u9664\u6B64\u6570\u636E?"),await(e=>y.delete({url:"/mp/auto-reply/delete?id="+e}))(a),await M(),f.success("\u5220\u9664\u6210\u529F")},z=async()=>{var m;await((m=w.value)==null?void 0:m.validate());const a={...r.value};var e;a.responseMessageType=o.value.type,a.responseContent=o.value.content,a.responseMediaId=o.value.mediaId,a.responseMediaUrl=o.value.url,a.responseTitle=o.value.title,a.responseDescription=o.value.description,a.responseThumbMediaId=o.value.thumbMediaId,a.responseThumbMediaUrl=o.value.thumbMediaUrl,a.responseArticles=o.value.articles,a.responseMusicUrl=o.value.musicUrl,a.responseHqMusicUrl=o.value.hqMusicUrl,r.value.id!==void 0?(await(e=a,y.put({url:"/mp/auto-reply/update",data:e})),f.success("\u4FEE\u6539\u6210\u529F")):(await(x=>y.post({url:"/mp/auto-reply/create",data:x}))(a),f.success("\u65B0\u589E\u6210\u529F")),n.value=!1,await M()},T=()=>{var a;r.value={id:void 0,accountId:i.accountId,type:p.value,requestKeyword:void 0,requestMatch:p.value===v.Keyword?1:void 0,requestMessageType:void 0},(a=w.value)==null||a.resetFields()},J=()=>{n.value=!1,T()};return(a,e)=>{const m=ne,x=Y,E=X,g=re,V=oe,G=ae,_=ee,C=pe,P=$,S=ue,Z=Q("hasPermi");return F(),W(ie,null,[l(m,{title:"\u81EA\u52A8\u56DE\u590D",url:"https://doc.iocoder.cn/mp/auto-reply/"}),l(t(A),null,{default:s(()=>[l(E,{class:"-mb-15px",model:t(i),inline:!0,"label-width":"68px"},{default:s(()=>[l(x,{label:"\u516C\u4F17\u53F7",prop:"accountId"},{default:s(()=>[l(t(me),{onChange:H})]),_:1})]),_:1},8,["model"])]),_:1}),l(t(A),null,{default:s(()=>[l(P,{modelValue:t(p),"onUpdate:modelValue":e[0]||(e[0]=d=>h(p)?p.value=d:null),onTabChange:K},{default:s(()=>[l(_,{gutter:10,class:"mb8"},{default:s(()=>[l(G,{span:1.5},{default:s(()=>[t(p)!==t(v).Follow||t(U).length<=0?le((F(),se(V,{key:0,type:"primary",plain:"",onClick:R},{default:s(()=>[l(g,{icon:"ep:plus"}),e[4]||(e[4]=c("\u65B0\u589E "))]),_:1})),[[Z,["mp:auto-reply:create"]]]):te("",!0)]),_:1})]),_:1}),l(C,{name:t(v).Follow},{label:s(()=>[l(_,{align:"middle"},{default:s(()=>[l(g,{icon:"ep:star",class:"mr-2px"}),e[5]||(e[5]=c(" \u5173\u6CE8\u65F6\u56DE\u590D"))]),_:1})]),_:1},8,["name"]),l(C,{name:t(v).Message},{label:s(()=>[l(_,{align:"middle"},{default:s(()=>[l(g,{icon:"ep:chat-line-round",class:"mr-2px"}),e[6]||(e[6]=c(" \u6D88\u606F\u56DE\u590D"))]),_:1})]),_:1},8,["name"]),l(C,{name:t(v).Keyword},{label:s(()=>[l(_,{align:"middle"},{default:s(()=>[l(g,{icon:"fa:newspaper-o",class:"mr-2px"}),e[7]||(e[7]=c(" \u5173\u952E\u8BCD\u56DE\u590D"))]),_:1})]),_:1},8,["name"])]),_:1},8,["modelValue"]),l(ce,{loading:t(I),list:t(U),"msg-type":t(p),onOnUpdate:N,onOnDelete:O},null,8,["loading","list","msg-type"]),l(S,{title:t(b)?"\u65B0\u589E\u81EA\u52A8\u56DE\u590D":"\u4FEE\u6539\u81EA\u52A8\u56DE\u590D",modelValue:t(n),"onUpdate:modelValue":e[3]||(e[3]=d=>h(n)?n.value=d:null),width:"800px","destroy-on-close":""},{footer:s(()=>[l(V,{onClick:J},{default:s(()=>e[8]||(e[8]=[c("\u53D6 \u6D88")])),_:1}),l(V,{type:"primary",onClick:z},{default:s(()=>e[9]||(e[9]=[c("\u786E \u5B9A")])),_:1})]),default:s(()=>[l(de,{modelValue:t(r),"onUpdate:modelValue":e[1]||(e[1]=d=>h(r)?r.value=d:null),reply:t(o),"onUpdate:reply":e[2]||(e[2]=d=>h(o)?o.value=d:null),"msg-type":t(p),ref_key:"formRef",ref:w},null,8,["modelValue","reply","msg-type"])]),_:1},8,["title","modelValue"])]),_:1})],64)}}});export{ve as default};
