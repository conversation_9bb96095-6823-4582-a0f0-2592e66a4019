import{d as p,r as o,q as u,c as d,o as n,g as e,w as f,J as y,a3 as _,a as r,A as h,M as v,F as g}from"./index-CvERnF9Y.js";import{_ as b}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as x}from"./IFrame.vue_vue_type_script_setup_true_lang-B5-s51Jd.js";import{_ as w}from"./index-CeUx6j9a.js";import{b as A}from"./index-CfXwz0ub.js";const S=p({name:"InfraAdminServer",__name:"index",setup(k){const s=o(!0),t=o("http://shouhou.iaa360.com/admin/applications");return u(async()=>{try{const a=await A("url.spring-boot-admin");a&&a.length>0&&(t.value=a)}finally{s.value=!1}}),(a,q)=>{const i=w,m=x,l=b,c=v;return n(),d(g,null,[e(i,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),e(l,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:f(()=>[r(s)?_("",!0):y((n(),h(m,{key:0,src:r(t)},null,8,["src"])),[[c,r(s)]])]),_:1})],64)}}});export{S as default};
