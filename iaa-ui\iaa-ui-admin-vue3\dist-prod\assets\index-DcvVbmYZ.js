import{d as g,p as k,r,eW as y,eX as S,aK as C,q as M,aL as L,A,o as F,w as R,g as m,a as p,aq as w,f0 as x}from"./index-CRsFgzy0.js";import I from"./KeFuConversationList-DWwpiOS5.js";import K from"./KeFuMessageList-4xbqNHkv.js";import N from"./MemberInfo-BPdi7wXo.js";import{u as T,W as l}from"./kefu-CwcoonwQ.js";import"./el-avatar-Nl9DW69B.js";import"./emoji-MM8zhL9J.js";import"./formatTime-DhdtkSIS.js";import"./el-empty-CqTDiVWi.js";import"./el-image-BQpHFDaE.js";import"./EmojiSelectPopover.vue_vue_type_script_setup_true_lang-CGvb_qyx.js";import"./PictureSelectUpload.vue_vue_type_script_setup_true_lang-DXhFfzoT.js";import"./picture-CTjip5lJ.js";import"./ProductItem-CSRkJo9p.js";import"./OrderItem-DgbO8iIn.js";import"./constants-uird_4gU.js";import"./relativeTime-Bcjvi3Pu.js";import"./ProductBrowsingHistory.vue_vue_type_script_setup_true_lang-Cj_zXu4C.js";import"./concat-BrdO5wJ1.js";import"./OrderBrowsingHistory.vue_vue_type_script_setup_true_lang-BKVnbDqz.js";import"./index-CtK0H6z7.js";import"./CardTitle-DCrrQp54.js";import"./UserBasicInfo-xueaK3SV.js";import"./el-descriptions-item-lelixL8M.js";import"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import"./color-CIFUYK2M.js";import"./Descriptions.vue_vue_type_style_index_0_scoped_74d4336e_lang-CcD6mrBi.js";import"./el-collapse-transition-l0sNRNKZ.js";import"./DescriptionsItemLabel-DoS3lyV9.js";import"./UserAccountInfo-Be72l2Cu.js";import"./index-BCuY8xr3.js";import"./index-BqI6YY4H.js";const b=g({name:"KeFu",__name:"index",setup(q){const c=k(),a=T(),v=r("http://shouhou.iaa360.com/infra/ws".replace("http","ws")+"?token="+y()),{data:d,close:h,open:_}=S(v.value,{autoReconnect:!0,heartbeat:!0});C(()=>d.value,t=>{var o;if(t)try{if(t==="pong")return;const e=JSON.parse(t),s=e.type;if(!s)return void c.error("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B\uFF1A"+t);if(s===l.KEFU_MESSAGE_TYPE){const f=JSON.parse(e.content);return a.updateConversation(f.conversationId),void((o=i.value)==null?void 0:o.refreshMessageList(f))}s===l.KEFU_MESSAGE_ADMIN_READ&&a.updateConversationStatus(x(e.content))}catch{}},{immediate:!1});const i=r(),n=r(),E=t=>{var o,e;(o=i.value)==null||o.getNewMessageList(t),(e=n.value)==null||e.initHistory(t)},u=r();return M(()=>{a.setConversationList().then(()=>{var t;(t=u.value)==null||t.calculationLastMessageTime()}),_()}),L(()=>{h()}),(t,o)=>{const e=w;return F(),A(e,{class:"kefu-layout"},{default:R(()=>[m(p(I),{ref_key:"keFuConversationRef",ref:u,onChange:E},null,512),m(p(K),{ref_key:"keFuChatBoxRef",ref:i},null,512),m(p(N),{ref_key:"memberInfoRef",ref:n},null,512)]),_:1})}}});export{b as default};
