import{d as q,r as P,dq as A,f as w,aR as y,q as j,c as z,o as d,g as a,i as D,w as r,J as p,A as f,h as F,a as l,M as J,E as K,k as X,K as B,L as G,F as H,eM as Y,R as N,D as U,_ as O}from"./index-CvERnF9Y.js";import{e as Q,_ as V}from"./Echart.vue_vue_type_script_setup_true_lang-D9HJSbBT.js";import{_ as W}from"./index-CeUx6j9a.js";import{d as Z,e as aa,f as ea,b as ta}from"./member-C3WykwsZ.js";import{_ as v}from"./index.vue_vue_type_script_setup_true_lang-CZoePPkw.js";import{c as ra}from"./china-aeAnb323.js";import{f as la}from"./formatter-UUK_ohaG.js";import oa from"./MemberFunnelCard-B8foiFAQ.js";import{_ as sa}from"./MemberTerminalCard.vue_vue_type_script_setup_true_lang-Bz8zYDlD.js";import{C as E}from"./CardTitle-CdidxETN.js";import"./echarts-BcS7Kngw.js";import"./formatTime-CmW2_KRq.js";import"./CountTo.vue_vue_type_script_setup_true_lang-DkUXM3W0.js";import"./index.vue_vue_type_script_setup_true_lang-PqRfzyQf.js";const ia={class:"flex flex-col"},na=O(q({name:"MemberStatistics",__name:"index",setup(ma){var C;const u=P(!0),b=P(),_=A();(C=Q)==null||C.registerMap("china",ra);const R=w({tooltip:{trigger:"item",confine:!0,formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right"},roseType:"area",series:[{name:"\u4F1A\u5458\u7EC8\u7AEF",type:"pie",label:{show:!1},labelLine:{show:!1},data:[]}]}),x=w({tooltip:{trigger:"item",confine:!0,formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"right"},roseType:"area",series:[{name:"\u4F1A\u5458\u6027\u522B",type:"pie",label:{show:!1},labelLine:{show:!1},data:[]}]}),h=w({tooltip:{trigger:"item",formatter:t=>{var s,o,e,i,m;return`${((s=t==null?void 0:t.data)==null?void 0:s.areaName)||(t==null?void 0:t.name)}<br/>
\u4F1A\u5458\u6570\u91CF\uFF1A${((o=t==null?void 0:t.data)==null?void 0:o.userCount)||0}<br/>
\u8BA2\u5355\u521B\u5EFA\u6570\u91CF\uFF1A${((e=t==null?void 0:t.data)==null?void 0:e.orderCreateUserCount)||0}<br/>
\u8BA2\u5355\u652F\u4ED8\u6570\u91CF\uFF1A${((i=t==null?void 0:t.data)==null?void 0:i.orderPayUserCount)||0}<br/>
\u8BA2\u5355\u652F\u4ED8\u91D1\u989D\uFF1A${y(((m=t==null?void 0:t.data)==null?void 0:m.orderPayPrice)||0)}`}},visualMap:{text:["\u9AD8","\u4F4E"],realtime:!1,calculable:!0,top:"middle",inRange:{color:["#fff","#3b82f6"]}},series:[{name:"\u4F1A\u5458\u5730\u57DF\u5206\u5E03",type:"map",map:"china",roam:!1,selectedMode:!1,data:[]}]}),S=async()=>{b.value=await Z()},$=async()=>{const t=await aa();_.value=t.map(e=>({...e,areaName:Y(e.areaName)}));let s=0,o=0;h.series[0].data=_.value.map(e=>(s=Math.min(s,e.orderPayUserCount||0),o=Math.max(o,e.orderPayUserCount||0),{...e,name:e.areaName,value:e.orderPayUserCount||0})),h.visualMap.min=s,h.visualMap.max=o},L=async()=>{const t=await ea(),s=N(U.SYSTEM_USER_SEX);s.push({label:"\u672A\u77E5",value:null}),x.series[0].data=s.map(o=>{var i;const e=(i=t.find(m=>m.sex===o.value))==null?void 0:i.userCount;return{name:o.label,value:e||0}})},T=async()=>{const t=await ta(),s=N(U.TERMINAL);s.push({label:"\u672A\u77E5",value:null}),R.series[0].data=s.map(o=>{var i;const e=(i=t.find(m=>m.terminal===o.value))==null?void 0:i.userCount;return{name:o.label,value:e||0}})};return j(async()=>{u.value=!0,await Promise.all([S(),T(),$(),L()]),u.value=!1}),(t,s)=>{const o=W,e=F,i=K,m=V,g=G,I=B,M=X,c=J;return d(),z(H,null,[a(o,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),D("div",ia,[a(i,{gutter:16,class:"summary"},{default:r(()=>[p((d(),f(e,{sm:6,xs:12},{default:r(()=>{var n;return[a(v,{value:((n=l(b))==null?void 0:n.userCount)||0,icon:"fa-solid:users","icon-bg-color":"text-blue-500","icon-color":"bg-blue-100",title:"\u7D2F\u8BA1\u4F1A\u5458\u6570"},null,8,["value"])]}),_:1})),[[c,l(u)]]),p((d(),f(e,{sm:6,xs:12},{default:r(()=>{var n;return[a(v,{value:((n=l(b))==null?void 0:n.rechargeUserCount)||0,icon:"fa-solid:user","icon-bg-color":"text-purple-500","icon-color":"bg-purple-100",title:"\u7D2F\u8BA1\u5145\u503C\u4EBA\u6570"},null,8,["value"])]}),_:1})),[[c,l(u)]]),p((d(),f(e,{sm:6,xs:12},{default:r(()=>{var n;return[a(v,{decimals:2,value:l(y)(((n=l(b))==null?void 0:n.rechargePrice)||0),icon:"fa-solid:money-check-alt","icon-bg-color":"text-yellow-500","icon-color":"bg-yellow-100",prefix:"\uFFE5",title:"\u7D2F\u8BA1\u5145\u503C\u91D1\u989D"},null,8,["value"])]}),_:1})),[[c,l(u)]]),p((d(),f(e,{sm:6,xs:12},{default:r(()=>{var n;return[a(v,{decimals:2,value:l(y)(((n=l(b))==null?void 0:n.expensePrice)||0),icon:"fa-solid:yen-sign","icon-bg-color":"text-green-500","icon-color":"bg-green-100",prefix:"\uFFE5",title:"\u7D2F\u8BA1\u6D88\u8D39\u91D1\u989D"},null,8,["value"])]}),_:1})),[[c,l(u)]])]),_:1}),a(i,{gutter:16,class:"mb-4"},{default:r(()=>[a(e,{md:18,sm:24},{default:r(()=>[a(oa)]),_:1}),a(e,{md:6,sm:24},{default:r(()=>[a(sa)]),_:1})]),_:1}),a(i,{gutter:16},{default:r(()=>[a(e,{md:18,sm:24},{default:r(()=>[a(M,{shadow:"never"},{header:r(()=>[a(l(E),{title:"\u4F1A\u5458\u5730\u57DF\u5206\u5E03"})]),default:r(()=>[p((d(),f(i,null,{default:r(()=>[a(e,{span:10},{default:r(()=>[a(m,{height:300,options:l(h)},null,8,["options"])]),_:1}),a(e,{span:14},{default:r(()=>[a(I,{data:l(_),height:300},{default:r(()=>[a(g,{"sort-method":(n,k)=>n.areaName.localeCompare(k.areaName,"zh-CN"),align:"center",label:"\u7701\u4EFD","min-width":"80",prop:"areaName","show-overflow-tooltip":"",sortable:""},null,8,["sort-method"]),a(g,{align:"center",label:"\u4F1A\u5458\u6570\u91CF","min-width":"105",prop:"userCount",sortable:""}),a(g,{align:"center",label:"\u8BA2\u5355\u521B\u5EFA\u6570\u91CF","min-width":"135",prop:"orderCreateUserCount",sortable:""}),a(g,{align:"center",label:"\u8BA2\u5355\u652F\u4ED8\u6570\u91CF","min-width":"135",prop:"orderPayUserCount",sortable:""}),a(g,{formatter:l(la),align:"center",label:"\u8BA2\u5355\u652F\u4ED8\u91D1\u989D","min-width":"135",prop:"orderPayPrice",sortable:""},null,8,["formatter"])]),_:1},8,["data"])]),_:1})]),_:1})),[[c,l(u)]])]),_:1})]),_:1}),a(e,{md:6,sm:24},{default:r(()=>[p((d(),f(M,{shadow:"never"},{header:r(()=>[a(l(E),{title:"\u4F1A\u5458\u6027\u522B\u6BD4\u4F8B"})]),default:r(()=>[a(m,{height:300,options:l(x)},null,8,["options"])]),_:1})),[[c,l(u)]])]),_:1})]),_:1})])],64)}}}),[["__scopeId","data-v-ab9204f6"]]);export{na as default};
