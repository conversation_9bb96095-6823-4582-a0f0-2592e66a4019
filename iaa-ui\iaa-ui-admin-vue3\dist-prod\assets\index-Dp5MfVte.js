import{d as w,r as l,q as g,c as x,o as v,g as a,w as e,G as k,H as I,I as K,i as P,J as j,a as n,M as A,F as C}from"./index-CRsFgzy0.js";import"./el-empty-CqTDiVWi.js";import"./el-virtual-list-AoPW9ghL.js";import{E as F,a as b}from"./el-table-v2-DMzSXqeI.js";import{_ as q}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as E}from"./index-DYfNUK1u.js";import{_ as G}from"./AreaForm.vue_vue_type_script_setup_true_lang-BX8DhInw.js";import{g as H}from"./index-DLC3Afbg.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";const J={style:{width:"100%",height:"700px"}},M=w({name:"SystemArea",__name:"index",setup(R){const m=[{dataKey:"id",title:"\u7F16\u53F7",width:400,fixed:!0,key:"id"},{dataKey:"name",title:"\u5730\u540D",width:200}],i=l(!0),s=l([]),r=l();return g(()=>{(async()=>{i.value=!0;try{s.value=await H()}finally{i.value=!1}})()}),(S,t)=>{const p=E,u=K,f=k,o=q,h=b,c=F,y=A;return v(),x(C,null,[a(p,{title:"\u5730\u533A & IP",url:"https://doc.iocoder.cn/area-and-ip/"}),a(o,null,{default:e(()=>[a(f,{type:"primary",plain:"",onClick:t[0]||(t[0]=d=>{r.value.open()})},{default:e(()=>[a(u,{icon:"ep:plus",class:"mr-5px"}),t[1]||(t[1]=I(" IP \u67E5\u8BE2 "))]),_:1})]),_:1}),a(o,null,{default:e(()=>[P("div",J,[a(c,null,{default:e(({height:d,width:_})=>[j(a(h,{columns:m,data:n(s),width:_,height:d,"expand-column-key":"id"},null,8,["data","width","height"]),[[y,n(i)]])]),_:1})])]),_:1}),a(G,{ref_key:"formRef",ref:r},null,512)],64)}}});export{M as default};
