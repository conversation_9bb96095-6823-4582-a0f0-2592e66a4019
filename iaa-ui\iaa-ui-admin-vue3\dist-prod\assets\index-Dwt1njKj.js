import{d as p,r as o,q as u,c as d,o as n,g as e,w as f,J as y,a3 as g,a as r,A as k,M as _,F as h}from"./index-CvERnF9Y.js";import{_ as v}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as w}from"./IFrame.vue_vue_type_script_setup_true_lang-B5-s51Jd.js";import{_ as x}from"./index-CeUx6j9a.js";import{b}from"./index-CfXwz0ub.js";const S=p({name:"InfraSkyWalking",__name:"index",setup(q){const s=o(!0),t=o("http://skywalking.shop.iocoder.cn");return u(async()=>{try{const a=await b("url.skywalking");a&&a.length>0&&(t.value=a)}finally{s.value=!1}}),(a,A)=>{const l=x,i=w,c=v,m=_;return n(),d(h,null,[e(l,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),e(c,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:f(()=>[r(s)?g("",!0):y((n(),k(i,{key:0,src:r(t)},null,8,["src"])),[[m,r(s)]])]),_:1})],64)}}});export{S as default};
