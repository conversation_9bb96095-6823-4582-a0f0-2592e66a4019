import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as n}from"./IFrame.vue_vue_type_script_setup_true_lang-B5-s51Jd.js";import{_ as c}from"./index-CeUx6j9a.js";import{d as m,r as p,c as i,o as l,g as r,w as d,a as u,F as _}from"./index-CvERnF9Y.js";const f=m({name:"GoView",__name:"index",setup(x){const s=p("http://127.0.0.1:3000");return(b,g)=>{const t=c,a=n,o=e;return l(),i(_,null,[r(t,{title:"\u5927\u5C4F\u8BBE\u8BA1\u5668",url:"https://doc.iocoder.cn/report/screen/"}),r(o,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:d(()=>[r(a,{src:u(s)},null,8,["src"])]),_:1})],64)}}});export{f as default};
