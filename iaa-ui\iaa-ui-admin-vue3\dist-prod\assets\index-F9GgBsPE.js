import{_ as R}from"./Table-KbyTcVjD.js";import{_ as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{u as M,_ as w}from"./useTable-BPpX5rev.js";import{d as j,r as P,q,O,c as U,o as m,g as o,w as r,a as t,J as l,A as c,G as D,H as u,I as F,F as G}from"./index-CRsFgzy0.js";import{_ as H}from"./index-DYfNUK1u.js";import{a as b}from"./account.data-D1BkkXq2.js";import{d as I,a as J}from"./index-DWTWNHcr.js";import{_ as T}from"./MailAccountForm.vue_vue_type_script_setup_true_lang-B-QMB9BO.js";import{_ as B}from"./MailAccountDetail.vue_vue_type_script_setup_true_lang-m04-14pE.js";import"./tsxHelper-DqnKTMG3.js";import"./index-CqPfoRkb.js";import"./Form-BF4H89jq.js";import"./el-virtual-list-AoPW9ghL.js";import"./el-tree-select-BijZG_HG.js";import"./el-time-select-DBwK3NDO.js";import"./InputPassword-CO9Ecx-K.js";import"./download-oWiM5xVU.js";import"./formatTime-DhdtkSIS.js";import"./formRules-V2Qetfkc.js";import"./useCrudSchemas-CNYomGr4.js";import"./tree-COGD3qag.js";import"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./Descriptions-BeaU49XW.js";import"./Descriptions.vue_vue_type_style_index_0_scoped_74d4336e_lang-CcD6mrBi.js";import"./el-collapse-transition-l0sNRNKZ.js";import"./el-descriptions-item-lelixL8M.js";const E=j({name:"SystemMailAccount",__name:"index",setup(K){const{tableObject:i,tableMethods:d}=M({getListApi:J,delListApi:I}),{getList:f,setSearchParams:g}=d,_=P(),y=(k,a)=>{_.value.open(k,a)},S=P();return q(()=>{f()}),(k,a)=>{const v=H,z=F,n=D,L=w,h=x,A=R,p=O("hasPermi");return m(),U(G,null,[o(v,{title:"\u90AE\u4EF6\u914D\u7F6E",url:"https://doc.iocoder.cn/mail"}),o(h,null,{default:r(()=>[o(L,{schema:t(b).searchSchema,onSearch:t(g),onReset:t(g)},{actionMore:r(()=>[l((m(),c(n,{type:"primary",plain:"",onClick:a[0]||(a[0]=e=>y("create"))},{default:r(()=>[o(z,{icon:"ep:plus",class:"mr-5px"}),a[3]||(a[3]=u(" \u65B0\u589E "))]),_:1})),[[p,["system:mail-account:create"]]])]),_:1},8,["schema","onSearch","onReset"])]),_:1}),o(h,null,{default:r(()=>[o(A,{columns:t(b).tableColumns,data:t(i).tableList,loading:t(i).loading,pagination:{total:t(i).total},pageSize:t(i).pageSize,"onUpdate:pageSize":a[1]||(a[1]=e=>t(i).pageSize=e),currentPage:t(i).currentPage,"onUpdate:currentPage":a[2]||(a[2]=e=>t(i).currentPage=e)},{action:r(({row:e})=>[l((m(),c(n,{link:"",type:"primary",onClick:C=>y("update",e.id)},{default:r(()=>a[4]||(a[4]=[u(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[p,["system:mail-account:update"]]]),l((m(),c(n,{link:"",type:"primary",onClick:C=>{return s=e.id,void S.value.open(s);var s}},{default:r(()=>a[5]||(a[5]=[u(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])),[[p,["system:mail-account:query"]]]),l((m(),c(n,{link:"",type:"danger",onClick:C=>{return s=e.id,void d.delList(s,!1);var s}},{default:r(()=>a[6]||(a[6]=[u(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[p,["system:mail-account:delete"]]])]),_:1},8,["columns","data","loading","pagination","pageSize","currentPage"])]),_:1}),o(T,{ref_key:"formRef",ref:_,onSuccess:t(f)},null,8,["onSuccess"]),o(B,{ref_key:"detailRef",ref:S},null,512)],64)}}});export{E as default};
