import{d as Z,p as $,b as ee,r as u,f as ae,q as le,O as te,c as v,o as s,g as e,w as o,s as oe,a as t,v as ne,P as se,Q as S,x as re,F as x,y as I,R as pe,D as K,A as c,B as ce,C as ie,J as g,G as ue,H as p,I as de,K as me,L as fe,aE as ye,a3 as _e,t as q,M as ge}from"./index-CRsFgzy0.js";import{_ as be}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as we}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as xe}from"./index-DYfNUK1u.js";import{d as z}from"./formatTime-DhdtkSIS.js";import{d as ke}from"./download-oWiM5xVU.js";import{_ as he,g as Ce,d as Ve,e as Me}from"./TenantForm.vue_vue_type_script_setup_true_lang-D2aCtqoG.js";import{g as Se}from"./index-5Q3zFXXS.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./constants-uird_4gU.js";const Ne=Z({name:"SystemTenant",__name:"index",setup(Te){const k=$(),{t:F}=ee(),h=u(!0),N=u(0),T=u([]),n=ae({pageNo:1,pageSize:10,name:void 0,contactName:void 0,contactMobile:void 0,status:void 0,createTime:[]}),U=u(),C=u(!1),D=u([]),f=async()=>{h.value=!0;try{const i=await Ce(n);T.value=i.list,N.value=i.total}finally{h.value=!1}},y=()=>{n.pageNo=1,f()},H=()=>{U.value.resetFields(),y()},O=u(),Y=(i,a)=>{O.value.open(i,a)},P=async()=>{try{await k.exportConfirm(),C.value=!0;const i=await Me(n);ke.excel(i,"\u79DF\u6237\u5217\u8868.xls")}catch{}finally{C.value=!1}};return le(async()=>{await f(),D.value=await Se()}),(i,a)=>{const R=xe,V=se,d=ne,J=ce,L=re,B=ie,b=de,m=ue,E=oe,A=ve,r=fe,M=ye,G=we,Q=me,j=be,w=te("hasPermi"),W=ge;return s(),v(x,null,[e(R,{title:"SaaS \u591A\u79DF\u6237",url:"https://doc.iocoder.cn/saas-tenant/"}),e(A,null,{default:o(()=>[e(E,{class:"-mb-15px",model:t(n),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:o(()=>[e(d,{label:"\u79DF\u6237\u540D",prop:"name"},{default:o(()=>[e(V,{modelValue:t(n).name,"onUpdate:modelValue":a[0]||(a[0]=l=>t(n).name=l),placeholder:"\u8BF7\u8F93\u5165\u79DF\u6237\u540D",clearable:"",onKeyup:S(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u8054\u7CFB\u4EBA",prop:"contactName"},{default:o(()=>[e(V,{modelValue:t(n).contactName,"onUpdate:modelValue":a[1]||(a[1]=l=>t(n).contactName=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u4EBA",clearable:"",onKeyup:S(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u8054\u7CFB\u624B\u673A",prop:"contactMobile"},{default:o(()=>[e(V,{modelValue:t(n).contactMobile,"onUpdate:modelValue":a[2]||(a[2]=l=>t(n).contactMobile=l),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u624B\u673A",clearable:"",onKeyup:S(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u79DF\u6237\u72B6\u6001",prop:"status"},{default:o(()=>[e(L,{modelValue:t(n).status,"onUpdate:modelValue":a[3]||(a[3]=l=>t(n).status=l),placeholder:"\u8BF7\u9009\u62E9\u79DF\u6237\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(s(!0),v(x,null,I(t(pe)(t(K).COMMON_STATUS),l=>(s(),c(J,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:o(()=>[e(B,{modelValue:t(n).createTime,"onUpdate:modelValue":a[4]||(a[4]=l=>t(n).createTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(d,null,{default:o(()=>[e(m,{onClick:y},{default:o(()=>[e(b,{icon:"ep:search",class:"mr-5px"}),a[8]||(a[8]=p(" \u641C\u7D22 "))]),_:1}),e(m,{onClick:H},{default:o(()=>[e(b,{icon:"ep:refresh",class:"mr-5px"}),a[9]||(a[9]=p(" \u91CD\u7F6E "))]),_:1}),g((s(),c(m,{type:"primary",plain:"",onClick:a[5]||(a[5]=l=>Y("create"))},{default:o(()=>[e(b,{icon:"ep:plus",class:"mr-5px"}),a[10]||(a[10]=p(" \u65B0\u589E "))]),_:1})),[[w,["system:tenant:create"]]]),g((s(),c(m,{type:"success",plain:"",onClick:P,loading:t(C)},{default:o(()=>[e(b,{icon:"ep:download",class:"mr-5px"}),a[11]||(a[11]=p(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[w,["system:tenant:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(A,null,{default:o(()=>[g((s(),c(Q,{data:t(T)},{default:o(()=>[e(r,{label:"\u79DF\u6237\u7F16\u53F7",align:"center",prop:"id"}),e(r,{label:"\u79DF\u6237\u540D",align:"center",prop:"name"}),e(r,{label:"\u79DF\u6237\u5957\u9910",align:"center",prop:"packageId"},{default:o(l=>[l.row.packageId===0?(s(),c(M,{key:0,type:"danger"},{default:o(()=>a[12]||(a[12]=[p("\u7CFB\u7EDF\u79DF\u6237")])),_:1})):(s(!0),v(x,{key:1},I(t(D),_=>(s(),v(x,null,[_.id===l.row.packageId?(s(),c(M,{type:"success",key:_.id},{default:o(()=>[p(q(_.name),1)]),_:2},1024)):_e("",!0)],64))),256))]),_:1}),e(r,{label:"\u8054\u7CFB\u4EBA",align:"center",prop:"contactName"}),e(r,{label:"\u8054\u7CFB\u624B\u673A",align:"center",prop:"contactMobile"}),e(r,{label:"\u8D26\u53F7\u989D\u5EA6",align:"center",prop:"accountCount"},{default:o(l=>[e(M,null,{default:o(()=>[p(q(l.row.accountCount),1)]),_:2},1024)]),_:1}),e(r,{label:"\u8FC7\u671F\u65F6\u95F4",align:"center",prop:"expireTime",width:"180",formatter:t(z)},null,8,["formatter"]),e(r,{label:"\u7ED1\u5B9A\u57DF\u540D",align:"center",prop:"website",width:"180"}),e(r,{label:"\u79DF\u6237\u72B6\u6001",align:"center",prop:"status"},{default:o(l=>[e(G,{type:t(K).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:t(z)},null,8,["formatter"]),e(r,{label:"\u64CD\u4F5C",align:"center","min-width":"110",fixed:"right"},{default:o(l=>[g((s(),c(m,{link:"",type:"primary",onClick:_=>Y("update",l.row.id)},{default:o(()=>a[13]||(a[13]=[p(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[w,["system:tenant:update"]]]),g((s(),c(m,{link:"",type:"danger",onClick:_=>(async X=>{try{await k.delConfirm(),await Ve(X),k.success(F("common.delSuccess")),await f()}catch{}})(l.row.id)},{default:o(()=>a[14]||(a[14]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[w,["system:tenant:delete"]]])]),_:1})]),_:1},8,["data"])),[[W,t(h)]]),e(j,{total:t(N),page:t(n).pageNo,"onUpdate:page":a[6]||(a[6]=l=>t(n).pageNo=l),limit:t(n).pageSize,"onUpdate:limit":a[7]||(a[7]=l=>t(n).pageSize=l),onPagination:f},null,8,["total","page","limit"])]),_:1}),e(he,{ref_key:"formRef",ref:O,onSuccess:f},null,512)],64)}}});export{Ne as default};
