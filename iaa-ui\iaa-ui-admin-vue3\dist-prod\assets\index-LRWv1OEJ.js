import{d as p,c as s,o as e,g as r,F as n,y as i,aw as c,t as d,_ as h}from"./index-CRsFgzy0.js";import{E as f}from"./el-image-BQpHFDaE.js";const m={class:"relative h-full min-h-30px w-full"},u=p({name:"HotZone",__name:"index",props:{property:{}},setup:y=>(a,v)=>{const l=f;return e(),s("div",m,[r(l,{src:a.property.imgUrl,class:"pointer-events-none h-full w-full select-none"},null,8,["src"]),(e(!0),s(n,null,i(a.property.list,(t,o)=>(e(),s("div",{key:o,class:"hot-zone",style:c({width:`${t.width}px`,height:`${t.height}px`,top:`${t.top}px`,left:`${t.left}px`})},d(t.name),5))),128))])}}),x=h(u,[["__scopeId","data-v-4e777ca4"]]);export{x as default};
