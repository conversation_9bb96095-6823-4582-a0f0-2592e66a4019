import{d,c as e,o,i as s,aw as r,g as p,I as i,t as n,J as y,F as h,y as u,a6 as g,_ as b}from"./index-CRsFgzy0.js";const v={class:"right"},x=b(d({name:"SearchBar",__name:"index",props:{property:{}},setup:_=>(a,m)=>{const t=i;return o(),e("div",{class:"search-bar",style:r({color:a.property.textColor})},[s("div",{class:"inner",style:r({height:`${a.property.height}px`,background:a.property.backgroundColor,borderRadius:`${a.property.borderRadius}px`})},[s("div",{class:"placeholder",style:r({justifyContent:a.property.placeholderPosition})},[p(t,{icon:"ep:search"}),s("span",null,n(a.property.placeholder||"\u641C\u7D22\u5546\u54C1"),1)],4),s("div",v,[(o(!0),e(h,null,u(a.property.hotKeywords,(l,c)=>(o(),e("span",{key:c},n(l),1))),128)),y(p(t,{icon:"ant-design:scan-outlined"},null,512),[[g,a.property.showScan]])])],4)],4)}}),[["__scopeId","data-v-9476ac91"]]);export{x as default};
