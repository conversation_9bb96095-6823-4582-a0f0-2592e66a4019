import{d as G,p as J,b as L,r as m,f as M,q as O,O as Q,c as T,o as s,g as e,w as l,s as U,a as r,v as j,P as z,Q as B,J as u,G as D,H as n,I as W,A as f,K as E,L as N,M as X,F as Y}from"./index-CRsFgzy0.js";import{_ as Z}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as $}from"./index-DYfNUK1u.js";import{d as ee}from"./formatTime-DhdtkSIS.js";import{g as ae,d as le}from"./index-C5r8YBwc.js";import{_ as re}from"./ProductCategoryForm.vue_vue_type_script_setup_true_lang-C9hQw-ND.js";import{h as te}from"./tree-COGD3qag.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";const oe=G({name:"CrmProductCategory",__name:"index",setup(se){const w=J(),{t:I}=L(),y=m(!0),x=m([]),c=M({name:null}),C=m(),d=async()=>{y.value=!0;try{const p=await ae(c);x.value=te(p,"id","parentId")}finally{y.value=!1}},_=()=>{d()},K=()=>{C.value.resetFields(),_()},b=m(),h=(p,a)=>{b.value.open(p,a)};return O(()=>{d()}),(p,a)=>{const P=$,V=z,v=j,k=W,t=D,q=U,F=Z,i=N,H=E,g=Q("hasPermi"),R=X;return s(),T(Y,null,[e(P,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u7BA1\u7406\u3001\u4EA7\u54C1\u5206\u7C7B",url:"https://doc.iocoder.cn/crm/product/"}),e(F,null,{default:l(()=>[e(q,{class:"-mb-15px",model:r(c),ref_key:"queryFormRef",ref:C,inline:!0,"label-width":"68px"},{default:l(()=>[e(v,{label:"\u540D\u79F0",prop:"name"},{default:l(()=>[e(V,{modelValue:r(c).name,"onUpdate:modelValue":a[0]||(a[0]=o=>r(c).name=o),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:B(_,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(v,null,{default:l(()=>[e(t,{onClick:_},{default:l(()=>[e(k,{icon:"ep:search",class:"mr-5px"}),a[2]||(a[2]=n(" \u641C\u7D22"))]),_:1}),e(t,{onClick:K},{default:l(()=>[e(k,{icon:"ep:refresh",class:"mr-5px"}),a[3]||(a[3]=n(" \u91CD\u7F6E"))]),_:1}),u((s(),f(t,{type:"primary",plain:"",onClick:a[1]||(a[1]=o=>h("create"))},{default:l(()=>[e(k,{icon:"ep:plus",class:"mr-5px"}),a[4]||(a[4]=n(" \u65B0\u589E "))]),_:1})),[[g,["crm:product-category:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(F,null,{default:l(()=>[u((s(),f(H,{data:r(x),"row-key":"id","default-expand-all":""},{default:l(()=>[e(i,{label:"\u5206\u7C7B\u7F16\u53F7",align:"center",prop:"id"}),e(i,{label:"\u5206\u7C7B\u540D\u79F0",align:"center",prop:"name"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:r(ee),width:"180px"},null,8,["formatter"]),e(i,{label:"\u64CD\u4F5C",align:"center"},{default:l(o=>[u((s(),f(t,{link:"",type:"primary",onClick:S=>h("update",o.row.id)},{default:l(()=>a[5]||(a[5]=[n(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[g,["crm:product-category:update"]]]),u((s(),f(t,{link:"",type:"danger",onClick:S=>(async A=>{try{await w.delConfirm(),await le(A),w.success(I("common.delSuccess")),await d()}catch{}})(o.row.id)},{default:l(()=>a[6]||(a[6]=[n(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["crm:product-category:delete"]]])]),_:1})]),_:1},8,["data"])),[[R,r(y)]])]),_:1}),e(re,{ref_key:"formRef",ref:b,onSuccess:d},null,512)],64)}}});export{oe as default};
