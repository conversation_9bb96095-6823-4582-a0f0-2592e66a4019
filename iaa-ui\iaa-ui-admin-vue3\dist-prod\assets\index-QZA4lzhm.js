import{d as ae,p as te,b as oe,r as m,f as re,u as ne,q as pe,O as ie,c as O,o as c,g as e,w as o,s as de,a as t,v as me,x as ce,Q as f,F as B,y as se,A as w,B as ue,P as fe,J as _,G as we,H as d,I as be,l as he,m as _e,n as ye,K as ge,L as xe,a5 as ve,t as T,D as H,M as ke}from"./index-CRsFgzy0.js";import{_ as Ce}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as Ve}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as Ne}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Ue}from"./index-DYfNUK1u.js";import{d as k}from"./formatTime-DhdtkSIS.js";import{d as Se}from"./download-oWiM5xVU.js";import{j as Ie,k as Te,l as Ke}from"./index-CYXIDMF1.js";import{_ as Ae}from"./ContactForm.vue_vue_type_script_setup_true_lang-DUsfrLHp.js";import{g as Re}from"./index-BI-6YuuK.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-D4y5Z4cM.js";import"./index-DLC3Afbg.js";import"./tree-COGD3qag.js";const Ee=ae({name:"CrmContact",__name:"index",setup(Fe){const C=te(),{t:M}=oe(),V=m(!0),K=m(0),A=m([]),r=re({pageNo:1,pageSize:10,sceneType:"1",mobile:void 0,telephone:void 0,email:void 0,customerId:void 0,name:void 0,wechat:void 0}),R=m(),N=m(!1),U=m("1"),E=m([]),b=async()=>{V.value=!0;try{const p=await Ie(r);A.value=p.list,K.value=p.total}finally{V.value=!1}},i=()=>{r.pageNo=1,b()},j=()=>{R.value.resetFields(),i()},J=p=>{r.sceneType=p.paneName,i()},F=m(),L=(p,a)=>{F.value.open(p,a)},Q=async()=>{try{await C.exportConfirm(),N.value=!0;const p=await Ke(r);Se.excel(p,"\u8054\u7CFB\u4EBA.xls")}catch{}finally{N.value=!1}},{push:P}=ne(),q=p=>{P({name:"CrmContactDetail",params:{id:p}})};return pe(async()=>{await b(),E.value=await Re()}),(p,a)=>{const z=Ue,X=ue,Y=ce,s=me,h=fe,y=be,u=we,W=de,D=Ne,S=ye,Z=he,I=ve,n=xe,G=Ve,$=ge,ee=Ce,g=ie("hasPermi"),le=ke;return c(),O(B,null,[e(z,{title:"\u3010\u5BA2\u6237\u3011\u5BA2\u6237\u7BA1\u7406\u3001\u516C\u6D77\u5BA2\u6237",url:"https://doc.iocoder.cn/crm/customer/"}),e(z,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),e(D,null,{default:o(()=>[e(W,{ref_key:"queryFormRef",ref:R,inline:!0,model:t(r),class:"-mb-15px","label-width":"68px"},{default:o(()=>[e(s,{label:"\u5BA2\u6237",prop:"customerId"},{default:o(()=>[e(Y,{modelValue:t(r).customerId,"onUpdate:modelValue":a[0]||(a[0]=l=>t(r).customerId=l),class:"!w-240px",clearable:"","lable-key":"name",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237","value-key":"id",onKeyup:f(i,["enter"])},{default:o(()=>[(c(!0),O(B,null,se(t(E),l=>(c(),w(X,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"\u59D3\u540D",prop:"name"},{default:o(()=>[e(h,{modelValue:t(r).name,"onUpdate:modelValue":a[1]||(a[1]=l=>t(r).name=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u59D3\u540D",onKeyup:f(i,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"\u624B\u673A\u53F7",prop:"mobile"},{default:o(()=>[e(h,{modelValue:t(r).mobile,"onUpdate:modelValue":a[2]||(a[2]=l=>t(r).mobile=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7",onKeyup:f(i,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"\u7535\u8BDD",prop:"telephone"},{default:o(()=>[e(h,{modelValue:t(r).telephone,"onUpdate:modelValue":a[3]||(a[3]=l=>t(r).telephone=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7535\u8BDD",onKeyup:f(i,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"\u5FAE\u4FE1",prop:"wechat"},{default:o(()=>[e(h,{modelValue:t(r).wechat,"onUpdate:modelValue":a[4]||(a[4]=l=>t(r).wechat=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5FAE\u4FE1",onKeyup:f(i,["enter"])},null,8,["modelValue"])]),_:1}),e(s,{label:"\u7535\u5B50\u90AE\u7BB1",prop:"email"},{default:o(()=>[e(h,{modelValue:t(r).email,"onUpdate:modelValue":a[5]||(a[5]=l=>t(r).email=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7535\u5B50\u90AE\u7BB1",onKeyup:f(i,["enter"])},null,8,["modelValue"])]),_:1}),e(s,null,{default:o(()=>[e(u,{onClick:i},{default:o(()=>[e(y,{class:"mr-5px",icon:"ep:search"}),a[10]||(a[10]=d(" \u641C\u7D22 "))]),_:1}),e(u,{onClick:j},{default:o(()=>[e(y,{class:"mr-5px",icon:"ep:refresh"}),a[11]||(a[11]=d(" \u91CD\u7F6E "))]),_:1}),_((c(),w(u,{type:"primary",onClick:a[6]||(a[6]=l=>L("create"))},{default:o(()=>[e(y,{class:"mr-5px",icon:"ep:plus"}),a[12]||(a[12]=d(" \u65B0\u589E "))]),_:1})),[[g,["crm:contact:create"]]]),_((c(),w(u,{loading:t(N),plain:"",type:"success",onClick:Q},{default:o(()=>[e(y,{class:"mr-5px",icon:"ep:download"}),a[13]||(a[13]=d(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[g,["crm:contact:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(D,null,{default:o(()=>[e(Z,{modelValue:t(U),"onUpdate:modelValue":a[7]||(a[7]=l=>_e(U)?U.value=l:null),onTabClick:J},{default:o(()=>[e(S,{label:"\u6211\u8D1F\u8D23\u7684",name:"1"}),e(S,{label:"\u6211\u53C2\u4E0E\u7684",name:"2"}),e(S,{label:"\u4E0B\u5C5E\u8D1F\u8D23\u7684",name:"3"})]),_:1},8,["modelValue"]),_((c(),w($,{data:t(A),"show-overflow-tooltip":!0,stripe:!0},{default:o(()=>[e(n,{align:"center",fixed:"left",label:"\u8054\u7CFB\u4EBA\u59D3\u540D",prop:"name",width:"160"},{default:o(l=>[e(I,{underline:!1,type:"primary",onClick:x=>q(l.row.id)},{default:o(()=>[d(T(l.row.name),1)]),_:2},1032,["onClick"])]),_:1}),e(n,{align:"center",fixed:"left",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:o(l=>[e(I,{underline:!1,type:"primary",onClick:x=>{return v=l.row.customerId,void P({name:"CrmCustomerDetail",params:{id:v}});var v}},{default:o(()=>[d(T(l.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),e(n,{align:"center",label:"\u624B\u673A",prop:"mobile",width:"120"}),e(n,{align:"center",label:"\u7535\u8BDD",prop:"telephone",width:"130"}),e(n,{align:"center",label:"\u90AE\u7BB1",prop:"email",width:"180"}),e(n,{align:"center",label:"\u804C\u4F4D",prop:"post",width:"120"}),e(n,{align:"center",label:"\u5730\u5740",prop:"detailAddress",width:"120"}),e(n,{align:"center",label:"\u5173\u952E\u51B3\u7B56\u4EBA",prop:"master",width:"100"},{default:o(l=>[e(G,{type:t(H).INFRA_BOOLEAN_STRING,value:l.row.master},null,8,["type","value"])]),_:1}),e(n,{align:"center",label:"\u76F4\u5C5E\u4E0A\u7EA7",prop:"parentName",width:"160"},{default:o(l=>[e(I,{underline:!1,type:"primary",onClick:x=>q(l.row.parentId)},{default:o(()=>[d(T(l.row.parentName),1)]),_:2},1032,["onClick"])]),_:1}),e(n,{label:"\u5730\u5740",align:"center",prop:"detailAddress",width:"180"}),e(n,{formatter:t(k),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime",width:"180px"},null,8,["formatter"]),e(n,{align:"center",label:"\u6027\u522B",prop:"sex"},{default:o(l=>[e(G,{type:t(H).SYSTEM_USER_SEX,value:l.row.sex},null,8,["type","value"])]),_:1}),e(n,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),e(n,{formatter:t(k),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),e(n,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),e(n,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100"}),e(n,{formatter:t(k),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),e(n,{formatter:t(k),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(n,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"120"}),e(n,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"200"},{default:o(l=>[_((c(),w(u,{link:"",type:"primary",onClick:x=>L("update",l.row.id)},{default:o(()=>a[14]||(a[14]=[d(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[g,["crm:contact:update"]]]),_((c(),w(u,{link:"",type:"danger",onClick:x=>(async v=>{try{await C.delConfirm(),await Te(v),C.success(M("common.delSuccess")),await b()}catch{}})(l.row.id)},{default:o(()=>a[15]||(a[15]=[d(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[g,["crm:contact:delete"]]])]),_:1})]),_:1},8,["data"])),[[le,t(V)]]),e(ee,{limit:t(r).pageSize,"onUpdate:limit":a[8]||(a[8]=l=>t(r).pageSize=l),page:t(r).pageNo,"onUpdate:page":a[9]||(a[9]=l=>t(r).pageNo=l),total:t(K),onPagination:b},null,8,["limit","page","total"])]),_:1}),e(Ae,{ref_key:"formRef",ref:F,onSuccess:b},null,512)],64)}}});export{Ee as default};
