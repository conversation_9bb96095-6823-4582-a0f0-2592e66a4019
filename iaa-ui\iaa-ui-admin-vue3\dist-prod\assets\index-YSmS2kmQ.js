import{d as j,p as q,r as m,eW as z,a2 as g,eX as A,bj as B,q as F,c as h,o as c,g as t,i as l,w as r,aE as G,a as s,H as y,t as x,P as H,m as b,G as X,x as K,B as L,F as k,y as $,A as M,k as Q}from"./index-CRsFgzy0.js";import{_ as Y}from"./index-DYfNUK1u.js";import{f as Z}from"./formatTime-DhdtkSIS.js";import{g as ee}from"./index-D4y5Z4cM.js";const le={class:"flex"},ae={class:"flex items-center"},se={class:"flex"},te={class:"max-h-80 overflow-auto"},re={class:"flex items-center"},ue=j({name:"InfraWebSocket",__name:"index",setup(oe){const w=q(),p=m("http://shouhou.iaa360.com/infra/ws".replace("http","ws")+"?token="+z()),o=g(()=>_.value==="OPEN"),D=g(()=>o.value?"success":"red"),{status:_,data:n,send:I,close:J,open:R}=A(p.value,{autoReconnect:!0,heartbeat:!0}),v=m([]),T=g(()=>v.value.slice().reverse());B(()=>{if(n.value)try{if(n.value==="pong")return;const d=JSON.parse(n.value),e=d.type,u=JSON.parse(d.content);if(!e)return void w.error("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B\uFF1A"+n.value);if(e==="demo-message-receive")return void(u.single?v.value.push({text:`\u3010\u5355\u53D1\u3011\u7528\u6237\u7F16\u53F7(${u.fromUserId})\uFF1A${u.text}`,time:new Date().getTime()}):v.value.push({text:`\u3010\u7FA4\u53D1\u3011\u7528\u6237\u7F16\u53F7(${u.fromUserId})\uFF1A${u.text}`,time:new Date().getTime()}));if(e==="notice-push")return void v.value.push({text:`\u3010\u7CFB\u7EDF\u901A\u77E5\u3011\uFF1A${u.title}`,time:new Date().getTime()});w.error("\u672A\u5904\u7406\u6D88\u606F\uFF1A"+n.value)}catch{w.error("\u5904\u7406\u6D88\u606F\u53D1\u751F\u5F02\u5E38\uFF1A"+n.value)}});const i=m(""),f=m(""),W=()=>{const d=JSON.stringify({text:i.value,toUserId:f.value}),e=JSON.stringify({type:"demo-message-send",content:d});I(e),i.value=""},C=()=>{o.value?J():R()},V=m([]);return F(async()=>{V.value=await ee()}),(d,e)=>{const u=Y,E=G,N=H,S=X,U=L,P=K,O=Q;return c(),h(k,null,[t(u,{title:"WebSocket \u5B9E\u65F6\u901A\u4FE1",url:"https://doc.iocoder.cn/websocket/"}),l("div",le,[t(O,{gutter:12,class:"w-1/2",shadow:"always"},{header:r(()=>e[3]||(e[3]=[l("div",{class:"card-header"},[l("span",null,"\u8FDE\u63A5")],-1)])),default:r(()=>[l("div",ae,[e[4]||(e[4]=l("span",{class:"mr-4 text-lg font-medium"}," \u8FDE\u63A5\u72B6\u6001: ",-1)),t(E,{color:s(D)},{default:r(()=>[y(x(s(_)),1)]),_:1},8,["color"])]),e[7]||(e[7]=l("hr",{class:"my-4"},null,-1)),l("div",se,[t(N,{modelValue:s(p),"onUpdate:modelValue":e[0]||(e[0]=a=>b(p)?p.value=a:null),disabled:""},{prepend:r(()=>e[5]||(e[5]=[y("\u670D\u52A1\u5730\u5740")])),_:1},8,["modelValue"]),t(S,{type:s(o)?"danger":"primary",onClick:C},{default:r(()=>[y(x(s(o)?"\u5173\u95ED\u8FDE\u63A5":"\u5F00\u542F\u8FDE\u63A5"),1)]),_:1},8,["type"])]),e[8]||(e[8]=l("p",{class:"mt-4 text-lg font-medium"},"\u6D88\u606F\u8F93\u5165\u6846",-1)),e[9]||(e[9]=l("hr",{class:"my-4"},null,-1)),t(N,{modelValue:s(i),"onUpdate:modelValue":e[1]||(e[1]=a=>b(i)?i.value=a:null),autosize:{minRows:2,maxRows:4},disabled:!s(o),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4F60\u8981\u53D1\u9001\u7684\u6D88\u606F",type:"textarea"},null,8,["modelValue","disabled"]),t(P,{modelValue:s(f),"onUpdate:modelValue":e[2]||(e[2]=a=>b(f)?f.value=a:null),class:"mt-4",placeholder:"\u8BF7\u9009\u62E9\u53D1\u9001\u4EBA"},{default:r(()=>[t(U,{key:"",label:"\u6240\u6709\u4EBA",value:""}),(c(!0),h(k,null,$(s(V),a=>(c(),M(U,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t(S,{disabled:!s(o),block:"",class:"ml-2 mt-4",type:"primary",onClick:W},{default:r(()=>e[6]||(e[6]=[y(" \u53D1\u9001 ")])),_:1},8,["disabled"])]),_:1}),t(O,{gutter:12,class:"w-1/2",shadow:"always"},{header:r(()=>e[10]||(e[10]=[l("div",{class:"card-header"},[l("span",null,"\u6D88\u606F\u8BB0\u5F55")],-1)])),default:r(()=>[l("div",te,[l("ul",null,[(c(!0),h(k,null,$(s(T),a=>(c(),h("li",{key:a.time,class:"mt-2"},[l("div",re,[e[11]||(e[11]=l("span",{class:"text-primary mr-2 font-medium"},"\u6536\u5230\u6D88\u606F:",-1)),l("span",null,x(s(Z)(a.time)),1)]),l("div",null,x(a.text),1)]))),128))])])]),_:1})])],64)}}});export{ue as default};
