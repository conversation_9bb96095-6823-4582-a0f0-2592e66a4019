import{d as le,p as ae,b as te,r as u,f as oe,q as re,O as se,c as g,o as s,g as a,w as o,s as ie,a as t,v as de,P as pe,Q as L,x as ue,F as h,y as S,A as d,B as ne,C as ce,R as me,D as q,J as m,G as fe,H as n,I as ve,K as ke,L as be,eQ as _e,e9 as we,M as ye}from"./index-CRsFgzy0.js";import{_ as ge}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as he}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as xe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Ve}from"./index-DYfNUK1u.js";import{b as Ce}from"./formatTime-DhdtkSIS.js";import{d as Se}from"./download-oWiM5xVU.js";import{_ as Ue,S as U}from"./StockMoveForm.vue_vue_type_script_setup_true_lang-BZKHjifc.js";import{P as Ie}from"./index-v67yau6-.js";import{W as Te}from"./index-mM5XVEAg.js";import{g as Pe}from"./index-D4y5Z4cM.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./StockMoveItemForm.vue_vue_type_script_setup_true_lang-BnTRdV7k.js";import"./index-CxlZ4TTH.js";const Me=le({name:"ErpStockMove",__name:"index",setup(De){const _=ae(),{t:F}=te(),I=u(!0),M=u([]),D=u(0),r=oe({pageNo:1,pageSize:10,no:void 0,productId:void 0,fromWarehouseId:void 0,moveTime:[],status:void 0,remark:void 0,creator:void 0}),N=u(),T=u(!1),R=u([]),W=u([]),A=u([]),k=async()=>{I.value=!0;try{const i=await U.getStockMovePage(r);M.value=i.list,D.value=i.total}finally{I.value=!1}},x=()=>{r.pageNo=1,k()},Q=()=>{N.value.resetFields(),x()},K=u(),P=(i,e)=>{K.value.open(i,e)},Y=async i=>{try{await _.delConfirm(),await U.deleteStockMove(i),_.success(F("common.delSuccess")),await k(),w.value=w.value.filter(e=>!i.includes(e.id))}catch{}},z=async(i,e)=>{try{await _.confirm(`\u786E\u5B9A${e===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u8C03\u5EA6\u5355\u5417\uFF1F`),await U.updateStockMoveStatus(i,e),_.success((e===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await k()}catch{}},B=async()=>{try{await _.exportConfirm(),T.value=!0;const i=await U.exportStockMove(r);Se.excel(i,"\u5E93\u5B58\u8C03\u5EA6\u5355.xls")}catch{}finally{T.value=!1}},w=u([]),G=i=>{w.value=i};return re(async()=>{await k(),R.value=await Ie.getProductSimpleList(),W.value=await Te.getWarehouseSimpleList(),A.value=await Pe()}),(i,e)=>{const J=Ve,E=pe,f=de,V=ne,C=ue,O=ce,y=ve,p=fe,$=ie,H=xe,c=be,j=he,X=ke,Z=ge,v=se("hasPermi"),ee=ye;return s(),g(h,null,[a(J,{title:"\u3010\u5E93\u5B58\u3011\u5E93\u5B58\u8C03\u62E8\u3001\u5E93\u5B58\u76D8\u70B9",url:"https://doc.iocoder.cn/erp/stock-move-check/"}),a(H,null,{default:o(()=>[a($,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:o(()=>[a(f,{label:"\u8C03\u5EA6\u5355\u53F7",prop:"no"},{default:o(()=>[a(E,{modelValue:t(r).no,"onUpdate:modelValue":e[0]||(e[0]=l=>t(r).no=l),placeholder:"\u8BF7\u8F93\u5165\u8C03\u5EA6\u5355\u53F7",clearable:"",onKeyup:L(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(f,{label:"\u4EA7\u54C1",prop:"productId"},{default:o(()=>[a(C,{modelValue:t(r).productId,"onUpdate:modelValue":e[1]||(e[1]=l=>t(r).productId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:o(()=>[(s(!0),g(h,null,S(t(R),l=>(s(),d(V,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(f,{label:"\u8C03\u5EA6\u65F6\u95F4",prop:"moveTime"},{default:o(()=>[a(O,{modelValue:t(r).moveTime,"onUpdate:modelValue":e[2]||(e[2]=l=>t(r).moveTime=l),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(f,{label:"\u4ED3\u5E93",prop:"fromWarehouseId"},{default:o(()=>[a(C,{modelValue:t(r).fromWarehouseId,"onUpdate:modelValue":e[3]||(e[3]=l=>t(r).fromWarehouseId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:o(()=>[(s(!0),g(h,null,S(t(W),l=>(s(),d(V,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(f,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:o(()=>[a(C,{modelValue:t(r).creator,"onUpdate:modelValue":e[4]||(e[4]=l=>t(r).creator=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:o(()=>[(s(!0),g(h,null,S(t(A),l=>(s(),d(V,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(f,{label:"\u72B6\u6001",prop:"status"},{default:o(()=>[a(C,{modelValue:t(r).status,"onUpdate:modelValue":e[5]||(e[5]=l=>t(r).status=l),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(s(!0),g(h,null,S(t(me)(t(q).ERP_AUDIT_STATUS),l=>(s(),d(V,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(f,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[a(E,{modelValue:t(r).remark,"onUpdate:modelValue":e[6]||(e[6]=l=>t(r).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:L(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(f,null,{default:o(()=>[a(p,{onClick:x},{default:o(()=>[a(y,{icon:"ep:search",class:"mr-5px"}),e[11]||(e[11]=n(" \u641C\u7D22"))]),_:1}),a(p,{onClick:Q},{default:o(()=>[a(y,{icon:"ep:refresh",class:"mr-5px"}),e[12]||(e[12]=n(" \u91CD\u7F6E"))]),_:1}),m((s(),d(p,{type:"primary",plain:"",onClick:e[7]||(e[7]=l=>P("create"))},{default:o(()=>[a(y,{icon:"ep:plus",class:"mr-5px"}),e[13]||(e[13]=n(" \u65B0\u589E "))]),_:1})),[[v,["erp:stock-move:create"]]]),m((s(),d(p,{type:"success",plain:"",onClick:B,loading:t(T)},{default:o(()=>[a(y,{icon:"ep:download",class:"mr-5px"}),e[14]||(e[14]=n(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[v,["erp:stock-move:export"]]]),m((s(),d(p,{type:"danger",plain:"",onClick:e[8]||(e[8]=l=>Y(t(w).map(b=>b.id))),disabled:t(w).length===0},{default:o(()=>[a(y,{icon:"ep:delete",class:"mr-5px"}),e[15]||(e[15]=n(" \u5220\u9664 "))]),_:1},8,["disabled"])),[[v,["erp:stock-move:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(H,null,{default:o(()=>[m((s(),d(X,{data:t(M),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:G},{default:o(()=>[a(c,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(c,{"min-width":"180",label:"\u8C03\u5EA6\u5355\u53F7",align:"center",prop:"no"}),a(c,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(c,{label:"\u8C03\u5EA6\u65F6\u95F4",align:"center",prop:"moveTime",formatter:t(Ce),width:"120px"},null,8,["formatter"]),a(c,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(c,{label:"\u6570\u91CF",align:"center",prop:"totalCount",formatter:t(_e)},null,8,["formatter"]),a(c,{label:"\u91D1\u989D",align:"center",prop:"totalPrice",formatter:t(we)},null,8,["formatter"]),a(c,{label:"\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:o(l=>[a(j,{type:t(q).ERP_AUDIT_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),a(c,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:o(l=>[m((s(),d(p,{link:"",onClick:b=>P("detail",l.row.id)},{default:o(()=>e[16]||(e[16]=[n(" \u8BE6\u60C5 ")])),_:2},1032,["onClick"])),[[v,["erp:stock-move:query"]]]),m((s(),d(p,{link:"",type:"primary",onClick:b=>P("update",l.row.id),disabled:l.row.status===20},{default:o(()=>e[17]||(e[17]=[n(" \u7F16\u8F91 ")])),_:2},1032,["onClick","disabled"])),[[v,["erp:stock-move:update"]]]),l.row.status===10?m((s(),d(p,{key:0,link:"",type:"primary",onClick:b=>z(l.row.id,20)},{default:o(()=>e[18]||(e[18]=[n(" \u5BA1\u6279 ")])),_:2},1032,["onClick"])),[[v,["erp:stock-move:update-status"]]]):m((s(),d(p,{key:1,link:"",type:"danger",onClick:b=>z(l.row.id,10)},{default:o(()=>e[19]||(e[19]=[n(" \u53CD\u5BA1\u6279 ")])),_:2},1032,["onClick"])),[[v,["erp:stock-move:update-status"]]]),m((s(),d(p,{link:"",type:"danger",onClick:b=>Y([l.row.id])},{default:o(()=>e[20]||(e[20]=[n(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[v,["erp:stock-move:delete"]]])]),_:1})]),_:1},8,["data"])),[[ee,t(I)]]),a(Z,{total:t(D),page:t(r).pageNo,"onUpdate:page":e[9]||(e[9]=l=>t(r).pageNo=l),limit:t(r).pageSize,"onUpdate:limit":e[10]||(e[10]=l=>t(r).pageSize=l),onPagination:k},null,8,["total","page","limit"])]),_:1}),a(Ue,{ref_key:"formRef",ref:K,onSuccess:k},null,512)],64)}}});export{Me as default};
