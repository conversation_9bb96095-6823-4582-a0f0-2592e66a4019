import{d as Q,r as p,q as W,O as X,c as U,o as m,g as e,w as l,h as Z,i as r,I as ee,a as t,E as ae,s as le,v as te,C as se,x as re,F as z,y as ie,R as oe,D as M,A as k,B as ne,G as pe,H as x,J as V,K as ue,L as de,t as E,M as ce,aV as me}from"./index-CRsFgzy0.js";import{_ as fe}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as xe}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{E as ve}from"./el-image-BQpHFDaE.js";import{E as ge}from"./el-avatar-Nl9DW69B.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as be}from"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";import{_ as he}from"./index-DYfNUK1u.js";import{_ as we,g as ye,a as Ce}from"./CombinationRecordListDialog.vue_vue_type_script_setup_true_lang-B85oKb88.js";import{h as ke,d as T}from"./formatTime-DhdtkSIS.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";const Te={class:"flex items-center"},Oe={class:"h-[50px] w-[50px] flex items-center justify-center",style:{color:"rgb(24 144 255)","background-color":"rgb(24 144 255 / 10%)"}},Ie={class:"ml-[20px]"},Ne={class:"flex items-center"},Re={class:"h-[50px] w-[50px] flex items-center justify-center",style:{color:"rgb(162 119 255)","background-color":"rgb(162 119 255 / 10%)"}},Se={class:"ml-[20px]"},Ue={class:"flex items-center"},ze={class:"h-[50px] w-[50px] flex items-center justify-center",style:{color:"rgb(162 119 255)","background-color":"rgb(162 119 255 / 10%)"}},Me={class:"ml-[20px]"},Ve={class:"align-middle"},Ee=Q({name:"PromotionCombinationRecord",__name:"index",setup(Ae){const o=p({status:void 0,createTime:void 0,pageSize:10,pageNo:1}),O=p(),I=p(),v=p(!0),N=p(0),g=p([]),_=async()=>{v.value=!0;try{const b=await ye(o.value);g.value=b.list,N.value=b.total}finally{v.value=!1}},f=p({successCount:0,userCount:0,virtualGroupCount:0}),R=()=>{o.value.pageNo=1,_()},A=()=>{O.value.resetFields(),R()};return W(async()=>{await(async()=>{f.value=await Ce()})(),await _()}),(b,s)=>{const D=he,d=ee,h=be,c=_e,w=Z,P=ae,j=se,y=te,H=ne,Y=re,C=pe,q=le,i=de,B=ge,F=ve,G=xe,L=ue,J=fe,K=X("hasPermi"),$=ce;return m(),U(z,null,[e(D,{title:"\u3010\u8425\u9500\u3011\u62FC\u56E2\u6D3B\u52A8",url:"https://doc.iocoder.cn/mall/promotion-combination/"}),e(P,{gutter:12},{default:l(()=>[e(w,{span:6},{default:l(()=>[e(c,{class:"h-[110px] pb-0!"},{default:l(()=>[r("div",Te,[r("div",Oe,[e(d,{size:23,icon:"fa:user-times"})]),r("div",Ie,[s[4]||(s[4]=r("div",{class:"mb-8px text-14px text-gray-400"},"\u53C2\u4E0E\u4EBA\u6570(\u4E2A)",-1)),e(h,{duration:2600,"end-val":t(f).userCount,"start-val":0,class:"text-20px"},null,8,["end-val"])])])]),_:1})]),_:1}),e(w,{span:6},{default:l(()=>[e(c,{class:"h-[110px]"},{default:l(()=>[r("div",Ne,[r("div",Re,[e(d,{size:23,icon:"fa:user-plus"})]),r("div",Se,[s[5]||(s[5]=r("div",{class:"mb-8px text-14px text-gray-400"},"\u6210\u56E2\u6570\u91CF(\u4E2A)",-1)),e(h,{duration:2600,"end-val":t(f).successCount,"start-val":0,class:"text-20px"},null,8,["end-val"])])])]),_:1})]),_:1}),e(w,{span:6},{default:l(()=>[e(c,{class:"h-[110px]"},{default:l(()=>[r("div",Ue,[r("div",ze,[e(d,{size:23,icon:"fa:user-plus"})]),r("div",Me,[s[6]||(s[6]=r("div",{class:"mb-8px text-14px text-gray-400"},"\u865A\u62DF\u6210\u56E2(\u4E2A)",-1)),e(h,{duration:2600,"end-val":t(f).virtualGroupCount,"start-val":0,class:"text-20px"},null,8,["end-val"])])])]),_:1})]),_:1})]),_:1}),e(c,null,{default:l(()=>[e(q,{ref_key:"queryFormRef",ref:O,inline:!0,model:t(o),class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(y,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[e(j,{modelValue:t(o).createTime,"onUpdate:modelValue":s[0]||(s[0]=a=>t(o).createTime=a),shortcuts:t(ke),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","shortcuts"])]),_:1}),e(y,{label:"\u62FC\u56E2\u72B6\u6001",prop:"status"},{default:l(()=>[e(Y,{modelValue:t(o).status,"onUpdate:modelValue":s[1]||(s[1]=a=>t(o).status=a),class:"!w-240px",clearable:"",placeholder:"\u5168\u90E8"},{default:l(()=>[(m(!0),U(z,null,ie(t(oe)(t(M).PROMOTION_COMBINATION_RECORD_STATUS),(a,u)=>(m(),k(H,{key:u,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(y,null,{default:l(()=>[e(C,{onClick:R},{default:l(()=>[e(d,{class:"mr-5px",icon:"ep:search"}),s[7]||(s[7]=x(" \u641C\u7D22 "))]),_:1}),e(C,{onClick:A},{default:l(()=>[e(d,{class:"mr-5px",icon:"ep:refresh"}),s[8]||(s[8]=x(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(c,null,{default:l(()=>[V((m(),k(L,{data:t(g)},{default:l(()=>[e(i,{align:"center",label:"\u7F16\u53F7",prop:"id","min-width":"50"}),e(i,{align:"center",label:"\u5934\u50CF",prop:"avatar","min-width":"80"},{default:l(a=>[e(B,{src:a.row.avatar},null,8,["src"])]),_:1}),e(i,{align:"center",label:"\u6635\u79F0",prop:"nickname","min-width":"100"}),e(i,{align:"center",label:"\u5F00\u56E2\u56E2\u957F",prop:"headId","min-width":"100"},{default:l(({row:a})=>{var u;return[x(E(a.headId?(u=t(g).find(n=>n.id===a.headId))==null?void 0:u.nickname:a.nickname),1)]}),_:1}),e(i,{formatter:t(T),align:"center",label:"\u5F00\u56E2\u65F6\u95F4",prop:"startTime",width:"180"},null,8,["formatter"]),e(i,{align:"center",label:"\u62FC\u56E2\u5546\u54C1",prop:"type","show-overflow-tooltip":"","min-width":"300"},{defaul:l(({row:a})=>[e(F,{src:a.picUrl,class:"mr-5px h-30px w-30px align-middle",onClick:u=>{return n=a.picUrl,void me({urlList:[n]});var n}},null,8,["src","onClick"]),r("span",Ve,E(a.spuName),1)]),_:1}),e(i,{align:"center",label:"\u51E0\u4EBA\u56E2",prop:"userSize","min-width":"100"}),e(i,{align:"center",label:"\u53C2\u4E0E\u4EBA\u6570",prop:"userCount","min-width":"100"}),e(i,{formatter:t(T),align:"center",label:"\u53C2\u56E2\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(i,{formatter:t(T),align:"center",label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),e(i,{align:"center",label:"\u62FC\u56E2\u72B6\u6001",prop:"status","min-width":"150"},{default:l(a=>[e(G,{type:t(M).PROMOTION_COMBINATION_RECORD_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(i,{align:"center",fixed:"right",label:"\u64CD\u4F5C"},{default:l(a=>[V((m(),k(C,{link:"",type:"primary",onClick:u=>{var S;return n=a.row,void((S=I.value)==null?void 0:S.open(n.headId||n.id));var n}},{default:l(()=>s[9]||(s[9]=[x(" \u67E5\u770B\u62FC\u56E2 ")])),_:2},1032,["onClick"])),[[K,["promotion:combination-record:query"]]])]),_:1})]),_:1},8,["data"])),[[$,t(v)]]),e(J,{limit:t(o).pageSize,"onUpdate:limit":s[2]||(s[2]=a=>t(o).pageSize=a),page:t(o).pageNo,"onUpdate:page":s[3]||(s[3]=a=>t(o).pageNo=a),total:t(N),onPagination:_},null,8,["limit","page","total"])]),_:1}),e(we,{ref_key:"combinationRecordListRef",ref:I},null,512)],64)}}});export{Ee as default};
