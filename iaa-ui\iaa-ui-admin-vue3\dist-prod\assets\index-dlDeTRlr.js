import{d as x,c as l,o as e,F as n,y as f,i as t,A as m,a3 as u,aw as a,t as r,g as d,I as y,_ as w}from"./index-CRsFgzy0.js";import{E as g}from"./el-image-BQpHFDaE.js";const v={class:"min-h-42px flex flex-col"},_={class:"flex flex-1 flex-row items-center gap-8px"},b={class:"item-center flex flex-row justify-center gap-4px"},h=x({name:"MenuList",__name:"index",props:{property:{}},setup:k=>(o,C)=>{const p=g,i=y;return e(),l("div",v,[(e(!0),l(n,null,f(o.property.list,(s,c)=>(e(),l("div",{key:c,class:"item h-42px flex flex-row items-center justify-between gap-4px p-x-12px"},[t("div",_,[s.iconUrl?(e(),m(p,{key:0,class:"h-16px w-16px",src:s.iconUrl},null,8,["src"])):u("",!0),t("span",{class:"text-16px",style:a({color:s.titleColor})},r(s.title),5)]),t("div",b,[t("span",{class:"text-12px",style:a({color:s.subtitleColor})},r(s.subtitle),5),d(i,{icon:"ep-arrow-right",color:"#000",size:16})])]))),128))])}}),j=w(h,[["__scopeId","data-v-7b86333e"]]);export{j as default};
