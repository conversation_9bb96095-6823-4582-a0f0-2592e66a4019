import{d as O,p as Q,b as R,r as s,f as j,q as D,O as T,c as b,o as p,g as e,w as o,s as X,a as t,v as Y,x as Z,F as w,y as z,A as n,B as $,J as x,G as ee,H as m,I as ae,K as le,L as te,eQ as oe,M as re}from"./index-CRsFgzy0.js";import{_ as se}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as pe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as ie}from"./index-DYfNUK1u.js";import{d as ue}from"./download-oWiM5xVU.js";import{S as F}from"./index-CxlZ4TTH.js";import{P as ne}from"./index-v67yau6-.js";import{W as de}from"./index-mM5XVEAg.js";import"./index-CqPfoRkb.js";const ce=O({name:"ErpStock",__name:"index",setup(me){const W=Q(),{t:fe}=R(),f=s(!0),y=s([]),h=s(0),r=j({pageNo:1,pageSize:10,productId:void 0,warehouseId:void 0}),k=s(),g=s(!1),I=s([]),S=s([]),v=async()=>{f.value=!0;try{const i=await F.getStockPage(r);y.value=i.list,h.value=i.total}finally{f.value=!1}},N=()=>{r.pageNo=1,v()},q=()=>{k.value.resetFields(),N()},A=s(),B=async()=>{try{await W.exportConfirm(),g.value=!0;const i=await F.exportStock(r);ue.excel(i,"\u4EA7\u54C1\u5E93\u5B58.xls")}catch{}finally{g.value=!1}};return D(async()=>{await v(),I.value=await ne.getProductSimpleList(),S.value=await de.getWarehouseSimpleList()}),(i,a)=>{const E=ie,P=$,V=Z,_=Y,d=ae,c=ee,G=X,C=pe,u=te,H=le,J=se,L=T("hasPermi"),K=re;return p(),b(w,null,[e(E,{title:"\u3010\u5E93\u5B58\u3011\u4EA7\u54C1\u5E93\u5B58\u3001\u5E93\u5B58\u660E\u7EC6",url:"https://doc.iocoder.cn/erp/stock/"}),e(C,null,{default:o(()=>[e(G,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:o(()=>[e(_,{label:"\u4EA7\u54C1",prop:"productId"},{default:o(()=>[e(V,{modelValue:t(r).productId,"onUpdate:modelValue":a[0]||(a[0]=l=>t(r).productId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:o(()=>[(p(!0),b(w,null,z(t(I),l=>(p(),n(P,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:o(()=>[e(V,{modelValue:t(r).warehouseId,"onUpdate:modelValue":a[1]||(a[1]=l=>t(r).warehouseId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:o(()=>[(p(!0),b(w,null,z(t(S),l=>(p(),n(P,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,null,{default:o(()=>[e(c,{onClick:N},{default:o(()=>[e(d,{icon:"ep:search",class:"mr-5px"}),a[5]||(a[5]=m(" \u641C\u7D22"))]),_:1}),e(c,{onClick:q},{default:o(()=>[e(d,{icon:"ep:refresh",class:"mr-5px"}),a[6]||(a[6]=m(" \u91CD\u7F6E"))]),_:1}),x((p(),n(c,{type:"primary",plain:"",onClick:a[2]||(a[2]=l=>{return U="create",void A.value.open(U,M);var U,M})},{default:o(()=>[e(d,{icon:"ep:plus",class:"mr-5px"}),a[7]||(a[7]=m(" \u65B0\u589E "))]),_:1})),[[L,["erp:stock:create"]]]),x((p(),n(c,{type:"success",plain:"",onClick:B,loading:t(g)},{default:o(()=>[e(d,{icon:"ep:download",class:"mr-5px"}),a[8]||(a[8]=m(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[L,["erp:stock:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(C,null,{default:o(()=>[x((p(),n(H,{data:t(y),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[e(u,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName"}),e(u,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unitName"}),e(u,{label:"\u4EA7\u54C1\u5206\u7C7B",align:"center",prop:"categoryName"}),e(u,{label:"\u5E93\u5B58\u91CF",align:"center",prop:"count",formatter:t(oe)},null,8,["formatter"]),e(u,{label:"\u4ED3\u5E93",align:"center",prop:"warehouseName"})]),_:1},8,["data"])),[[K,t(f)]]),e(J,{total:t(h),page:t(r).pageNo,"onUpdate:page":a[3]||(a[3]=l=>t(r).pageNo=l),limit:t(r).pageSize,"onUpdate:limit":a[4]||(a[4]=l=>t(r).pageSize=l),onPagination:v},null,8,["total","page","limit"])]),_:1})],64)}}});export{ce as default};
