import{as as i}from"./index-CRsFgzy0.js";const o={getPointActivityPage:async t=>await i.get({url:"/promotion/point-activity/page",params:t}),getPointActivity:async t=>await i.get({url:"/promotion/point-activity/get?id="+t}),getPointActivityListByIds:async t=>i.get({url:`/promotion/point-activity/list-by-ids?ids=${t}`}),createPointActivity:async t=>await i.post({url:"/promotion/point-activity/create",data:t}),updatePointActivity:async t=>await i.put({url:"/promotion/point-activity/update",data:t}),deletePointActivity:async t=>await i.delete({url:"/promotion/point-activity/delete?id="+t}),closePointActivity:async t=>await i.put({url:"/promotion/point-activity/close?id="+t})};export{o as P};
