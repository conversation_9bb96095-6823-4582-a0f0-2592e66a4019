import{d as L,p as Q,b as j,r as m,f as W,q as X,O as Y,c as S,o as n,g as e,w as t,s as Z,a as s,v as $,P as ee,Q as ae,x as le,F as x,y as A,R as te,D as M,A as p,B as se,J as _,G as oe,H as i,I as re,K as ne,L as ie,i as pe,t as v,aE as ue,M as de}from"./index-CvERnF9Y.js";import{_ as ce}from"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import{_ as me}from"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import{_ as fe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as _e}from"./index-CeUx6j9a.js";import{d as ye}from"./formatTime-CmW2_KRq.js";import{_ as ge,g as he,d as be}from"./ClientForm.vue_vue_type_script_setup_true_lang-Wln9nNET.js";import"./index-DHM6tdge.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./constants-uird_4gU.js";const ke=["src"],we=L({name:"SystemOAuth2Client",__name:"index",setup(Se){const C=Q(),{t:F}=j(),y=m(!0),T=m(0),V=m([]),o=W({pageNo:1,pageSize:10,name:null,status:void 0}),O=m(),u=async()=>{y.value=!0;try{const d=await he(o);V.value=d.list,T.value=d.total}finally{y.value=!1}},g=()=>{o.pageNo=1,u()},G=()=>{O.value.resetFields(),g()},N=m(),U=(d,l)=>{N.value.open(d,l)};return X(()=>{u()}),(d,l)=>{const I=_e,P=ee,h=$,R=se,q=le,b=re,c=oe,H=Z,z=fe,r=ie,K=me,B=ue,D=ne,E=ce,k=Y("hasPermi"),J=de;return n(),S(x,null,[e(I,{title:"OAuth 2.0\uFF08SSO \u5355\u70B9\u767B\u5F55)",url:"https://doc.iocoder.cn/oauth2/"}),e(z,null,{default:t(()=>[e(H,{class:"-mb-15px",model:s(o),ref_key:"queryFormRef",ref:O,inline:!0,"label-width":"68px"},{default:t(()=>[e(h,{label:"\u5E94\u7528\u540D",prop:"name"},{default:t(()=>[e(P,{modelValue:s(o).name,"onUpdate:modelValue":l[0]||(l[0]=a=>s(o).name=a),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D",clearable:"",onKeyup:ae(g,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(h,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[e(q,{modelValue:s(o).status,"onUpdate:modelValue":l[1]||(l[1]=a=>s(o).status=a),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(n(!0),S(x,null,A(s(te)(s(M).COMMON_STATUS),a=>(n(),p(R,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:t(()=>[e(c,{onClick:g},{default:t(()=>[e(b,{icon:"ep:search",class:"mr-5px"}),l[5]||(l[5]=i(" \u641C\u7D22"))]),_:1}),e(c,{onClick:G},{default:t(()=>[e(b,{icon:"ep:refresh",class:"mr-5px"}),l[6]||(l[6]=i(" \u91CD\u7F6E"))]),_:1}),_((n(),p(c,{plain:"",type:"primary",onClick:l[2]||(l[2]=a=>U("create"))},{default:t(()=>[e(b,{icon:"ep:plus",class:"mr-5px"}),l[7]||(l[7]=i(" \u65B0\u589E "))]),_:1})),[[k,["system:oauth2-client:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(z,null,{default:t(()=>[_((n(),p(D,{data:s(V)},{default:t(()=>[e(r,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",align:"center",prop:"clientId"}),e(r,{label:"\u5BA2\u6237\u7AEF\u5BC6\u94A5",align:"center",prop:"secret"}),e(r,{label:"\u5E94\u7528\u540D",align:"center",prop:"name"}),e(r,{label:"\u5E94\u7528\u56FE\u6807",align:"center",prop:"logo"},{default:t(a=>[pe("img",{width:"40px",height:"40px",src:a.row.logo},null,8,ke)]),_:1}),e(r,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:t(a=>[e(K,{type:s(M).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(r,{label:"\u8BBF\u95EE\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"accessTokenValiditySeconds"},{default:t(a=>[i(v(a.row.accessTokenValiditySeconds)+" \u79D2",1)]),_:1}),e(r,{label:"\u5237\u65B0\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"refreshTokenValiditySeconds"},{default:t(a=>[i(v(a.row.refreshTokenValiditySeconds)+" \u79D2",1)]),_:1}),e(r,{label:"\u6388\u6743\u7C7B\u578B",align:"center",prop:"authorizedGrantTypes"},{default:t(a=>[(n(!0),S(x,null,A(a.row.authorizedGrantTypes,(w,f)=>(n(),p(B,{"disable-transitions":!0,key:f,index:f,class:"mr-5px"},{default:t(()=>[i(v(w),1)]),_:2},1032,["index"]))),128))]),_:1}),e(r,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:s(ye)},null,8,["formatter"]),e(r,{label:"\u64CD\u4F5C",align:"center"},{default:t(a=>[_((n(),p(c,{link:"",type:"primary",onClick:w=>U("update",a.row.id)},{default:t(()=>l[8]||(l[8]=[i(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[k,["system:oauth2-client:update"]]]),_((n(),p(c,{link:"",type:"danger",onClick:w=>(async f=>{try{await C.delConfirm(),await be(f),C.success(F("common.delSuccess")),await u()}catch{}})(a.row.id)},{default:t(()=>l[9]||(l[9]=[i(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[k,["system:oauth2-client:delete"]]])]),_:1})]),_:1},8,["data"])),[[J,s(y)]]),e(E,{total:s(T),page:s(o).pageNo,"onUpdate:page":l[3]||(l[3]=a=>s(o).pageNo=a),limit:s(o).pageSize,"onUpdate:limit":l[4]||(l[4]=a=>s(o).pageSize=a),onPagination:u},null,8,["total","page","limit"])]),_:1}),e(ge,{ref_key:"formRef",ref:N,onSuccess:u},null,512)],64)}}});export{we as default};
