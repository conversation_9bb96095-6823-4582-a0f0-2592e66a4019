import{as as a}from"./index-CRsFgzy0.js";const r={getWarehousePage:async e=>await a.get({url:"/erp/warehouse/page",params:e}),getWarehouseSimpleList:async()=>await a.get({url:"/erp/warehouse/simple-list"}),getWarehouse:async e=>await a.get({url:"/erp/warehouse/get?id="+e}),createWarehouse:async e=>await a.post({url:"/erp/warehouse/create",data:e}),updateWarehouse:async e=>await a.put({url:"/erp/warehouse/update",data:e}),updateWarehouseDefaultStatus:async(e,t)=>await a.put({url:"/erp/warehouse/update-default-status",params:{id:e,defaultStatus:t}}),deleteWarehouse:async e=>await a.delete({url:"/erp/warehouse/delete?id="+e}),exportWarehouse:async e=>await a.download({url:"/erp/warehouse/export-excel",params:e})};export{r as W};
