import{d as oe,u as te,r as i,f as de,aK as re,q as ue,O as pe,c as s,o as r,g as o,w as t,s as ie,a as l,A as u,a3 as C,v as se,x as ne,F as n,y,R as T,D as h,B as me,dU as ce,C as ve,P as ye,Q as fe,J as g,a6 as _e,G as be,H as k,I as ke,K as Ve,i as he,cY as Ue,cZ as we,c_ as Ce,M as Te}from"./index-CvERnF9Y.js";import{_ as ge}from"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import{_ as xe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as Ee}from"./index-CeUx6j9a.js";import{_ as Pe}from"./OrderDeliveryForm.vue_vue_type_script_setup_true_lang-O-34vZYb.js";import{_ as Ie}from"./OrderUpdateRemarkForm.vue_vue_type_script_setup_true_lang-Csz3v4Ez.js";import{e as De}from"./index-BuF2kS0D.js";import{a as Re}from"./index-DN1RTrPS.js";import{g as Se}from"./index-BWb1z3Vm.js";import{D as x,T as qe}from"./constants-uird_4gU.js";import Ne from"./OrderTableColumn-D0o61hTH.js";import"./index-DHM6tdge.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./el-image-DTDUrxnp.js";import"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import"./color-CIFUYK2M.js";import"./formatTime-CmW2_KRq.js";const Ye={class:"flex items-center justify-center"},Ae=oe({name:"TradeOrder",__name:"index",setup(Oe){const{currentRoute:X,push:j}=te(),E=i(!0),R=i(2),P=i([]),S=i(),d=i({pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0}),V=de({queryParam:""}),q=i([{value:"no",label:"\u8BA2\u5355\u53F7"},{value:"userId",label:"\u7528\u6237UID"},{value:"userNickname",label:"\u7528\u6237\u6635\u79F0"},{value:"userMobile",label:"\u7528\u6237\u7535\u8BDD"}]),B=m=>{var a;(a=q.value.filter(f=>f.value!==m))==null||a.forEach(f=>{d.value.hasOwnProperty(f.value)&&delete d.value[f.value]})},_=async()=>{E.value=!0;try{const m=await De(l(d));P.value=m.list,R.value=m.total}finally{E.value=!1}},I=async()=>{d.value.pageNo=1,await _()},G=()=>{var m;(m=S.value)==null||m.resetFields(),d.value={pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0},I()},N=i(),Y=i();re(()=>X.value,()=>{_()});const D=i([]),A=i([]);return ue(async()=>{await _(),D.value=await Re(),A.value=await Se()}),(m,a)=>{const f=Ee,c=me,v=ne,p=se,Q=ve,O=ye,b=ke,U=be,Z=ie,K=xe,M=Ce,W=we,$=Ue,ee=Ve,le=ge,z=pe("hasPermi"),ae=Te;return r(),s(n,null,[o(f,{title:"\u3010\u4EA4\u6613\u3011\u4EA4\u6613\u8BA2\u5355",url:"https://doc.iocoder.cn/mall/trade-order/"}),o(f,{title:"\u3010\u4EA4\u6613\u3011\u8D2D\u7269\u8F66",url:"https://doc.iocoder.cn/mall/trade-cart/"}),o(K,null,{default:t(()=>[o(Z,{ref_key:"queryFormRef",ref:S,inline:!0,model:l(d),class:"-mb-15px","label-width":"68px"},{default:t(()=>[o(p,{label:"\u8BA2\u5355\u72B6\u6001",prop:"status"},{default:t(()=>[o(v,{modelValue:l(d).status,"onUpdate:modelValue":a[0]||(a[0]=e=>l(d).status=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(r(!0),s(n,null,y(l(T)(l(h).TRADE_ORDER_STATUS),e=>(r(),u(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(p,{label:"\u652F\u4ED8\u65B9\u5F0F",prop:"payChannelCode"},{default:t(()=>[o(v,{modelValue:l(d).payChannelCode,"onUpdate:modelValue":a[1]||(a[1]=e=>l(d).payChannelCode=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(r(!0),s(n,null,y(l(ce)(l(h).PAY_CHANNEL_CODE),e=>(r(),u(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(p,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[o(Q,{modelValue:l(d).createTime,"onUpdate:modelValue":a[2]||(a[2]=e=>l(d).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),o(p,{label:"\u8BA2\u5355\u6765\u6E90",prop:"terminal"},{default:t(()=>[o(v,{modelValue:l(d).terminal,"onUpdate:modelValue":a[3]||(a[3]=e=>l(d).terminal=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(r(!0),s(n,null,y(l(T)(l(h).TERMINAL),e=>(r(),u(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(p,{label:"\u8BA2\u5355\u7C7B\u578B",prop:"type"},{default:t(()=>[o(v,{modelValue:l(d).type,"onUpdate:modelValue":a[4]||(a[4]=e=>l(d).type=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(r(!0),s(n,null,y(l(T)(l(h).TRADE_ORDER_TYPE),e=>(r(),u(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(p,{label:"\u914D\u9001\u65B9\u5F0F",prop:"deliveryType"},{default:t(()=>[o(v,{modelValue:l(d).deliveryType,"onUpdate:modelValue":a[5]||(a[5]=e=>l(d).deliveryType=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(r(!0),s(n,null,y(l(T)(l(h).TRADE_DELIVERY_TYPE),e=>(r(),u(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(d).deliveryType===l(x).EXPRESS.type?(r(),u(p,{key:0,label:"\u5FEB\u9012\u516C\u53F8",prop:"logisticsId"},{default:t(()=>[o(v,{modelValue:l(d).logisticsId,"onUpdate:modelValue":a[6]||(a[6]=e=>l(d).logisticsId=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(r(!0),s(n,null,y(l(A),e=>(r(),u(c,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):C("",!0),l(d).deliveryType===l(x).PICK_UP.type?(r(),u(p,{key:1,label:"\u81EA\u63D0\u95E8\u5E97",prop:"pickUpStoreId"},{default:t(()=>[o(v,{modelValue:l(d).pickUpStoreId,"onUpdate:modelValue":a[7]||(a[7]=e=>l(d).pickUpStoreId=e),class:"!w-280px",clearable:"",multiple:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(r(!0),s(n,null,y(l(D),e=>(r(),u(c,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):C("",!0),l(d).deliveryType===l(x).PICK_UP.type?(r(),u(p,{key:2,label:"\u6838\u9500\u7801",prop:"pickUpVerifyCode"},{default:t(()=>[o(O,{modelValue:l(d).pickUpVerifyCode,"onUpdate:modelValue":a[8]||(a[8]=e=>l(d).pickUpVerifyCode=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u81EA\u63D0\u6838\u9500\u7801",onKeyup:fe(I,["enter"])},null,8,["modelValue"])]),_:1})):C("",!0),o(p,{label:"\u805A\u5408\u641C\u7D22"},{default:t(()=>[g(o(O,{modelValue:l(d)[l(V).queryParam],"onUpdate:modelValue":a[10]||(a[10]=e=>l(d)[l(V).queryParam]=e),type:l(V).queryParam==="userId"?"number":"text",class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165"},{prepend:t(()=>[o(v,{modelValue:l(V).queryParam,"onUpdate:modelValue":a[9]||(a[9]=e=>l(V).queryParam=e),class:"!w-110px",clearable:"",placeholder:"\u5168\u90E8",onChange:B},{default:t(()=>[(r(!0),s(n,null,y(l(q),e=>(r(),u(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","type"]),[[_e,!0]])]),_:1}),o(p,null,{default:t(()=>[o(U,{onClick:I},{default:t(()=>[o(b,{class:"mr-5px",icon:"ep:search"}),a[13]||(a[13]=k(" \u641C\u7D22 "))]),_:1}),o(U,{onClick:G},{default:t(()=>[o(b,{class:"mr-5px",icon:"ep:refresh"}),a[14]||(a[14]=k(" \u91CD\u7F6E "))]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),o(K,null,{default:t(()=>[g((r(),u(ee,{data:l(P),"row-key":"id"},{default:t(()=>[o(l(Ne),{list:l(P),"pick-up-store-list":l(D)},{default:t(({row:e})=>[he("div",Ye,[g((r(),u(U,{link:"",type:"primary",onClick:F=>{return w=e.id,void j({name:"TradeOrderDetail",params:{id:w}});var w}},{default:t(()=>[o(b,{icon:"ep:notification"}),a[15]||(a[15]=k(" \u8BE6\u60C5 "))]),_:2},1032,["onClick"])),[[z,["trade:order:query"]]]),g((r(),u($,{onCommand:F=>((w,H)=>{var L,J;switch(w){case"remark":(L=Y.value)==null||L.open(H);break;case"delivery":(J=N.value)==null||J.open(H)}})(F,e)},{dropdown:t(()=>[o(W,null,{default:t(()=>[e.deliveryType===l(x).EXPRESS.type&&e.status===l(qe).UNDELIVERED.status?(r(),u(M,{key:0,command:"delivery"},{default:t(()=>[o(b,{icon:"ep:takeaway-box"}),a[17]||(a[17]=k(" \u53D1\u8D27 "))]),_:1})):C("",!0),o(M,{command:"remark"},{default:t(()=>[o(b,{icon:"ep:chat-line-square"}),a[18]||(a[18]=k(" \u5907\u6CE8 "))]),_:1})]),_:2},1024)]),default:t(()=>[o(U,{link:"",type:"primary"},{default:t(()=>[o(b,{icon:"ep:d-arrow-right"}),a[16]||(a[16]=k(" \u66F4\u591A "))]),_:1})]),_:2},1032,["onCommand"])),[[z,["trade:order:update"]]])])]),_:1},8,["list","pick-up-store-list"])]),_:1},8,["data"])),[[ae,l(E)]]),o(le,{limit:l(d).pageSize,"onUpdate:limit":a[11]||(a[11]=e=>l(d).pageSize=e),page:l(d).pageNo,"onUpdate:page":a[12]||(a[12]=e=>l(d).pageNo=e),total:l(R),onPagination:_},null,8,["limit","page","total"])]),_:1}),o(Pe,{ref_key:"deliveryFormRef",ref:N,onSuccess:_},null,512),o(Ie,{ref_key:"updateRemarkForm",ref:Y,onSuccess:_},null,512)],64)}}});export{Ae as default};
