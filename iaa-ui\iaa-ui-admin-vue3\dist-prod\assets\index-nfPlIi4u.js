import{_ as e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{_ as m}from"./IFrame.vue_vue_type_script_setup_true_lang-B5-s51Jd.js";import{_ as n}from"./index-CeUx6j9a.js";import{d as p,r as c,eW as i,c as l,o as u,g as o,w as d,a as _,F as f}from"./index-CvERnF9Y.js";const h=p({name:"JimuReport",__name:"index",setup(x){const r=c("http://shouhou.iaa360.com/jmreport/list?token="+i());return(b,g)=>{const t=n,s=m,a=e;return u(),l(f,null,[o(t,{title:"\u62A5\u8868\u8BBE\u8BA1\u5668",url:"https://doc.iocoder.cn/report/"}),o(a,{bodyStyle:{padding:"0px"},class:"!mb-0"},{default:d(()=>[o(s,{src:_(r)},null,8,["src"])]),_:1})],64)}}});export{h as default};
