import{d,c as e,o as a,g as t,j as u,w as o,F as y,y as m,A as f,i as g,t as h,I as v,aw as _}from"./index-CRsFgzy0.js";import{E as k,a as w}from"./el-carousel-item-CR2zrYVC.js";import{E as C}from"./el-image-BQpHFDaE.js";const b={class:"h-24px truncate leading-24px"},j=d({name:"NoticeBar",__name:"index",props:{property:{}},setup:E=>(r,A)=>{const s=C,p=u,l=k,c=w,i=v;return a(),e("div",{class:"flex items-center p-y-4px text-12px",style:_({backgroundColor:r.property.backgroundColor,color:r.property.textColor})},[t(s,{src:r.property.iconUrl,class:"h-18px"},null,8,["src"]),t(p,{direction:"vertical"}),t(c,{height:"24px",direction:"vertical",autoplay:!0,class:"flex-1 p-r-8px"},{default:o(()=>[(a(!0),e(y,null,m(r.property.contents,(n,x)=>(a(),f(l,{key:x},{default:o(()=>[g("div",b,h(n.text),1)]),_:2},1024))),128))]),_:1}),t(i,{icon:"ep:arrow-right"})],4)}});export{j as default};
