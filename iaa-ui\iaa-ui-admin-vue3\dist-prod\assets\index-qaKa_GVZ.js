import{d as te,p as oe,b as ie,r as m,f as ne,u as pe,q as ce,O as se,c as B,o as n,g as e,w as r,s as de,a as t,v as me,x as ue,Q as H,F as J,y as fe,A as c,B as be,P as we,J as b,G as ve,H as p,I as _e,l as ye,m as ge,n as he,K as ke,L as xe,a5 as Ce,t as w,e9 as Ne,D as Te,dv as Ve,ea as k,M as Re}from"./index-CRsFgzy0.js";import{_ as Ie}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as Ue}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as Pe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as Se}from"./index-DYfNUK1u.js";import{b as R,d as M}from"./formatTime-DhdtkSIS.js";import{d as Ee}from"./download-oWiM5xVU.js";import{e as Fe,d as ze,h as De}from"./index-_JDDr0Wd.js";import{_ as Ke}from"./ReceivablePlanForm.vue_vue_type_script_setup_true_lang-CZrkmqUV.js";import{g as Le}from"./index-BI-6YuuK.js";import{_ as qe}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-BHJ4SZ5G.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./index-D4y5Z4cM.js";import"./index-DbVUAHjz.js";import"./index-Bie3J7yG.js";const Ae=te({name:"ReceivablePlan",__name:"index",setup(Be){const x=oe(),{t:j}=ie(),C=m(!0),I=m(0),U=m([]),i=ne({pageNo:1,pageSize:10,sceneType:"1",customerId:void 0,contractNo:void 0}),P=m(),N=m(!1),T=m("1"),S=m([]),G=d=>{i.sceneType=d.paneName,v()},f=async()=>{C.value=!0;try{const d=await Fe(i);U.value=d.list,I.value=d.total}finally{C.value=!1}},v=()=>{i.pageNo=1,f()},O=()=>{P.value.resetFields(),v()},E=m(),F=(d,l)=>{E.value.open(d,l)},z=m(),Q=async()=>{try{await x.exportConfirm(),N.value=!0;const d=await De(i);Ee.excel(d,"\u56DE\u6B3E\u8BA1\u5212.xls")}catch{}finally{N.value=!1}},{push:D}=pe();return ce(async()=>{await f(),S.value=await Le()}),(d,l)=>{const K=Se,W=be,X=ue,V=me,Y=we,y=_e,u=ve,Z=de,L=Pe,q=he,$=ye,A=Ce,o=xe,ee=Ue,g=Ve,ae=ke,le=Ie,_=se("hasPermi"),re=Re;return n(),B(J,null,[e(K,{title:"\u3010\u56DE\u6B3E\u3011\u56DE\u6B3E\u7BA1\u7406\u3001\u56DE\u6B3E\u8BA1\u5212",url:"https://doc.iocoder.cn/crm/receivable/"}),e(K,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),e(L,null,{default:r(()=>[e(Z,{ref_key:"queryFormRef",ref:P,inline:!0,model:t(i),class:"-mb-15px","label-width":"68px"},{default:r(()=>[e(V,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerId"},{default:r(()=>[e(X,{modelValue:t(i).customerId,"onUpdate:modelValue":l[0]||(l[0]=a=>t(i).customerId=a),class:"!w-240px",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",onKeyup:H(v,["enter"])},{default:r(()=>[(n(!0),B(J,null,fe(t(S),a=>(n(),c(W,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(V,{label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo"},{default:r(()=>[e(Y,{modelValue:t(i).contractNo,"onUpdate:modelValue":l[1]||(l[1]=a=>t(i).contractNo=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5408\u540C\u7F16\u53F7",onKeyup:H(v,["enter"])},null,8,["modelValue"])]),_:1}),e(V,null,{default:r(()=>[e(u,{onClick:v},{default:r(()=>[e(y,{class:"mr-5px",icon:"ep:search"}),l[6]||(l[6]=p(" \u641C\u7D22 "))]),_:1}),e(u,{onClick:O},{default:r(()=>[e(y,{class:"mr-5px",icon:"ep:refresh"}),l[7]||(l[7]=p(" \u91CD\u7F6E "))]),_:1}),b((n(),c(u,{plain:"",type:"primary",onClick:l[2]||(l[2]=a=>F("create"))},{default:r(()=>[e(y,{class:"mr-5px",icon:"ep:plus"}),l[8]||(l[8]=p(" \u65B0\u589E "))]),_:1})),[[_,["crm:receivable-plan:create"]]]),b((n(),c(u,{loading:t(N),plain:"",type:"success",onClick:Q},{default:r(()=>[e(y,{class:"mr-5px",icon:"ep:download"}),l[9]||(l[9]=p(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[_,["crm:receivable-plan:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(L,null,{default:r(()=>[e($,{modelValue:t(T),"onUpdate:modelValue":l[3]||(l[3]=a=>ge(T)?T.value=a:null),onTabClick:G},{default:r(()=>[e(q,{label:"\u6211\u8D1F\u8D23\u7684",name:"1"}),e(q,{label:"\u4E0B\u5C5E\u8D1F\u8D23\u7684",name:"3"})]),_:1},8,["modelValue"]),b((n(),c(ae,{data:t(U),"show-overflow-tooltip":!0,stripe:!0},{default:r(()=>[e(o,{align:"center",fixed:"left",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"150"},{default:r(a=>[e(A,{underline:!1,type:"primary",onClick:h=>{return s=a.row.customerId,void D({name:"CrmCustomerDetail",params:{id:s}});var s}},{default:r(()=>[p(w(a.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),e(o,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"200px"}),e(o,{align:"center",label:"\u671F\u6570",prop:"period"},{default:r(a=>[e(A,{underline:!1,type:"primary",onClick:h=>{return s=a.row.id,void D({name:"CrmReceivablePlanDetail",params:{id:s}});var s}},{default:r(()=>[p(w(a.row.period),1)]),_:2},1032,["onClick"])]),_:1}),e(o,{align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"price",width:"160",formatter:t(Ne)},null,8,["formatter"]),e(o,{formatter:t(R),align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"180px"},null,8,["formatter"]),e(o,{align:"center",label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192",prop:"remindDays",width:"150"}),e(o,{align:"center",label:"\u63D0\u9192\u65E5\u671F",prop:"remindTime",width:"180px",formatter:t(R)},null,8,["formatter"]),e(o,{align:"center",label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType",width:"130px"},{default:r(a=>[e(ee,{type:t(Te).CRM_RECEIVABLE_RETURN_TYPE,value:a.row.returnType},null,8,["type","value"])]),_:1}),e(o,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),e(o,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),e(o,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:r(a=>[a.row.receivable?(n(),c(g,{key:0},{default:r(()=>[p(w(t(k)(a.row.receivable.price)),1)]),_:2},1024)):(n(),c(g,{key:1},{default:r(()=>[p(w(t(k)(0)),1)]),_:1}))]),_:1}),e(o,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u65E5\u671F",prop:"receivable.returnTime",width:"180px",formatter:t(R)},null,8,["formatter"]),e(o,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:r(a=>[a.row.receivable?(n(),c(g,{key:0},{default:r(()=>[p(w(t(k)(a.row.price-a.row.receivable.price)),1)]),_:2},1024)):(n(),c(g,{key:1},{default:r(()=>[p(w(t(k)(a.row.price)),1)]),_:2},1024))]),_:1}),e(o,{formatter:t(M),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),e(o,{formatter:t(M),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(o,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"}),e(o,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"180px"},{default:r(a=>[b((n(),c(u,{link:"",type:"success",onClick:h=>{return s=a.row,void z.value.open("create",void 0,s);var s},disabled:a.row.receivableId},{default:r(()=>l[10]||(l[10]=[p(" \u521B\u5EFA\u56DE\u6B3E ")])),_:2},1032,["onClick","disabled"])),[[_,["crm:receivable:create"]]]),b((n(),c(u,{link:"",type:"primary",onClick:h=>F("update",a.row.id)},{default:r(()=>l[11]||(l[11]=[p(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[_,["crm:receivable-plan:update"]]]),b((n(),c(u,{link:"",type:"danger",onClick:h=>(async s=>{try{await x.delConfirm(),await ze(s),x.success(j("common.delSuccess")),await f()}catch{}})(a.row.id)},{default:r(()=>l[12]||(l[12]=[p(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[_,["crm:receivable-plan:delete"]]])]),_:1})]),_:1},8,["data"])),[[re,t(C)]]),e(le,{limit:t(i).pageSize,"onUpdate:limit":l[4]||(l[4]=a=>t(i).pageSize=a),page:t(i).pageNo,"onUpdate:page":l[5]||(l[5]=a=>t(i).pageNo=a),total:t(I),onPagination:f},null,8,["limit","page","total"])]),_:1}),e(Ke,{ref_key:"formRef",ref:E,onSuccess:f},null,512),e(qe,{ref_key:"receivableFormRef",ref:z,onSuccess:f},null,512)],64)}}});export{Ae as default};
