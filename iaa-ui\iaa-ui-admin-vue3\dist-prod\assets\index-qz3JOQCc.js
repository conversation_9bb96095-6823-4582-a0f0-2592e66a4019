import{as as e}from"./index-CvERnF9Y.js";const t=async a=>await e.get({url:"/infra/demo03-student/page",params:a}),d=async a=>await e.get({url:"/infra/demo03-student/get?id="+a}),s=async a=>await e.post({url:"/infra/demo03-student/create",data:a}),r=async a=>await e.put({url:"/infra/demo03-student/update",data:a}),n=async a=>await e.delete({url:"/infra/demo03-student/delete?id="+a}),u=async a=>await e.download({url:"/infra/demo03-student/export-excel",params:a}),o=async a=>await e.get({url:"/infra/demo03-student/demo03-course/page",params:a}),i=async a=>await e.post({url:"/infra/demo03-student/demo03-course/create",data:a}),m=async a=>await e.put({url:"/infra/demo03-student/demo03-course/update",data:a}),c=async a=>await e.delete({url:"/infra/demo03-student/demo03-course/delete?id="+a}),l=async a=>await e.get({url:"/infra/demo03-student/demo03-course/get?id="+a}),p=async a=>await e.get({url:"/infra/demo03-student/demo03-grade/page",params:a}),f=async a=>await e.post({url:"/infra/demo03-student/demo03-grade/create",data:a}),g=async a=>await e.put({url:"/infra/demo03-student/demo03-grade/update",data:a}),w=async a=>await e.delete({url:"/infra/demo03-student/demo03-grade/delete?id="+a}),y=async a=>await e.get({url:"/infra/demo03-student/demo03-grade/get?id="+a});export{l as a,i as b,s as c,m as d,o as e,c as f,d as g,y as h,f as i,g as j,p as k,w as l,t as m,n,u as o,r as u};
