import{d as j,p as X,b as Y,r as d,f as Z,q as $,O as ee,c as O,o as u,g as e,w as o,s as ae,a as t,v as le,P as te,Q as oe,x as re,F as T,y as se,R as ne,D as z,A as m,B as ue,J as g,G as pe,H as f,I as ie,K as ce,L as de,e9 as q,a0 as me,M as fe}from"./index-CRsFgzy0.js";import{_ as ge}from"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import{_ as we}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import{_ as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{_ as he}from"./index-DYfNUK1u.js";import{d as ye}from"./formatTime-DhdtkSIS.js";import{d as ve}from"./download-oWiM5xVU.js";import{W as v}from"./index-mM5XVEAg.js";import{_ as be}from"./WarehouseForm.vue_vue_type_script_setup_true_lang-w4LJ-xtj.js";import"./index-CqPfoRkb.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./constants-uird_4gU.js";const xe=j({name:"ErpWarehouse",__name:"index",setup(Se){const w=X(),{t:A}=Y(),b=d(!0),C=d([]),V=d(0),r=Z({pageNo:1,pageSize:10,name:void 0,status:void 0}),U=d(),x=d(!1),i=async()=>{b.value=!0;try{const p=await v.getWarehousePage(r);C.value=p.list,V.value=p.total}finally{b.value=!1}},S=()=>{r.pageNo=1,i()},F=()=>{U.value.resetFields(),S()},N=d(),P=(p,a)=>{N.value.open(p,a)},R=async()=>{try{await w.exportConfirm(),x.value=!0;const p=await v.exportWarehouse(r);ve.excel(p,"\u4ED3\u5E93.xls")}catch{}finally{x.value=!1}};return $(()=>{i()}),(p,a)=>{const D=he,K=te,k=le,B=ue,E=re,_=ie,c=pe,G=ae,W=_e,s=de,H=we,I=me,J=ce,L=ge,h=ee("hasPermi"),Q=fe;return u(),O(T,null,[e(D,{title:"\u3010\u5E93\u5B58\u3011\u4EA7\u54C1\u5E93\u5B58\u3001\u5E93\u5B58\u660E\u7EC6",url:"https://doc.iocoder.cn/erp/stock/"}),e(W,null,{default:o(()=>[e(G,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:o(()=>[e(k,{label:"\u4ED3\u5E93\u540D\u79F0",prop:"name"},{default:o(()=>[e(K,{modelValue:t(r).name,"onUpdate:modelValue":a[0]||(a[0]=l=>t(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u5E93\u540D\u79F0",clearable:"",onKeyup:oe(S,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(k,{label:"\u4ED3\u5E93\u72B6\u6001",prop:"status"},{default:o(()=>[e(E,{modelValue:t(r).status,"onUpdate:modelValue":a[1]||(a[1]=l=>t(r).status=l),placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93\u72B6\u6001",clearable:"",class:"!w-240px"},{default:o(()=>[(u(!0),O(T,null,se(t(ne)(t(z).COMMON_STATUS),l=>(u(),m(B,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(k,null,{default:o(()=>[e(c,{onClick:S},{default:o(()=>[e(_,{icon:"ep:search",class:"mr-5px"}),a[5]||(a[5]=f(" \u641C\u7D22"))]),_:1}),e(c,{onClick:F},{default:o(()=>[e(_,{icon:"ep:refresh",class:"mr-5px"}),a[6]||(a[6]=f(" \u91CD\u7F6E"))]),_:1}),g((u(),m(c,{type:"primary",plain:"",onClick:a[2]||(a[2]=l=>P("create"))},{default:o(()=>[e(_,{icon:"ep:plus",class:"mr-5px"}),a[7]||(a[7]=f(" \u65B0\u589E "))]),_:1})),[[h,["erp:warehouse:create"]]]),g((u(),m(c,{type:"success",plain:"",onClick:R,loading:t(x)},{default:o(()=>[e(_,{icon:"ep:download",class:"mr-5px"}),a[8]||(a[8]=f(" \u5BFC\u51FA "))]),_:1},8,["loading"])),[[h,["erp:warehouse:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(W,null,{default:o(()=>[g((u(),m(J,{data:t(C),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[e(s,{label:"\u4ED3\u5E93\u540D\u79F0",align:"center",prop:"name"}),e(s,{label:"\u4ED3\u5E93\u5730\u5740",align:"center",prop:"address"}),e(s,{label:"\u4ED3\u50A8\u8D39",align:"center",prop:"warehousePrice",formatter:t(q)},null,8,["formatter"]),e(s,{label:"\u642C\u8FD0\u8D39",align:"center",prop:"truckagePrice",formatter:t(q)},null,8,["formatter"]),e(s,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"principal"}),e(s,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),e(s,{label:"\u6392\u5E8F",align:"center",prop:"sort"}),e(s,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:o(l=>[e(H,{type:t(z).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),e(s,{label:"\u662F\u5426\u9ED8\u8BA4",align:"center",prop:"defaultStatus"},{default:o(l=>[e(I,{modelValue:l.row.defaultStatus,"onUpdate:modelValue":y=>l.row.defaultStatus=y,"active-value":!0,"inactive-value":!1,onChange:y=>(async n=>{try{const M=n.defaultStatus?"\u8BBE\u7F6E":"\u53D6\u6D88";await w.confirm("\u786E\u8BA4\u8981"+M+'"'+n.name+'"\u9ED8\u8BA4\u5417?'),await v.updateWarehouseDefaultStatus(n.id,n.defaultStatus),await i()}catch{n.defaultStatus=!n.defaultStatus}})(l.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(ye),width:"180px"},null,8,["formatter"]),e(s,{label:"\u64CD\u4F5C",align:"center"},{default:o(l=>[g((u(),m(c,{link:"",type:"primary",onClick:y=>P("update",l.row.id)},{default:o(()=>a[9]||(a[9]=[f(" \u7F16\u8F91 ")])),_:2},1032,["onClick"])),[[h,["erp:warehouse:update"]]]),g((u(),m(c,{link:"",type:"danger",onClick:y=>(async n=>{try{await w.delConfirm(),await v.deleteWarehouse(n),w.success(A("common.delSuccess")),await i()}catch{}})(l.row.id)},{default:o(()=>a[10]||(a[10]=[f(" \u5220\u9664 ")])),_:2},1032,["onClick"])),[[h,["erp:warehouse:delete"]]])]),_:1})]),_:1},8,["data"])),[[Q,t(b)]]),e(L,{total:t(V),page:t(r).pageNo,"onUpdate:page":a[3]||(a[3]=l=>t(r).pageNo=l),limit:t(r).pageSize,"onUpdate:limit":a[4]||(a[4]=l=>t(r).pageSize=l),onPagination:i},null,8,["total","page","limit"])]),_:1}),e(be,{ref_key:"formRef",ref:N,onSuccess:i},null,512)],64)}}});export{xe as default};
