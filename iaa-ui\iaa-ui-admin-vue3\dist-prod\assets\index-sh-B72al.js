import{d as V,ah as i,a2 as _,A as f,o as c,P as g,a as o,m as s,w as v,g as x,c7 as I,_ as P}from"./index-CvERnF9Y.js";import{P as U}from"./color-CIFUYK2M.js";const h=P(V({name:"ColorInput",__name:"index",props:{modelValue:i.string.def("")},emits:["update:modelValue"],setup(u,{emit:t}){const m=u,n=t,e=_({get:()=>m.modelValue,set:d=>{n("update:modelValue",d)}});return(d,a)=>{const r=I,p=g;return c(),f(p,{modelValue:o(e),"onUpdate:modelValue":a[1]||(a[1]=l=>s(e)?e.value=l:null)},{prepend:v(()=>[x(r,{modelValue:o(e),"onUpdate:modelValue":a[0]||(a[0]=l=>s(e)?e.value=l:null),predefine:o(U)},null,8,["modelValue","predefine"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-d4fede95"]]);export{h as _};
