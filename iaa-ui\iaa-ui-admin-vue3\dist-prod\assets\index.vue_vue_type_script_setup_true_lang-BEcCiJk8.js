import{d as _,ah as v,fs as k,e5 as h,f5 as V,c as w,o as m,F as C,g as e,dv as I,w as a,H as j,a as i,i as d,b3 as $,bi as q,I as z,A,a3 as D,G as F,aP as G}from"./index-CRsFgzy0.js";import{V as H}from"./vuedraggable.umd-V1xhRSm3.js";const P={class:"mb-4px flex flex-col gap-4px border border-gray-2 border-rounded rounded border-solid p-8px"},R={class:"m--8px m-b-4px flex flex-row items-center justify-between p-8px",style:{"background-color":"var(--app-content-bg-color)"}},B=_({name:"Draggable",__name:"index",props:{modelValue:h().isRequired,emptyItem:k().def({}),limit:v.number.def(0)},emits:["update:modelValue"],setup(l,{emit:p}){const r=l,s=V(r,"modelValue",p),u=()=>s.value.push(G(r.emptyItem||{}));return(f,t)=>{const b=I,o=z,n=q,x=F;return m(),w(C,null,[e(b,{type:"info",size:"small"},{default:a(()=>t[0]||(t[0]=[j(" \u62D6\u52A8\u5DE6\u4E0A\u89D2\u7684\u5C0F\u5706\u70B9\u53EF\u5BF9\u5176\u6392\u5E8F ")])),_:1}),e(i(H),{list:i(s),"force-fallback":!0,animation:200,handle:".drag-icon",class:"m-t-8px","item-key":"index"},{item:a(({element:g,index:c})=>[d("div",P,[d("div",R,[e(n,{content:"\u62D6\u52A8\u6392\u5E8F"},{default:a(()=>[e(o,{icon:"ic:round-drag-indicator",class:"drag-icon cursor-move",style:{color:"#8a909c"}})]),_:1}),e(n,{content:"\u5220\u9664"},{default:a(()=>[i(s).length>1?(m(),A(o,{key:0,icon:"ep:delete",class:"cursor-pointer text-red-5",onClick:E=>(y=>s.value.splice(y,1))(c)},null,8,["onClick"])):D("",!0)]),_:2},1024)]),$(f.$slots,"default",{element:g,index:c})])]),_:3},8,["list"]),e(n,{disabled:l.limit<1,content:`\u6700\u591A\u6DFB\u52A0${l.limit}\u4E2A`},{default:a(()=>[e(x,{type:"primary",plain:"",class:"m-t-4px w-full",disabled:l.limit>0&&i(s).length>=l.limit,onClick:u},{default:a(()=>[e(o,{icon:"ep:plus"}),t[1]||(t[1]=d("span",null,"\u6DFB\u52A0",-1))]),_:1},8,["disabled"])]),_:1},8,["disabled","content"])],64)}}});export{B as _};
