import{d as r,c as i,o as x,i as t,b3 as a,t as l}from"./index-CRsFgzy0.js";const n={class:"mb-12px"},o={class:"flex text-[var(--el-text-color-primary)] justify-between items-center"},p={class:"text-[var(--el-text-color-secondary)] text-12px my-8px"},c=r({name:"Index",__name:"index",props:{title:{type:String},desc:{type:String}},setup:e=>(s,d)=>(x(),i("div",n,[t("div",o,[t("span",null,l(e.title),1),a(s.$slots,"extra")]),t("div",p,l(e.desc),1),a(s.$slots,"default")]))});export{c as _};
