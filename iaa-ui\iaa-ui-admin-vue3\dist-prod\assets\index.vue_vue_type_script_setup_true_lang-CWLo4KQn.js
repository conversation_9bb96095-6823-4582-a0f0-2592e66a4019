import{d as D,r as v,q as V,c as b,o as Y,g as t,b3 as k,aB as y,a as f,m as i,w as s,cf as H,H as d,C,ck as u}from"./index-CRsFgzy0.js";import{i as M,j,k as U,l as q,m as B}from"./formatTime-DhdtkSIS.js";const O={class:"flex flex-row items-center gap-2"},P=D({name:"ShortcutDateRangePicker",__name:"index",emits:["change"],setup(R,{expose:p,emit:x}){const l=v(7),e=v(["",""]);p({times:e});const g=[{text:"\u6628\u5929",value:()=>M(new Date,-1)},{text:"\u6700\u8FD17\u5929",value:()=>j()},{text:"\u672C\u6708",value:()=>[u().startOf("M"),u().subtract(1,"d")]},{text:"\u6700\u8FD130\u5929",value:()=>U()},{text:"\u6700\u8FD11\u5E74",value:()=>q()}],r=async()=>{(function(){const n=u().subtract(l.value,"d"),a=u().subtract(1,"d");e.value=B(n,a)})(),await m()},h=x,m=async()=>{h("change",e.value)};return V(()=>{r()}),(n,a)=>{const o=H,w=y,_=C;return Y(),b("div",O,[t(w,{modelValue:f(l),"onUpdate:modelValue":a[0]||(a[0]=c=>i(l)?l.value=c:null),onChange:r},{default:s(()=>[t(o,{value:1},{default:s(()=>a[2]||(a[2]=[d("\u6628\u5929")])),_:1}),t(o,{value:7},{default:s(()=>a[3]||(a[3]=[d("\u6700\u8FD17\u5929")])),_:1}),t(o,{value:30},{default:s(()=>a[4]||(a[4]=[d("\u6700\u8FD130\u5929")])),_:1})]),_:1},8,["modelValue"]),t(_,{modelValue:f(e),"onUpdate:modelValue":a[1]||(a[1]=c=>i(e)?e.value=c:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],shortcuts:g,class:"!w-240px",onChange:m},null,8,["modelValue","default-time"]),k(n.$slots,"default")])}}});export{P as _};
