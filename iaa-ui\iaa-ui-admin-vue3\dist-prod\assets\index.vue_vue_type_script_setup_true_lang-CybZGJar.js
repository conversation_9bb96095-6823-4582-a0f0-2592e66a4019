import{d as p,bv as d,c as u,o as f,i as s,g as l,a as x,I as m,t as o}from"./index-CRsFgzy0.js";import{E as g}from"./el-image-BQpHFDaE.js";const v={class:"flex bg-[var(--el-bg-color-overlay)] p-12px mb-12px rounded-1"},b={class:"bg-black bg-op-40 absolute top-0 left-0 w-full h-full flex items-center justify-center cursor-pointer"},I={class:"ml-8px"},y={class:"mt-8px text-12px text-[var(--el-text-color-secondary)] line-clamp-2"},w=p({name:"Index",__name:"index",props:{songInfo:{type:Object,default:()=>({})}},emits:["play"],setup(e,{emit:n}){const t=n,r=d("currentSong",{});function a(){t("play")}return(_,h)=>{const c=g,i=m;return f(),u("div",v,[s("div",{class:"relative",onClick:a},[l(c,{src:e.songInfo.imageUrl,class:"flex-none w-80px"},null,8,["src"]),s("div",b,[l(i,{icon:x(r).id===e.songInfo.id?"solar:pause-circle-bold":"mdi:arrow-right-drop-circle",size:30},null,8,["icon"])])]),s("div",I,[s("div",null,o(e.songInfo.title),1),s("div",y,o(e.songInfo.desc),1)])])}}});export{w as _};
