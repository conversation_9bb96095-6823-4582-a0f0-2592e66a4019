import{_ as g}from"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CuNmIW-p.js";import{d as h,ah as k,r as n,aK as d,c as x,o as C,g as u,w as m,G as w,H as y,m as A,a as F,P as G,F as H}from"./index-CRsFgzy0.js";const I=h({name:"AppLinkInput",__name:"index",props:{modelValue:k.string.def("")},emits:["update:modelValue"],setup(t,{emit:p}){const o=t,e=n(""),s=n(),r=()=>{var a;return(a=s.value)==null?void 0:a.open(e.value)},i=a=>e.value=a;d(()=>o.modelValue,()=>e.value=o.modelValue,{immediate:!0});const v=p;return d(()=>e.value,()=>v("update:modelValue",e.value)),(a,l)=>{const V=w,c=G,f=g;return C(),x(H,null,[u(c,{modelValue:F(e),"onUpdate:modelValue":l[0]||(l[0]=_=>A(e)?e.value=_:null),placeholder:"\u8F93\u5165\u6216\u9009\u62E9\u94FE\u63A5"},{append:m(()=>[u(V,{onClick:r},{default:m(()=>l[1]||(l[1]=[y("\u9009\u62E9")])),_:1})]),_:1},8,["modelValue"]),u(f,{ref_key:"dialogRef",ref:s,onChange:i},null,512)],64)}}});export{I as _};
