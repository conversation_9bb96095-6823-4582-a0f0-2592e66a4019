import{_ as m}from"./CountTo.vue_vue_type_script_setup_true_lang-F1ckenVV.js";import{d as g,ah as s,c as o,o as l,i as a,g as t,I as u,X as c,A as v,a3 as p,t as d,w as b,bi as y,a as r,dW as i}from"./index-CRsFgzy0.js";const h={class:"flex flex-row items-center gap-3 rounded bg-[var(--el-bg-color-overlay)] p-4"},w={class:"flex flex-col gap-1"},C={class:"flex items-center gap-1 text-gray-500"},_={class:"text-3.5"},k={class:"flex flex-row items-baseline gap-2"},B={class:"text-7"},j={class:"text-sm"},S=g({name:"SummaryCard",__name:"index",props:{title:s.string.def(""),tooltip:s.string.def(""),icon:s.string.def(""),iconColor:s.string.def(""),iconBgColor:s.string.def(""),prefix:s.string.def(""),value:s.number.def(0),decimals:s.number.def(0),percent:s.oneOfType([Number,String]).def(void 0)},setup:e=>($,A)=>{const n=u,f=y,x=m;return l(),o("div",h,[a("div",{class:c(["h-12 w-12 flex flex-shrink-0 items-center justify-center rounded-1",`${e.iconColor} ${e.iconBgColor}`])},[t(n,{icon:e.icon,class:"!text-6"},null,8,["icon"])],2),a("div",w,[a("div",C,[a("span",_,d(e.title),1),e.tooltip?(l(),v(f,{key:0,content:e.tooltip,placement:"top-start"},{default:b(()=>[t(n,{icon:"ep:warning",class:"item-center flex !text-3"})]),_:1},8,["content"])):p("",!0)]),a("div",k,[a("div",B,[t(x,{prefix:e.prefix,"end-val":e.value,decimals:e.decimals},null,8,["prefix","end-val","decimals"])]),e.percent!=null?(l(),o("span",{key:0,class:c(r(i)(e.percent)>0?"text-red-500":"text-green-500")},[a("span",j,d(Math.abs(r(i)(e.percent)))+"%",1),t(n,{icon:r(i)(e.percent)>0?"ep:caret-top":"ep:caret-bottom",class:"ml-0.5 !text-3"},null,8,["icon"])],2)):p("",!0)])])])}});export{S as _};
