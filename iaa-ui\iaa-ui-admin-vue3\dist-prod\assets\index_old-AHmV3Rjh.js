import{d as le,N as te,u as se,p as oe,r,a2 as re,q as ie,aD as ue,c as h,o as u,g as o,J as me,A as _,a,M as ne,w as l,l as pe,m as G,F as k,y as V,n as de,E as fe,h as ce,k as ve,i as F,dv as ye,H as P,t as H,G as _e,I as be,s as ge,v as he,x as ke,B as xe}from"./index-CRsFgzy0.js";import{_ as we}from"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import{E as Ve}from"./el-image-BQpHFDaE.js";import{_ as Ce}from"./index-DYfNUK1u.js";import{b as Ue,a as Se}from"./index-DDx8QKkB.js";import{f as Ie,e as Ae}from"./index-CwimCuCj.js";import{d as Fe,b as Pe}from"./formCreate-BpHylIoj.js";import qe from"./ProcessInstanceBpmnViewer-CIBJAU-t.js";import{C as Be}from"./index-B3ec0EoQ.js";import{u as De}from"./tagsView-BnrVTrUo.js";import{g as Ee}from"./index-D4y5Z4cM.js";import"./bpmn-embedded-CdPLP5pg.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./XTextButton-HT7YhdoB.js";import"./XButton-BAqgoI8I.js";import"./consts-BcUrucFp.js";import"./utils-DxYKKFpa.js";import"./index-BGzgWl8v.js";import"./index-BCN8BzfC.js";import"./index-V053jm9V.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./index-ypa-aU70.js";import"./index-C0yL_L5C.js";import"./index-BD8pFnai.js";import"./index-CE1njtGE.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./constants-uird_4gU.js";import"./index-BjER-jGF.js";import"./el-drawer-DKlCjx7i.js";import"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";import"./color-CIFUYK2M.js";import"./index-CuupyWp_.js";import"./formatTime-DhdtkSIS.js";const Re={class:"flex"},ze={class:"clearfix"},Ge={class:"el-icon-document"},He=le({name:"BpmProcessInstanceCreate",__name:"index_old",setup(Je){const J=te(),{push:q,currentRoute:M}=se(),C=oe(),{delView:N}=De(),x=J.query.processInstanceId,U=r(!0),w=r([]),b=r(""),S=r([]),T=re(()=>S.value.filter(t=>t.category==b.value)),n=r(),p=r({rule:[],option:{},value:{}}),d=r(),B=r(null),f=r([]),c=r({}),D=r(),I=r({}),E=r([]),R=async(t,e)=>{var v;if(d.value=t,f.value=[],c.value={},I.value={},t.formType==10){const y=Fe(t.formFields).map(i=>i.field);for(const i in e)y.includes(i)||delete e[i];Pe(p,t.formConf,t.formFields,e);const g=await Se(t.id);if(g&&(B.value=g.bpmnXml,f.value=g.startUserSelectTasks,((v=f.value)==null?void 0:v.length)>0)){p.value.rule.push({type:"startUserSelect",props:{title:"\u6307\u5B9A\u5BA1\u6279\u4EBA"}});for(const i of f.value)c.value[i.id]=[],I.value[i.id]=[{required:!0,message:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA",trigger:"blur"}];E.value=await Ee()}}else t.formCustomCreatePath&&await q({path:t.formCustomCreatePath})},X=async t=>{var e;if(n.value&&d.value){((e=f.value)==null?void 0:e.length)>0&&await D.value.validate(),n.value.btn.loading(!0);try{await Ae({processDefinitionId:d.value.id,variables:t,startUserSelectAssignees:c.value}),C.success("\u53D1\u8D77\u6D41\u7A0B\u6210\u529F"),N(a(M)),await q({name:"BpmProcessInstanceMy"})}finally{n.value.btn.loading(!1)}}};return ie(()=>{(async()=>{U.value=!0;try{if(w.value=await Be.getCategorySimpleList(),w.value.length>0&&(b.value=w.value[0].code),S.value=await Ue({suspensionState:1}),(x==null?void 0:x.length)>0){const t=await Ie(x);if(!t)return void C.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9E\u4F8B\u4E0D\u5B58\u5728");const e=S.value.find(v=>{var y;return v.key==((y=t.processDefinition)==null?void 0:y.key)});if(!e)return void C.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9A\u4E49\u4E0D\u5B58\u5728");await R(e,t.formVariables)}}finally{U.value=!1}})()}),(t,e)=>{const v=Ce,y=Ve,g=ye,i=ve,A=ce,Y=fe,$=de,L=pe,z=we,Q=be,j=_e,K=xe,O=ke,W=he,Z=ge,ee=ue("form-create"),ae=ne;return u(),h(k,null,[o(v,{title:"\u6D41\u7A0B\u53D1\u8D77\u3001\u53D6\u6D88\u3001\u91CD\u65B0\u53D1\u8D77",url:"https://doc.iocoder.cn/bpm/process-instance/"}),a(d)?(u(),_(z,{key:1},{default:l(()=>[o(i,{class:"box-card"},{default:l(()=>[F("div",ze,[F("span",Ge,"\u7533\u8BF7\u4FE1\u606F\u3010"+H(a(d).name)+"\u3011",1),o(j,{style:{float:"right"},type:"primary",onClick:e[1]||(e[1]=s=>d.value=void 0)},{default:l(()=>[o(Q,{icon:"ep:delete"}),e[4]||(e[4]=P(" \u9009\u62E9\u5176\u5B83\u6D41\u7A0B "))]),_:1})]),o(A,{span:16,offset:6,style:{"margin-top":"20px"}},{default:l(()=>[o(ee,{rule:a(p).rule,api:a(n),"onUpdate:api":e[2]||(e[2]=s=>G(n)?n.value=s:null),modelValue:a(p).value,"onUpdate:modelValue":e[3]||(e[3]=s=>a(p).value=s),option:a(p).option,onSubmit:X},{"type-startUserSelect":l(()=>[o(A,{span:24},{default:l(()=>[o(i,{class:"mb-10px"},{header:l(()=>e[5]||(e[5]=[P("\u6307\u5B9A\u5BA1\u6279\u4EBA")])),default:l(()=>[o(Z,{model:a(c),rules:a(I),ref_key:"startUserSelectAssigneesFormRef",ref:D},{default:l(()=>[(u(!0),h(k,null,V(a(f),s=>(u(),_(W,{key:s.id,label:`\u4EFB\u52A1\u3010${s.name}\u3011`,prop:s.id},{default:l(()=>[o(O,{modelValue:a(c)[s.id],"onUpdate:modelValue":m=>a(c)[s.id]=m,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA"},{default:l(()=>[(u(!0),h(k,null,V(a(E),m=>(u(),_(K,{key:m.id,label:m.nickname,value:m.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1},8,["rule","api","modelValue","option"])]),_:1})]),_:1}),o(qe,{"bpmn-xml":a(B)},null,8,["bpmn-xml"])]),_:1})):me((u(),_(z,{key:0},{default:l(()=>[o(L,{"tab-position":"left",modelValue:a(b),"onUpdate:modelValue":e[0]||(e[0]=s=>G(b)?b.value=s:null)},{default:l(()=>[(u(!0),h(k,null,V(a(w),s=>(u(),_($,{label:s.name,name:s.code,key:s.code},{default:l(()=>[o(Y,{gutter:20},{default:l(()=>[(u(!0),h(k,null,V(a(T),m=>(u(),_(A,{lg:6,sm:12,xs:24,key:m.id},{default:l(()=>[o(i,{shadow:"hover",class:"mb-20px cursor-pointer",onClick:Me=>R(m)},{default:l(()=>[F("div",Re,[o(y,{src:m.icon,class:"w-32px h-32px"},null,8,["src"]),o(g,{class:"!ml-10px",size:"large"},{default:l(()=>[P(H(m.name),1)]),_:2},1024)])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1})),[[ae,a(U)]])],64)}}});export{He as default};
