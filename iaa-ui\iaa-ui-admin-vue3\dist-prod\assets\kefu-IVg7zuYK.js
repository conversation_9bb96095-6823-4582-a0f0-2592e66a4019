import{as as e,cV as o,dD as a}from"./index-CvERnF9Y.js";const i={getConversationList:async()=>await e.get({url:"/promotion/kefu-conversation/list"}),getConversation:async t=>await e.get({url:"/promotion/kefu-conversation/get?id="+t}),updateConversationPinned:async t=>await e.put({url:"/promotion/kefu-conversation/update-conversation-pinned",data:t}),deleteConversation:async t=>await e.delete({url:`/promotion/kefu-conversation/delete?id=${t}`})},r={TEXT:1,IMAGE:2,VOICE:3,VIDEO:4,SYSTEM:5,PRODUCT:10,ORDER:11},c={KEFU_MESSAGE_TYPE:"kefu_message_type",KEFU_MESSAGE_ADMIN_READ:"kefu_message_read_status_change"},v=o("mall-kefu",{state:()=>({conversationList:[],conversationMessageList:new Map}),getters:{getConversationList(){return this.conversationList},getConversationMessageList(){return t=>this.conversationMessageList.get(t)}},actions:{saveMessageList(t,s){this.conversationMessageList.set(t,s)},async setConversationList(){this.conversationList=await i.getConversationList(),this.conversationSort()},async updateConversationStatus(t){if(a(this.conversationList))return;const s=this.conversationList.find(n=>n.id===t);s&&(s.adminUnreadMessageCount=0)},async updateConversation(t){if(a(this.conversationList))return;const s=await i.getConversation(t);this.deleteConversation(t),s&&this.conversationList.push(s),this.conversationSort()},deleteConversation(t){const s=this.conversationList.findIndex(n=>n.id===t);s>-1&&this.conversationList.splice(s,1)},conversationSort(){this.conversationList.sort((t,s)=>t.adminPinned!==s.adminPinned?t.adminPinned?-1:1:s.lastMessageTime-t.lastMessageTime)}}});export{r as K,c as W,i as a,v as u};
