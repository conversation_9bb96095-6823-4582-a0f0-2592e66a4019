import{d as o,c as a,o as t,F as d,y as p,i as s,g as u,t as i,_ as v}from"./index-CRsFgzy0.js";import{E as w}from"./el-image-BQpHFDaE.js";const h={class:"news-home"},g=["href"],f={class:"news-main"},_={class:"news-content"},k={class:"news-content-title"},x=["href"],y={class:"news-main-item"},b={class:"news-content-item"},U={class:"news-content-item-title"},E={class:"news-content-item-img"},F=["src"],I=v(o({name:"WxNews",__name:"main",props:{articles:{default:null}},setup:(n,{expose:c})=>(c({articles:n.articles}),(r,N)=>{const m=w;return t(),a("div",h,[(t(!0),a(d,null,p(r.articles,(e,l)=>(t(),a("div",{key:l,class:"news-div"},[l===0?(t(),a("a",{key:0,href:e.url,target:"_blank"},[s("div",f,[s("div",_,[u(m,{src:e.picUrl,class:"material-img",style:{width:"100%",height:"120px"}},null,8,["src"]),s("div",k,[s("span",null,i(e.title),1)])])])],8,g)):(t(),a("a",{key:1,href:e.url,target:"_blank"},[s("div",y,[s("div",b,[s("div",U,i(e.title),1),s("div",E,[s("img",{src:e.picUrl,class:"material-img",height:"100%"},null,8,F)])])])],8,x))]))),128))])})}),[["__scopeId","data-v-1476749b"]]);export{I as default};
