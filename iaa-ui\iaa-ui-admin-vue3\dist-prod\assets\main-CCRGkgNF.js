import{as as j,d as W,p as A,r as c,f as x,q as B,A as D,o as p,w as H,i as _,J as T,c as f,a3 as k,g as S,M as E,a as s,m as P,G as L,H as U,aG as M,_ as F}from"./index-CvERnF9Y.js";import{_ as K}from"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import{R as O}from"./TabNews-CP5s1A4W.js";import Q from"./main-BJb7Su3Y.js";import X from"./MsgList-BGYpSbhp.js";import{g as Y}from"./index-DMzW2xxo.js";const R=y=>j.get({url:"/mp/message/page",params:y}),Z={key:0},$={key:1,class:"el-table__empty-block"},ee={class:"msg-send"},V=F(W({name:"WxMsg",__name:"main",props:{userId:{type:Number,required:!0}},setup(y){const q=A(),I=y,d=c(-1),v=c(!1),b=c(!0),i=c([]),n=x({pageNo:1,pageSize:14,accountId:d}),o=x({nickname:"\u7528\u6237",avatar:"/assets/profile-BiJdjH9E.jpg",accountId:d}),h=c(!1),l=c({type:O.Text,accountId:6,articles:[]}),w=c(null),t=c(null);B(async()=>{var e,u;const a=await Y(I.userId);o.nickname=((e=a.nickname)==null?void 0:e.length)>0?a.nickname:o.nickname,o.avatar=((u=o.avatar)==null?void 0:u.length)>0?a.avatar:o.avatar,d.value=a.accountId,l.value.accountId=a.accountId,J()});const C=async()=>{var e;if(!s(l))return;l.value.type===O.News&&l.value.articles&&l.value.articles.length>1&&(l.value.articles=[l.value.articles[0]],q.success("\u56FE\u6587\u6D88\u606F\u6761\u6570\u9650\u5236\u5728 1 \u6761\u4EE5\u5185\uFF0C\u5DF2\u9ED8\u8BA4\u53D1\u9001\u7B2C\u4E00\u6761"));const a=await(u=>j.post({url:"/mp/message/send",data:u}))({userId:I.userId,...l.value});h.value=!1,i.value=[...i.value,a],await z(),(e=w.value)==null||e.clear()},G=()=>{n.pageNo++,N(n,null)},N=async(a,e=null)=>{var g;v.value=!0;let u=await R(Object.assign({pageNo:a.pageNo,pageSize:a.pageSize,userId:I.userId,accountId:a.accountId},e));const m=((g=t.value)==null?void 0:g.scrollHeight)??0,r=u.list.reverse();i.value=[...r,...i.value],v.value=!1,(r.length<n.pageSize||r.length===0)&&(b.value=!1),n.pageNo=a.pageNo,n.pageSize=a.pageSize,n.pageNo===1?await z():r.length!==0&&(await M(),m!==0&&t.value&&(t.value.scrollTop=t.value.scrollHeight-m-100))},J=()=>{N(n)},z=async()=>{await M(),t.value&&(t.value.scrollTop=t.value.scrollHeight)};return(a,e)=>{const u=L,m=K,r=E;return p(),D(m,null,{default:H(()=>[_("div",{class:"msg-div",ref_key:"msgDivRef",ref:t},[T(_("div",null,null,512),[[r,s(v)]]),s(v)?k("",!0):(p(),f("div",Z,[s(b)?(p(),f("div",{key:0,class:"el-table__empty-block",onClick:G},e[1]||(e[1]=[_("span",{class:"el-table__empty-text"},"\u70B9\u51FB\u52A0\u8F7D\u66F4\u591A",-1)]))):k("",!0),s(b)?k("",!0):(p(),f("div",$,e[2]||(e[2]=[_("span",{class:"el-table__empty-text"},"\u6CA1\u6709\u66F4\u591A\u4E86",-1)])))])),S(X,{list:s(i),"account-id":s(d),user:s(o)},null,8,["list","account-id","user"])],512),T((p(),f("div",ee,[S(s(Q),{ref_key:"replySelectRef",ref:w,modelValue:s(l),"onUpdate:modelValue":e[0]||(e[0]=g=>P(l)?l.value=g:null)},null,8,["modelValue"]),S(u,{type:"success",class:"send-but",onClick:C},{default:H(()=>e[3]||(e[3]=[U("\u53D1\u9001(S)")])),_:1})])),[[r,s(h)]])]),_:1})}}}),[["__scopeId","data-v-6b90c37e"]]),ae=Object.freeze(Object.defineProperty({__proto__:null,default:V},Symbol.toStringTag,{value:"Module"}));export{V as W,R as g,ae as m};
