import{d as h,r as i,c as l,o as e,g as v,a3 as d,ay as q,w as p,A as y,a as u,I as E,t as m,H as f,aE as I,_ as P}from"./index-CRsFgzy0.js";import{B as S}from"./BenzAMRRecorder-WUNTwMf2.js";const W={key:2,class:"amr-duration"},j={key:0},A=P(h({name:"WxVoicePlayer",__name:"main",props:{url:{type:String,required:!0},content:{type:String,required:!1}},setup(t){const g=t,a=i(),s=i(!1),n=i(),_=()=>{a.value!==void 0?a.value.isPlaying()?w():o():k()},k=()=>{a.value=new S,a.value.initWithUrl(g.url).then(function(){o(),n.value=a.value.getDuration()}),a.value.onEnded(function(){s.value=!1})},o=()=>{s.value=!0,a.value.play()},w=()=>{s.value=!1,a.value.stop()};return(B,r)=>{const c=E,x=q,z=I;return e(),l("div",{class:"wx-voice-div",onClick:_},[v(x,null,{default:p(()=>[u(s)!==!0?(e(),y(c,{key:0,icon:"ep:video-play",size:32})):(e(),y(c,{key:1,icon:"ep:video-pause",size:32})),u(n)?(e(),l("span",W,m(u(n))+" \u79D2",1)):d("",!0)]),_:1}),t.content?(e(),l("div",j,[v(z,{type:"success",size:"small"},{default:p(()=>r[0]||(r[0]=[f("\u8BED\u97F3\u8BC6\u522B")])),_:1}),f(" "+m(t.content),1)])):d("",!0)])}}}),[["__scopeId","data-v-1128cc66"]]);export{A as default};
