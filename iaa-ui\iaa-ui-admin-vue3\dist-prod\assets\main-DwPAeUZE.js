import{d as I,a2 as U,r as T,aK as w,A as h,o as M,w as o,g as a,n as R,a as t,E as S,H as r,I as j,m as c,l as A,_ as E}from"./index-CRsFgzy0.js";import{N as H,R as p,c as v}from"./MessageTypes-D5TdOPUL.js";import K from"./ImText-CWd-FiGu.js";import N from"./ImImage-DLxchMwd.js";import P from"./ImVideo-Bmcq5w3y.js";import"./main-87AQXDxa.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./main-3TzxBWwr.js";import"./el-image-BQpHFDaE.js";import"./main-DDCad-8G.js";import"./BenzAMRRecorder-WUNTwMf2.js";import"./main.vue_vue_type_script_setup_true_lang-DoLPAFxa.js";import"./index-1FyZgYZc.js";import"./index-C64AUasM.js";import"./formatTime-DhdtkSIS.js";import"./useUpload-D8UAyHOj.js";const W=E(I({name:"WxReplySelect",__name:"main",props:{modelValue:{},newsType:{default:()=>H.Published}},emits:["update:modelValue","success"],setup(g,{expose:x,emit:b}){const f=g,V=b,l=U({get:()=>f.modelValue,set:u=>V("update:modelValue",u)}),_=new Map,n=T(f.modelValue.type||p.Text);return w(n,(u,e)=>{if(e===void 0||u===void 0)return;_.set(e,t(l));const d=_.get(u);if(d)l.value=d;else{let s=v(l);s.type=u,l.value=s}},{immediate:!0}),x({clear:()=>{l.value=v(l)}}),(u,e)=>{const d=j,s=S,i=R,y=A;return M(),h(y,{type:"border-card",modelValue:t(n),"onUpdate:modelValue":e[4]||(e[4]=m=>c(n)?n.value=m:null)},{default:o(()=>[a(i,{name:t(p).Text},{label:o(()=>[a(s,{align:"middle"},{default:o(()=>[a(d,{icon:"ep:document"}),e[5]||(e[5]=r(" \u6587\u672C"))]),_:1})]),default:o(()=>[a(K,{modelValue:t(l).content,"onUpdate:modelValue":e[0]||(e[0]=m=>t(l).content=m),onSuccess:e[1]||(e[1]=m=>V("success"))},null,8,["modelValue"])]),_:1},8,["name"]),a(i,{name:t(p).Image},{label:o(()=>[a(s,{align:"middle"},{default:o(()=>[a(d,{icon:"ep:picture",class:"mr-5px"}),e[6]||(e[6]=r(" \u56FE\u7247"))]),_:1})]),default:o(()=>[a(N,{modelValue:t(l),"onUpdate:modelValue":e[2]||(e[2]=m=>c(l)?l.value=m:null)},null,8,["modelValue"])]),_:1},8,["name"]),a(i,{name:t(p).Video},{label:o(()=>[a(s,{align:"middle"},{default:o(()=>[a(d,{icon:"ep:share"}),e[7]||(e[7]=r(" \u89C6\u9891"))]),_:1})]),default:o(()=>[a(P,{modelValue:t(l),"onUpdate:modelValue":e[3]||(e[3]=m=>c(l)?l.value=m:null)},null,8,["modelValue"])]),_:1},8,["name"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-0eff74a6"]]);export{W as default};
