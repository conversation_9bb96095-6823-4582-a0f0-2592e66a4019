import{d as t,c as d,o as c,g as u,a5 as l,w as n,i as a,t as e,_ as o}from"./index-CvERnF9Y.js";const p={class:"avue-card__body",style:{padding:"10px","background-color":"#fff","border-radius":"5px"}},_={class:"avue-card__avatar"},m=["src"],v={class:"avue-card__detail"},g={class:"avue-card__title",style:{"margin-bottom":"unset"}},f={class:"avue-card__info",style:{height:"unset"}},y=o(t({name:"WxMusic",__name:"main",props:{title:{required:!1,type:String},description:{required:!1,type:String},musicUrl:{required:!1,type:String},hqMusicUrl:{required:!1,type:String},thumbMediaUrl:{required:!0,type:String}},setup:(r,{expose:s})=>(s({musicUrl:r.musicUrl}),(h,U)=>{const i=l;return c(),d("div",null,[u(i,{type:"success",underline:!1,target:"_blank",href:r.hqMusicUrl?r.hqMusicUrl:r.musicUrl},{default:n(()=>[a("div",p,[a("div",_,[a("img",{src:r.thumbMediaUrl,alt:""},null,8,m)]),a("div",v,[a("div",g,e(r.title),1),a("div",f,e(r.description),1)])])]),_:1},8,["href"])])})}),[["__scopeId","data-v-f4a8c2e0"]]);export{y as default};
