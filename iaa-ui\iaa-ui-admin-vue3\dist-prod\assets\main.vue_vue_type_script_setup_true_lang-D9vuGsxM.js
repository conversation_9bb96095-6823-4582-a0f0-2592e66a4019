import{d as s,c,o as u,g as a,a5 as m,w as o,h as q,E as y,i as d,H as b,I as f,t as _}from"./index-CRsFgzy0.js";const g=["src"],h=s({name:"WxLocation",__name:"main",props:{locationX:{required:!0,type:Number},locationY:{required:!0,type:Number},label:{required:!0,type:String},qqMapKey:{required:!1,type:String,default:"TVDBZ-TDILD-4ON4B-PFDZA-RNLKH-VVF6E"}},setup(e,{expose:n}){const t=e;return n({locationX:t.locationX,locationY:t.locationY,label:t.label,qqMapKey:t.qqMapKey}),(k,K)=>{const l=y,r=f,i=q,p=m;return u(),c("div",null,[a(p,{type:"primary",target:"_blank",href:"https://map.qq.com/?type=marker&isopeninfowin=1&markertype=1&pointx="+e.locationY+"&pointy="+e.locationX+"&name="+e.label+"&ref=yudao"},{default:o(()=>[a(i,null,{default:o(()=>[a(l,null,{default:o(()=>[d("img",{src:"https://apis.map.qq.com/ws/staticmap/v2/?zoom=10&markers=color:blue|label:A|"+e.locationX+","+e.locationY+"&key="+e.qqMapKey+"&size=250*180"},null,8,g)]),_:1}),a(l,null,{default:o(()=>[a(r,{icon:"ep:location"}),b(" "+_(e.label),1)]),_:1})]),_:1})]),_:1},8,["href"])])}}});export{h as _};
