import{d as g,p as w,u as x,f as V,r as _,q as y,A as o,o as u,w as A,c as b,F as j,y as k,B as q,a as s,x as B}from"./index-CRsFgzy0.js";import{f as C}from"./index-vwqkkhoL.js";import{u as F}from"./tagsView-BnrVTrUo.js";const M=g({name:"WxAccountSelect",__name:"main",emits:["change"],setup(R,{emit:d}){const r=w(),{delView:c}=F(),{push:p,currentRoute:v}=x(),a=V({id:-1,name:""}),e=_([]),i=d,h=m=>{const n=e.value.find(t=>t.id===m);a.id&&(a.name=n?n.name:"",i("change",a.id,a.name))};return y(()=>{(async()=>{if(e.value=await C(),e.value.length==0)return r.error("\u672A\u914D\u7F6E\u516C\u4F17\u53F7\uFF0C\u8BF7\u5728\u3010\u516C\u4F17\u53F7\u7BA1\u7406 -> \u8D26\u53F7\u7BA1\u7406\u3011\u83DC\u5355\uFF0C\u8FDB\u884C\u914D\u7F6E"),c(s(v)),void await p({name:"MpAccount"});e.value.length>0&&(a.id=e.value[0].id,a.id&&(a.name=e.value[0].name,i("change",a.id,a.name)))})()}),(m,n)=>{const t=q,f=B;return u(),o(f,{modelValue:s(a).id,"onUpdate:modelValue":n[0]||(n[0]=l=>s(a).id=l),placeholder:"\u8BF7\u9009\u62E9\u516C\u4F17\u53F7",class:"!w-240px",onChange:h},{default:A(()=>[(u(!0),b(j,null,k(s(e),l=>(u(),o(t,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])}}});export{M as _};
