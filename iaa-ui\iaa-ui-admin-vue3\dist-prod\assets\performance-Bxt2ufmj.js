import{as as e}from"./index-CvERnF9Y.js";const t={getContractPricePerformance:r=>e.get({url:"/crm/statistics-performance/get-contract-price-performance",params:r}),getReceivablePricePerformance:r=>e.get({url:"/crm/statistics-performance/get-receivable-price-performance",params:r}),getContractCountPerformance:r=>e.get({url:"/crm/statistics-performance/get-contract-count-performance",params:r})};export{t as S};
