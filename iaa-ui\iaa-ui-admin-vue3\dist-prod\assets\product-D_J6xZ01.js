import{as as s}from"./index-CRsFgzy0.js";const a={getProductStatisticsAnalyse:t=>s.get({url:"/statistics/product/analyse",params:t}),getProductStatisticsList:t=>s.get({url:"/statistics/product/list",params:t}),exportProductStatisticsExcel:t=>s.download({url:"/statistics/product/export-excel",params:t}),getProductStatisticsRankPage:async t=>await s.get({url:"/statistics/product/rank-page",params:t})};export{a as P};
