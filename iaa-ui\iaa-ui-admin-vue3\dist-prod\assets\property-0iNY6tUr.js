import{d as x,f5 as v,A as T,o as m,w as a,g as e,c,a3 as U,v as w,ca as z,a as o,aB as k,F as f,y as W,bi as C,cf as j,I as A,s as B}from"./index-CRsFgzy0.js";import{_ as D}from"./index-CGOSLF-t.js";import"./color-CIFUYK2M.js";const F=x({name:"DividerProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(y,{emit:V}){const t=v(y,"modelValue",V),_=[{icon:"vaadin:line-h",text:"\u5B9E\u7EBF",type:"solid"},{icon:"tabler:line-dashed",text:"\u865A\u7EBF",type:"dashed"},{icon:"tabler:line-dotted",text:"\u70B9\u7EBF",type:"dotted"},{icon:"entypo:progress-empty",text:"\u65E0",type:"none"}];return(I,d)=>{const s=z,n=w,p=A,u=j,i=C,r=k,b=D,h=B;return m(),T(h,{"label-width":"80px",model:o(t)},{default:a(()=>[e(n,{label:"\u9AD8\u5EA6",prop:"height"},{default:a(()=>[e(s,{modelValue:o(t).height,"onUpdate:modelValue":d[0]||(d[0]=l=>o(t).height=l),min:1,max:100,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u9009\u62E9\u6837\u5F0F",prop:"borderType"},{default:a(()=>[e(r,{modelValue:o(t).borderType,"onUpdate:modelValue":d[1]||(d[1]=l=>o(t).borderType=l)},{default:a(()=>[(m(),c(f,null,W(_,(l,g)=>e(i,{placement:"top",key:g,content:l.text},{default:a(()=>[e(u,{value:l.type},{default:a(()=>[e(p,{icon:l.icon},null,8,["icon"])]),_:2},1032,["value"])]),_:2},1032,["content"])),64))]),_:1},8,["modelValue"])]),_:1}),o(t).borderType!=="none"?(m(),c(f,{key:0},[e(n,{label:"\u7EBF\u5BBD",prop:"lineWidth"},{default:a(()=>[e(s,{modelValue:o(t).lineWidth,"onUpdate:modelValue":d[2]||(d[2]=l=>o(t).lineWidth=l),min:1,max:30,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u5DE6\u53F3\u8FB9\u8DDD",prop:"paddingType"},{default:a(()=>[e(r,{modelValue:o(t).paddingType,"onUpdate:modelValue":d[3]||(d[3]=l=>o(t).paddingType=l)},{default:a(()=>[e(i,{content:"\u65E0\u8FB9\u8DDD",placement:"top"},{default:a(()=>[e(u,{value:"none"},{default:a(()=>[e(p,{icon:"tabler:box-padding"})]),_:1})]),_:1}),e(i,{content:"\u5DE6\u53F3\u7559\u8FB9",placement:"top"},{default:a(()=>[e(u,{value:"horizontal"},{default:a(()=>[e(p,{icon:"vaadin:padding"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u989C\u8272"},{default:a(()=>[e(b,{modelValue:o(t).lineColor,"onUpdate:modelValue":d[4]||(d[4]=l=>o(t).lineColor=l)},null,8,["modelValue"])]),_:1})],64)):U("",!0)]),_:1},8,["model"])}}});export{F as default};
