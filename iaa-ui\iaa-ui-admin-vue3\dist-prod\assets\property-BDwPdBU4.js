import{d as y,f5 as b,A as w,o as g,w as l,g as e,a as u,v as x,c4 as v,aB as T,bi as $,aC as P,H as i,s as j}from"./index-CvERnF9Y.js";import{_ as A}from"./index.vue_vue_type_script_setup_true_lang-BxGOchjX.js";import{_ as B}from"./index.vue_vue_type_script_setup_true_lang-DbWjkU0t.js";import"./vuedraggable.umd-DQlbMgN-.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-DEmiXCKi.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-Nl_f7Xki.js";import"./el-tree-select-CD6tMKW2.js";import"./tree-COGD3qag.js";import"./category-Cruo05cH.js";const C=y({name:"PopoverProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(n,{emit:V}){const p=b(n,"modelValue",V);return(H,o)=>{const f=v,m=x,_=B,r=P,s=$,c=T,U=A,h=j;return g(),w(h,{"label-width":"80px",model:u(p)},{default:l(()=>[e(U,{modelValue:u(p).list,"onUpdate:modelValue":o[0]||(o[0]=a=>u(p).list=a),"empty-item":{showType:"once"}},{default:l(({element:a,index:d})=>[e(m,{label:"\u56FE\u7247",prop:`list[${d}].imgUrl`},{default:l(()=>[e(f,{modelValue:a.imgUrl,"onUpdate:modelValue":t=>a.imgUrl=t,height:"56px",width:"56px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),e(m,{label:"\u8DF3\u8F6C\u94FE\u63A5",prop:`list[${d}].url`},{default:l(()=>[e(_,{modelValue:a.url,"onUpdate:modelValue":t=>a.url=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),e(m,{label:"\u663E\u793A\u6B21\u6570",prop:`list[${d}].showType`},{default:l(()=>[e(c,{modelValue:a.showType,"onUpdate:modelValue":t=>a.showType=t},{default:l(()=>[e(s,{content:"\u53EA\u663E\u793A\u4E00\u6B21\uFF0C\u4E0B\u6B21\u6253\u5F00\u65F6\u4E0D\u663E\u793A",placement:"bottom"},{default:l(()=>[e(r,{value:"once"},{default:l(()=>o[1]||(o[1]=[i("\u4E00\u6B21")])),_:1})]),_:1}),e(s,{content:"\u6BCF\u6B21\u6253\u5F00\u65F6\u90FD\u4F1A\u663E\u793A",placement:"bottom"},{default:l(()=>[e(r,{value:"always"},{default:l(()=>o[2]||(o[2]=[i("\u4E0D\u9650")])),_:1})]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1},8,["modelValue"])]),_:1},8,["model"])}}});export{C as default};
