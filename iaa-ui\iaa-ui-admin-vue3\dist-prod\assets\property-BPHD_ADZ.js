import{d as g,f5 as V,A as b,o as f,w as r,g as a,v as _,P as k,a as o,c4 as x,H as C,s as y}from"./index-CRsFgzy0.js";import{_ as I}from"./index-CGOSLF-t.js";import"./color-CIFUYK2M.js";const P=g({name:"PageConfigProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(u,{emit:p}){const m={},l=V(u,"modelValue",p);return(U,e)=>{const s=k,t=_,n=I,i=x,c=y;return f(),b(c,{"label-width":"80px",model:o(l),rules:m},{default:r(()=>[a(t,{label:"\u9875\u9762\u63CF\u8FF0",prop:"description"},{default:r(()=>[a(s,{type:"textarea",modelValue:o(l).description,"onUpdate:modelValue":e[0]||(e[0]=d=>o(l).description=d),placeholder:"\u7528\u6237\u901A\u8FC7\u5FAE\u4FE1\u5206\u4EAB\u7ED9\u670B\u53CB\u65F6\uFF0C\u4F1A\u81EA\u52A8\u663E\u793A\u9875\u9762\u63CF\u8FF0"},null,8,["modelValue"])]),_:1}),a(t,{label:"\u80CC\u666F\u989C\u8272",prop:"backgroundColor"},{default:r(()=>[a(n,{modelValue:o(l).backgroundColor,"onUpdate:modelValue":e[1]||(e[1]=d=>o(l).backgroundColor=d)},null,8,["modelValue"])]),_:1}),a(t,{label:"\u80CC\u666F\u56FE\u7247",prop:"backgroundImage"},{default:r(()=>[a(i,{modelValue:o(l).backgroundImage,"onUpdate:modelValue":e[2]||(e[2]=d=>o(l).backgroundImage=d),limit:1},{tip:r(()=>e[3]||(e[3]=[C("\u5EFA\u8BAE\u5BBD\u5EA6 750px")])),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])}}});export{P as default};
