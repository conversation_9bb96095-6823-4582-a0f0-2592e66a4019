import{d as $,f5 as O,a2 as H,r as N,c as f,o as m,i as w,g as l,a3 as k,a as t,m as q,F as y,y as F,w as a,aB as P,aC as B,H as s,v as S,P as D,c4 as j,ca as E,A as v,bi as G,k as J,a4 as K,s as L}from"./index-CvERnF9Y.js";import{_ as A}from"./index-sh-B72al.js";import{_ as Q}from"./index.vue_vue_type_script_setup_true_lang-DbWjkU0t.js";import{_ as W}from"./index-B0Fg6tbh.js";import{_ as X}from"./app-nav-bar-mp-QvSN8lzY.js";import"./color-CIFUYK2M.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-DEmiXCKi.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-Nl_f7Xki.js";import"./el-tree-select-CD6tMKW2.js";import"./tree-COGD3qag.js";import"./category-Cruo05cH.js";const Y={class:"h-40px flex items-center justify-center"},Z={key:0,alt:"",class:"h-30px w-76px",src:X},I=$({name:"NavigationBarCellProperty",__name:"CellProperty",props:{modelValue:{default:()=>[]},isMp:{type:Boolean,default:!0}},emits:["update:modelValue"],setup(U,{emit:x}){const g=U,o=O(g,"modelValue",x),T=H(()=>g.isMp?6:8),e=N(0),c=(_,d)=>{e.value=d,_.type||(_.type="text",_.textColor="#111111")};return(_,d)=>{const V=W,h=B,C=P,n=S,b=D,M=A,p=Q,R=j,z=E;return m(),f(y,null,[w("div",Y,[l(V,{modelValue:t(o),"onUpdate:modelValue":d[0]||(d[0]=u=>q(o)?o.value=u:null),cols:t(T),"cube-size":38,rows:1,class:"m-b-16px",onHotAreaSelected:c},null,8,["modelValue","cols"]),_.isMp?(m(),f("img",Z)):k("",!0)]),(m(!0),f(y,null,F(t(o),(u,i)=>(m(),f(y,{key:i},[t(e)===i?(m(),f(y,{key:0},[l(n,{prop:`cell[${i}].type`,label:"\u7C7B\u578B"},{default:a(()=>[l(C,{modelValue:u.type,"onUpdate:modelValue":r=>u.type=r},{default:a(()=>[l(h,{value:"text"},{default:a(()=>d[1]||(d[1]=[s("\u6587\u5B57")])),_:1}),l(h,{value:"image"},{default:a(()=>d[2]||(d[2]=[s("\u56FE\u7247")])),_:1}),l(h,{value:"search"},{default:a(()=>d[3]||(d[3]=[s("\u641C\u7D22\u6846")])),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),u.type==="text"?(m(),f(y,{key:0},[l(n,{prop:`cell[${i}].text`,label:"\u5185\u5BB9"},{default:a(()=>[l(b,{modelValue:u.text,"onUpdate:modelValue":r=>u.text=r,maxlength:"10","show-word-limit":""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),l(n,{prop:`cell[${i}].text`,label:"\u989C\u8272"},{default:a(()=>[l(M,{modelValue:u.textColor,"onUpdate:modelValue":r=>u.textColor=r},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),l(n,{prop:`cell[${i}].url`,label:"\u94FE\u63A5"},{default:a(()=>[l(p,{modelValue:u.url,"onUpdate:modelValue":r=>u.url=r},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])],64)):u.type==="image"?(m(),f(y,{key:1},[l(n,{prop:`cell[${i}].imgUrl`,label:"\u56FE\u7247"},{default:a(()=>[l(R,{modelValue:u.imgUrl,"onUpdate:modelValue":r=>u.imgUrl=r,limit:1,height:"56px",width:"56px"},{tip:a(()=>d[4]||(d[4]=[s("\u5EFA\u8BAE\u5C3A\u5BF8 56*56")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),l(n,{prop:`cell[${i}].url`,label:"\u94FE\u63A5"},{default:a(()=>[l(p,{modelValue:u.url,"onUpdate:modelValue":r=>u.url=r},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])],64)):(m(),f(y,{key:2},[l(n,{prop:`cell[${i}].placeholder`,label:"\u63D0\u793A\u6587\u5B57"},{default:a(()=>[l(b,{modelValue:u.placeholder,"onUpdate:modelValue":r=>u.placeholder=r,maxlength:"10","show-word-limit":""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),l(n,{prop:`cell[${i}].borderRadius`,label:"\u5706\u89D2"},{default:a(()=>[l(z,{modelValue:u.borderRadius,"onUpdate:modelValue":r=>u.borderRadius=r,max:100,min:0,"show-input-controls":!1,"input-size":"small","show-input":""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])],64))],64)):k("",!0)],64))),128))],64)}}}),ee={class:"flex items-center justify-between"},le={class:"flex items-center justify-between"},ae=$({name:"NavigationBarProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(U,{emit:x}){const g={name:[{required:!0,message:"\u8BF7\u8F93\u5165\u9875\u9762\u540D\u79F0",trigger:"blur"}]},o=O(U,"modelValue",x);return o.value._local||(o.value._local={previewMp:!0,previewOther:!1}),(T,e)=>{const c=B,_=G,d=P,V=S,h=A,C=j,n=K,b=J,M=L;return m(),v(M,{"label-width":"80px",model:t(o),rules:g},{default:a(()=>[l(V,{label:"\u6837\u5F0F",prop:"styleType"},{default:a(()=>[l(d,{modelValue:t(o).styleType,"onUpdate:modelValue":e[0]||(e[0]=p=>t(o).styleType=p)},{default:a(()=>[l(c,{value:"normal"},{default:a(()=>e[11]||(e[11]=[s("\u6807\u51C6")])),_:1}),l(_,{content:"\u6C89\u4FB5\u5F0F\u5934\u90E8\u4EC5\u652F\u6301\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\u3001APP\uFF0C\u5EFA\u8BAE\u9875\u9762\u7B2C\u4E00\u4E2A\u7EC4\u4EF6\u4E3A\u56FE\u7247\u5C55\u793A\u7C7B\u7EC4\u4EF6",placement:"top"},{default:a(()=>[l(c,{value:"inner"},{default:a(()=>e[12]||(e[12]=[s("\u6C89\u6D78\u5F0F")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(o).styleType==="inner"?(m(),v(V,{key:0,label:"\u5E38\u9A7B\u663E\u793A",prop:"alwaysShow"},{default:a(()=>[l(d,{modelValue:t(o).alwaysShow,"onUpdate:modelValue":e[1]||(e[1]=p=>t(o).alwaysShow=p)},{default:a(()=>[l(c,{value:!1},{default:a(()=>e[13]||(e[13]=[s("\u5173\u95ED")])),_:1}),l(_,{content:"\u5E38\u9A7B\u663E\u793A\u5173\u95ED\u540E,\u5934\u90E8\u5C0F\u7EC4\u4EF6\u5C06\u5728\u9875\u9762\u6ED1\u52A8\u65F6\u6DE1\u5165",placement:"top"},{default:a(()=>[l(c,{value:!0},{default:a(()=>e[14]||(e[14]=[s("\u5F00\u542F")])),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})):k("",!0),l(V,{label:"\u80CC\u666F\u7C7B\u578B",prop:"bgType"},{default:a(()=>[l(d,{modelValue:t(o).bgType,"onUpdate:modelValue":e[2]||(e[2]=p=>t(o).bgType=p)},{default:a(()=>[l(c,{value:"color"},{default:a(()=>e[15]||(e[15]=[s("\u7EAF\u8272")])),_:1}),l(c,{value:"img"},{default:a(()=>e[16]||(e[16]=[s("\u56FE\u7247")])),_:1})]),_:1},8,["modelValue"])]),_:1}),t(o).bgType==="color"?(m(),v(V,{key:1,label:"\u80CC\u666F\u989C\u8272",prop:"bgColor"},{default:a(()=>[l(h,{modelValue:t(o).bgColor,"onUpdate:modelValue":e[3]||(e[3]=p=>t(o).bgColor=p)},null,8,["modelValue"])]),_:1})):(m(),v(V,{key:2,label:"\u80CC\u666F\u56FE\u7247",prop:"bgImg"},{default:a(()=>[l(C,{modelValue:t(o).bgImg,"onUpdate:modelValue":e[4]||(e[4]=p=>t(o).bgImg=p),limit:1,width:"56px",height:"56px"},null,8,["modelValue"])]),_:1})),l(b,{class:"property-group",shadow:"never"},{header:a(()=>[w("div",ee,[e[18]||(e[18]=w("span",null,"\u5185\u5BB9\uFF08\u5C0F\u7A0B\u5E8F\uFF09",-1)),l(V,{prop:"_local.previewMp",class:"m-b-0!"},{default:a(()=>[l(n,{modelValue:t(o)._local.previewMp,"onUpdate:modelValue":e[5]||(e[5]=p=>t(o)._local.previewMp=p),onChange:e[6]||(e[6]=p=>t(o)._local.previewOther=!t(o)._local.previewMp)},{default:a(()=>e[17]||(e[17]=[s("\u9884\u89C8")])),_:1},8,["modelValue"])]),_:1})])]),default:a(()=>[l(I,{modelValue:t(o).mpCells,"onUpdate:modelValue":e[7]||(e[7]=p=>t(o).mpCells=p),"is-mp":""},null,8,["modelValue"])]),_:1}),l(b,{class:"property-group",shadow:"never"},{header:a(()=>[w("div",le,[e[20]||(e[20]=w("span",null,"\u5185\u5BB9\uFF08\u975E\u5C0F\u7A0B\u5E8F\uFF09",-1)),l(V,{prop:"_local.previewOther",class:"m-b-0!"},{default:a(()=>[l(n,{modelValue:t(o)._local.previewOther,"onUpdate:modelValue":e[8]||(e[8]=p=>t(o)._local.previewOther=p),onChange:e[9]||(e[9]=p=>t(o)._local.previewMp=!t(o)._local.previewOther)},{default:a(()=>e[19]||(e[19]=[s("\u9884\u89C8")])),_:1},8,["modelValue"])]),_:1})])]),default:a(()=>[l(I,{modelValue:t(o).otherCells,"onUpdate:modelValue":e[10]||(e[10]=p=>t(o).otherCells=p),"is-mp":!1},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])}}});export{ae as default};
