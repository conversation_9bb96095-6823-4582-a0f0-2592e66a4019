import{_ as A}from"./ComponentContainerProperty-CMKoqler.js";import{d as B,f5 as F,A as n,o as i,w as l,g as e,s as H,a as d,k as I,a3 as P,v as j,aB as D,bi as E,cf as J,I as L,aC as Q,H as p,a0 as R,ca as S,dv as T,c as W,c4 as q,F as G,c2 as K}from"./index-CRsFgzy0.js";import{_ as M}from"./index.vue_vue_type_script_setup_true_lang-BEcCiJk8.js";import{_ as N}from"./index.vue_vue_type_script_setup_true_lang-DS6I4M_U.js";import"./index-CGOSLF-t.js";import"./color-CIFUYK2M.js";import"./vuedraggable.umd-V1xhRSm3.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CuNmIW-p.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";const O=B({name:"CarouselProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(y,{emit:U}){const o=F(y,"modelValue",U);return(X,t)=>{const V=L,f=J,c=E,r=D,u=j,s=Q,h=R,x=S,v=T,_=I,b=q,w=K,g=N,k=M,C=H,z=A;return i(),n(z,{modelValue:d(o).style,"onUpdate:modelValue":t[5]||(t[5]=a=>d(o).style=a)},{default:l(()=>[e(C,{"label-width":"80px",model:d(o)},{default:l(()=>[e(_,{header:"\u6837\u5F0F\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:l(()=>[e(u,{label:"\u6837\u5F0F",prop:"type"},{default:l(()=>[e(r,{modelValue:d(o).type,"onUpdate:modelValue":t[0]||(t[0]=a=>d(o).type=a)},{default:l(()=>[e(c,{class:"item",content:"\u9ED8\u8BA4",placement:"bottom"},{default:l(()=>[e(f,{value:"default"},{default:l(()=>[e(V,{icon:"system-uicons:carousel"})]),_:1})]),_:1}),e(c,{class:"item",content:"\u5361\u7247",placement:"bottom"},{default:l(()=>[e(f,{value:"card"},{default:l(()=>[e(V,{icon:"ic:round-view-carousel"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u6307\u793A\u5668",prop:"indicator"},{default:l(()=>[e(r,{modelValue:d(o).indicator,"onUpdate:modelValue":t[1]||(t[1]=a=>d(o).indicator=a)},{default:l(()=>[e(s,{value:"dot"},{default:l(()=>t[6]||(t[6]=[p("\u5C0F\u5706\u70B9")])),_:1}),e(s,{value:"number"},{default:l(()=>t[7]||(t[7]=[p("\u6570\u5B57")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u662F\u5426\u8F6E\u64AD",prop:"autoplay"},{default:l(()=>[e(h,{modelValue:d(o).autoplay,"onUpdate:modelValue":t[2]||(t[2]=a=>d(o).autoplay=a)},null,8,["modelValue"])]),_:1}),d(o).autoplay?(i(),n(u,{key:0,label:"\u64AD\u653E\u95F4\u9694",prop:"interval"},{default:l(()=>[e(x,{modelValue:d(o).interval,"onUpdate:modelValue":t[3]||(t[3]=a=>d(o).interval=a),max:10,min:.5,step:.5,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"]),e(v,{type:"info"},{default:l(()=>t[8]||(t[8]=[p("\u5355\u4F4D\uFF1A\u79D2")])),_:1})]),_:1})):P("",!0)]),_:1}),e(_,{header:"\u5185\u5BB9\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:l(()=>[e(k,{modelValue:d(o).items,"onUpdate:modelValue":t[4]||(t[4]=a=>d(o).items=a),"empty-item":{type:"img"}},{default:l(({element:a})=>[e(u,{label:"\u7C7B\u578B",prop:"type",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[e(r,{modelValue:a.type,"onUpdate:modelValue":m=>a.type=m},{default:l(()=>[e(s,{value:"img"},{default:l(()=>t[9]||(t[9]=[p("\u56FE\u7247")])),_:1}),e(s,{value:"video"},{default:l(()=>t[10]||(t[10]=[p("\u89C6\u9891")])),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),a.type==="img"?(i(),n(u,{key:0,label:"\u56FE\u7247",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[e(b,{modelValue:a.imgUrl,"onUpdate:modelValue":m=>a.imgUrl=m,draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):(i(),W(G,{key:1},[e(u,{label:"\u5C01\u9762",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[e(b,{modelValue:a.imgUrl,"onUpdate:modelValue":m=>a.imgUrl=m,draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(u,{label:"\u89C6\u9891",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[e(w,{modelValue:a.videoUrl,"onUpdate:modelValue":m=>a.videoUrl=m,"file-type":["mp4"],limit:1,"file-size":100,class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)],64)),e(u,{label:"\u94FE\u63A5",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[e(g,{modelValue:a.url,"onUpdate:modelValue":m=>a.url=m},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{O as default};
