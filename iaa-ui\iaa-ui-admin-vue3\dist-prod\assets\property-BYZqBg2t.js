import{_ as v}from"./ComponentContainerProperty-CMKoqler.js";import{d as R,f5 as T,A as f,o as V,w as t,g as e,s as z,a,k as B,v as I,aB as k,bi as C,cf as P,I as S,i as h,a4 as A,a3 as H,a0 as L,c4 as j,H as D,ca as W}from"./index-CRsFgzy0.js";import{_ as q}from"./index-CGOSLF-t.js";import E from"./SpuShowcase-DtIWpNCA.js";import"./color-CIFUYK2M.js";import"./el-image-BQpHFDaE.js";import"./spu-BHhhuUrI.js";import"./SpuTableSelect.vue_vue_type_script_setup_true_lang-Cuvk6Xh9.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-BFuiZ9pV.js";import"./index.vue_vue_type_script_setup_true_lang-Bx4vFz3w.js";import"./index-CqPfoRkb.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";const F={class:"flex gap-8px"},G={class:"flex gap-8px"},J=R({name:"ProductListProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(b,{emit:w}){const l=T(b,"modelValue",w);return(K,o)=>{const u=B,p=S,m=P,r=C,_=k,s=I,n=q,c=A,g=L,y=j,i=W,U=z,x=v;return V(),f(x,{modelValue:a(l).style,"onUpdate:modelValue":o[11]||(o[11]=d=>a(l).style=d)},{default:t(()=>[e(U,{"label-width":"80px",model:a(l)},{default:t(()=>[e(u,{header:"\u5546\u54C1\u5217\u8868",class:"property-group",shadow:"never"},{default:t(()=>[e(E,{modelValue:a(l).spuIds,"onUpdate:modelValue":o[0]||(o[0]=d=>a(l).spuIds=d)},null,8,["modelValue"])]),_:1}),e(u,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:t(()=>[e(s,{label:"\u5E03\u5C40",prop:"type"},{default:t(()=>[e(_,{modelValue:a(l).layoutType,"onUpdate:modelValue":o[1]||(o[1]=d=>a(l).layoutType=d)},{default:t(()=>[e(r,{class:"item",content:"\u53CC\u5217",placement:"bottom"},{default:t(()=>[e(m,{value:"twoCol"},{default:t(()=>[e(p,{icon:"fluent:text-column-two-24-filled"})]),_:1})]),_:1}),e(r,{class:"item",content:"\u4E09\u5217",placement:"bottom"},{default:t(()=>[e(m,{value:"threeCol"},{default:t(()=>[e(p,{icon:"fluent:text-column-three-24-filled"})]),_:1})]),_:1}),e(r,{class:"item",content:"\u6C34\u5E73\u6ED1\u52A8",placement:"bottom"},{default:t(()=>[e(m,{value:"horizSwiper"},{default:t(()=>[e(p,{icon:"system-uicons:carousel"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"\u5546\u54C1\u540D\u79F0",prop:"fields.name.show"},{default:t(()=>[h("div",F,[e(n,{modelValue:a(l).fields.name.color,"onUpdate:modelValue":o[2]||(o[2]=d=>a(l).fields.name.color=d)},null,8,["modelValue"]),e(c,{modelValue:a(l).fields.name.show,"onUpdate:modelValue":o[3]||(o[3]=d=>a(l).fields.name.show=d)},null,8,["modelValue"])])]),_:1}),e(s,{label:"\u5546\u54C1\u4EF7\u683C",prop:"fields.price.show"},{default:t(()=>[h("div",G,[e(n,{modelValue:a(l).fields.price.color,"onUpdate:modelValue":o[4]||(o[4]=d=>a(l).fields.price.color=d)},null,8,["modelValue"]),e(c,{modelValue:a(l).fields.price.show,"onUpdate:modelValue":o[5]||(o[5]=d=>a(l).fields.price.show=d)},null,8,["modelValue"])])]),_:1})]),_:1}),e(u,{header:"\u89D2\u6807",class:"property-group",shadow:"never"},{default:t(()=>[e(s,{label:"\u89D2\u6807",prop:"badge.show"},{default:t(()=>[e(g,{modelValue:a(l).badge.show,"onUpdate:modelValue":o[6]||(o[6]=d=>a(l).badge.show=d)},null,8,["modelValue"])]),_:1}),a(l).badge.show?(V(),f(s,{key:0,label:"\u89D2\u6807",prop:"badge.imgUrl"},{default:t(()=>[e(y,{modelValue:a(l).badge.imgUrl,"onUpdate:modelValue":o[7]||(o[7]=d=>a(l).badge.imgUrl=d),height:"44px",width:"72px"},{tip:t(()=>o[12]||(o[12]=[D(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A36 * 22 ")])),_:1},8,["modelValue"])]),_:1})):H("",!0)]),_:1}),e(u,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:t(()=>[e(s,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:t(()=>[e(i,{modelValue:a(l).borderRadiusTop,"onUpdate:modelValue":o[8]||(o[8]=d=>a(l).borderRadiusTop=d),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),e(s,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:t(()=>[e(i,{modelValue:a(l).borderRadiusBottom,"onUpdate:modelValue":o[9]||(o[9]=d=>a(l).borderRadiusBottom=d),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),e(s,{label:"\u95F4\u9694",prop:"space"},{default:t(()=>[e(i,{modelValue:a(l).space,"onUpdate:modelValue":o[10]||(o[10]=d=>a(l).space=d),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{J as default};
