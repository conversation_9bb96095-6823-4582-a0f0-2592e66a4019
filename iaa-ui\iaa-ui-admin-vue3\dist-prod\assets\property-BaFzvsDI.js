import{_ as V}from"./ComponentContainerProperty-CMKoqler.js";import{d as f,f5 as _,A as g,o as c,w as m,g as r,s as U,a,v as y,c4 as b,H as h}from"./index-CRsFgzy0.js";import{_ as w}from"./index.vue_vue_type_script_setup_true_lang-DS6I4M_U.js";import"./index-CGOSLF-t.js";import"./color-CIFUYK2M.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CuNmIW-p.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";const x=f({name:"ImageBarProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(t,{emit:d}){const l=_(t,"modelValue",d);return(j,e)=>{const s=b,p=y,u=w,i=U,n=V;return c(),g(n,{modelValue:a(l).style,"onUpdate:modelValue":e[2]||(e[2]=o=>a(l).style=o)},{default:m(()=>[r(i,{"label-width":"80px",model:a(l)},{default:m(()=>[r(p,{label:"\u4E0A\u4F20\u56FE\u7247",prop:"imgUrl"},{default:m(()=>[r(s,{modelValue:a(l).imgUrl,"onUpdate:modelValue":e[0]||(e[0]=o=>a(l).imgUrl=o),draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},{tip:m(()=>e[3]||(e[3]=[h(" \u5EFA\u8BAE\u5BBD\u5EA6750 ")])),_:1},8,["modelValue"])]),_:1}),r(p,{label:"\u94FE\u63A5",prop:"url"},{default:m(()=>[r(u,{modelValue:a(l).url,"onUpdate:modelValue":e[1]||(e[1]=o=>a(l).url=o)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{x as default};
