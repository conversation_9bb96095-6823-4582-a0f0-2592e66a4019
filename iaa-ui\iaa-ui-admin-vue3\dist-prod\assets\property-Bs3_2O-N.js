import{_ as P}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as R,f5 as E,r as I,q as T,A as h,o as i,w as t,g as l,s as z,a as o,k as A,v as H,aB as L,bi as q,cf as F,I as N,i as p,a4 as S,a3 as j,a0 as D,c4 as M,H as f,c as O,F as W,P as X,ca as Y}from"./index-CvERnF9Y.js";import{_ as G}from"./index-sh-B72al.js";import{a as J}from"./combinationActivity-Dvth6Wn-.js";import{C as K}from"./constants-uird_4gU.js";import Q from"./CombinationShowcase-Cu6X-ezA.js";import"./color-CIFUYK2M.js";import"./el-image-DTDUrxnp.js";import"./CombinationTableSelect.vue_vue_type_script_setup_true_lang-ByTtth_L.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import"./index-DHM6tdge.js";import"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import"./tree-COGD3qag.js";import"./category-Cruo05cH.js";import"./formatter-UUK_ohaG.js";import"./formatTime-CmW2_KRq.js";const Z={class:"flex gap-8px"},$={class:"flex gap-8px"},ee={class:"flex gap-8px"},le={class:"flex gap-8px"},ae={class:"flex gap-8px"},oe={class:"flex gap-8px"},de=R({name:"PromotionCombinationProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:U}){const a=E(_,"modelValue",U),x=I([]);return T(async()=>{const{list:w}=await J({status:K.ENABLE});x.value=w}),(w,e)=>{const n=A,V=N,r=F,c=q,g=L,u=H,s=G,m=S,B=D,y=M,v=X,b=Y,C=z,k=P;return i(),h(k,{modelValue:o(a).style,"onUpdate:modelValue":e[24]||(e[24]=d=>o(a).style=d)},{default:t(()=>[l(C,{"label-width":"80px",model:o(a)},{default:t(()=>[l(n,{header:"\u62FC\u56E2\u6D3B\u52A8",class:"property-group",shadow:"never"},{default:t(()=>[l(Q,{modelValue:o(a).activityIds,"onUpdate:modelValue":e[0]||(e[0]=d=>o(a).activityIds=d)},null,8,["modelValue"])]),_:1}),l(n,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:t(()=>[l(u,{label:"\u5E03\u5C40",prop:"type"},{default:t(()=>[l(g,{modelValue:o(a).layoutType,"onUpdate:modelValue":e[1]||(e[1]=d=>o(a).layoutType=d)},{default:t(()=>[l(c,{class:"item",content:"\u5355\u5217\u5927\u56FE",placement:"bottom"},{default:t(()=>[l(r,{value:"oneColBigImg"},{default:t(()=>[l(V,{icon:"fluent:text-column-one-24-filled"})]),_:1})]),_:1}),l(c,{class:"item",content:"\u5355\u5217\u5C0F\u56FE",placement:"bottom"},{default:t(()=>[l(r,{value:"oneColSmallImg"},{default:t(()=>[l(V,{icon:"fluent:text-column-two-left-24-filled"})]),_:1})]),_:1}),l(c,{class:"item",content:"\u53CC\u5217",placement:"bottom"},{default:t(()=>[l(r,{value:"twoCol"},{default:t(()=>[l(V,{icon:"fluent:text-column-two-24-filled"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"\u5546\u54C1\u540D\u79F0",prop:"fields.name.show"},{default:t(()=>[p("div",Z,[l(s,{modelValue:o(a).fields.name.color,"onUpdate:modelValue":e[2]||(e[2]=d=>o(a).fields.name.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.name.show,"onUpdate:modelValue":e[3]||(e[3]=d=>o(a).fields.name.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u7B80\u4ECB",prop:"fields.introduction.show"},{default:t(()=>[p("div",$,[l(s,{modelValue:o(a).fields.introduction.color,"onUpdate:modelValue":e[4]||(e[4]=d=>o(a).fields.introduction.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.introduction.show,"onUpdate:modelValue":e[5]||(e[5]=d=>o(a).fields.introduction.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u4EF7\u683C",prop:"fields.price.show"},{default:t(()=>[p("div",ee,[l(s,{modelValue:o(a).fields.price.color,"onUpdate:modelValue":e[6]||(e[6]=d=>o(a).fields.price.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.price.show,"onUpdate:modelValue":e[7]||(e[7]=d=>o(a).fields.price.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5E02\u573A\u4EF7",prop:"fields.marketPrice.show"},{default:t(()=>[p("div",le,[l(s,{modelValue:o(a).fields.marketPrice.color,"onUpdate:modelValue":e[8]||(e[8]=d=>o(a).fields.marketPrice.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.marketPrice.show,"onUpdate:modelValue":e[9]||(e[9]=d=>o(a).fields.marketPrice.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u9500\u91CF",prop:"fields.salesCount.show"},{default:t(()=>[p("div",ae,[l(s,{modelValue:o(a).fields.salesCount.color,"onUpdate:modelValue":e[10]||(e[10]=d=>o(a).fields.salesCount.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.salesCount.show,"onUpdate:modelValue":e[11]||(e[11]=d=>o(a).fields.salesCount.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u5E93\u5B58",prop:"fields.stock.show"},{default:t(()=>[p("div",oe,[l(s,{modelValue:o(a).fields.stock.color,"onUpdate:modelValue":e[12]||(e[12]=d=>o(a).fields.stock.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.stock.show,"onUpdate:modelValue":e[13]||(e[13]=d=>o(a).fields.stock.show=d)},null,8,["modelValue"])])]),_:1})]),_:1}),l(n,{header:"\u89D2\u6807",class:"property-group",shadow:"never"},{default:t(()=>[l(u,{label:"\u89D2\u6807",prop:"badge.show"},{default:t(()=>[l(B,{modelValue:o(a).badge.show,"onUpdate:modelValue":e[14]||(e[14]=d=>o(a).badge.show=d)},null,8,["modelValue"])]),_:1}),o(a).badge.show?(i(),h(u,{key:0,label:"\u89D2\u6807",prop:"badge.imgUrl"},{default:t(()=>[l(y,{modelValue:o(a).badge.imgUrl,"onUpdate:modelValue":e[15]||(e[15]=d=>o(a).badge.imgUrl=d),height:"44px",width:"72px"},{tip:t(()=>e[25]||(e[25]=[f(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A36 * 22")])),_:1},8,["modelValue"])]),_:1})):j("",!0)]),_:1}),l(n,{header:"\u6309\u94AE",class:"property-group",shadow:"never"},{default:t(()=>[l(u,{label:"\u6309\u94AE\u7C7B\u578B",prop:"btnBuy.type"},{default:t(()=>[l(g,{modelValue:o(a).btnBuy.type,"onUpdate:modelValue":e[16]||(e[16]=d=>o(a).btnBuy.type=d)},{default:t(()=>[l(r,{value:"text"},{default:t(()=>e[26]||(e[26]=[f("\u6587\u5B57")])),_:1}),l(r,{value:"img"},{default:t(()=>e[27]||(e[27]=[f("\u56FE\u7247")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(a).btnBuy.type==="text"?(i(),O(W,{key:0},[l(u,{label:"\u6309\u94AE\u6587\u5B57",prop:"btnBuy.text"},{default:t(()=>[l(v,{modelValue:o(a).btnBuy.text,"onUpdate:modelValue":e[17]||(e[17]=d=>o(a).btnBuy.text=d)},null,8,["modelValue"])]),_:1}),l(u,{label:"\u5DE6\u4FA7\u80CC\u666F",prop:"btnBuy.bgBeginColor"},{default:t(()=>[l(s,{modelValue:o(a).btnBuy.bgBeginColor,"onUpdate:modelValue":e[18]||(e[18]=d=>o(a).btnBuy.bgBeginColor=d)},null,8,["modelValue"])]),_:1}),l(u,{label:"\u53F3\u4FA7\u80CC\u666F",prop:"btnBuy.bgEndColor"},{default:t(()=>[l(s,{modelValue:o(a).btnBuy.bgEndColor,"onUpdate:modelValue":e[19]||(e[19]=d=>o(a).btnBuy.bgEndColor=d)},null,8,["modelValue"])]),_:1})],64)):(i(),h(u,{key:1,label:"\u56FE\u7247",prop:"btnBuy.imgUrl"},{default:t(()=>[l(y,{modelValue:o(a).btnBuy.imgUrl,"onUpdate:modelValue":e[20]||(e[20]=d=>o(a).btnBuy.imgUrl=d),height:"56px",width:"56px"},{tip:t(()=>e[28]||(e[28]=[f(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A56 * 56")])),_:1},8,["modelValue"])]),_:1}))]),_:1}),l(n,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:t(()=>[l(u,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:t(()=>[l(b,{modelValue:o(a).borderRadiusTop,"onUpdate:modelValue":e[21]||(e[21]=d=>o(a).borderRadiusTop=d),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(u,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:t(()=>[l(b,{modelValue:o(a).borderRadiusBottom,"onUpdate:modelValue":e[22]||(e[22]=d=>o(a).borderRadiusBottom=d),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(u,{label:"\u95F4\u9694",prop:"space"},{default:t(()=>[l(b,{modelValue:o(a).space,"onUpdate:modelValue":e[23]||(e[23]=d=>o(a).space=d),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{de as default};
