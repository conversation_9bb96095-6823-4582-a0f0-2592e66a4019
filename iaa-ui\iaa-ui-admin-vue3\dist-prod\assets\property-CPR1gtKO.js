import{_ as j}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as B,f5 as D,r as g,aK as F,c as p,o as d,g as e,w as t,s as G,a as o,k as H,F as v,y as K,dv as T,H as h,t as i,a3 as q,aN as x,v as A,G as J,I as L,aB as M,bi as O,cf as Q,c4 as W,ca as $,m as X}from"./index-CvERnF9Y.js";import{_ as Y}from"./index-sh-B72al.js";import{e as Z}from"./couponTemplate-Cqtr819c.js";import{i as ee,h as le}from"./constants-uird_4gU.js";import{_ as te}from"./CouponSelect.vue_vue_type_script_setup_true_lang-C6ps6WfJ.js";import"./color-CIFUYK2M.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import"./index-DHM6tdge.js";import"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import"./formatter-ByxI31dc.js";import"./formatTime-CmW2_KRq.js";const oe={key:0},ae={key:1},ue={key:2},se=B({name:"CouponCardProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(w,{emit:C}){const a=D(w,"modelValue",C),n=g([]),b=g(),k=()=>{b.value.open()},I=()=>{a.value.couponIds=n.value.map(m=>m.id)};return F(()=>a.value.couponIds,async()=>{var m;((m=a.value.couponIds)==null?void 0:m.length)>0&&(n.value=await Z(a.value.couponIds))},{immediate:!0,deep:!0}),(m,u)=>{const V=T,r=L,U=J,s=A,y=H,c=Q,f=O,P=M,S=W,_=Y,z=$,E=G,N=j;return d(),p(v,null,[e(N,{modelValue:o(a).style,"onUpdate:modelValue":u[6]||(u[6]=l=>o(a).style=l)},{default:t(()=>[e(E,{"label-width":"80px",model:o(a)},{default:t(()=>[e(y,{header:"\u4F18\u60E0\u5238\u5217\u8868",class:"property-group",shadow:"never"},{default:t(()=>[(d(!0),p(v,null,K(o(n),(l,R)=>(d(),p("div",{key:R,class:"flex items-center justify-between"},[e(V,{size:"large",truncated:""},{default:t(()=>[h(i(l.name),1)]),_:2},1024),e(V,{type:"info",truncated:""},{default:t(()=>[l.usePrice>0?(d(),p("span",oe,"\u6EE1"+i(o(x)(l.usePrice))+"\u5143\uFF0C",1)):q("",!0),l.discountType===o(ee).PRICE.type?(d(),p("span",ae," \u51CF"+i(o(x)(l.discountPrice))+"\u5143 ",1)):(d(),p("span",ue," \u6253"+i(l.discountPercent)+"\u6298 ",1))]),_:2},1024)]))),128)),e(s,{"label-width":"0"},{default:t(()=>[e(U,{onClick:k,type:"primary",plain:"",class:"m-t-8px w-full"},{default:t(()=>[e(r,{icon:"ep:plus",class:"mr-5px"}),u[8]||(u[8]=h(" \u6DFB\u52A0 "))]),_:1})]),_:1})]),_:1}),e(y,{header:"\u4F18\u60E0\u5238\u6837\u5F0F",class:"property-group",shadow:"never"},{default:t(()=>[e(s,{label:"\u5217\u6570",prop:"type"},{default:t(()=>[e(P,{modelValue:o(a).columns,"onUpdate:modelValue":u[0]||(u[0]=l=>o(a).columns=l)},{default:t(()=>[e(f,{class:"item",content:"\u4E00\u5217",placement:"bottom"},{default:t(()=>[e(c,{value:1},{default:t(()=>[e(r,{icon:"fluent:text-column-one-24-filled"})]),_:1})]),_:1}),e(f,{class:"item",content:"\u4E8C\u5217",placement:"bottom"},{default:t(()=>[e(c,{value:2},{default:t(()=>[e(r,{icon:"fluent:text-column-two-24-filled"})]),_:1})]),_:1}),e(f,{class:"item",content:"\u4E09\u5217",placement:"bottom"},{default:t(()=>[e(c,{value:3},{default:t(()=>[e(r,{icon:"fluent:text-column-three-24-filled"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(s,{label:"\u80CC\u666F\u56FE\u7247",prop:"bgImg"},{default:t(()=>[e(S,{modelValue:o(a).bgImg,"onUpdate:modelValue":u[1]||(u[1]=l=>o(a).bgImg=l),height:"80px",width:"100%",class:"min-w-160px"},null,8,["modelValue"])]),_:1}),e(s,{label:"\u6587\u5B57\u989C\u8272",prop:"textColor"},{default:t(()=>[e(_,{modelValue:o(a).textColor,"onUpdate:modelValue":u[2]||(u[2]=l=>o(a).textColor=l)},null,8,["modelValue"])]),_:1}),e(s,{label:"\u6309\u94AE\u80CC\u666F",prop:"button.bgColor"},{default:t(()=>[e(_,{modelValue:o(a).button.bgColor,"onUpdate:modelValue":u[3]||(u[3]=l=>o(a).button.bgColor=l)},null,8,["modelValue"])]),_:1}),e(s,{label:"\u6309\u94AE\u6587\u5B57",prop:"button.color"},{default:t(()=>[e(_,{modelValue:o(a).button.color,"onUpdate:modelValue":u[4]||(u[4]=l=>o(a).button.color=l)},null,8,["modelValue"])]),_:1}),e(s,{label:"\u95F4\u9694",prop:"space"},{default:t(()=>[e(z,{modelValue:o(a).space,"onUpdate:modelValue":u[5]||(u[5]=l=>o(a).space=l),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(te,{ref_key:"couponSelectDialog",ref:b,"multiple-selection":o(n),"onUpdate:multipleSelection":u[7]||(u[7]=l=>X(n)?n.value=l:null),"take-type":o(le).USER.type,onChange:I},null,8,["multiple-selection","take-type"])],64)}}});export{se as default};
