import{_ as z}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as v,f5 as T,r as k,A as B,o as d,w as s,g as o,s as j,a,c as i,dv as A,H as f,F as n,y as C,a3 as H,v as $,c4 as F,ca as M}from"./index-CvERnF9Y.js";import{_ as P}from"./index.vue_vue_type_script_setup_true_lang-DbWjkU0t.js";import{_ as S}from"./index-B0Fg6tbh.js";import"./index-sh-B72al.js";import"./color-CIFUYK2M.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-DEmiXCKi.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-Nl_f7Xki.js";import"./el-tree-select-CD6tMKW2.js";import"./tree-COGD3qag.js";import"./category-Cruo05cH.js";const q=v({name:"MagicCubeProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:b}){const t=T(_,"modelValue",b),V=k(-1),U=(h,l)=>{V.value=l};return(h,l)=>{const c=A,w=S,x=F,u=$,y=P,p=M,g=j,R=z;return d(),B(R,{modelValue:a(t).style,"onUpdate:modelValue":l[4]||(l[4]=e=>a(t).style=e)},{default:s(()=>[o(g,{"label-width":"80px",model:a(t),class:"m-t-8px"},{default:s(()=>[o(c,{tag:"p"},{default:s(()=>l[5]||(l[5]=[f(" \u9B54\u65B9\u8BBE\u7F6E ")])),_:1}),o(c,{type:"info",size:"small"},{default:s(()=>l[6]||(l[6]=[f(" \u6BCF\u683C\u5C3A\u5BF8187 * 187 ")])),_:1}),o(w,{class:"m-y-16px",modelValue:a(t).list,"onUpdate:modelValue":l[0]||(l[0]=e=>a(t).list=e),rows:4,cols:4,onHotAreaSelected:U},null,8,["modelValue"]),(d(!0),i(n,null,C(a(t).list,(e,m)=>(d(),i(n,{key:m},[a(V)===m?(d(),i(n,{key:0},[o(u,{label:"\u4E0A\u4F20\u56FE\u7247",prop:`list[${m}].imgUrl`},{default:s(()=>[o(x,{modelValue:e.imgUrl,"onUpdate:modelValue":r=>e.imgUrl=r,height:"80px",width:"80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),o(u,{label:"\u94FE\u63A5",prop:`list[${m}].url`},{default:s(()=>[o(y,{modelValue:e.url,"onUpdate:modelValue":r=>e.url=r},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])],64)):H("",!0)],64))),128)),o(u,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:s(()=>[o(p,{modelValue:a(t).borderRadiusTop,"onUpdate:modelValue":l[1]||(l[1]=e=>a(t).borderRadiusTop=e),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),o(u,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:s(()=>[o(p,{modelValue:a(t).borderRadiusBottom,"onUpdate:modelValue":l[2]||(l[2]=e=>a(t).borderRadiusBottom=e),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),o(u,{label:"\u95F4\u9694",prop:"space"},{default:s(()=>[o(p,{modelValue:a(t).space,"onUpdate:modelValue":l[3]||(l[3]=e=>a(t).space=e),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{q as default};
