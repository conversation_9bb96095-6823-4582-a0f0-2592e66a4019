import{_ as h}from"./ComponentContainerProperty-CMKoqler.js";import{d as v,f5 as C,A as k,o as V,w as a,g as l,s as P,a as d,v as A,aB as B,aC as F,H as p,k as S,aP as T,c as j,a3 as D,c4 as E,a0 as H,F as I}from"./index-CRsFgzy0.js";import{_ as M}from"./index.vue_vue_type_script_setup_true_lang-BEcCiJk8.js";import{_ as Q}from"./index-CGOSLF-t.js";import{_ as q}from"./index.vue_vue_type_script_setup_true_lang-DS6I4M_U.js";import{_ as z}from"./index-D7llcUGC.js";import{b as G}from"./util-DVSby-U6.js";import"./vuedraggable.umd-V1xhRSm3.js";import"./color-CIFUYK2M.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CuNmIW-p.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";import"./Qrcode-CSDarUlq.js";import"./IFrame.vue_vue_type_script_setup_true_lang-CgNW9vNM.js";const J=v({name:"MenuSwiperProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(i,{emit:f}){const u=C(i,"modelValue",f);return(K,o)=>{const r=F,s=B,m=A,_=E,n=z,b=q,U=H,c=Q,g=M,w=S,x=P,y=h;return V(),k(y,{modelValue:d(u).style,"onUpdate:modelValue":o[4]||(o[4]=e=>d(u).style=e)},{default:a(()=>[l(x,{"label-width":"80px",model:d(u),class:"m-t-8px"},{default:a(()=>[l(m,{label:"\u5E03\u5C40",prop:"layout"},{default:a(()=>[l(s,{modelValue:d(u).layout,"onUpdate:modelValue":o[0]||(o[0]=e=>d(u).layout=e)},{default:a(()=>[l(r,{value:"iconText"},{default:a(()=>o[5]||(o[5]=[p("\u56FE\u6807+\u6587\u5B57")])),_:1}),l(r,{value:"icon"},{default:a(()=>o[6]||(o[6]=[p("\u4EC5\u56FE\u6807")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"\u884C\u6570",prop:"row"},{default:a(()=>[l(s,{modelValue:d(u).row,"onUpdate:modelValue":o[1]||(o[1]=e=>d(u).row=e)},{default:a(()=>[l(r,{value:1},{default:a(()=>o[7]||(o[7]=[p("1\u884C")])),_:1}),l(r,{value:2},{default:a(()=>o[8]||(o[8]=[p("2\u884C")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(m,{label:"\u5217\u6570",prop:"column"},{default:a(()=>[l(s,{modelValue:d(u).column,"onUpdate:modelValue":o[2]||(o[2]=e=>d(u).column=e)},{default:a(()=>[l(r,{value:3},{default:a(()=>o[9]||(o[9]=[p("3\u5217")])),_:1}),l(r,{value:4},{default:a(()=>o[10]||(o[10]=[p("4\u5217")])),_:1}),l(r,{value:5},{default:a(()=>o[11]||(o[11]=[p("5\u5217")])),_:1})]),_:1},8,["modelValue"])]),_:1}),l(w,{header:"\u83DC\u5355\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:a(()=>[l(g,{modelValue:d(u).list,"onUpdate:modelValue":o[3]||(o[3]=e=>d(u).list=e),"empty-item":d(T)(d(G))},{default:a(({element:e})=>[l(m,{label:"\u56FE\u6807",prop:"iconUrl"},{default:a(()=>[l(_,{modelValue:e.iconUrl,"onUpdate:modelValue":t=>e.iconUrl=t,height:"80px",width:"80px"},{tip:a(()=>o[12]||(o[12]=[p(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A98 * 98 ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(m,{label:"\u6807\u9898",prop:"title"},{default:a(()=>[l(n,{modelValue:e.title,"onUpdate:modelValue":t=>e.title=t,color:e.titleColor,"onUpdate:color":t=>e.titleColor=t},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1024),l(m,{label:"\u94FE\u63A5",prop:"url"},{default:a(()=>[l(b,{modelValue:e.url,"onUpdate:modelValue":t=>e.url=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l(m,{label:"\u663E\u793A\u89D2\u6807",prop:"badge.show"},{default:a(()=>[l(U,{modelValue:e.badge.show,"onUpdate:modelValue":t=>e.badge.show=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e.badge.show?(V(),j(I,{key:0},[l(m,{label:"\u89D2\u6807\u5185\u5BB9",prop:"badge.text"},{default:a(()=>[l(n,{modelValue:e.badge.text,"onUpdate:modelValue":t=>e.badge.text=t,color:e.badge.textColor,"onUpdate:color":t=>e.badge.textColor=t},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1024),l(m,{label:"\u80CC\u666F\u989C\u8272",prop:"badge.bgColor"},{default:a(()=>[l(c,{modelValue:e.badge.bgColor,"onUpdate:modelValue":t=>e.badge.bgColor=t},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)],64)):D("",!0)]),_:1},8,["modelValue","empty-item"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{J as default};
