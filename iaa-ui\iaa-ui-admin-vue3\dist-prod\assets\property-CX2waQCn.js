import{_ as w}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as y,f5 as v,A as k,o as n,w as a,g as e,s as A,a as u,v as E,aB as F,aC as P,H as p,k as j,c as B,a3 as D,c4 as G,a0 as H,F as I}from"./index-CvERnF9Y.js";import{_ as M}from"./index.vue_vue_type_script_setup_true_lang-BxGOchjX.js";import{_ as Q}from"./index-sh-B72al.js";import{_ as S}from"./index.vue_vue_type_script_setup_true_lang-DbWjkU0t.js";import{_ as T}from"./index-xiFW_pje.js";import{E as q}from"./util-BexthcSe.js";import"./vuedraggable.umd-DQlbMgN-.js";import"./color-CIFUYK2M.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-DEmiXCKi.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-Nl_f7Xki.js";import"./el-tree-select-CD6tMKW2.js";import"./tree-COGD3qag.js";import"./category-Cruo05cH.js";import"./Qrcode-CVwISGR4.js";import"./IFrame.vue_vue_type_script_setup_true_lang-B5-s51Jd.js";const z=y({name:"MenuGridProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(i,{emit:V}){const m=v(i,"modelValue",V);return(J,t)=>{const s=P,b=F,d=E,U=G,r=T,c=S,f=H,_=Q,g=M,C=j,h=A,x=w;return n(),k(x,{modelValue:u(m).style,"onUpdate:modelValue":t[2]||(t[2]=l=>u(m).style=l)},{default:a(()=>[e(h,{"label-width":"80px",model:u(m),class:"m-t-8px"},{default:a(()=>[e(d,{label:"\u6BCF\u884C\u6570\u91CF",prop:"column"},{default:a(()=>[e(b,{modelValue:u(m).column,"onUpdate:modelValue":t[0]||(t[0]=l=>u(m).column=l)},{default:a(()=>[e(s,{value:3},{default:a(()=>t[3]||(t[3]=[p("3\u4E2A")])),_:1}),e(s,{value:4},{default:a(()=>t[4]||(t[4]=[p("4\u4E2A")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e(C,{header:"\u83DC\u5355\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:a(()=>[e(g,{modelValue:u(m).list,"onUpdate:modelValue":t[1]||(t[1]=l=>u(m).list=l),"empty-item":u(q)},{default:a(({element:l})=>[e(d,{label:"\u56FE\u6807",prop:"iconUrl"},{default:a(()=>[e(U,{modelValue:l.iconUrl,"onUpdate:modelValue":o=>l.iconUrl=o,height:"80px",width:"80px"},{tip:a(()=>t[5]||(t[5]=[p(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A44 * 44 ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d,{label:"\u6807\u9898",prop:"title"},{default:a(()=>[e(r,{modelValue:l.title,"onUpdate:modelValue":o=>l.title=o,color:l.titleColor,"onUpdate:color":o=>l.titleColor=o},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1024),e(d,{label:"\u526F\u6807\u9898",prop:"subtitle"},{default:a(()=>[e(r,{modelValue:l.subtitle,"onUpdate:modelValue":o=>l.subtitle=o,color:l.subtitleColor,"onUpdate:color":o=>l.subtitleColor=o},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1024),e(d,{label:"\u94FE\u63A5",prop:"url"},{default:a(()=>[e(c,{modelValue:l.url,"onUpdate:modelValue":o=>l.url=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(d,{label:"\u663E\u793A\u89D2\u6807",prop:"badge.show"},{default:a(()=>[e(f,{modelValue:l.badge.show,"onUpdate:modelValue":o=>l.badge.show=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),l.badge.show?(n(),B(I,{key:0},[e(d,{label:"\u89D2\u6807\u5185\u5BB9",prop:"badge.text"},{default:a(()=>[e(r,{modelValue:l.badge.text,"onUpdate:modelValue":o=>l.badge.text=o,color:l.badge.textColor,"onUpdate:color":o=>l.badge.textColor=o},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1024),e(d,{label:"\u80CC\u666F\u989C\u8272",prop:"badge.bgColor"},{default:a(()=>[e(_,{modelValue:l.badge.bgColor,"onUpdate:modelValue":o=>l.badge.bgColor=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)],64)):D("",!0)]),_:1},8,["modelValue","empty-item"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{z as default};
