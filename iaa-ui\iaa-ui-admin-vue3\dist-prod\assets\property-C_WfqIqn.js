import{_ as U}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as x,f5 as v,A as C,o as P,w as l,g as e,s as k,a,k as K,P as S,v as R,aB as z,bi as A,cf as j,I as B,a0 as I,ca as q}from"./index-CvERnF9Y.js";import{_ as D}from"./index-sh-B72al.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang-BxGOchjX.js";import"./color-CIFUYK2M.js";import"./vuedraggable.umd-DQlbMgN-.js";const F=x({name:"SearchProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(V,{emit:f}){const o=v(V,"modelValue",f);return(G,d)=>{const m=S,h=E,s=K,n=B,r=j,p=A,i=z,u=R,_=I,b=q,c=D,g=k,w=U;return P(),C(w,{modelValue:a(o).style,"onUpdate:modelValue":d[8]||(d[8]=t=>a(o).style=t)},{default:l(()=>[e(g,{"label-width":"80px",model:a(o),class:"m-t-8px"},{default:l(()=>[e(s,{header:"\u641C\u7D22\u70ED\u8BCD",class:"property-group",shadow:"never"},{default:l(()=>[e(h,{modelValue:a(o).hotKeywords,"onUpdate:modelValue":d[0]||(d[0]=t=>a(o).hotKeywords=t),"empty-item":""},{default:l(({index:t})=>[e(m,{modelValue:a(o).hotKeywords[t],"onUpdate:modelValue":y=>a(o).hotKeywords[t]=y,placeholder:"\u8BF7\u8F93\u5165\u70ED\u8BCD"},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["modelValue"])]),_:1}),e(s,{header:"\u641C\u7D22\u6837\u5F0F",class:"property-group",shadow:"never"},{default:l(()=>[e(u,{label:"\u6846\u4F53\u6837\u5F0F"},{default:l(()=>[e(i,{modelValue:a(o).borderRadius,"onUpdate:modelValue":d[1]||(d[1]=t=>a(o).borderRadius=t)},{default:l(()=>[e(p,{content:"\u65B9\u5F62",placement:"top"},{default:l(()=>[e(r,{value:0},{default:l(()=>[e(n,{icon:"tabler:input-search"})]),_:1})]),_:1}),e(p,{content:"\u5706\u5F62",placement:"top"},{default:l(()=>[e(r,{value:10},{default:l(()=>[e(n,{icon:"iconoir:input-search"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u63D0\u793A\u6587\u5B57",prop:"placeholder"},{default:l(()=>[e(m,{modelValue:a(o).placeholder,"onUpdate:modelValue":d[2]||(d[2]=t=>a(o).placeholder=t)},null,8,["modelValue"])]),_:1}),e(u,{label:"\u6587\u672C\u4F4D\u7F6E",prop:"placeholderPosition"},{default:l(()=>[e(i,{modelValue:a(o).placeholderPosition,"onUpdate:modelValue":d[3]||(d[3]=t=>a(o).placeholderPosition=t)},{default:l(()=>[e(p,{content:"\u5C45\u5DE6",placement:"top"},{default:l(()=>[e(r,{value:"left"},{default:l(()=>[e(n,{icon:"ant-design:align-left-outlined"})]),_:1})]),_:1}),e(p,{content:"\u5C45\u4E2D",placement:"top"},{default:l(()=>[e(r,{value:"center"},{default:l(()=>[e(n,{icon:"ant-design:align-center-outlined"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u626B\u4E00\u626B",prop:"showScan"},{default:l(()=>[e(_,{modelValue:a(o).showScan,"onUpdate:modelValue":d[4]||(d[4]=t=>a(o).showScan=t)},null,8,["modelValue"])]),_:1}),e(u,{label:"\u6846\u4F53\u9AD8\u5EA6",prop:"height"},{default:l(()=>[e(b,{modelValue:a(o).height,"onUpdate:modelValue":d[5]||(d[5]=t=>a(o).height=t),max:50,min:28,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u6846\u4F53\u989C\u8272",prop:"backgroundColor"},{default:l(()=>[e(c,{modelValue:a(o).backgroundColor,"onUpdate:modelValue":d[6]||(d[6]=t=>a(o).backgroundColor=t)},null,8,["modelValue"])]),_:1}),e(u,{class:"lef",label:"\u6587\u672C\u989C\u8272",prop:"textColor"},{default:l(()=>[e(c,{modelValue:a(o).textColor,"onUpdate:modelValue":d[7]||(d[7]=t=>a(o).textColor=t)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{F as default};
