import{_ as z}from"./ComponentContainerProperty-CMKoqler.js";import{d as C,f5 as S,A as W,o as h,w as d,g as e,s as A,a as o,k,v as B,aB as P,bi as F,cf as H,I,ca as J,c as T,a3 as j,a4 as D,F as E,J as L,aC as R,H as m,P as q,a6 as G}from"./index-CRsFgzy0.js";import{_ as K}from"./index.vue_vue_type_script_setup_true_lang-DS6I4M_U.js";import{_ as M}from"./index-D7llcUGC.js";import"./index-CGOSLF-t.js";import"./color-CIFUYK2M.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CuNmIW-p.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";const N=C({name:"TitleBarProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:w}){const a=S(_,"modelValue",w),x={};return(O,l)=>{const n=I,s=H,V=F,c=P,p=B,u=k,f=M,i=J,g=D,r=R,b=q,U=K,y=A,v=z;return h(),W(v,{modelValue:o(a).style,"onUpdate:modelValue":l[13]||(l[13]=t=>o(a).style=t)},{default:d(()=>[e(y,{model:o(a),rules:x,"label-width":"85px"},{default:d(()=>[e(u,{class:"property-group",header:"\u98CE\u683C",shadow:"never"},{default:d(()=>[e(p,{label:"\u6807\u9898\u4F4D\u7F6E",prop:"textAlign"},{default:d(()=>[e(c,{modelValue:o(a).textAlign,"onUpdate:modelValue":l[0]||(l[0]=t=>o(a).textAlign=t)},{default:d(()=>[e(V,{content:"\u5C45\u5DE6",placement:"top"},{default:d(()=>[e(s,{value:"left"},{default:d(()=>[e(n,{icon:"ant-design:align-left-outlined"})]),_:1})]),_:1}),e(V,{content:"\u5C45\u4E2D",placement:"top"},{default:d(()=>[e(s,{value:"center"},{default:d(()=>[e(n,{icon:"ant-design:align-center-outlined"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{class:"property-group",header:"\u4E3B\u6807\u9898",shadow:"never"},{default:d(()=>[e(p,{label:"\u6587\u5B57","label-width":"40px",prop:"title"},{default:d(()=>[e(f,{modelValue:o(a).title,"onUpdate:modelValue":l[1]||(l[1]=t=>o(a).title=t),color:o(a).titleColor,"onUpdate:color":l[2]||(l[2]=t=>o(a).titleColor=t),maxlength:"20","show-word-limit":""},null,8,["modelValue","color"])]),_:1}),e(p,{label:"\u5927\u5C0F","label-width":"40px",prop:"titleSize"},{default:d(()=>[e(i,{modelValue:o(a).titleSize,"onUpdate:modelValue":l[3]||(l[3]=t=>o(a).titleSize=t),max:60,min:10,"input-size":"small","show-input":""},null,8,["modelValue"])]),_:1}),e(p,{label:"\u7C97\u7EC6","label-width":"40px",prop:"titleWeight"},{default:d(()=>[e(i,{modelValue:o(a).titleWeight,"onUpdate:modelValue":l[4]||(l[4]=t=>o(a).titleWeight=t),max:900,min:100,step:100,"input-size":"small","show-input":""},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{class:"property-group",header:"\u526F\u6807\u9898",shadow:"never"},{default:d(()=>[e(p,{label:"\u6587\u5B57","label-width":"40px",prop:"description"},{default:d(()=>[e(f,{modelValue:o(a).description,"onUpdate:modelValue":l[5]||(l[5]=t=>o(a).description=t),color:o(a).descriptionColor,"onUpdate:color":l[6]||(l[6]=t=>o(a).descriptionColor=t),maxlength:"50","show-word-limit":""},null,8,["modelValue","color"])]),_:1}),e(p,{label:"\u5927\u5C0F","label-width":"40px",prop:"descriptionSize"},{default:d(()=>[e(i,{modelValue:o(a).descriptionSize,"onUpdate:modelValue":l[7]||(l[7]=t=>o(a).descriptionSize=t),max:60,min:10,"input-size":"small","show-input":""},null,8,["modelValue"])]),_:1}),e(p,{label:"\u7C97\u7EC6","label-width":"40px",prop:"descriptionWeight"},{default:d(()=>[e(i,{modelValue:o(a).descriptionWeight,"onUpdate:modelValue":l[8]||(l[8]=t=>o(a).descriptionWeight=t),max:900,min:100,step:100,"input-size":"small","show-input":""},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{class:"property-group",header:"\u67E5\u770B\u66F4\u591A",shadow:"never"},{default:d(()=>[e(p,{label:"\u662F\u5426\u663E\u793A",prop:"more.show"},{default:d(()=>[e(g,{modelValue:o(a).more.show,"onUpdate:modelValue":l[9]||(l[9]=t=>o(a).more.show=t)},null,8,["modelValue"])]),_:1}),o(a).more.show?(h(),T(E,{key:0},[e(p,{label:"\u6837\u5F0F",prop:"more.type"},{default:d(()=>[e(c,{modelValue:o(a).more.type,"onUpdate:modelValue":l[10]||(l[10]=t=>o(a).more.type=t)},{default:d(()=>[e(r,{value:"text"},{default:d(()=>l[14]||(l[14]=[m("\u6587\u5B57")])),_:1}),e(r,{value:"icon"},{default:d(()=>l[15]||(l[15]=[m("\u56FE\u6807")])),_:1}),e(r,{value:"all"},{default:d(()=>l[16]||(l[16]=[m("\u6587\u5B57+\u56FE\u6807")])),_:1})]),_:1},8,["modelValue"])]),_:1}),L(e(p,{label:"\u66F4\u591A\u6587\u5B57",prop:"more.text"},{default:d(()=>[e(b,{modelValue:o(a).more.text,"onUpdate:modelValue":l[11]||(l[11]=t=>o(a).more.text=t)},null,8,["modelValue"])]),_:1},512),[[G,o(a).more.type!=="icon"]]),e(p,{label:"\u8DF3\u8F6C\u94FE\u63A5",prop:"more.url"},{default:d(()=>[e(U,{modelValue:o(a).more.url,"onUpdate:modelValue":l[12]||(l[12]=t=>o(a).more.url=t)},null,8,["modelValue"])]),_:1})],64)):j("",!0)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{N as default};
