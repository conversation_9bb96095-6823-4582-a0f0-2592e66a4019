import{_ as y}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as h,f5 as U,A as _,o as c,w as s,g as t,s as g,a,v as w,c2 as x,c4 as b,H as v,a0 as z,ca as P}from"./index-CvERnF9Y.js";import"./index-sh-B72al.js";import"./color-CIFUYK2M.js";const A=h({name:"VideoPlayerProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(p,{emit:u}){const l=U(p,"modelValue",u);return(H,e)=>{const m=P,d=w,r=x,i=b,n=z,V=g,f=y;return c(),_(f,{modelValue:a(l).style,"onUpdate:modelValue":e[4]||(e[4]=o=>a(l).style=o)},{style:s(()=>[t(d,{label:"\u9AD8\u5EA6",prop:"height"},{default:s(()=>[t(m,{modelValue:a(l).style.height,"onUpdate:modelValue":e[0]||(e[0]=o=>a(l).style.height=o),max:500,min:100,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),default:s(()=>[t(V,{"label-width":"80px",model:a(l)},{default:s(()=>[t(d,{label:"\u4E0A\u4F20\u89C6\u9891",prop:"videoUrl"},{default:s(()=>[t(r,{modelValue:a(l).videoUrl,"onUpdate:modelValue":e[1]||(e[1]=o=>a(l).videoUrl=o),"file-type":["mp4"],limit:1,"file-size":100,class:"min-w-80px"},null,8,["modelValue"])]),_:1}),t(d,{label:"\u4E0A\u4F20\u5C01\u9762",prop:"posterUrl"},{default:s(()=>[t(i,{modelValue:a(l).posterUrl,"onUpdate:modelValue":e[2]||(e[2]=o=>a(l).posterUrl=o),draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},{tip:s(()=>e[5]||(e[5]=[v(" \u5EFA\u8BAE\u5BBD\u5EA6750 ")])),_:1},8,["modelValue"])]),_:1}),t(d,{label:"\u81EA\u52A8\u64AD\u653E",prop:"autoplay"},{default:s(()=>[t(n,{modelValue:a(l).autoplay,"onUpdate:modelValue":e[3]||(e[3]=o=>a(l).autoplay=o)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{A as default};
