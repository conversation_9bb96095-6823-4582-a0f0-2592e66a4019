import{_ as s}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as p,f5 as u,A as d,o as n,a as t}from"./index-CvERnF9Y.js";import"./index-sh-B72al.js";import"./color-CIFUYK2M.js";const i=p({name:"UserOrderProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(a,{emit:l}){const e=u(a,"modelValue",l);return(V,o)=>{const r=s;return n(),d(r,{modelValue:t(e).style,"onUpdate:modelValue":o[0]||(o[0]=m=>t(e).style=m)},null,8,["modelValue"])}}});export{i as default};
