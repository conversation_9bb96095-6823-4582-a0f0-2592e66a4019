import{d as w,f5 as b,A as v,o as y,w as l,g as e,k as C,v as T,aB as $,a as r,aC as k,H as i,a0 as A,c4 as B,s as j}from"./index-CRsFgzy0.js";import{_ as z}from"./index.vue_vue_type_script_setup_true_lang-BEcCiJk8.js";import{_ as F}from"./index.vue_vue_type_script_setup_true_lang-DS6I4M_U.js";import{_ as H}from"./index-D7llcUGC.js";import"./vuedraggable.umd-V1xhRSm3.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CuNmIW-p.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";import"./color-CIFUYK2M.js";const P=w({name:"FloatingActionButtonProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(n,{emit:V}){const t=b(n,"modelValue",V);return(q,a)=>{const m=k,f=$,p=T,_=A,s=C,c=B,x=H,U=F,h=z,g=j;return y(),v(g,{"label-width":"80px",model:r(t)},{default:l(()=>[e(s,{header:"\u6309\u94AE\u914D\u7F6E",class:"property-group",shadow:"never"},{default:l(()=>[e(p,{label:"\u5C55\u5F00\u65B9\u5411",prop:"direction"},{default:l(()=>[e(f,{modelValue:r(t).direction,"onUpdate:modelValue":a[0]||(a[0]=o=>r(t).direction=o)},{default:l(()=>[e(m,{value:"vertical"},{default:l(()=>a[3]||(a[3]=[i("\u5782\u76F4")])),_:1}),e(m,{value:"horizontal"},{default:l(()=>a[4]||(a[4]=[i("\u6C34\u5E73")])),_:1})]),_:1},8,["modelValue"])]),_:1}),e(p,{label:"\u663E\u793A\u6587\u5B57",prop:"showText"},{default:l(()=>[e(_,{modelValue:r(t).showText,"onUpdate:modelValue":a[1]||(a[1]=o=>r(t).showText=o)},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{header:"\u6309\u94AE\u5217\u8868",class:"property-group",shadow:"never"},{default:l(()=>[e(h,{modelValue:r(t).list,"onUpdate:modelValue":a[2]||(a[2]=o=>r(t).list=o),"empty-item":{textColor:"#fff"}},{default:l(({element:o,index:u})=>[e(p,{label:"\u56FE\u6807",prop:`list[${u}].imgUrl`},{default:l(()=>[e(c,{modelValue:o.imgUrl,"onUpdate:modelValue":d=>o.imgUrl=d,height:"56px",width:"56px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),e(p,{label:"\u6587\u5B57",prop:`list[${u}].text`},{default:l(()=>[e(x,{modelValue:o.text,"onUpdate:modelValue":d=>o.text=d,color:o.textColor,"onUpdate:color":d=>o.textColor=d},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1032,["prop"]),e(p,{label:"\u8DF3\u8F6C\u94FE\u63A5",prop:`list[${u}].url`},{default:l(()=>[e(U,{modelValue:o.url,"onUpdate:modelValue":d=>o.url=d},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])}}});export{P as default};
