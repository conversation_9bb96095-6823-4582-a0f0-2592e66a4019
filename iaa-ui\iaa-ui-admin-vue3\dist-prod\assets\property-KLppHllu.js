import{_ as m}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as p,f5 as u,A as d,o as n,a}from"./index-CvERnF9Y.js";import"./index-sh-B72al.js";import"./color-CIFUYK2M.js";const i=p({name:"UserWalletProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(t,{emit:l}){const e=u(t,"modelValue",l);return(V,o)=>{const r=m;return n(),d(r,{modelValue:a(e).style,"onUpdate:modelValue":o[0]||(o[0]=s=>a(e).style=s)},null,8,["modelValue"])}}});export{i as default};
