import{_ as b}from"./ComponentContainerProperty-CMKoqler.js";import{d as y,f5 as x,A as C,o as g,w as a,g as e,dv as h,H as r,s as v,a as m,v as w,c4 as j}from"./index-CRsFgzy0.js";import{_ as k}from"./index.vue_vue_type_script_setup_true_lang-BEcCiJk8.js";import{_ as z}from"./index.vue_vue_type_script_setup_true_lang-DS6I4M_U.js";import{_ as A}from"./index-D7llcUGC.js";import{a as H}from"./util-DVSby-U6.js";import"./index-CGOSLF-t.js";import"./color-CIFUYK2M.js";import"./vuedraggable.umd-V1xhRSm3.js";import"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CuNmIW-p.js";import"./Dialog.vue_vue_type_style_index_0_lang-D4U6HRVJ.js";import"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-DdloU4n0.js";import"./el-tree-select-BijZG_HG.js";import"./tree-COGD3qag.js";import"./category--cl9fhwU.js";import"./Qrcode-CSDarUlq.js";import"./IFrame.vue_vue_type_script_setup_true_lang-CgNW9vNM.js";const L=y({name:"MenuListProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(s,{emit:n}){const d=x(s,"modelValue",n);return(M,t)=>{const u=h,V=j,p=w,i=A,f=z,U=k,c=v,_=b;return g(),C(_,{modelValue:m(d).style,"onUpdate:modelValue":t[1]||(t[1]=l=>m(d).style=l)},{default:a(()=>[e(u,{tag:"p"},{default:a(()=>t[2]||(t[2]=[r(" \u83DC\u5355\u8BBE\u7F6E ")])),_:1}),e(u,{type:"info",size:"small"},{default:a(()=>t[3]||(t[3]=[r(" \u62D6\u52A8\u5DE6\u4FA7\u7684\u5C0F\u5706\u70B9\u53EF\u4EE5\u8C03\u6574\u987A\u5E8F ")])),_:1}),e(c,{"label-width":"60px",model:m(d),class:"m-t-8px"},{default:a(()=>[e(U,{modelValue:m(d).list,"onUpdate:modelValue":t[0]||(t[0]=l=>m(d).list=l),"empty-item":m(H)},{default:a(({element:l})=>[e(p,{label:"\u56FE\u6807",prop:"iconUrl"},{default:a(()=>[e(V,{modelValue:l.iconUrl,"onUpdate:modelValue":o=>l.iconUrl=o,height:"80px",width:"80px"},{tip:a(()=>t[4]||(t[4]=[r(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A44 * 44 ")])),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),e(p,{label:"\u6807\u9898",prop:"title"},{default:a(()=>[e(i,{modelValue:l.title,"onUpdate:modelValue":o=>l.title=o,color:l.titleColor,"onUpdate:color":o=>l.titleColor=o},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1024),e(p,{label:"\u526F\u6807\u9898",prop:"subtitle"},{default:a(()=>[e(i,{modelValue:l.subtitle,"onUpdate:modelValue":o=>l.subtitle=o,color:l.subtitleColor,"onUpdate:color":o=>l.subtitleColor=o},null,8,["modelValue","onUpdate:modelValue","color","onUpdate:color"])]),_:2},1024),e(p,{label:"\u94FE\u63A5",prop:"url"},{default:a(()=>[e(f,{modelValue:l.url,"onUpdate:modelValue":o=>l.url=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1},8,["modelValue","empty-item"])]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{L as default};
