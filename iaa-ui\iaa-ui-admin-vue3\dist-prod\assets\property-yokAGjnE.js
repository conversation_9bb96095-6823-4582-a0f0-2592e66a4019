import{_ as k}from"./ComponentContainerProperty-DD2IzOEg.js";import{d as C,f5 as P,A as h,o as i,w as t,g as l,s as R,a as o,k as I,v as T,aB as z,bi as E,cf as S,I as A,i as p,a4 as F,a3 as H,a0 as j,c4 as q,H as f,c as D,F as G,P as J,ca as M}from"./index-CvERnF9Y.js";import{_ as N}from"./index-sh-B72al.js";import O from"./PointShowcase-VKXvnBCk.js";import"./color-CIFUYK2M.js";import"./el-image-DTDUrxnp.js";import"./PointTableSelect.vue_vue_type_script_setup_true_lang-CmVTV46c.js";import"./Dialog.vue_vue_type_style_index_0_lang-BPgXY6G0.js";import"./ContentWrap.vue_vue_type_script_setup_true_lang-C0yuU9BO.js";import"./index.vue_vue_type_script_setup_true_lang-BMiFeSUs.js";import"./index-DHM6tdge.js";import"./DictTag.vue_vue_type_script_lang-DMA1PnYw.js";import"./index-D-8joCbL.js";import"./formatter-UUK_ohaG.js";import"./formatTime-CmW2_KRq.js";const W={class:"flex gap-8px"},$={class:"flex gap-8px"},K={class:"flex gap-8px"},L={class:"flex gap-8px"},Q={class:"flex gap-8px"},X={class:"flex gap-8px"},Y=C({name:"PromotionPointProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:y}){const a=P(_,"modelValue",y);return(Z,e)=>{const n=I,V=A,r=S,c=E,g=z,u=T,s=N,m=F,U=j,w=q,x=J,b=M,B=R,v=k;return i(),h(v,{modelValue:o(a).style,"onUpdate:modelValue":e[24]||(e[24]=d=>o(a).style=d)},{default:t(()=>[l(B,{model:o(a),"label-width":"80px"},{default:t(()=>[l(n,{class:"property-group",header:"\u79EF\u5206\u5546\u57CE\u6D3B\u52A8",shadow:"never"},{default:t(()=>[l(O,{modelValue:o(a).activityIds,"onUpdate:modelValue":e[0]||(e[0]=d=>o(a).activityIds=d)},null,8,["modelValue"])]),_:1}),l(n,{class:"property-group",header:"\u5546\u54C1\u6837\u5F0F",shadow:"never"},{default:t(()=>[l(u,{label:"\u5E03\u5C40",prop:"type"},{default:t(()=>[l(g,{modelValue:o(a).layoutType,"onUpdate:modelValue":e[1]||(e[1]=d=>o(a).layoutType=d)},{default:t(()=>[l(c,{class:"item",content:"\u5355\u5217\u5927\u56FE",placement:"bottom"},{default:t(()=>[l(r,{value:"oneColBigImg"},{default:t(()=>[l(V,{icon:"fluent:text-column-one-24-filled"})]),_:1})]),_:1}),l(c,{class:"item",content:"\u5355\u5217\u5C0F\u56FE",placement:"bottom"},{default:t(()=>[l(r,{value:"oneColSmallImg"},{default:t(()=>[l(V,{icon:"fluent:text-column-two-left-24-filled"})]),_:1})]),_:1}),l(c,{class:"item",content:"\u53CC\u5217",placement:"bottom"},{default:t(()=>[l(r,{value:"twoCol"},{default:t(()=>[l(V,{icon:"fluent:text-column-two-24-filled"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"\u5546\u54C1\u540D\u79F0",prop:"fields.name.show"},{default:t(()=>[p("div",W,[l(s,{modelValue:o(a).fields.name.color,"onUpdate:modelValue":e[2]||(e[2]=d=>o(a).fields.name.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.name.show,"onUpdate:modelValue":e[3]||(e[3]=d=>o(a).fields.name.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u7B80\u4ECB",prop:"fields.introduction.show"},{default:t(()=>[p("div",$,[l(s,{modelValue:o(a).fields.introduction.color,"onUpdate:modelValue":e[4]||(e[4]=d=>o(a).fields.introduction.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.introduction.show,"onUpdate:modelValue":e[5]||(e[5]=d=>o(a).fields.introduction.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u4EF7\u683C",prop:"fields.price.show"},{default:t(()=>[p("div",K,[l(s,{modelValue:o(a).fields.price.color,"onUpdate:modelValue":e[6]||(e[6]=d=>o(a).fields.price.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.price.show,"onUpdate:modelValue":e[7]||(e[7]=d=>o(a).fields.price.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5E02\u573A\u4EF7",prop:"fields.marketPrice.show"},{default:t(()=>[p("div",L,[l(s,{modelValue:o(a).fields.marketPrice.color,"onUpdate:modelValue":e[8]||(e[8]=d=>o(a).fields.marketPrice.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.marketPrice.show,"onUpdate:modelValue":e[9]||(e[9]=d=>o(a).fields.marketPrice.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u9500\u91CF",prop:"fields.salesCount.show"},{default:t(()=>[p("div",Q,[l(s,{modelValue:o(a).fields.salesCount.color,"onUpdate:modelValue":e[10]||(e[10]=d=>o(a).fields.salesCount.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.salesCount.show,"onUpdate:modelValue":e[11]||(e[11]=d=>o(a).fields.salesCount.show=d)},null,8,["modelValue"])])]),_:1}),l(u,{label:"\u5546\u54C1\u5E93\u5B58",prop:"fields.stock.show"},{default:t(()=>[p("div",X,[l(s,{modelValue:o(a).fields.stock.color,"onUpdate:modelValue":e[12]||(e[12]=d=>o(a).fields.stock.color=d)},null,8,["modelValue"]),l(m,{modelValue:o(a).fields.stock.show,"onUpdate:modelValue":e[13]||(e[13]=d=>o(a).fields.stock.show=d)},null,8,["modelValue"])])]),_:1})]),_:1}),l(n,{class:"property-group",header:"\u89D2\u6807",shadow:"never"},{default:t(()=>[l(u,{label:"\u89D2\u6807",prop:"badge.show"},{default:t(()=>[l(U,{modelValue:o(a).badge.show,"onUpdate:modelValue":e[14]||(e[14]=d=>o(a).badge.show=d)},null,8,["modelValue"])]),_:1}),o(a).badge.show?(i(),h(u,{key:0,label:"\u89D2\u6807",prop:"badge.imgUrl"},{default:t(()=>[l(w,{modelValue:o(a).badge.imgUrl,"onUpdate:modelValue":e[15]||(e[15]=d=>o(a).badge.imgUrl=d),height:"44px",width:"72px"},{tip:t(()=>e[25]||(e[25]=[f(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A36 * 22")])),_:1},8,["modelValue"])]),_:1})):H("",!0)]),_:1}),l(n,{class:"property-group",header:"\u6309\u94AE",shadow:"never"},{default:t(()=>[l(u,{label:"\u6309\u94AE\u7C7B\u578B",prop:"btnBuy.type"},{default:t(()=>[l(g,{modelValue:o(a).btnBuy.type,"onUpdate:modelValue":e[16]||(e[16]=d=>o(a).btnBuy.type=d)},{default:t(()=>[l(r,{value:"text"},{default:t(()=>e[26]||(e[26]=[f("\u6587\u5B57")])),_:1}),l(r,{value:"img"},{default:t(()=>e[27]||(e[27]=[f("\u56FE\u7247")])),_:1})]),_:1},8,["modelValue"])]),_:1}),o(a).btnBuy.type==="text"?(i(),D(G,{key:0},[l(u,{label:"\u6309\u94AE\u6587\u5B57",prop:"btnBuy.text"},{default:t(()=>[l(x,{modelValue:o(a).btnBuy.text,"onUpdate:modelValue":e[17]||(e[17]=d=>o(a).btnBuy.text=d)},null,8,["modelValue"])]),_:1}),l(u,{label:"\u5DE6\u4FA7\u80CC\u666F",prop:"btnBuy.bgBeginColor"},{default:t(()=>[l(s,{modelValue:o(a).btnBuy.bgBeginColor,"onUpdate:modelValue":e[18]||(e[18]=d=>o(a).btnBuy.bgBeginColor=d)},null,8,["modelValue"])]),_:1}),l(u,{label:"\u53F3\u4FA7\u80CC\u666F",prop:"btnBuy.bgEndColor"},{default:t(()=>[l(s,{modelValue:o(a).btnBuy.bgEndColor,"onUpdate:modelValue":e[19]||(e[19]=d=>o(a).btnBuy.bgEndColor=d)},null,8,["modelValue"])]),_:1})],64)):(i(),h(u,{key:1,label:"\u56FE\u7247",prop:"btnBuy.imgUrl"},{default:t(()=>[l(w,{modelValue:o(a).btnBuy.imgUrl,"onUpdate:modelValue":e[20]||(e[20]=d=>o(a).btnBuy.imgUrl=d),height:"56px",width:"56px"},{tip:t(()=>e[28]||(e[28]=[f(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A56 * 56")])),_:1},8,["modelValue"])]),_:1}))]),_:1}),l(n,{class:"property-group",header:"\u5546\u54C1\u6837\u5F0F",shadow:"never"},{default:t(()=>[l(u,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:t(()=>[l(b,{modelValue:o(a).borderRadiusTop,"onUpdate:modelValue":e[21]||(e[21]=d=>o(a).borderRadiusTop=d),max:100,min:0,"show-input-controls":!1,"input-size":"small","show-input":""},null,8,["modelValue"])]),_:1}),l(u,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:t(()=>[l(b,{modelValue:o(a).borderRadiusBottom,"onUpdate:modelValue":e[22]||(e[22]=d=>o(a).borderRadiusBottom=d),max:100,min:0,"show-input-controls":!1,"input-size":"small","show-input":""},null,8,["modelValue"])]),_:1}),l(u,{label:"\u95F4\u9694",prop:"space"},{default:t(()=>[l(b,{modelValue:o(a).space,"onUpdate:modelValue":e[23]||(e[23]=d=>o(a).space=d),max:100,min:0,"show-input-controls":!1,"input-size":"small","show-input":""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}});export{Y as default};
