import{dr as b}from"./index-CRsFgzy0.js";var B={exports:{}};const N=b(B.exports=function(d,w,i){d=d||{};var e=w.prototype,y={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function p(r,t,a,f){return e.fromToBase(r,t,a,f)}i.en.relativeTime=y,e.fromToBase=function(r,t,a,f,T){for(var h,u,m,l=a.$locale().relativeTime||y,c=d.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],x=c.length,s=0;s<x;s+=1){var n=c[s];n.d&&(h=f?i(r).diff(a,n.d,!0):a.diff(r,n.d,!0));var o=(d.rounding||Math.round)(Math.abs(h));if(m=h>0,o<=n.r||!n.r){o<=1&&s>0&&(n=c[s-1]);var M=l[n.l];T&&(o=T(""+o)),u=typeof M=="string"?M.replace("%d",o):M(o,t,n.l,m);break}}if(t)return u;var v=m?l.future:l.past;return typeof v=="function"?v(u):v.replace("%s",u)},e.to=function(r,t){return p(r,t,this,!0)},e.from=function(r,t){return p(r,t,this)};var g=function(r){return r.$u?i.utc():i()};e.toNow=function(r){return this.to(g(this),r)},e.fromNow=function(r){return this.from(g(this),r)}});export{N as r};
