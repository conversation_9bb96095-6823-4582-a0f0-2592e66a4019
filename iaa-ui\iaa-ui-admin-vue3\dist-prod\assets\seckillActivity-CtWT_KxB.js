import{as as t}from"./index-CvERnF9Y.js";const i=async a=>await t.get({url:"/promotion/seckill-activity/page",params:a}),s=a=>t.get({url:`/promotion/seckill-activity/list-by-ids?ids=${a}`}),o=async a=>await t.get({url:"/promotion/seckill-activity/get?id="+a}),l=async a=>await t.post({url:"/promotion/seckill-activity/create",data:a}),c=async a=>await t.put({url:"/promotion/seckill-activity/update",data:a}),e=async a=>await t.put({url:"/promotion/seckill-activity/close?id="+a}),r=async a=>await t.delete({url:"/promotion/seckill-activity/delete?id="+a});export{i as a,e as b,l as c,r as d,s as e,o as g,c as u};
