import{as as t}from"./index-CvERnF9Y.js";const o={getSeckillConfigPage:async i=>await t.get({url:"/promotion/seckill-config/page",params:i}),getSimpleSeckillConfigList:async()=>await t.get({url:"/promotion/seckill-config/list"}),getSeckillConfig:async i=>await t.get({url:"/promotion/seckill-config/get?id="+i}),createSeckillConfig:async i=>await t.post({url:"/promotion/seckill-config/create",data:i}),updateSeckillConfig:async i=>await t.put({url:"/promotion/seckill-config/update",data:i}),deleteSeckillConfig:async i=>await t.delete({url:"/promotion/seckill-config/delete?id="+i}),updateSeckillConfigStatus:async(i,e)=>{const a={id:i,status:e};return t.put({url:"/promotion/seckill-config/update-status",data:a})}};export{o as S};
