import{f as P,b as D,cF as S,e_ as T,ch as F,bz as E,R as Y,z as I}from"./index-CRsFgzy0.js";import{e as y,b as z,f as A}from"./tree-COGD3qag.js";import{_ as H}from"./DictTag.vue_vue_type_script_lang-Ett7EP4C.js";const{t:w}=D(),M=s=>{const a=P({searchSchema:[],tableColumns:[],formSchema:[],detailSchema:[]}),t=_(s,a);a.searchSchema=t||[];const l=x(s);a.tableColumns=l||[];const o=N(s,a);a.formSchema=o;const i=R(s);return a.detailSchema=i,{allSchemas:a}},_=(s,a)=>{const t=[],l=[];y(s,o=>{var i,p,f,m;if(o!=null&&o.isSearch||(i=o.search)!=null&&i.show){let u=((p=o==null?void 0:o.search)==null?void 0:p.component)||"Input";const e=[];let h={};if(o.dictType){const n={label:"\u5168\u90E8",value:""};e.push(n),S(o.dictType).forEach(c=>{e.push(c)}),h={options:e},(f=o.search)!=null&&f.component||(u="Select")}const d=T({component:u,...o.search,field:o.field,label:((m=o.search)==null?void 0:m.label)||o.label},{componentProps:h});d.api&&l.push(async()=>{var c;const n=await d.api();if(n){const r=F(a.searchSchema,b=>b.field===d.field);r!==-1&&(a.searchSchema[r].componentProps.options=v(n,(c=d.componentProps.optionsAlias)==null?void 0:c.labelField))}}),delete d.show,t.push(d)}});for(const o of l)o();return t},x=s=>{const a=z(s,{conversion:t=>{var l;if((t==null?void 0:t.isTable)!==!1&&((l=t==null?void 0:t.table)==null?void 0:l.show)!==!1)return!t.formatter&&t.dictType&&(t.formatter=(o,i,p)=>E(H,{type:t.dictType,value:p})),{...t.table,...t}}});return A(a,t=>(t.children===void 0&&delete t.children,!!t.field))},N=(s,a)=>{const t=[],l=[];y(s,o=>{var i,p,f,m,u;if((o==null?void 0:o.isForm)!==!1&&((i=o==null?void 0:o.form)==null?void 0:i.show)!==!1){let e=((p=o==null?void 0:o.form)==null?void 0:p.component)||"Input",h="";(f=o.form)!=null&&f.value?h=(m=o.form)==null?void 0:m.value:e==="InputNumber"&&(h=0);let d={};if(o.dictType){const c=[];o.dictClass&&o.dictClass==="number"?Y(o.dictType).forEach(r=>{c.push(r)}):o.dictClass&&o.dictClass==="boolean"?I(o.dictType).forEach(r=>{c.push(r)}):S(o.dictType).forEach(r=>{c.push(r)}),d={options:c},o.form&&o.form.component||(e="Select")}const n=T({component:e,value:h,...o.form,field:o.field,label:((u=o.form)==null?void 0:u.label)||o.label},{componentProps:d});n.api&&l.push(async()=>{var r;const c=await n.api();if(c){const b=F(a.formSchema,C=>C.field===n.field);b!==-1&&(a.formSchema[b].componentProps.options=v(c,(r=n.componentProps.optionsAlias)==null?void 0:r.labelField))}}),delete n.show,t.push(n)}});for(const o of l)o();return t},R=s=>{const a=[];return y(s,t=>{var l,o,i,p,f;if((t==null?void 0:t.isDetail)!==!1&&((l=t.detail)==null?void 0:l.show)!==!1){const m={...t.detail,field:t.field,label:((o=t.detail)==null?void 0:o.label)||t.label};t.dictType&&(m.dictType=t.dictType),((i=t.detail)!=null&&i.dateFormat||t.formatter=="formatDate")&&(m.dateFormat=(p=t==null?void 0:t.detail)!=null&&p.dateFormat?(f=t==null?void 0:t.detail)==null?void 0:f.dateFormat:"YYYY-MM-DD HH:mm:ss"),delete m.show,a.push(m)}}),a},v=(s,a)=>s==null?void 0:s.map(t=>(a?t.labelField=w(t.labelField):t.label=w(t.label),t));export{M as u};
