import{_ as I}from"./Form-CtCwm2Jr.js";import{r as v,aG as O,a as o,d as V,ah as f,b as B,a2 as R,aP as W,ch as G,c as T,o as w,F as H,g as P,a3 as x,bZ as K,y as Z,w as b,b3 as M,A as k,H as L,I as E,t as F,G as J,aw as Q,f as U,aK as D,aI as N,ak as $}from"./index-CvERnF9Y.js";import{d as X}from"./download-oWiM5xVU.js";const Y={key:0},ee=V({name:"Search",__name:"Search",props:{schema:{type:Array,default:()=>[]},isCol:f.bool.def(!1),labelWidth:f.oneOfType([String,Number]).def("auto"),layout:f.string.validate(e=>["inline","bottom"].includes(e)).def("inline"),buttomPosition:f.string.validate(e=>["left","center","right"].includes(e)).def("center"),showSearch:f.bool.def(!0),showReset:f.bool.def(!0),expand:f.bool.def(!1),expandField:f.string.def(""),inline:f.bool.def(!0),model:{type:Object,default:()=>({})}},emits:["search","reset"],setup(e,{emit:t}){const{t:g}=B(),d=e,_=t,m=v(!0),A=R(()=>{let s=W(d.schema);if(d.expand&&d.expandField&&!o(m)){const p=G(s,r=>r.field===d.expandField);if(p>-1){const r=s.length;s.splice(p+1,r)}}return d.layout==="inline"&&(s=s.concat([{field:"action",formItemProps:{labelWidth:"0px"}}])),s}),{register:h,elFormRef:a,methods:i}=(s=>{const p=v(),r=v(),l=async()=>(await O(),o(p)),C={setProps:async(c={})=>{const n=await l();n==null||n.setProps(c),c.model&&(n==null||n.setValues(c.model))},setValues:async c=>{const n=await l();n==null||n.setValues(c)},setSchema:async c=>{const n=await l();n==null||n.setSchema(c)},addSchema:async(c,n)=>{const z=await l();z==null||z.addSchema(c,n)},delSchema:async c=>{const n=await l();n==null||n.delSchema(c)},getFormData:async()=>{const c=await l();return c==null?void 0:c.formModel}};return s&&C.setProps(s),{register:(c,n)=>{p.value=c,r.value=n},elFormRef:r,methods:C}})({model:d.model||{}}),y=async()=>{var s;await((s=o(a))==null?void 0:s.validate(async p=>{if(p){const{getFormData:r}=i,l=await r();_("search",l)}}))},S=async()=>{var r;(r=o(a))==null||r.resetFields();const{getFormData:s}=i,p=await s();_("reset",p)},q=R(()=>({textAlign:d.buttomPosition})),j=()=>{var s;(s=o(a))==null||s.resetFields(),m.value=!o(m)};return(s,p)=>{const r=E,l=J,C=I;return w(),T(H,null,[P(C,{inline:e.inline,"is-col":e.isCol,"is-custom":!1,"label-width":e.labelWidth,schema:o(A),class:"-mb-15px","hide-required-asterisk":"",onRegister:o(h)},K({action:b(()=>[e.layout==="inline"?(w(),T("div",Y,[e.showSearch?(w(),k(l,{key:0,onClick:y},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:search"}),L(" "+F(o(g)("common.query")),1)]),_:1})):x("",!0),e.showReset?(w(),k(l,{key:1,onClick:S},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:refresh"}),L(" "+F(o(g)("common.reset")),1)]),_:1})):x("",!0),e.expand?(w(),k(l,{key:2,text:"",onClick:j},{default:b(()=>[L(F(o(g)(o(m)?"common.shrink":"common.expand"))+" ",1),P(r,{icon:o(m)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):x("",!0),M(s.$slots,"actionMore")])):x("",!0)]),_:2},[Z(Object.keys(s.$slots),c=>({name:c,fn:b(()=>[M(s.$slots,c)])}))]),1032,["inline","is-col","label-width","schema","onRegister"]),e.layout==="bottom"?(w(),T("div",{key:0,style:Q(o(q))},[e.showSearch?(w(),k(l,{key:0,type:"primary",onClick:y},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:search"}),L(" "+F(o(g)("common.query")),1)]),_:1})):x("",!0),e.showReset?(w(),k(l,{key:1,onClick:S},{default:b(()=>[P(r,{class:"mr-5px",icon:"ep:refresh-right"}),L(" "+F(o(g)("common.reset")),1)]),_:1})):x("",!0),e.expand?(w(),k(l,{key:2,text:"",onClick:j},{default:b(()=>[L(F(o(g)(o(m)?"common.shrink":"common.expand"))+" ",1),P(r,{icon:o(m)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):x("",!0),M(s.$slots,"actionMore")],4)):x("",!0)],64)}}}),{t:u}=B(),ae=e=>{const t=U({pageSize:10,currentPage:1,total:10,tableList:[],params:{...(e==null?void 0:e.defaultParams)||{}},loading:!0,exportLoading:!1,currentRow:null}),g=R(()=>({...t.params,pageSize:t.pageSize,pageNo:t.currentPage}));D(()=>t.currentPage,()=>{h.getList()}),D(()=>t.pageSize,()=>{t.currentPage===1||(t.currentPage=1),h.getList()});const d=v(),_=v(),m=async()=>(await O(),o(d)),A=async a=>{let i=1;a instanceof Array?(i=a.length,await Promise.all(a.map(async y=>{await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(y)))}))):await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(a))),$.success(u("common.delSuccess")),t.currentPage=(t.total%t.pageSize===i||t.pageSize===1)&&t.currentPage>1?t.currentPage-1:t.currentPage,await h.getList()},h={getList:async()=>{t.loading=!0;const a=await(e==null?void 0:e.getListApi(o(g)).finally(()=>{t.loading=!1}));a&&(t.tableList=a.list,t.total=a.total??0)},setProps:async(a={})=>{const i=await m();i==null||i.setProps(a)},setColumn:async a=>{const i=await m();i==null||i.setColumn(a)},getSelections:async()=>{const a=await m();return(a==null?void 0:a.selections)||[]},setSearchParams:a=>{t.params=Object.assign(t.params,{pageSize:t.pageSize,pageNo:1,...a}),t.currentPage!==1?t.currentPage=1:h.getList()},delList:async(a,i,y=!0)=>{const S=await m();!i||S!=null&&S.selections.length?y?N.confirm(u("common.delMessage"),u("common.confirmTitle"),{confirmButtonText:u("common.ok"),cancelButtonText:u("common.cancel"),type:"warning"}).then(async()=>{await A(a)}):await A(a):$.warning(u("common.delNoData"))},exportList:async a=>{t.exportLoading=!0,N.confirm(u("common.exportMessage"),u("common.confirmTitle"),{confirmButtonText:u("common.ok"),cancelButtonText:u("common.cancel"),type:"warning"}).then(async()=>{var y;const i=await((y=e==null?void 0:e.exportListApi)==null?void 0:y.call(e,o(g)));i&&X.excel(i,a)}).finally(()=>{t.exportLoading=!1})}};return e!=null&&e.props&&h.setProps(e.props),{register:(a,i)=>{d.value=a,_.value=i},elTableRef:_,tableObject:t,methods:h,tableMethods:h}};export{ee as _,ae as u};
