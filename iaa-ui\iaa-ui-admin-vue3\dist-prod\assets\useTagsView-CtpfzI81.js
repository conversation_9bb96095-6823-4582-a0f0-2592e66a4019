import{a as d}from"./tagsView-BnrVTrUo.js";import{u as h,a2 as u,a as r,aG as f}from"./index-CRsFgzy0.js";const n=()=>{const l=d(),{replace:o,currentRoute:i}=h(),t=u(()=>l.getSelectedTag);return{closeAll:e=>{l.delAllViews(),e==null||e()},closeLeft:e=>{l.delLeftViews(r(t)),e==null||e()},closeRight:e=>{l.delRightViews(r(t)),e==null||e()},closeOther:e=>{l.delOthersViews(r(t)),e==null||e()},closeCurrent:(e,s)=>{var a;(a=e==null?void 0:e.meta)!=null&&a.affix||(l.delView(e||r(i)),s==null||s())},refreshPage:async(e,s)=>{l.delCachedView();const{path:a,query:c}=e||r(i);await f(),o({path:"/redirect"+a,query:c}),s==null||s()},setTitle:(e,s)=>{l.setTitle(e,s)}}};export{n as u};
