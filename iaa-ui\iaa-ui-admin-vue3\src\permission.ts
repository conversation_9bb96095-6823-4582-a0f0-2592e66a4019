import router from './router'
import type { RouteRecordRaw } from 'vue-router'
import { isRelogin } from '@/config/axios/service'
import { getAccessToken } from '@/utils/auth'
import { useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { useDictStoreWithOut } from '@/store/modules/dict'
import { useUserStoreWithOut } from '@/store/modules/user'
import { usePermissionStoreWithOut } from '@/store/modules/permission'

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()

const parseURL = (
  url: string | null | undefined
): { basePath: string; paramsObject: { [key: string]: string } } => {
  // 如果输入为 null 或 undefined，返回空字符串和空对象
  if (url == null) {
    return { basePath: '', paramsObject: {} }
  }

  // 找到问号 (?) 的位置，它之前是基础路径，之后是查询参数
  const questionMarkIndex = url.indexOf('?')
  let basePath = url
  const paramsObject: { [key: string]: string } = {}

  // 如果找到了问号，说明有查询参数
  if (questionMarkIndex !== -1) {
    // 获取 basePath
    basePath = url.substring(0, questionMarkIndex)

    // 从 URL 中获取查询字符串部分
    const queryString = url.substring(questionMarkIndex + 1)

    // 使用 URLSearchParams 遍历参数
    const searchParams = new URLSearchParams(queryString)
    searchParams.forEach((value, key) => {
      // 封装进 paramsObject 对象
      paramsObject[key] = value
    })
  }

  // 返回 basePath 和 paramsObject
  return { basePath, paramsObject }
}

// 路由不重定向白名单
const whiteList = [
  '/login',
  '/social-login',
  '/auth-redirect',
  '/bind',
  '/agent/im/target-path',
  '/agent/form/workOrderForm',
  '/agent/form/workUsers',
  '/target-path',
  '/oauthLogin/gitee'
]

// 微信公众号页面列表（这些页面需要特殊处理，不显示重新登录弹窗）
const wechatPages = [
  '/agent/form/workOrderForm',
  '/agent/form/workUsers'
]

// 路由加载前
router.beforeEach(async (to, from, next) => {
  start()
  loadStart()

  // 检查是否是白名单路由
  const isWhiteListRoute = whiteList.indexOf(to.path) !== -1;
  // 检查是否是微信公众号页面
  const isWechatPage = wechatPages.indexOf(to.path) !== -1;

  if (getAccessToken()) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else if (isWhiteListRoute) {
      // 对于白名单路由，直接放行，不加载用户信息
      console.log('白名单路由，直接放行:', to.path);
      next()
    } else {
      // 获取所有字典
      const dictStore = useDictStoreWithOut()
      const userStore = useUserStoreWithOut()
      const permissionStore = usePermissionStoreWithOut()

      try {
        if (!dictStore.getIsSetDict) {
          await dictStore.setDictMap()
        }

        if (!userStore.getIsSetUser) {
          // 对于微信公众号页面，不显示重新登录提示，避免用户体验问题
          if (!isWechatPage) {
            isRelogin.show = true
          }
          await userStore.setUserInfoAction()
          if (!isWechatPage) {
            isRelogin.show = false
          }
          // 后端过滤菜单
          await permissionStore.generateRoutes()
          permissionStore.getAddRouters.forEach((route) => {
            router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
          })
          const redirectPath = from.query.redirect || to.path
          // 修复跳转时不带参数的问题
          const redirect = decodeURIComponent(redirectPath as string)
          const { paramsObject: query } = parseURL(redirect)
          const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect, query }
          next(nextData)
        } else {
          next()
        }
      } catch (error) {
        console.error('路由守卫加载用户信息失败:', error);

        // 如果加载用户信息失败，但是有 token，可能是 token 过期或无效
        // 对于非白名单路由，重定向到登录页
        if (!isWhiteListRoute) {
          next(`/login?redirect=${to.fullPath}`)
        } else {
          // 对于白名单路由，即使加载用户信息失败，也允许访问
          next()
        }
      }
    }
  } else {
    if (isWhiteListRoute) {
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
    }
  }
})

router.afterEach((to) => {
  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
})
