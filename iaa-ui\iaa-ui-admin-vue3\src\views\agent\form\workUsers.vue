<template>
  <div class="container">
    <!-- 骨架屏 -->
    <el-skeleton :rows="6" animated v-if="loading" />
    <ContentWrap v-else-if="!isMobile">
      <!-- PC端表格 -->
      <el-table :data="pcList" stripe @row-click="handleRowClick">
        <el-table-column label="工单编码" align="center" prop="workId" width="330px" />
        <el-table-column label="工单类型" align="center" prop="workType">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.HBY_WORK_TYPE" :value="scope.row.workType" />
          </template>
        </el-table-column>
        <el-table-column label="产品型号" align="center" prop="productsModel">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.HBY_PRODUCTS_MODEL" :value="scope.row.productsModel" />
          </template>
        </el-table-column>
        <el-table-column label="产品数量" align="center" prop="productsNum" />
        <el-table-column prop="problemType" label="问题分类" width="200px">
          <template #default="scope">
            <template v-if="scope.row.problemType">
              <template v-for="value in scope.row.problemType.split(',')" :key="value">
                <dict-tag v-if="value" :key="value" :type="DICT_TYPE.HBY_QUESTION_CLASSIFICATION"
                  :value="parseInt(value)" style="margin:2px" />
              </template>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="问题描述" :height="100" align="center" prop="remark" :show-overflow-tooltip="true">
          <template #default="scope">
            <span v-html="scope.row.remark"></span>
          </template>
        </el-table-column>
        <el-table-column label="工单状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.HBY_WORK_STATUS" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }: { row: Ticket }">
            <span class="detail-link" @click="handleRowClick(row)">查看详情</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination :total="pcTotal" v-model:page="pcParams.pageNo" v-model:limit="pcParams.pageSize"
        @pagination="handlePcPagination" />
    </ContentWrap>
    <!-- 移动端卡片 -->
    <div v-else class="mobile-container">
      <!-- 下拉刷新提示 -->
      <div class="refresh-tip" :style="{ transform: `translateY(${pullDistance}px)` }">
        ↓ 下拉刷新
      </div>
      <!-- 列表容器 -->
      <div ref="ticketListRef" class="ticket-list-container" @scroll="handleMobileScroll">
        <!-- 列表项 -->
        <div v-for="ticket in mobileList" :key="ticket.id" class="ticket-card" @click="handleRowClick(ticket)">
          <!-- 显示工单编码和状态 -->
          <div class="card-header">
            <span class="title">类型：<dict-tag :type="DICT_TYPE.HBY_WORK_TYPE" :value="ticket.workType" /></span>
            <span class="title">产品：<dict-tag :type="DICT_TYPE.HBY_PRODUCTS_MODEL"
                :value="ticket.productsModel" /></span>
            <dict-tag :type="DICT_TYPE.HBY_WORK_STATUS" :value="ticket.status" />
          </div>
          <!-- 创建时间 -->
          <div style="margin-top: 20px;">
            <div class="meta">创建时间：{{ formatDate(ticket.createTime) }}</div>
          </div>
        </div>
        <!-- 加载提示 -->
        <div v-if="mobileLoadingMore" class="loading-indicator">
          <el-icon>
            <Loading />
          </el-icon>
        </div>
        <!-- 数据结束提示 -->
        <div v-if="!mobileCanLoadMore && !mobileLoadingMore" class="no-more-data">
          没有更多数据了
        </div>
      </div>
    </div>
    <!-- PC端抽屉 -->
    <el-drawer v-model="drawerVisible" title="工单详情" direction="rtl" size="40%">
      <TicketDetail v-if="currentList" :ticket="currentList" :logs="currentLogs" :progress="workProgressList" :progress-loading="progressLoading" @close="drawerVisible = false" />
    </el-drawer>

    <!-- 移动端弹层部分 -->

    <transition name="modal-fade">
      <div v-if="mobileDetailVisible" class="mobile-detail-modal-container" @click="handleMaskClick">
        <div ref="modalElement" class="mobile-detail-modal" :class="{ 'modal-show': mobileDetailVisible }"
          @touchstart="handleModalTouchStart" @touchmove="handleModalTouchMove">
          <!-- 顶部操作栏 -->
          <div class="modal-header" @click="mobileDetailVisible = false">
            <div class="drag-handle"></div>
            <h3>工单详情</h3>
            <el-icon class="close-icon">
              <Close />
            </el-icon>
          </div>

          <!-- 内容区域 -->
          <div class="modal-content">
            <TicketDetail v-if="currentList" :ticket="currentList" :logs="currentLogs" :progress="workProgressList" :progress-loading="progressLoading" :is-mobile="true"
              @close="mobileDetailVisible = false" />
          </div>
        </div>
      </div>
    </transition>
  </div>

</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { dateFormatter, formatDate } from '@/utils/formatTime';
import { Close, Loading } from '@element-plus/icons-vue';
import { useWindowSize } from '@vueuse/core';
import { ElTable, ElTableColumn, ElTag, ElDrawer, ElSkeleton } from 'element-plus';
import { WorkorderApi, WorkorderVO } from '@/api/workorder/worklist/index'
import TicketDetail from './TicketDetail.vue';
import { DICT_TYPE } from '@/utils/dict'
import request from '@/config/axios'
import { getOpenid,setTokenAndOpenid,getAccessToken,removeToken} from '@/utils/auth'
import { useRoute } from 'vue-router';

interface TicketLog {
  createTime: Date
  operationContent: string
  status: number // 0 已创建 1 已审核 2 已退回 3 已完成
  logType: number // 0 操作日志 1 流程变动日志
}
// 响应式状态
const ticketListRef = ref<HTMLElement | null>(null);
// 响应式状态
const drawerVisible = ref(false);
const mobileDetailVisible = ref(false); // 确保初始值为 false
const pullDistance = ref(0);
const modalElement = ref<HTMLElement | null>(null);
const loading = ref(true) // 列表的加载中
const currentList = ref<WorkorderVO | null>(null);// 当前选中的工单
const currentLogs = ref<TicketLog[]>([]);
const pcParams = reactive({
  pageNo: 1,
  pageSize: 10,
  openid: '',
})
// 工作进度列表
const workProgressList = ref<any[]>([])
const progressLoading = ref(false)
const mobileParams = reactive({
  pageNo: 1,
  pageSize: 10,
  openid: '',
});
// 独立数据列表
const pcList = ref<WorkorderVO[]>([]);
const mobileList = ref<WorkorderVO[]>([]);
const pcTotal = ref(0);
const mobileTotal = ref(0);

// 移动端状态
const mobileLoadingMore = ref(false);
const mobileCanLoadMore = ref(true); // 是否可以加载更多

// PC端分页处理
const handlePcPagination = async () => {
  try {
    const openid = getOpenid();
    console.log('PC端分页处理，使用 openid:', openid);

    if (!openid) {
      console.error('PC端分页处理失败: openid 为空');
      throw new Error('openid 为空');
    }

    pcParams.openid = openid;
    const data = await WorkorderApi.getWorkorderPageUser(pcParams);
    pcList.value = data.list;
    pcTotal.value = data.total;
    console.log('PC端数据加载成功，共', data.total, '条数据');
  } catch (error) {
    console.error('PC端分页处理失败:', error);
    throw error;
  } finally {
    // ✅ 关闭全局加载状态
    loading.value = false;
  }
};


// 移动端滚动加载
const handleMobileScroll = () => {
  if (!mobileCanLoadMore.value || mobileLoadingMore.value) return;
  const { scrollTop, scrollHeight, clientHeight } = ticketListRef.value!;
  if (scrollHeight - scrollTop <= clientHeight + 20) {
    mobileLoadingMore.value = true;
    mobileParams.pageNo++;
    getMobileList();
  }
};

// 移动端数据请求
const getMobileList = async (isRefresh = false) => {
  try {
    const openid = getOpenid();
    console.log('移动端数据请求，使用 openid:', openid);

    if (!openid) {
      console.error('移动端数据请求失败: openid 为空');
      throw new Error('openid 为空');
    }

    mobileParams.openid = openid;
    const data = await WorkorderApi.getWorkorderPageUser(mobileParams);

    if (isRefresh) {
      mobileList.value = data.list;
    } else {
      mobileList.value.push(...data.list);
    }

    mobileTotal.value = data.total;
    console.log('移动端数据加载成功，共', data.total, '条数据');

    if (data.list.length < mobileParams.pageSize) {
      mobileCanLoadMore.value = false;
    }
  } catch (error) {
    console.error('移动端数据请求失败:', error);
    throw error;
  } finally {
    mobileLoadingMore.value = false;
  }
};
// 下拉刷新逻辑
const handleTouchEnd = () => {
  if (pullDistance.value >= 60) {
    // 下拉刷新时重置分页参数
    mobileParams.pageNo = 1;
    mobileCanLoadMore.value = true;
    mobileList.value = []; // 清空列表数据
    getMobileList(true);
  }
  pullDistance.value = 0;
};



// 触摸事件状态
let startY = 0;
let currentY = 0;

// 响应式布局
const { width: windowWidth } = useWindowSize();
const isMobile = computed(() => windowWidth.value < 768);

// 新增点击遮罩关闭逻辑
const handleMaskClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (target.classList.contains('mobile-detail-modal-container')) {
    mobileDetailVisible.value = false;
  }
};

// 表格点击处理
const handleRowClick = async (row: any) => {
  currentList.value = row;
  progressLoading.value = true;
  try {
    const [logs, progressRes] = await Promise.all([
      WorkorderApi.getTicketLogs(row.id, 0), // 操作日志
      WorkorderApi.getWorkProgressList({ workTypeId: row.workType,workOrderId: row.id }), // 进度节点（已按数字降序）
    ]);

    workProgressList.value = progressRes;

    currentLogs.value = logs;
  } finally {
    progressLoading.value = false;
  }

  if (isMobile.value) {
    mobileDetailVisible.value = true;
  } else {
    drawerVisible.value = true;
  }
};
// 移动端下拉刷新逻辑
const handleTouchStart = (e: TouchEvent) => {
  if (!isMobile.value) return;
  startY = e.touches[0].clientY;
};

const handleTouchMove = (e: TouchEvent) => {
  if (!isMobile.value) return;
  currentY = e.touches[0].clientY;
  const scrollTop = document.documentElement.scrollTop;

  // 检查是否处于滚动状态
  if (scrollTop > 0) {
    return;
  }

  if (scrollTop === 0 && currentY > startY) {
    pullDistance.value = Math.min((currentY - startY) * 0.5, 100);
  }
};
const redirectToAuth = async () => {
  try {
    // 清理当前URL，移除之前的code和state参数，避免参数累积
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.delete('code');
    currentUrl.searchParams.delete('state');
    const cleanUrl = currentUrl.toString();
    const encodedUrl = encodeURIComponent(cleanUrl);

    console.log('清理后的重定向URL:', cleanUrl);

    // 调用后端接口获取微信授权链接
    const response = await request.get({
      url: '/system/auth/social-auth-redirect-wx',
      params: {
        type: 31, // 社交平台类型，31为微信公众号
        redirectUri: cleanUrl
      }
    });

    // 替换 redirect_uri 的值为编码后的 URL
    const redirectUrl = response.replace(
      /redirect_uri=([^&]*)/,
      `redirect_uri=${encodedUrl}`
    );

    console.log('跳转到微信授权链接:', redirectUrl);

    // 跳转到微信授权链接
    window.location.href = redirectUrl;
  } catch (error) {
    console.error('获取授权链接失败:', error);
  }
};

const fetchToken = async () => {
  try {
    console.log('开始获取 Token，code:', code);

    const response = await request.post({
      url: '/system/wxauth/social-login',
      data: {
        type: 31, // 社交平台类型，31为微信公众号
        code,
        state
      }
    });

    console.log('获取 Token 成功，openid:', response.openid);

    // 存储 Token
    setTokenAndOpenid(response);

    return response.openid;
  } catch (error) {
    console.error('获取 Token 失败:', error);
    throw error;
  }
};
// 移动端弹层触摸处理
const touchStartY = ref(0);

// 触摸处理逻辑
const handleModalTouchStart = (e: TouchEvent) => {
  // 只在顶部栏触发滑动关闭
  const target = e.target as HTMLElement;
  if (!target.closest('.modal-header')) return;

  touchStartY.value = e.touches[0].clientY;
};

// 触摸滑动逻辑
const handleModalTouchMove = (e: TouchEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest('.modal-header')) return;

  const deltaY = e.touches[0].clientY - touchStartY.value;
  if (deltaY > 30) {
    modalElement.value!.style.transform = `translateY(${deltaY * 0.5}px)`;
    if (deltaY > 100) {
      mobileDetailVisible.value = false;
    }
  }
};
// 获取路由参数
const route = useRoute();
// 只取第一个code和state参数，避免多个参数导致的问题
const code = Array.isArray(route.query.code) ? route.query.code[0] : route.query.code as string | undefined;
const state = Array.isArray(route.query.state) ? route.query.state[0] : route.query.state as string | undefined;
// 添加重试计数器，防止无限循环
const authRetryCount = ref(0);
const MAX_AUTH_RETRY = 3;

// 检查token是否有效的函数
const checkTokenValid = async () => {
  const accessToken = getAccessToken();
  if (!accessToken) {
    return false;
  }

  try {
    // 尝试调用一个简单的API来验证token是否有效
    await request.get({
      url: '/rpc-api/system/oauth2/token/check',
      params: { accessToken }
    });
    return true;
  } catch (error) {
    console.log('Token验证失败:', error);
    return false;
  }
};

// 生命周期
onMounted(async () => {
  try {
    console.log('工单列表页面初始化...', { code, state });

    // 如果URL中有多个code参数，说明可能存在循环重定向问题
    if (window.location.href.includes('code=') && window.location.href.split('code=').length > 2) {
      console.warn('检测到URL中有多个code参数，可能存在循环重定向问题');
      // 清理URL并重新开始
      const cleanUrl = new URL(window.location.href);
      cleanUrl.searchParams.delete('code');
      cleanUrl.searchParams.delete('state');
      window.history.replaceState({}, '', cleanUrl.toString());
      // 重新开始授权流程
      await redirectToAuth();
      return;
    }

    // 检查是否已有 openid
    const existingOpenid = getOpenid();
    const existingToken = getAccessToken();
    console.log('现有 openid:', existingOpenid, '现有 token:', existingToken ? '存在' : '不存在');

    if (existingOpenid && existingToken) {
      // 如果已有 openid 和 token，先验证token是否有效
      console.log('检查现有token是否有效...');
      const isTokenValid = await checkTokenValid();

      if (isTokenValid) {
        console.log('Token有效，直接加载数据');
        try {
          // 加载数据
          loading.value = true;
          await handlePcPagination(); // 初始化PC列表
          await getMobileList(true);  // 初始化移动端列表
        } catch (error) {
          console.error('加载数据失败:', error);
          // 数据加载失败，可能是其他问题，重新授权
          removeToken();
          localStorage.removeItem('OPEN_ID');
          sessionStorage.removeItem('OPEN_ID');

          if (authRetryCount.value < MAX_AUTH_RETRY) {
            authRetryCount.value++;
            await redirectToAuth();
          } else {
            console.error('授权重试次数超过限制，停止重试');
            loading.value = false;
          }
        }
      } else {
        console.log('Token无效，清除缓存并重新授权');
        // Token无效，清除缓存并重新授权
        removeToken();
        localStorage.removeItem('OPEN_ID');
        sessionStorage.removeItem('OPEN_ID');

        if (authRetryCount.value < MAX_AUTH_RETRY) {
          authRetryCount.value++;
          await redirectToAuth();
        } else {
          console.error('授权重试次数超过限制，停止重试');
          loading.value = false;
        }
      }
    } else if (code && state) {
      // 有 code，进行授权
      console.log('使用 code 授权:', { code, state });
      try {
        await fetchToken();
        loading.value = true;
        await handlePcPagination(); // 初始化PC列表
        await getMobileList(true);  // 初始化移动端列表

        // 授权成功后清理URL中的code和state参数
        const cleanUrl = new URL(window.location.href);
        cleanUrl.searchParams.delete('code');
        cleanUrl.searchParams.delete('state');
        window.history.replaceState({}, '', cleanUrl.toString());
      } catch (error) {
        console.error('授权失败:', error);
        // 授权失败，重新跳转到微信授权页面
        if (authRetryCount.value < MAX_AUTH_RETRY) {
          authRetryCount.value++;
          await redirectToAuth();
        } else {
          console.error('授权重试次数超过限制，停止重试');
          loading.value = false;
        }
      }
    } else {
      // 无 code 且无 openid，跳转到微信授权页面
      console.log('无 code 且无 openid，跳转到微信授权页面');
      if (authRetryCount.value < MAX_AUTH_RETRY) {
        authRetryCount.value++;
        await redirectToAuth();
      } else {
        console.error('授权重试次数超过限制，停止重试');
        loading.value = false;
      }
    }

    // 添加事件监听器
    document.addEventListener('touchstart', handleTouchStart);
    document.addEventListener('touchmove', handleTouchMove);
    document.addEventListener('touchend', handleTouchEnd);
  } catch (error) {
    console.error('页面初始化失败:', error);
    loading.value = false;
  }
});

onUnmounted(() => {
  document.removeEventListener('touchstart', handleTouchStart);
  document.removeEventListener('touchmove', handleTouchMove);
  document.removeEventListener('touchend', handleTouchEnd);
});
</script>


<style scoped>
.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
}

.mobile-container {
  position: relative;
  height: 100vh;
}

.ticket-list-container {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding: 10px;
}

/* 新增数据加载完毕样式 */
.no-more-data {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.loading-indicator {
  text-align: center;
  padding: 20px;
  color: #999;
}

.refresh-tip {
  text-align: center;
  color: #999;
  font-size: 12px;
  height: 40px;
  transition: transform 0.3s;
}

.ticket-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  padding: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.card-header .title {
  font-weight: bold;
  font-size: 16px;
}

.meta {
  color: #666;
  font-size: 12px;
  margin-bottom: 10px;
}

.detail-link {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
}

@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .el-table {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-container {
    display: none;
  }
}

/* 移动端弹层样式 */
.mobile-detail-modal {
  position: fixed;
  top: 20vh;
  /* 留出顶部空间 */
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.modal-header {
  position: relative;
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid #eee;

  /* 顶部拖动指示条 */
  .drag-handle {
    width: 40px;
    height: 4px;
    background: #ddd;
    border-radius: 2px;
    margin: 0 auto 8px;
  }

  h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }

  .close-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    color: #666;
  }
}

.modal-content {
  height: calc(100% - 60px);
  /* 留出顶部操作栏空间 */
  overflow-y: auto;
  padding: 16px;
}

/* 优化内容展示 */
.ticket-detail.mobile-view {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .content-section,
  .log-section {
    margin-bottom: 20px;

    h4 {
      font-size: 15px;
      margin-bottom: 12px;
    }
  }

  .el-timeline-item {
    font-size: 14px;
  }
}

.mobile-detail-modal.modal-show {
  transform: translateY(0);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 新增遮罩层样式 */
.mobile-detail-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

/* 过渡动画样式 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: all 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

/* 弹窗位置调整 */
.mobile-detail-modal {
  position: absolute;
  top: 20vh;
  left: 0;
  right: 0;
  bottom: 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}
</style>